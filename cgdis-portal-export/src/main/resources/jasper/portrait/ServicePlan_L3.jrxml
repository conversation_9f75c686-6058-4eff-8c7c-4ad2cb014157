<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="Ass_Interventions_ServicePlanning_L3_portrait" columnCount="3" printOrder="Horizontal" pageWidth="535" pageHeight="842" columnWidth="175" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="a1da62cc-943e-44ea-bbd6-235025da1099">
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="lu.fujitsu.ts.cgdis.portal.core.domain.serviceplan.prestation.PrestationPerson"/>
	<field name="person" class="lu.fujitsu.ts.cgdis.portal.core.domain.serviceplan.prestation.PrestationPerson"/>
    <field name="position" class="lu.fujitsu.ts.cgdis.portal.core.domain.ServicePlanPosition"/>
	<field name="timeSlotId" class="java.lang.Long"/>
	<field name="barracked" class="java.lang.Boolean"/>
	<detail>
		<band height="16">
			<rectangle>
				<reportElement x="0" y="0" width="175" height="16" forecolor="#000000" backcolor="#FFFFFF" uuid="fec68f6d-bcc4-4648-ae83-f44bb0c5d08c"/>
				<graphicElement>
					<pen lineWidth="0.5" lineStyle="Solid"/>
				</graphicElement>
			</rectangle>



			<textField>
				<reportElement x="15" y="2" width="165" height="14" uuid="19c0dd64-8171-4484-aeed-f89a130bf110">
					<printWhenExpression><![CDATA[new Boolean($F{person}.getLastName()!=null && $F{person}.getFirstName()!=null)]]></printWhenExpression>
				</reportElement>

				<textFieldExpression><![CDATA[($F{person}.getCgdisRegistrationNumber() != null && $F{person}.getCgdisRegistrationNumber().length()>0 ? "  " + $F{person}.getCgdisRegistrationNumber() + "  " : " ")  + $F{person}.getLastName() + " " + $F{person}.getFirstName().substring(0,1) + "."]]></textFieldExpression>
<!--                <textFieldExpression><![CDATA[$F{position}.getLabel()]]></textFieldExpression>-->

			</textField>

			<image>
				<reportElement x="5" y="2" width="10" height="14" uuid="19c0dd64-8171-4484-aeed-f89a130bf111">
					<printWhenExpression><![CDATA[($F{barracked} != null && $F{barracked} == true)]]></printWhenExpression>
				</reportElement>
                <imageExpression><![CDATA[ "images/icon-barricaded-red.png" ]]></imageExpression>
			</image>
		</band>
	</detail>
</jasperReport>
