package lu.fujitsu.ts.cgdis.portal.export.properties;

import lombok.Data;
import lu.fujitsu.ts.cgdis.portal.export.properties.prestations.ExportPrestationsProperties;
import lu.fujitsu.ts.cgdis.portal.export.properties.serviceplan.ExportServicePlanProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.NestedConfigurationProperty;
import org.springframework.stereotype.Component;

/**
 * The type Export properties.
 */
@Data
@Component
@ConfigurationProperties(prefix = "cgdis-portal.export")
public class ExportProperties {
    /**
     * The Service plan properties.
     */
    @NestedConfigurationProperty
    private ExportServicePlanProperties servicePlanProperties;

    /**
     * The Jasper templates path.
     */
    private String jasperTemplatesPath;

    /**
     * In Milliseconds
     */
    private long defaultFileCreationTimeout;

    /**
     * The Prestations.
     */
    @NestedConfigurationProperty
    private ExportPrestationsProperties prestations;




}
