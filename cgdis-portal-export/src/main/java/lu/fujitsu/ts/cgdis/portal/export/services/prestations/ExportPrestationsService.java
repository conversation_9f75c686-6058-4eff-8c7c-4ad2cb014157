package lu.fujitsu.ts.cgdis.portal.export.services.prestations;

import lu.fujitsu.ts.cgdis.portal.connector.portaldb.services.export.ExportPrestationsRepositoryService;
import lu.fujitsu.ts.cgdis.portal.core.config.properties.CoreExportProperties;
import lu.fujitsu.ts.cgdis.portal.core.config.properties.CoreProperties;
import lu.fujitsu.ts.cgdis.portal.core.domain.export.ExportPrestation;
import lu.fujitsu.ts.cgdis.portal.core.exception.export.ExportDurationException;
import lu.fujitsu.ts.cgdis.portal.core.exception.export.GenerateExportException;
import lu.fujitsu.ts.cgdis.portal.export.domain.ExportConfig;
import lu.fujitsu.ts.cgdis.portal.export.properties.ExportProperties;
import lu.fujitsu.ts.cgdis.portal.export.properties.prestations.ExportPrestationsProperties;
import lu.fujitsu.ts.cgdis.portal.export.services.IExportGenerateService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.OutputStream;
import java.time.Duration;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Export all prestations (Export Backup 112)
 */
@Service
public class ExportPrestationsService {

    /**
     * The constant LOGGER.
     */
    private static final Logger LOGGER = LoggerFactory.getLogger(ExportPrestationsService.class);
    /**
     * The Xlsx export generate service.
     */
    private IExportGenerateService xlsxExportGenerateService;
    private IExportGenerateService csvExportGenerateService;
    /**
     * The Properties.
     */
    private final ExportPrestationsProperties properties;
    /**
     * The Export prestations repository service.
     */
    private ExportPrestationsRepositoryService exportPrestationsRepositoryService;
    /**
     * The Export generate service.
     */
    private IExportGenerateService pdfExportGenerateService;
    /**
     * The Core export properties.
     */
    private CoreExportProperties coreExportProperties;

    /**
     * Instantiates a new Export prestations service.
     *
     * @param pdfExportGenerateService           the export generate service
     * @param xlsxExportGenerateService          the xlsx export generate service
     * @param exportProperties                   the export properties
     * @param exportPrestationsRepositoryService the export prestations repository service
     * @param coreProperties                     the core properties
     */
    @Autowired
    public ExportPrestationsService(
            @Qualifier("defaultExportJasperPdfService") IExportGenerateService pdfExportGenerateService,
            @Qualifier("defaultExportJasperXlsxService") IExportGenerateService xlsxExportGenerateService,
            @Qualifier("defaultExportJasperCsvService") IExportGenerateService csvExportGenerateService,
            ExportProperties exportProperties,
            ExportPrestationsRepositoryService exportPrestationsRepositoryService,
            CoreProperties coreProperties) {
        this.pdfExportGenerateService = pdfExportGenerateService;
        this.xlsxExportGenerateService = xlsxExportGenerateService;
        this.csvExportGenerateService = csvExportGenerateService;
        this.properties = exportProperties.getPrestations();
        this.exportPrestationsRepositoryService = exportPrestationsRepositoryService;
        this.coreExportProperties = coreProperties.getExport();
    }

    /**
     * Export prestations.
     *
     * @param pdfOutput  the output
     * @param xlsxOutput the xlsx output
     * @param csvOutput  the csv output
     * @param from       from date
     * @param to         to date (inclusive)
     * @throws GenerateExportException the generate export exception
     */
    @Transactional(readOnly = true)
    public void exportPrestations(OutputStream pdfOutput,OutputStream xlsxOutput,OutputStream csvOutput, LocalDate from, LocalDate to) throws GenerateExportException {
        LOGGER.info("Export all prestations from {} to {}", from, to);



        LocalDate startDate = from;
        if (startDate == null) {
            startDate = LocalDate.now();
            LOGGER.debug("use default start date now: {}", startDate);
        }

        if ( to == null ){
            to = startDate.plusDays(coreExportProperties.getPeriodDefaultDays());
            LOGGER.info("to date is null, use default duration to export all prestations: from {} to {}", startDate, to);
        }

        // Add + 1 to the duration because the to date is inclusive for the export BUT is exclusive for the method Period.between

        long nbDays = Duration.between(startDate.atStartOfDay(),to.atStartOfDay()).toDays() +1;

        int maxDays = coreExportProperties.getPeriodMaxDays();
        if ( nbDays>maxDays ||nbDays<=0) {
            throw new ExportDurationException(String.format("Export prestations (backup 112) invalid : from %s to %s, duration %s, max %s",startDate, to, nbDays, maxDays), nbDays, maxDays );
        }


        List<ExportPrestation> collect = this.exportPrestationsRepositoryService.findAll(startDate, to);
        Map<String, Object> pdfParameters = getParameters(startDate, to,false);
        Map<String, Object> pxlsxParameters = getParameters(startDate, to,true);
        Map<String, Object> csvParameters = getParameters(startDate, to,false);
        pdfExportGenerateService.generateCollection(pdfOutput, properties.getTemplate(), collect , pdfParameters);
        ExportConfig xlsxConfig = new ExportConfig();
        xlsxConfig.setIgnorePagination(Boolean.TRUE);
        xlsxExportGenerateService.generateCollection(xlsxOutput, properties.getTemplate(), collect , pxlsxParameters, xlsxConfig);
        csvExportGenerateService.generateCollection(csvOutput, properties.getTemplate(), collect , csvParameters);
    }

    private Map<String, Object> getParameters(LocalDate startDate, LocalDate to, boolean displayHeaderDate) {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("NO_DATA_FOUND_MESSAGE", "pas de data");
        parameters.put("DATE_FORMATTER", DateTimeFormatter.ofPattern("dd/MM/yy"));
        parameters.put("TIME_FORMATTER", DateTimeFormatter.ofPattern("HH:mm"));
        parameters.put("PRINT_HEADER_DATE", Boolean.valueOf(displayHeaderDate));
        DateTimeFormatter titleDateFormatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");
        parameters.put("REPORT_DATE_TITLE", String.format("Liste des permanences pour la période %s - %s", titleDateFormatter.format(startDate), titleDateFormatter.format(to)));
        return parameters;
    }

}
