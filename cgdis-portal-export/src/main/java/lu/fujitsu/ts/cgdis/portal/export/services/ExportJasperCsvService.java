package lu.fujitsu.ts.cgdis.portal.export.services;

import lu.fujitsu.ts.cgdis.portal.export.properties.ExportProperties;
import net.sf.jasperreports.engine.JasperPrint;
import net.sf.jasperreports.engine.export.JRCsvExporter;
import net.sf.jasperreports.export.SimpleCsvExporterConfiguration;
import net.sf.jasperreports.export.SimpleExporterInput;
import net.sf.jasperreports.export.SimpleWriterExporterOutput;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Service;

import java.io.OutputStream;

/**
 * The type Export jasper pdf service.
 */
@Service("defaultExportJasperCsvService")
public  class ExportJasperCsvService extends AbstractExportJasperService<JRCsvExporter> {

    /**
     * The constant LOGGER.
     */
    private static final Logger LOGGER = LoggerFactory.getLogger(ExportJasperCsvService.class);

    @Autowired
    public ExportJasperCsvService(ExportProperties exportProperties, ResourceLoader resourceLoader) {
        super(exportProperties, resourceLoader);
    }


    @Override
    protected JRCsvExporter getExporter(JasperPrint jasperPrint, OutputStream writer) {
        JRCsvExporter exporter = new JRCsvExporter();
        exporter.setExporterInput(new SimpleExporterInput(jasperPrint));
        SimpleWriterExporterOutput exporterOutput = new SimpleWriterExporterOutput(writer,"ISO-8859-1");
        exporter.setExporterOutput(exporterOutput);


        SimpleCsvExporterConfiguration exportConfig
                = new SimpleCsvExporterConfiguration();
        exporter.setConfiguration(exportConfig);


        return exporter;
    }


}
