package lu.fujitsu.ts.cgdis.portal.export.csv;

import lu.fujitsu.ts.cgdis.portal.core.config.properties.CoreProperties;
import lu.fujitsu.ts.cgdis.portal.core.domain.serviceplan.ServicePlan;
import lu.fujitsu.ts.cgdis.portal.core.domain.serviceplan.prestation.Prestation;
import lu.fujitsu.ts.cgdis.portal.core.exception.CGDISTechnicalException;
import lu.fujitsu.ts.cgdis.portal.core.exception.export.ExportDurationException;
import lu.fujitsu.ts.cgdis.portal.core.security.ICGDISSecurityConstraint;
import lu.fujitsu.ts.cgdis.portal.core.security.utils.CGDISPortalSecurityHolder;
import lu.fujitsu.ts.cgdis.portal.export.IServicePlanCSVExportService;
import lu.fujitsu.ts.cgdis.portal.export.commons.Row;
import lu.fujitsu.ts.cgdis.portal.services.serviceplan.IPrestationRepositoryService;
import lu.fujitsu.ts.cgdis.portal.services.serviceplan.IServicePlanRepositoryService;
import lu.fujitsu.ts.cgdis.portal.services.serviceplan.IServicePlanVersionRepositoryService;
import lu.fujitsu.ts.cgdis.portal.services.serviceplan.IServicePlanVersionTimeSlotRepositoryService;
import lu.fujitsu.ts.cgdis.portal.services.serviceplanmodel.IServicePlanModelVersionWithPositionsRepositoryService;
import lu.fujitsu.ts.eportal.server.core.exceptions.NotFoundException;
import org.springframework.stereotype.Service;

import java.io.Writer;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.StringJoiner;

@Service
public class ServicePlanCSVExportService extends AbstractServicePlanCSVExportService implements IServicePlanCSVExportService {
    public ServicePlanCSVExportService(IServicePlanRepositoryService servicePlanRepositoryService, IServicePlanVersionRepositoryService servicePlanVersionRepository, IServicePlanVersionTimeSlotRepositoryService servicePlanTimeSlotRepositoryService, IPrestationRepositoryService prestationRepositoryService, IServicePlanModelVersionWithPositionsRepositoryService servicePlanModelVersionWithPositionsRepositoryService, CoreProperties coreProperties) {
        super(servicePlanRepositoryService, servicePlanVersionRepository, servicePlanTimeSlotRepositoryService, prestationRepositoryService, servicePlanModelVersionWithPositionsRepositoryService, coreProperties);
    }

    @Override
    protected Row buildHeader(ServicePlan servicePlan) {
        LOGGER.debug("Build CSV header");
        if (servicePlan != null) {
            Row headerRow = new Row();
            ArrayList<String> header = new ArrayList<>();
            header.add(servicePlan.getEntity().getName());
            header.add(servicePlan.getLabel());
            headerRow.setElements(header);

            return headerRow;
        } else {
            LOGGER.error("Can not build CSV header, service plan is null");
            throw new CGDISTechnicalException("Can not build CSV header");
        }
    }



    @Override
    protected String buildPrestationRowPersonInformations(Prestation prestation) {
        return new StringJoiner(" ")
                .add(prestation.getPerson().getCgdisRegistrationNumber())
                .add(prestation.getPerson().getLastName())
                .add(prestation.getPerson().getFirstName())
                .toString();
    }

    @Override
    protected void buildPrestationRowBeforeSlotDatas(ServicePlan servicePlan, LocalDate date, ArrayList<String> elements) {
        // Add nothing before slots data
    }

    /**
     * Build a local date row
     *
     * @param date the date
     * @return the row
     */

    private Row buildDateRow(LocalDate date) {
        LOGGER.debug("Build date header for date {}", date);
        if (date != null) {
            Row dateRow = new Row();
            ArrayList<String> dateElement = new ArrayList<>();

            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("EEEE dd/MM/yyyy");
            String formattedString = date.format(formatter);

            dateElement.add(formattedString);
            dateRow.setElements(dateElement);
            return dateRow;
        } else {
            LOGGER.error("Can not build date header. The date is null");
            throw new CGDISTechnicalException("Can not build date header. The date is null");
        }
    }

    @Override
    public void generateServicePlanCSV(ICGDISSecurityConstraint securityConstraint, Writer writer, long servicePlanId, LocalDate from, LocalDate to, boolean smsi) {
        executeGenerateServicePlanCSV(securityConstraint, writer, servicePlanId, from, to);
    }

    @Override
    protected List<Row> buildRows(ICGDISSecurityConstraint securityConstraint, ServicePlan servicePlan , LocalDate from, LocalDate to) throws NotFoundException {
        List<Row> rows = new ArrayList<>();
        if (to == null) {
            to = from.plusDays(coreExportProperties.getPeriodDefaultDays());
        }

        // Add + 1 to the duration because the to date is inclusive for the export BUT is exclusive for the method Period.between

        long nbDays = Duration.between(from.atStartOfDay(), to.atStartOfDay()).toDays() + 1;
        int maxDays = coreExportProperties.getPeriodMaxDays();
        if (nbDays > maxDays || nbDays <= 0) {
            throw new ExportDurationException(String.format("Export service plan CSV period invalid : from %s to %s, duration %s, max %s", from, to, nbDays, maxDays), nbDays, maxDays);
        }


        // get prestations for each day between dates
        LocalDate maxDate = to.plusDays(1);
        LocalDateTime now = LocalDateTime.now();
        for (LocalDate date = from; date.isBefore(maxDate); date = date.plusDays(1)) {
            // add date
            rows.add(buildDateRow(date));
            List<Row> dayRows = buildOneDay(securityConstraint, servicePlan.getTecid(), servicePlan, now, date);
            if (!dayRows.isEmpty()) {
                rows.addAll(dayRows);
            }
        }
        return rows;
    }

    /**
     * Get a filename depending on the id of witch object to extract data
     *
     * @param servicePlanTecid the service plan id
     * @return the filename or null
     */
    @Override
    public String generateFilename(long servicePlanTecid) {

        String filenameFormat = "%s_%s%s_%s.csv";

        try {
            LOGGER.debug("Get service plan with tecid {}", servicePlanTecid);
            ServicePlan servicePlan = this.servicePlanRepositoryService.get(CGDISPortalSecurityHolder.getConnectedUserDetails(), servicePlanTecid);

            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
            String formattedString = LocalDate.now().format(formatter);
            String entityName = servicePlan.getEntity().getName();
            String servicePlanLabel = servicePlan.getLabel();

            String filename = String.format(filenameFormat, "PDS", formattedString, entityName, servicePlanLabel);
            filename = sanitize(filename);

            return filename;
        } catch (NotFoundException e) {
            throw new CGDISTechnicalException("Can find service plan");
        }
    }
}
