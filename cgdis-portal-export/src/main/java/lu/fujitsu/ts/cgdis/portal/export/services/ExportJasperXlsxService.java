package lu.fujitsu.ts.cgdis.portal.export.services;

import lu.fujitsu.ts.cgdis.portal.export.properties.ExportProperties;
import net.sf.jasperreports.engine.JasperPrint;
import net.sf.jasperreports.engine.export.ooxml.JRXlsxExporter;
import net.sf.jasperreports.export.SimpleExporterInput;
import net.sf.jasperreports.export.SimpleOutputStreamExporterOutput;
import net.sf.jasperreports.export.SimpleXlsxExporterConfiguration;
import net.sf.jasperreports.export.SimpleXlsxReportConfiguration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Service;

import java.io.OutputStream;

/**
 * The type Export jasper pdf service.
 */
@Service("defaultExportJasperXlsxService")
public  class ExportJasperXlsxService extends AbstractExportJasperService<JRXlsxExporter> {

    /**
     * The constant LOGGER.
     */
    private static final Logger LOGGER = LoggerFactory.getLogger(ExportJasperXlsxService.class);

    @Autowired
    public ExportJasperXlsxService(ExportProperties exportProperties, ResourceLoader resourceLoader) {
        super(exportProperties, resourceLoader);
    }


    @Override
    protected JRXlsxExporter getExporter(JasperPrint jasperPrint, OutputStream writer) {
        JRXlsxExporter exporter = new JRXlsxExporter();
        exporter.setExporterInput(new SimpleExporterInput(jasperPrint));
        exporter.setExporterOutput(new SimpleOutputStreamExporterOutput(writer));

        SimpleXlsxReportConfiguration reportConfig
                = new SimpleXlsxReportConfiguration();
        reportConfig.setForcePageBreaks(false);
        reportConfig.setOnePagePerSheet(false);

        SimpleXlsxExporterConfiguration exportConfig
                = new SimpleXlsxExporterConfiguration();
        exportConfig.setMetadataAuthor("Direction des Services de Secours");
        exporter.setConfiguration(reportConfig);
        exporter.setConfiguration(exportConfig);
        return exporter;
    }


}
