node('slave-docker-master') {
    def SOURCES_BRANCH = "${BRANCH_TO_DELIVER}"

    def releaseVersion = ""
    def nextVersion = ""
    def releaseTag = ""

    stage('Clean workspace') {
        deleteDir()
    }

    stage('Update sources') {
        git branch: '${<PERSON><PERSON>CH_TO_DELIVER}', changelog: false, credentialsId: 'jenkins_gitlab', poll: false, url: 'https://gitlab.az.fujitsubas.lu/client/protex/cgdis-portal.git'
        // Clean any locally modified files and ensure we are actually on origin/${DELIVERY_BRANCH}
        // as a failed release could leave the local workspace ahead of origin/${DELIVERY_BRANCH}

        
        withCredentials([usernamePassword(credentialsId: 'jenkins_gitlab', passwordVariable: 'GIT_PASSWORD', usernameVariable: 'GIT_USERNAME')]) {
            sh "git config credential.username ${env.GIT_USERNAME}"
            sh "git config credential.helper '!echo password=\$GIT_PASSWORD; echo'"
            //sh "GIT_ASKPASS=true git clean -f && git reset --hard origin/${SOURCES_BRANCH}"
            //sh "GIT_ASKPASS=true git clean -f && git reset --hard origin/${SOURCES_BRANCH}"
            sh "GIT_ASKPASS=true git fetch origin ${SOURCES_BRANCH}"
            //sh "GIT_ASKPASS=true git merge origin/${SOURCES_BRANCH} --ff-only"
        }

    }


    // Prepare the version by extracting major, minor and patch number and creating a new number: [major].[minor].[patch]
    stage("Set version") {
        def originalV = version();
        def major = originalV[0] as int;
        def minor = originalV[1] as int;
        def patch = originalV[2] as int;
        def patchNext = patch + 1;
        def v = "${major}.${minor}.${patch}"
        def vN = "${major}.${minor}.${patchNext}-SNAPSHOT"
        def versions = input(
                id: "snapshotVersion", message: "The versions ?", parameters: [
                [$class: "TextParameterDefinition", defaultValue: "${v}", description: "Release version", name: "release"],
                [$class: "TextParameterDefinition", defaultValue: "${vN}", description: "Next snapshot version", name: "next"]
        ]
        )
        releaseVersion = versions["release"]
        nextVersion = versions["next"]
        releaseTag = "Release-${releaseVersion}"

        withMaven(
                maven: 'Maven') {

            // Update the project pom.xml files
            sh "mvn -B versions:set -DgenerateBackupPoms=false -DnewVersion=${releaseVersion} -Dmaven.compiler.fork=true -Dmaven.compiler.executable=/opt/java-jdk/jdk1.8.0_191/bin/javac"
        }

        // Update test docker-stack
        sh "sed 's/:latest/:${releaseVersion}/g' ./environments/test/docker-stack-gateway.yml > ./environments/test/docker-stack-gateway-${releaseVersion}.yml"
        sh "ls ./environments/test/"
        sh "git add ./environments/test/docker-stack-gateway-${releaseVersion}.yml"

        sh "sed 's/:latest/:${releaseVersion}/g' ./environments/test/docker-stack-processes.yml > ./environments/test/docker-stack-processes-${releaseVersion}.yml"
        sh "ls ./environments/test/"
        sh "git add ./environments/test/docker-stack-processes-${releaseVersion}.yml"



        // Update test docker-stack
        sh "sed 's/:latest/:${releaseVersion}/g' ./environments/prod/docker-stack-gateway.yml > ./environments/prod/docker-stack-gateway-${releaseVersion}.yml"
        sh "ls ./environments/prod/"
        sh "git add ./environments/prod/docker-stack-gateway-${releaseVersion}.yml"

        sh "sed 's/:latest/:${releaseVersion}/g' ./environments/prod/docker-stack-processes.yml > ./environments/prod/docker-stack-processes-${releaseVersion}.yml"
        sh "ls ./environments/prod/"
        sh "git add ./environments/prod/docker-stack-processes-${releaseVersion}.yml"


        // Add the pom.xml files and create a commit+tag
        sh 'git add .'


        withCredentials([usernamePassword(credentialsId: 'jenkins_gitlab', passwordVariable: 'GIT_PASSWORD', usernameVariable: 'GIT_USERNAME')]) {
            sh "git config credential.username ${env.GIT_USERNAME}"
            sh "git config credential.helper '!echo password=\$GIT_PASSWORD; echo'"

            sh "GIT_ASKPASS=true git commit -m '[Jenkins] Moved to version ${releaseVersion}'"
            sh "GIT_ASKPASS=true git tag ${releaseTag} -f"
        }

    }
    // Builds the release
    stage('Build') {
        echo "Building version ${releaseVersion}"
        withMaven(
                maven: 'Maven') {

            // Update the project pom.xml files
            sh "mvn clean package deploy -Pprod -DskipTests -Dmaven.compiler.fork=true -Dmaven.compiler.executable=/opt/java-jdk/jdk1.8.0_191/bin/javac"
        }


        // Push the commit and the created tag
        echo "execute git push after build"
        withCredentials([usernamePassword(credentialsId: 'jenkins_gitlab', passwordVariable: 'GIT_PASSWORD', usernameVariable: 'GIT_USERNAME')]) {
            sh "git config credential.username ${env.GIT_USERNAME}"
            sh "git config credential.helper '!echo password=\$GIT_PASSWORD; echo'"

            sh "git status"
            sh "GIT_ASKPASS=true git push origin ${SOURCES_BRANCH}"
            sh "GIT_ASKPASS=true git push origin ${releaseTag}"
        }

    }


    stage('Build & Publish Docker images') {
        script {
            APP_VERSION = readMavenPom().getVersion()
        }
        parallel(
                "Portal Webapp": {
                    img_portal = docker.build("cgdis-portal-webapp:${releaseVersion}", "-f Dockerfile-springboot-app --build-arg JAR_FILE=cgdis-portal-webapp/target/cgdis-portal-webapp-${releaseVersion}.jar  --build-arg APP_VERSION=${releaseVersion} --build-arg APP_NAME=cgdis-portal .")
                    docker.withRegistry('http://nexus-linux-vm.fujitsubas.local:5080', 'jenkins_nexus') {
                        img_portal.push(APP_VERSION)
                        img_portal.push('latest')
                    }
                },
                "ESB - Internal Gateway": {
                    img_internal_gw = docker.build("cgdis-portal-internal-gw:${releaseVersion}", "-f Dockerfile-springboot-app --build-arg JAR_FILE=cgdis-portal-internal-gateway/target/cgdis-portal-internal-gateway-${releaseVersion}.jar  --build-arg APP_VERSION=${releaseVersion} --build-arg APP_NAME=internal-gw .")
                    docker.withRegistry('http://nexus-linux-vm.fujitsubas.local:5080', 'jenkins_nexus') {
                        img_internal_gw.push(APP_VERSION)
                        img_internal_gw.push('latest')
                    }
                },
                "ESB - Gateway": {
                    img_gateway = docker.build("cgdis-portal-gateway:${releaseVersion}", "-f Dockerfile-springboot-app --build-arg JAR_FILE=cgdis-portal-gateway/target/cgdis-portal-gateway-${releaseVersion}.jar  --build-arg APP_VERSION=${releaseVersion} --build-arg APP_NAME=gateway .")
                    docker.withRegistry('http://nexus-linux-vm.fujitsubas.local:5080', 'jenkins_nexus') {
                        img_gateway.push(APP_VERSION)
                        img_gateway.push('latest')
                    }
                },
                "ESB - Processes": {
                    img_processes = docker.build("cgdis-portal-processes:${releaseVersion}", "-f Dockerfile-springboot-app --build-arg JAR_FILE=cgdis-portal-processes/target/cgdis-portal-processes-${releaseVersion}.jar  --build-arg APP_VERSION=${releaseVersion} --build-arg APP_NAME=process-engine .")
                    docker.withRegistry('http://nexus-linux-vm.fujitsubas.local:5080', 'jenkins_nexus') {
                        img_processes.push(APP_VERSION)
                        img_processes.push('latest')
                    }

                },
                "ESB - Scheduler": {
                    img_scheduler = docker.build("cgdis-portal-scheduler:${releaseVersion}", "-f Dockerfile-springboot-app --build-arg JAR_FILE=cgdis-portal-scheduler/target/cgdis-portal-scheduler-${releaseVersion}.jar  --build-arg APP_VERSION=${releaseVersion} --build-arg APP_NAME=scheduler .")
                    docker.withRegistry('http://nexus-linux-vm.fujitsubas.local:5080', 'jenkins_nexus') {
                        img_scheduler.push(APP_VERSION)
                        img_scheduler.push('latest')
                    }
                },
                "ESB - Notifier": {
                    img_notifier = docker.build("cgdis-portal-notifier:${releaseVersion}", "-f Dockerfile-springboot-app --build-arg JAR_FILE=cgdis-portal-notifier/target/cgdis-portal-notifier-${releaseVersion}.jar  --build-arg APP_VERSION=${releaseVersion} --build-arg APP_NAME=notifier .")
                    docker.withRegistry('http://nexus-linux-vm.fujitsubas.local:5080', 'jenkins_nexus') {
                        img_notifier.push(APP_VERSION)
                        img_notifier.push('latest')
                    }
                },

        )

    }



}

def version() {
    def matcher = readFile('pom.xml') =~ '<version>(.+)-.*</version>'
    matcher ? matcher[0][1].tokenize(".") : null
}
