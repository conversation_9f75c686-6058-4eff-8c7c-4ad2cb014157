package lu.fujitsu.ts.cgdis.portal.webapp.aop.audit.aspect.modelversion;

import lu.fujitsu.ts.cgdis.portal.core.domain.audit.AuditActionType;
import lu.fujitsu.ts.cgdis.portal.core.domain.audit.AuditModelVersion;
import lu.fujitsu.ts.cgdis.portal.core.domain.serviceplanmodel.ServicePlanModelVersionWithPositions;
import lu.fujitsu.ts.cgdis.portal.core.service.serviceplanmodel.IServicePlanModelVersionBusinessService;
import lu.fujitsu.ts.eportal.server.core.exceptions.NotFoundException;
import org.aspectj.lang.ProceedingJoinPoint;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.Map;

@Component
public class ModelVersionDeleteAuditAspect extends AbstractModelVersionAuditAspect<ServicePlanModelVersionWithPositions> {

    private static final Logger LOGGER = LoggerFactory.getLogger(ModelVersionDeleteAuditAspect.class);


    public ModelVersionDeleteAuditAspect() {
        super(AuditActionType.DELETE, AuditModelVersion.class
        );
    }

       @Autowired
    private IServicePlanModelVersionBusinessService servicePlanModelVersionBusinessService;

    @Override
    protected void populateAuditAfterProcessingMethod(ProceedingJoinPoint point, Method method, AuditModelVersion audit, Map<String, Object> parameters, ServicePlanModelVersionWithPositions result) {

    }

    @Override
    protected void populateAuditBeforeProcessingMethod(ProceedingJoinPoint point, Method method, AuditModelVersion audit, Map<String, Object> parameters) {
        Long servicePlanModelVersionTecid = (Long) parameters.get("servicePlanModelVersionTecid");
        ServicePlanModelVersionWithPositions servicePlanModelVersion = null;
        try {
            servicePlanModelVersion = servicePlanModelVersionBusinessService.get(getUserDetails(), servicePlanModelVersionTecid);
            mapModelVersion(audit, servicePlanModelVersion);
        } catch (NotFoundException e) {
            LOGGER.debug("Error to find service plan model version with id "+servicePlanModelVersionTecid,e);
        }
    }

    @Override
    protected boolean mustCreateAuditLog(AuditModelVersion audit, Map<String, Object> parameters, ServicePlanModelVersionWithPositions result) {
        return true;
    }
}
