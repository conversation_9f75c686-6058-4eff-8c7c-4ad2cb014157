package lu.fujitsu.ts.cgdis.portal.webapp.controllers;

import lu.fujitsu.ts.cgdis.portal.core.domain.CGDISSecurityConstraint;
import lu.fujitsu.ts.cgdis.portal.core.domain.serviceplan.team.TeamWithMembers;
import lu.fujitsu.ts.cgdis.portal.core.exception.CGDISTechnicalException;
import lu.fujitsu.ts.cgdis.portal.core.logging.LoggingCategory;
import lu.fujitsu.ts.cgdis.portal.core.logging.LoggingDomain;
import lu.fujitsu.ts.cgdis.portal.core.security.ICGDISSecurityConstraint;
import lu.fujitsu.ts.cgdis.portal.core.security.Permission;
import lu.fujitsu.ts.cgdis.portal.core.service.serviceplan.team.ITeamBusinessService;
import lu.fujitsu.ts.cgdis.portal.webapp.controllers.admin.form.serviceplan.team.create.CreateTeamForm;
import lu.fujitsu.ts.cgdis.portal.webapp.controllers.admin.form.serviceplan.team.update.UpdateTeamForm;
import lu.fujitsu.ts.eportal.server.core.domain.SearchCriterion;
import lu.fujitsu.ts.eportal.server.core.exceptions.InvalidRQLExpressionException;
import lu.fujitsu.ts.eportal.server.core.exceptions.NotFoundException;
import lu.fujitsu.ts.eportal.server.core.utils.rql.SearchRqlParser;
import lu.fujitsu.ts.eportal.server.logging.milestones.LogMilestone;
import lu.fujitsu.ts.eportal.server.logging.milestones.LoggableValue;
import lu.fujitsu.ts.eportal.server.mvc.utils.rql.RqlParams;
import lu.fujitsu.ts.eportal.server.mvc.utils.rql.RqlRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Collections;
import java.util.List;

/**
 * The Service plan controller.
 */
@RestController
@RequestMapping("/admin/service-plan/{servicePlanId:[0-9]+}/teams")
@LogMilestone(category = LoggingCategory.REST_API_CALL, domain = LoggingDomain.TEAM)
public class AdminServicePlanTeamController  implements IPageRequestController {

private static final Logger LOGGER = LoggerFactory.getLogger(AdminServicePlanTeamController.class);

  private final SearchRqlParser rqlParser;
  private final String defaultSortField;
  private final Sort.Direction defaultSortDirection;
  private final int defaultPageSize;
  private final ITeamBusinessService service;

  /**
   * Instantiates a new Admin service plan controller.
   * @param service the service
   */
  @Autowired
  public AdminServicePlanTeamController(ITeamBusinessService service) {
    this.service = service;
    this.rqlParser = this.buildRqlParser();
    this.defaultSortField = "label";
    this.defaultSortDirection = Sort.Direction.ASC;
    this.defaultPageSize = 10;

  }

  protected List<SearchCriterion> parseRql(String rql) throws InvalidRQLExpressionException {
    return this.rqlParser.parse(rql);
  }

  public SearchRqlParser buildRqlParser() {
    SearchRqlParser parser = new SearchRqlParser(){};
    parser.addCriterionParser("tecid", SearchRqlParser.RQL_LONG_VALUE_PARSER);
    return parser;
  }

  @Override
  public PageRequest getPageRequest(RqlRequest rqlRequest) {
    return PageRequest.of(
      rqlRequest.getPage() == null ? 0 : rqlRequest.getPage(),
      rqlRequest.getPageSize() == null ? this.defaultPageSize : rqlRequest.getPageSize(),
      rqlRequest.getDirection() == null ? this.defaultSortDirection : rqlRequest.getDirection(),
      new String[]{StringUtils.isEmpty(rqlRequest.getOrderBy()) ? this.defaultSortField : rqlRequest.getOrderBy()});
  }

  @GetMapping(value = {"/", ""})
  public Page<TeamWithMembers> search(@PathVariable Long servicePlanId, @RqlParams RqlRequest rqlRequest) throws InvalidRQLExpressionException {
    PageRequest pageRequest = getPageRequest(rqlRequest);
    List<SearchCriterion> searchCriterions = this.parseRql(rqlRequest.getRql());
    searchCriterions.add(new SearchCriterion("servicePlanTecid", servicePlanId, SearchCriterion.Operator.eq));
    return (Page<TeamWithMembers>) this.service.search(this.getSecurityConstraint(), searchCriterions, pageRequest);
  }

  /**
   * The security constraint. By default, all visible entities by the connected user
   * @return the security constraint
   */
  public ICGDISSecurityConstraint getSecurityConstraint() {
    return new CGDISSecurityConstraint(Collections.singletonList(Permission.ROLE_PERMISSION_ADMIN_PDS_TEAM_VIEW));
  }

  public ICGDISSecurityConstraint getUpdateSecurityConstraint() {
    return new CGDISSecurityConstraint(Collections.singletonList(Permission.ROLE_PERMISSION_ADMIN_PDS_TEAM_UPDATE));
  }

  public ICGDISSecurityConstraint getCreateSecurityConstraint() {
    return new CGDISSecurityConstraint(Collections.singletonList(Permission.ROLE_PERMISSION_ADMIN_PDS_TEAM_CREATE));
  }

  public ICGDISSecurityConstraint getDeleteSecurityConstraint() {
    return new CGDISSecurityConstraint(Collections.singletonList(Permission.ROLE_PERMISSION_ADMIN_PDS_TEAM_DELETE));
  }


  @GetMapping("/{id}")
  @LogMilestone(action = "create team for service plan")
  // Do not delete @PathVariable, it's mandatory for OpenAPI generation
  public TeamWithMembers get(@PathVariable Long servicePlanId, @PathVariable("id") Long id) throws NotFoundException {
    return this.service.get(this.getSecurityConstraint(), id);
  }



  @GetMapping("/_count")
  // Do not delete @PathVariable, it's mandatory for OpenAPI generation
  public long countAll(@PathVariable Long servicePlanId, @RqlParams RqlRequest rqlRequest) throws InvalidRQLExpressionException {
    return this.service.count(this.getSecurityConstraint(), this.rqlParser.parse(rqlRequest.getRql()));
  }

  @PostMapping({"/", ""})
  @LogMilestone(action = "create team for service plan")
  public TeamWithMembers create(@PathVariable @LoggableValue Long servicePlanId, @Valid @RequestBody CreateTeamForm model) throws NotFoundException {
    LOGGER.debug("Create team for service plan {}: {}", servicePlanId, model);
    if (!servicePlanId.equals(model.getServicePlanId())) {
      throw new CGDISTechnicalException("service plan id in request body and in url are differents");
    }


    return this.service.create(getCreateSecurityConstraint(), servicePlanId, model.create());
  }

  @PutMapping({"/", ""})
  @LogMilestone(action = "update team for service plan")
  public TeamWithMembers update(@PathVariable @LoggableValue Long servicePlanId, @LoggableValue(value = "tecid") @Valid @RequestBody UpdateTeamForm model) throws NotFoundException {
    if (!servicePlanId.equals(model.getServicePlanId())) {
      throw new CGDISTechnicalException("service plan id in request body and in url are differents");
    }
    return this.service.update(getCreateSecurityConstraint(), servicePlanId, model);
  }

  @DeleteMapping({"/{id}"})
  @LogMilestone(action = "delete team for service plan")
  // Do not delete @PathVariable, it's mandatory for OpenAPI generation
  public void deleteById(@PathVariable @LoggableValue Long servicePlanId, @LoggableValue @PathVariable("id") Long id) {
    this.service.deleteById(getDeleteSecurityConstraint(),servicePlanId, id);
  }

}
