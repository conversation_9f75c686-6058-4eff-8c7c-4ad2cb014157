package lu.fujitsu.ts.cgdis.portal.webapp.controllers;

import lu.fujitsu.ts.cgdis.portal.core.domain.dashboard.DashboardMemberPlanningDay;
import lu.fujitsu.ts.cgdis.portal.core.security.utils.CGDISPortalSecurityHolder;
import lu.fujitsu.ts.cgdis.portal.core.service.person.IDashboardBusinessService;
import lu.fujitsu.ts.cgdis.portal.webapp.annotation.CGDISDateFormat;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.util.List;

/**
 * The type Dashboard controller.
 */
@RestController
@RequestMapping("/dashboard")
public class DashboardController {

  private IDashboardBusinessService dashboardBusinessService;

  @Autowired
  public DashboardController(IDashboardBusinessService dashboardBusinessService) {
    this.dashboardBusinessService = dashboardBusinessService;
  }

  /**
   * Get planning for given person and dates
   * @param personTecid the person id
   * @param startDate the start date
   * @param endDate the end date
   * @return the planning
   */
  @GetMapping("planning/{personTecid}")
  public List<DashboardMemberPlanningDay> getPersonPlanning(@PathVariable Long personTecid, @RequestParam(value = "startDate") @CGDISDateFormat LocalDate startDate, @RequestParam(value = "endDate") @CGDISDateFormat LocalDate endDate) {
    return dashboardBusinessService.getPersonPlanning(CGDISPortalSecurityHolder.getConnectedUserDetails(), personTecid, startDate, endDate);
  }

}
