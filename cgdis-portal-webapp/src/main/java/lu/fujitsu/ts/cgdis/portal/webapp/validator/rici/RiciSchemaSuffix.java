package lu.fujitsu.ts.cgdis.portal.webapp.validator.rici;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.*;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

@Target({METHOD, PARAMETER, TYPE, ANNOTATION_TYPE})
@Retention(RUNTIME)
@Constraint(validatedBy = {RiciSchemaSuffixValidator.class})
@Documented
public @interface RiciSchemaSuffix {
  String message() default "Ric schema suffix does already exist";

  Class<?>[] groups() default {};

  Class<? extends Payload>[] payload() default {};


  @Target({TYPE, ANNOTATION_TYPE})
  @Retention(RUNTIME)
  @Documented
  @interface List {
    RiciSchemaSuffix[] value();
  }
}
