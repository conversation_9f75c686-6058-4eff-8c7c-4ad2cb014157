package lu.fujitsu.ts.cgdis.portal.webapp.validator.required;

/**
 * Created by LUXJVERC on 22/05/2017.
 */
public class RequiredIfStringValueValidator extends AbstractRequiredIfValidator<RequiredIfStringValue,Object,String> {

  @Override
  protected String getFieldname(RequiredIfStringValue constraintAnnotation) {
    return constraintAnnotation.fieldName();
  }

  @Override
  protected String getDependsOnField(RequiredIfStringValue constraintAnnotation) {
    return constraintAnnotation.dependsOnField();
  }

  @Override
  protected String getDependsOnFieldValue(RequiredIfStringValue constraintAnnotation) {
    return constraintAnnotation.dependOnFieldValue();
  }

  @Override
  protected boolean isRequired(String dependentValue) {
    return dependsOnFieldValue.equals(dependentValue);
  }
}
