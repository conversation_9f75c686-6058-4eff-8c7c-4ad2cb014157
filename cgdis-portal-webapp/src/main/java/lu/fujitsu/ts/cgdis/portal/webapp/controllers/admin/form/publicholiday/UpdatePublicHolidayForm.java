package lu.fujitsu.ts.cgdis.portal.webapp.controllers.admin.form.publicholiday;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lu.fujitsu.ts.cgdis.portal.core.domain.BaseModel;
import lu.fujitsu.ts.cgdis.portal.core.domain.PublicHoliday;
import lu.fujitsu.ts.cgdis.portal.webapp.validator.FutureDate;
import lu.fujitsu.ts.eportal.server.core.domain.IModelUpdate;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;

@Data
@EqualsAndHashCode(callSuper = true)
public class UpdatePublicHolidayForm extends BaseModel implements IModelUpdate<PublicHoliday, Long> {

  @NotNull
  private String name;

  @NotNull
  @FutureDate
  private LocalDate date;



  @Override
  public PublicHoliday update(PublicHoliday updateModel) {
    if ( updateModel.getTecid() == null ){
      updateModel.setTecid(getTecid());
      updateModel.setTeclock(getTeclock());
    }
    updateModel.setName(getName());
    updateModel.setDate(getDate());
    updateModel.setYear((long) getDate().getYear());
    return updateModel;
  }
}
