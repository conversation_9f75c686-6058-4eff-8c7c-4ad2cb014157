package lu.fujitsu.ts.cgdis.portal.webapp.filter;


import ch.qos.logback.classic.ClassicConstants;
import org.slf4j.MDC;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

@Component
@Order(1)
public class MdcLogEnhancerFilter implements Filter {

  private static final String USERID="UserId";
  private static final String SESSIONID="SessionId";
  public static final String NO_SESSION_ID = "NO_SESSION_ID";
  public static final String NO_USER_ID = "NO_USER_ID";

  @Override
  public void destroy() {
    // do nothing
  }
@Override
  public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {

    insertIntoMDC(request);
    try {
      chain.doFilter(request, response);
    } finally {
      clearMDC();
    }
  }

  void insertIntoMDC(ServletRequest request) {


//    if(connectedUserDetails == null){
//
//    } else {
//
//    }

    MDC.put(ClassicConstants.REQUEST_REMOTE_HOST_MDC_KEY, request.getRemoteHost());
    if (request instanceof HttpServletRequest) {
      HttpServletRequest httpServletRequest = (HttpServletRequest) request;
      String userId = httpServletRequest.getUserPrincipal()==null? NO_USER_ID :httpServletRequest.getUserPrincipal().getName();
      String sessionId = httpServletRequest.getSession(false)==null? NO_SESSION_ID :httpServletRequest.getSession(false).getId();
      MDC.put(USERID, userId);
      MDC.put(SESSIONID, sessionId);


      MDC.put(ClassicConstants.REQUEST_REQUEST_URI, httpServletRequest.getRequestURI());
      StringBuffer requestURL = httpServletRequest.getRequestURL();
      if (requestURL != null) {
        MDC.put(ClassicConstants.REQUEST_REQUEST_URL, requestURL.toString());
      }
      MDC.put(ClassicConstants.REQUEST_METHOD, httpServletRequest.getMethod());
      MDC.put(ClassicConstants.REQUEST_QUERY_STRING, httpServletRequest.getQueryString());
      MDC.put(ClassicConstants.REQUEST_USER_AGENT_MDC_KEY, httpServletRequest.getHeader("User-Agent"));
      MDC.put(ClassicConstants.REQUEST_X_FORWARDED_FOR, httpServletRequest.getHeader("X-Forwarded-For"));
    }



  }

  void clearMDC() {
    MDC.remove(USERID);
    MDC.remove(SESSIONID);




      MDC.remove(ClassicConstants.REQUEST_REMOTE_HOST_MDC_KEY);
      MDC.remove(ClassicConstants.REQUEST_REQUEST_URI);
      MDC.remove(ClassicConstants.REQUEST_QUERY_STRING);
      // removing possibly inexistent item is OK
      MDC.remove(ClassicConstants.REQUEST_REQUEST_URL);
      MDC.remove(ClassicConstants.REQUEST_METHOD);
      MDC.remove(ClassicConstants.REQUEST_USER_AGENT_MDC_KEY);
      MDC.remove(ClassicConstants.REQUEST_X_FORWARDED_FOR);


  }

  public void init(FilterConfig arg0) throws ServletException {
    // do nothing
  }
}
