package lu.fujitsu.ts.cgdis.portal.webapp.controllers.rici;

import lombok.extern.slf4j.Slf4j;
import lu.fujitsu.ts.cgdis.portal.business.security.ILogAsAccessManager;
import lu.fujitsu.ts.cgdis.portal.core.domain.CGDISSecurityConstraint;
import lu.fujitsu.ts.cgdis.portal.core.domain.audit.AuditActionType;
import lu.fujitsu.ts.cgdis.portal.core.domain.audit.AuditType;
import lu.fujitsu.ts.cgdis.portal.core.domain.person.PersonInfoRici;
import lu.fujitsu.ts.cgdis.portal.core.domain.rici.*;
import lu.fujitsu.ts.cgdis.portal.core.domain.rici.enums.RiciPagerAssignmentType;
import lu.fujitsu.ts.cgdis.portal.core.domain.rici.enums.RiciPagerProgrammingStatus;
import lu.fujitsu.ts.cgdis.portal.core.domain.rici.enums.RiciPagerStatus;
import lu.fujitsu.ts.cgdis.portal.core.logging.LoggingCategory;
import lu.fujitsu.ts.cgdis.portal.core.logging.LoggingDomain;
import lu.fujitsu.ts.cgdis.portal.core.security.Permission;
import lu.fujitsu.ts.cgdis.portal.core.domain.csv_import.CsvImportReport;
import lu.fujitsu.ts.cgdis.portal.core.service.rici.IRiciPagerROBusinessService;
import lu.fujitsu.ts.cgdis.portal.core.service.rici.IRiciPagerWriteBusinessService;
import lu.fujitsu.ts.cgdis.portal.core.service.rici.IRiciSimCardPagerWriteBusinessService;
import lu.fujitsu.ts.cgdis.portal.core.service.rici.IRiciPagerCSVImportBusinessService;
import lu.fujitsu.ts.cgdis.portal.webapp.aop.audit.annotation.AuditLog;
import lu.fujitsu.ts.cgdis.portal.webapp.aop.audit.annotation.AuditLogParameter;
import lu.fujitsu.ts.cgdis.portal.webapp.controllers.IPageRequestController;
import lu.fujitsu.ts.cgdis.portal.webapp.controllers.form.rici.*;
import lu.fujitsu.ts.cgdis.portal.webapp.utils.rql.RqlStringBooleanValueParser;
import lu.fujitsu.ts.eportal.server.core.domain.SearchCriterion;
import lu.fujitsu.ts.eportal.server.core.exceptions.InvalidRQLExpressionException;
import lu.fujitsu.ts.eportal.server.core.exceptions.NotFoundException;
import lu.fujitsu.ts.eportal.server.core.utils.rql.SearchRqlParser;
import lu.fujitsu.ts.eportal.server.core.utils.rql.parsers.RqlEnumValueParser;
import lu.fujitsu.ts.eportal.server.logging.milestones.LogMilestone;
import lu.fujitsu.ts.eportal.server.logging.milestones.LoggableType;
import lu.fujitsu.ts.eportal.server.logging.milestones.LoggableValue;
import lu.fujitsu.ts.eportal.server.mvc.utils.rql.RqlParams;
import lu.fujitsu.ts.eportal.server.mvc.utils.rql.RqlRequest;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.time.LocalDate;
import java.util.*;

@Slf4j
@RestController
@RequestMapping("/rici/pagers")
@LogMilestone(category = LoggingCategory.REST_API_CALL, domain = LoggingDomain.RICI)
public class RiciPagerController implements IPageRequestController {

  private final IRiciPagerROBusinessService pagerROBusinessService;
  private final IRiciPagerWriteBusinessService pagerWriteBusinessService;
  private final IRiciSimCardPagerWriteBusinessService riciSimCardPagerWriteBusinessService;
  private final ILogAsAccessManager logAsAccessManager;
  private final IRiciPagerCSVImportBusinessService riciPagerCSVImportBusinessService;
  // Remove maintenance repository service field

  private SearchRqlParser rqlParser;

  @Autowired
  public RiciPagerController(
    IRiciPagerROBusinessService pagerROBusinessService,
    IRiciPagerWriteBusinessService pagerWriteBusinessService,
    IRiciSimCardPagerWriteBusinessService riciSimCardPagerWriteBusinessService, ILogAsAccessManager logAsAccessManager,
    IRiciPagerCSVImportBusinessService riciPagerCSVImportBusinessService) { // Remove maintenance service from constructor
    this.pagerROBusinessService = pagerROBusinessService;
    this.pagerWriteBusinessService = pagerWriteBusinessService;
    this.riciSimCardPagerWriteBusinessService = riciSimCardPagerWriteBusinessService;
    this.logAsAccessManager = logAsAccessManager;
    this.riciPagerCSVImportBusinessService = riciPagerCSVImportBusinessService;
    // Remove maintenance service assignment
    this.buildRqlParser();
  }

  protected List<SearchCriterion> parseRql(String rql) throws InvalidRQLExpressionException {
    return this.rqlParser.parse(rql);
  }

  public void buildRqlParser() {
    rqlParser = new SearchRqlParser() {
    };
    rqlParser.addCriterionParser("showInactivPagers", new RqlStringBooleanValueParser());
    rqlParser.addCriterionParser("pagerId", SearchRqlParser.RQL_STRING_VALUE_PARSER);
    rqlParser.addCriterionParser("serialNumber", SearchRqlParser.RQL_STRING_VALUE_PARSER);
    rqlParser.addCriterionParser("simCard.msisdn", SearchRqlParser.RQL_LONG_VALUE_PARSER);
    rqlParser.addCriterionParser("riciPagerProgramming.programmingStatus", new RqlEnumValueParser(RiciPagerProgrammingStatus.class));
    rqlParser.addCriterionParser("activePager.riciPagerProgrammingStatus", new RqlEnumValueParser(RiciPagerProgrammingStatus.class));
    // Status calculated by the RICI_PAGER_PROGRAMMING_STATUS_VIEW (accessed via the new association)
    rqlParser.addCriterionParser("programmingStatusView.calculatedProgrammingStatus", new RqlEnumValueParser(RiciPagerProgrammingStatus.class));
    rqlParser.addCriterionParser("status", new RqlEnumValueParser(RiciPagerStatus.class));
    rqlParser.addCriterionParser("riciPagerProgramming.lastSuccessfulUpdateDate", SearchRqlParser.RQL_LOCALDATE_VALUE_PARSER);
    rqlParser.addCriterionParser("riciPagerProgramming.lastProgrammingSentDate", SearchRqlParser.RQL_LOCALDATE_VALUE_PARSER);
    rqlParser.addCriterionParser("activeAssignment.type", new RqlEnumValueParser(RiciPagerAssignmentType.class));
    rqlParser.addCriterionParser("activeAssignment.assignedEntityTecid", SearchRqlParser.RQL_LONG_VALUE_PARSER);
    rqlParser.addCriterionParser("entityTecid", SearchRqlParser.RQL_LONG_VALUE_PARSER);
    rqlParser.addCriterionParser("alertGroupFalse", SearchRqlParser.RQL_LONG_VALUE_PARSER);
    rqlParser.addCriterionParser("alertGroupTrue", SearchRqlParser.RQL_LONG_VALUE_PARSER);
    rqlParser.addCriterionParser("activeAssignment.assignedPersonTecid", SearchRqlParser.RQL_LONG_VALUE_PARSER);


    rqlParser.addCriterionParser("firstName", SearchRqlParser.RQL_STRING_VALUE_PARSER);
    rqlParser.addCriterionParser("lastName", SearchRqlParser.RQL_STRING_VALUE_PARSER);
    rqlParser.addCriterionParser("cgdisRegistrationNumber", SearchRqlParser.RQL_STRING_VALUE_PARSER);


  }

  @DeleteMapping("{tecid}")
  @LogMilestone(action = "delete pager by id") // Corrected action name
  @AuditLog(auditType = AuditType.RICI_PAGER, actionType = AuditActionType.DELETE) // Add AuditLog
  public ResponseEntity<Void> deletePager(@PathVariable @AuditLogParameter(name = "tecid") Long tecid) throws NotFoundException { // Changed return type, added AuditLogParameter
    pagerWriteBusinessService.deletePagerById(new CGDISSecurityConstraint(Permission.ROLE_PERMISSION_RICI_PAGER_DELETE), tecid); // Use specific delete permission
    return ResponseEntity.noContent().build(); // Return 204 No Content on success
  }

  @GetMapping("all")
  @LogMilestone(action = "get all ric pagers", logResult = @LoggableValue(type = LoggableType.PAGE))
  public Page<? extends RiciPager> getAllRicPagers(@RqlParams RqlRequest rqlRequest) throws InvalidRQLExpressionException {
    String rql = rqlRequest.getRql();
    List<SearchCriterion> searchCriteria = parseRql(rql);
    List<SearchCriterion> finalCriterionList = new ArrayList<>();
    prepareCriterias(searchCriteria, finalCriterionList);
    PageRequest pageRequest = getPageRequest(rqlRequest);
    return this.pagerROBusinessService.getAllPagersWithFilters(new CGDISSecurityConstraint(Permission.ROLE_PERMISSION_RICI_PAGER_VIEW), finalCriterionList, pageRequest);
  }

  @GetMapping("assignments/all")
  @LogMilestone(action = "get all ric pager persons", logResult = @LoggableValue(type = LoggableType.PAGE))
  public Page<? extends RiciPersonWithPagerAndAlertGroups> getAllPersonsWithPagersAndAlertGroupsWithFilters(@RqlParams RqlRequest rqlRequest) throws InvalidRQLExpressionException, NotFoundException {
    String rql = rqlRequest.getRql();
    List<SearchCriterion> searchCriteria = parseRql(rql);
    List<SearchCriterion> finalCriterionList = new ArrayList<>();
    prepareCriterias(searchCriteria, finalCriterionList);
    PageRequest pageRequest = getPageRequest(rqlRequest);
    return this.pagerROBusinessService.getAllPersonsWithPagersAndAlertGroupsWithFilters(new CGDISSecurityConstraint(Permission.ROLE_PERMISSION_RICI_PAGER_VIEW), finalCriterionList, pageRequest);
  }


  @GetMapping("entity/alert/{entityTecid}")
  @LogMilestone(action = "get all ric pager persons alert groups by entityTecid", logResult = @LoggableValue(type = LoggableType.COLLECTION))
  public Set<RiciAlertGroup> getAllEntityAlertGroupsByTecid(@LoggableValue @PathVariable Long entityTecid) throws NotFoundException {
    return this.pagerROBusinessService.getAllEntityAlertGroupsByTecid(new CGDISSecurityConstraint(Permission.ROLE_PERMISSION_RICI_PAGER_VIEW), entityTecid);
  }

  /**
   * Endpoint to get pagers that are assignable (status INACTIVE and not associated with a SIM card).
   * Used for populating dropdowns for SIM card assignment.
   *
   * @return A list of assignable RiciPager objects.
   */
  @GetMapping("all/assignable")
  @LogMilestone(action = "get assignable pagers")
  public ResponseEntity<List<RiciPager>> getAssignablePagers() {
    // Apply appropriate security constraint for viewing pagers (adjust if needed)
    CGDISSecurityConstraint securityConstraint = new CGDISSecurityConstraint(Permission.ROLE_PERMISSION_RICI_PAGER_VIEW);
    List<RiciPager> assignablePagers = pagerROBusinessService.getAssignablePagers(securityConstraint);
    return ResponseEntity.ok(assignablePagers);
  }


  @GetMapping("all/person/assignables/{entityTecid}")
  @LogMilestone(action = "get all ric pager person assignable pagers")
  public ResponseEntity<List<RiciPager>> getAllPersonAssignablePagers(@LoggableValue @PathVariable Long entityTecid) {
    CGDISSecurityConstraint securityConstraint = new CGDISSecurityConstraint(Permission.ROLE_PERMISSION_RICI_PAGER_VIEW);
    List<RiciPager> assignablePagers = pagerROBusinessService.getAllPersonAssignablePagers(securityConstraint, entityTecid);
    return ResponseEntity.ok(assignablePagers);
  }


  @GetMapping("persons")
  @LogMilestone(action = "get all persons for datatable filter", logResult = @LoggableValue(type = LoggableType.PAGE))
  public Page<PersonInfoRici> getAllPersons(@RequestParam(name = "search", required = false) String search,
                                            @RequestParam(name = "pagerTecid", required = false) Long pagerTecid) {
    return this.pagerROBusinessService.getAllPersons(search, pagerTecid);

  }

  @GetMapping("{pagerTecId}")
  @LogMilestone(action = "get pager by id")
  public ResponseEntity<RiciPagerWithPassword> getPagerByTecId(@LoggableValue @PathVariable Long pagerTecId) throws NotFoundException {
    // Call the business service method that handles fetching both pager and password
    CGDISSecurityConstraint viewConstraint = new CGDISSecurityConstraint(Permission.ROLE_PERMISSION_RICI_PAGER_VIEW);
    RiciPagerWithPassword pagerWithPassword = pagerROBusinessService.getPagerWithPasswordByTecId(viewConstraint, pagerTecId);
    return ResponseEntity.ok(pagerWithPassword);
    // Exception handling (like NotFoundException) should be managed by global exception handlers
    // or specific exception handlers within the business service if needed.
  }

  @PostMapping
  @LogMilestone(action = "create pager")
  @AuditLog(auditType = AuditType.RICI_PAGER, actionType = AuditActionType.CREATE)
  public ResponseEntity<RiciPagerWithPassword> createPager(@RequestBody @Valid @AuditLogParameter(name = "form") RiciPagerForm form) throws NotFoundException {
    Pair<RiciPager, String> result = pagerWriteBusinessService.createPager(
      new CGDISSecurityConstraint(Permission.ROLE_PERMISSION_RICI_PAGER_CREATE),
      form.create(),
      form.getSimCardTecid(),
      form.getAssignmentType(),
      form.getAssignedToPersonTecid(),
      form.getAssignedToEntityTecid()
    );
    return ResponseEntity.status(HttpStatus.CREATED)
      .body(RiciPagerWithPassword.from(result.getLeft(), result.getRight()));
  }

  @PatchMapping
  @LogMilestone(action = "update pager")
  @AuditLog(auditType = AuditType.RICI_PAGER, actionType = AuditActionType.UPDATE)
  public ResponseEntity<RiciPagerWithPassword> updatePager(
    @RequestBody @Valid @AuditLogParameter(name = "form") RiciPagerPatchForm form) throws NotFoundException {
    Pair<RiciPager, String> result = pagerWriteBusinessService.updatePager(
      new CGDISSecurityConstraint(Permission.ROLE_PERMISSION_RICI_PAGER_UPDATE),
      form,
      form.getAssignmentType(),
      form.getAssignedToPersonTecid(),
      form.getAssignedToEntityTecid()
    );
    return ResponseEntity.ok(RiciPagerWithPassword.from(result.getLeft(), result.getRight()));
  }


  @PutMapping("assignment/person/alert")
  @LogMilestone(action = "update rici person alert group")
  public RiciPersonWithPagerAndAlertGroups updateAlertGroup(
    @RequestBody @Valid @AuditLogParameter(name = "form") RiciPersonPagerUpdateAlertGroupForm form) throws NotFoundException {

    return this.pagerWriteBusinessService.updateAlertGroup(
      new CGDISSecurityConstraint(Permission.ROLE_PERMISSION_RICI_PAGER_ASSOCIATION),
      form.getPersonTecid(),
      form.getAlertGroupTecid(),
      form.getAlertGroupValue());
  }

  @PostMapping("association")
  @LogMilestone(action = "associate pager with sim card")
  @AuditLog(auditType = AuditType.RICI_SIM_CARD, actionType = AuditActionType.ASSOCIATE)
  public ResponseEntity<RiciSimCardPager> associatePagerWithSimCard(
    @RequestBody @Valid RiciSimCardPagerAssociationForm form) throws NotFoundException {
    log.debug("Attempting to associate pager with SIM card using form data");

    // Extract tecid values from the form
    Long simCardTecid = form.getSimCard().getTecid();
    Long pagerTecid = form.getPager().getTecid();
    // Use the common business service method with extracted IDs and status
    RiciSimCardPager simCardPager = riciSimCardPagerWriteBusinessService.associateSimCardWithPagerByIds(
      new CGDISSecurityConstraint(Permission.ROLE_PERMISSION_RICI_PAGER_ASSOCIATION),
      simCardTecid,
      pagerTecid,
      form.getStatus() != null ? form.getStatus() : "ACTIVE" // Default to "ACTIVE" if status is null
    );

    log.info("Successfully associated SIM card tecid: {} with pager tecid: {}", simCardTecid, pagerTecid);
    return ResponseEntity.status(HttpStatus.CREATED).body(simCardPager);
  }


  @PatchMapping("assignment/person/pager")
  @LogMilestone(action = "assign ric pager person with pager")
  public RiciPager assignPagerWithPerson(@RequestBody @Valid RiciPersonPagerAssignmentForm form) throws NotFoundException {
    authorizePersonAccess(form.getPersonTecid());

    RiciPagerPatchForm patchForm = buildPersonAssignmentPatchForm(form.getPagerTecid(), form.getPersonTecid());

    Pair<RiciPager, String> result = pagerWriteBusinessService.updatePager(
      new CGDISSecurityConstraint(Permission.ROLE_PERMISSION_RICI_PAGER_UPDATE),
      patchForm,
      patchForm.getAssignmentType(),
      patchForm.getAssignedToPersonTecid(),
      patchForm.getAssignedToEntityTecid()
    );

    return result.getLeft();
  }

  @PatchMapping("assignment/person/repair")
  @LogMilestone(action = "assign ric pager person with new pager")
  public RiciPager assignPersonWithNewPager(@RequestBody @Valid RiciPersonPagerRepairForm form) throws NotFoundException {
    authorizePersonAccess(form.getPersonTecid());

    // Update old pager
    RiciPagerPatchForm oldPatchForm = buildPersonAssignmentPatchForm(form.getPagerTecid(), form.getPersonTecid());
    RiciPagerAssignmentType newAssignmentType = resolveNewAssignmentType(oldPatchForm.getStatus());

    pagerWriteBusinessService.updatePager(
      new CGDISSecurityConstraint(Permission.ROLE_PERMISSION_RICI_PAGER_UPDATE),
      oldPatchForm,
      newAssignmentType,
      null,
      null
    );

    // Update new pager
    RiciPagerPatchForm newPatchForm = buildPersonAssignmentPatchForm(form.getNewPagerTecid(), form.getPersonTecid());

    Pair<RiciPager, String> result = pagerWriteBusinessService.updatePager(
      new CGDISSecurityConstraint(Permission.ROLE_PERMISSION_RICI_PAGER_UPDATE),
      newPatchForm,
      newPatchForm.getAssignmentType(),
      newPatchForm.getAssignedToPersonTecid(),
      newPatchForm.getAssignedToEntityTecid()
    );

    return result.getLeft();
  }

  private void authorizePersonAccess(Long personTecid) {
    List<String> permissions = Collections.singletonList(Permission.ROLE_PERMISSION_RICI_PAGER_VIEW.name());
    logAsAccessManager.canAccess(personTecid, permissions, permissions, true);
  }

  private RiciPagerPatchForm buildPersonAssignmentPatchForm(Long pagerTecid, Long personTecid) throws NotFoundException {
    RiciPager currentPager = pagerROBusinessService.getByTecId(
      new CGDISSecurityConstraint(Permission.ROLE_PERMISSION_RICI_PAGER_VIEW),
      pagerTecid
    );

    RiciPagerPatchForm patchForm = new RiciPagerPatchForm();
    patchForm.prePopulateFormFields(currentPager);
    patchForm.setAssignmentType(RiciPagerAssignmentType.PERSON);
    patchForm.setAssignedToPersonTecid(personTecid);
    patchForm.setAssignedToEntityTecid(null);

    return patchForm;
  }

  private RiciPagerAssignmentType resolveNewAssignmentType(RiciPagerStatus status) {
    if (status == RiciPagerStatus.LOST) {
      return RiciPagerAssignmentType.LOST;
    } else if (status == RiciPagerStatus.DEFECTIVE) {
      return RiciPagerAssignmentType.STOCK;
    } else {
      return null;
    }

  }


  @PatchMapping("/{tecid}/reset-maintenance-password")
  @LogMilestone(action = "reset pager maintenance password")
//  @AuditLog(auditType = AuditType.RICI_PAGER, actionType = AuditActionType.UPDATE)
  // Or a specific PASSWORD_RESET action type
  public ResponseEntity<Map<String, String>> resetMaintenancePassword(@PathVariable @LoggableValue Long tecid) throws NotFoundException {
    log.debug("Attempting to reset maintenance password for pager with tecid: {}", tecid);
    CGDISSecurityConstraint securityConstraint = new CGDISSecurityConstraint(Permission.ROLE_PERMISSION_RICI_PAGER_UPDATE); // Adjust permission if needed
    String newPassword = pagerWriteBusinessService.resetMaintenancePassword(securityConstraint, tecid);
    // Log the success
    log.info("Successfully reset maintenance password for pager tecid: {}", tecid);
    // Return the new password in a simple JSON structure
    return ResponseEntity.ok(Collections.singletonMap("maintenancePassword", newPassword));
  }


  private void prepareCriterias(List<SearchCriterion> criterionList, List<SearchCriterion> finalCriterionList) {
    for (SearchCriterion criterion : criterionList) {
      if (criterion.getAttribute().equals("riciPagerProgramming.lastSuccessfulUpdateDate")) {
        LocalDate creationDate = (LocalDate) criterion.getValue();
        SearchCriterion from = new SearchCriterion("riciPagerProgramming.lastSuccessfulUpdateDate", creationDate.atStartOfDay(), SearchCriterion.Operator.ge);
        SearchCriterion to = new SearchCriterion("riciPagerProgramming.lastSuccessfulUpdateDate", creationDate.plusDays(1).atStartOfDay(), SearchCriterion.Operator.lt);
        finalCriterionList.add(from);
        finalCriterionList.add(to);
      } else if (criterion.getAttribute().equals("riciPagerProgramming.lastProgrammingSentDate")) {
        LocalDate creationDate = (LocalDate) criterion.getValue();
        SearchCriterion from = new SearchCriterion("riciPagerProgramming.lastProgrammingSentDate", creationDate.atStartOfDay(), SearchCriterion.Operator.ge);
        SearchCriterion to = new SearchCriterion("riciPagerProgramming.lastProgrammingSentDate", creationDate.plusDays(1).atStartOfDay(), SearchCriterion.Operator.lt);
        finalCriterionList.add(from);
        finalCriterionList.add(to);
      } else if (criterion.getAttribute().equals("activePager.riciPagerProgrammingStatus")) {
        finalCriterionList.add(new SearchCriterion("activePager.riciPagerProgramming.lastProgrammingSentDate", criterion.getValue(), SearchCriterion.Operator.eq));

      } else {
        finalCriterionList.add(criterion);
      }
    }
  }


  @PostMapping("/import-csv/{lang}")
  @LogMilestone(action = "import pagers from csv file")
  @AuditLog(auditType = AuditType.RICI_PAGER_IMPORT, actionType = AuditActionType.IMPORT_CSV)
  public ResponseEntity<CsvImportReport> importPagersFromCsv(
          @RequestParam("file") MultipartFile file,
          @PathVariable String lang) {
    log.info("Received request to import Pagers from CSV file: {}, language: {}", file.getOriginalFilename(), lang);
    CGDISSecurityConstraint securityConstraint = new CGDISSecurityConstraint(Permission.ROLE_PERMISSION_RICI_PAGER_CREATE);
    CsvImportReport report = riciPagerCSVImportBusinessService.importCsvFile(file, lang, securityConstraint);
    log.info("Finished importing Pagers from CSV file: {}. Report generated.", file.getOriginalFilename());

    // Determine HTTP status based on report (e.g., if critical file-level errors occurred)
    boolean hasCriticalError = report.getLineInfos().stream()
                                   .anyMatch(lineInfo -> lineInfo.getValidatorInfo() != null &&
                                                        lineInfo.getValidatorInfo().getLineNumber() == 0 &&
                                                        !lineInfo.getValidatorInfo().isValid());
    if (hasCriticalError) {
        return ResponseEntity.badRequest().body(report);
    }
    return ResponseEntity.ok(report);
  }
}
