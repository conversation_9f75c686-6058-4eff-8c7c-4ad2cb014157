package lu.fujitsu.ts.cgdis.portal.webapp.validator.date;

import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.time.LocalDate;

/**
 * Created by LUXJVERC on 22/05/2017.
 */
public class DateAfterAnotherValidator implements ConstraintValidator<DateAfterAnother, Object> {

  private String beforeFieldname;
  private String afterFieldname;

  private boolean equalsAllowed;

  @Override
  public void initialize(DateAfterAnother constraintAnnotation) {
    this.beforeFieldname = constraintAnnotation.dateBeforeFieldname();
    this.afterFieldname = constraintAnnotation.dateAfterFieldname();
    this.equalsAllowed = constraintAnnotation.equalsAllowed();
  }

  @Override
  public boolean isValid(Object value, ConstraintValidatorContext context) {
    BeanWrapper wrapper = new BeanWrapperImpl(value);
    LocalDate before = (LocalDate)wrapper.getPropertyValue(beforeFieldname);
    LocalDate after = (LocalDate)wrapper.getPropertyValue(afterFieldname);
    boolean isValid= false;
    if ( before == null ){
      isValid = true;
    } else if ( after == null){
      isValid = true;
    } else  {
      if ( equalsAllowed && after.isEqual(before)){
        isValid = true;
      } else {
        isValid = after.isAfter(before);
      }

    }

    if ( !isValid){
      context.buildConstraintViolationWithTemplate( context.getDefaultConstraintMessageTemplate()  ).addPropertyNode(afterFieldname).addConstraintViolation();
    }


    return isValid ;
  }
}
