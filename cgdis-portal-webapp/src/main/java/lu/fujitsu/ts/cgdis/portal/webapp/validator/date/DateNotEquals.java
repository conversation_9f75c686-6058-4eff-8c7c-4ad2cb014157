package lu.fujitsu.ts.cgdis.portal.webapp.validator.date;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.*;
import static java.lang.annotation.RetentionPolicy.RUNTIME;


/**
 * The interface Required if boolean value.
 */
@Target({METHOD, PARAMETER, FIELD, TYPE, ANNOTATION_TYPE})
@Retention(RUNTIME)
@Constraint(validatedBy = {DateNotEqualsValidator.class})
@Documented
public @interface DateNotEquals {

  /**
   * Message string.
   *
   * @return the string
   */
  String message() default "Dates must not be equals";

  /**
   * Groups class [ ].
   *
   * @return the class [ ]
   */
  Class<?>[] groups() default {};

  /**
   * Payload class [ ].
   *
   * @return the class [ ]
   */
  Class<? extends Payload>[] payload() default {};


  /**
   * The constant dateFieldname1.
   */
  String dateFieldname1();
  /**
   * The constant dateFieldname2.
   */
  String dateFieldname2();


  /**
   * The interface List.
   */
  @Target({TYPE, ANNOTATION_TYPE})
    @Retention(RUNTIME)
    @Documented
    @interface List {

    /**
     * Value date not equals [ ].
     *
     * @return the date not equals [ ]
     */
    DateNotEquals[] value();
    }
}
