package lu.fujitsu.ts.cgdis.portal.webapp.aop.audit.aspect.rici;

import lu.fujitsu.ts.cgdis.portal.core.domain.audit.AuditActionType;
import lu.fujitsu.ts.cgdis.portal.core.domain.audit.AuditRiciAlertGroup;
import lu.fujitsu.ts.cgdis.portal.core.domain.rici.RiciAlertGroup;
import org.aspectj.lang.ProceedingJoinPoint;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.Map;

@Component
public class RiciAlertGroupCreateAuditAspect extends AbstractRiciAlertGroupAuditAspect<RiciAlertGroup, RiciAlertGroup> {
  private static final Logger LOGGER = LoggerFactory.getLogger(RiciAlertGroupCreateAuditAspect.class);

  public RiciAlertGroupCreateAuditAspect() {
    super(AuditActionType.CREATE, AuditRiciAlertGroup.class);
  }


  @Override
  protected boolean mustCreateAuditLog(AuditRiciAlertGroup audit, Map<String, Object> parameters, RiciAlertGroup result) {
    return true;
  }

  @Override
  protected void populateAuditAfterProcessingMethod(ProceedingJoinPoint point, Method method, AuditRiciAlertGroup audit, Map<String, Object> parameters, RiciAlertGroup result) {
    mapAlertGroupToAudit(audit, result);

  }

  @Override
  protected void populateAuditBeforeProcessingMethod(ProceedingJoinPoint point, Method method, AuditRiciAlertGroup audit, Map<String, Object> parameters) {

  }
}
