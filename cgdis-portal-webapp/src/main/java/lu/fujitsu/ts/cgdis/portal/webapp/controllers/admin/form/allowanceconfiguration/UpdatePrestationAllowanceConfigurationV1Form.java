package lu.fujitsu.ts.cgdis.portal.webapp.controllers.admin.form.allowanceconfiguration;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lu.fujitsu.ts.cgdis.portal.core.pojo.prestation.allowance.PrestationAllowanceConfiguration;
import lu.fujitsu.ts.cgdis.portal.core.pojo.prestation.allowance.v1.PrestationAllowanceConfigurationV1;
import lu.fujitsu.ts.eportal.server.core.domain.IModelUpdate;

import java.math.BigDecimal;


/**
 * The type Create Allowance configuration form.
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class UpdatePrestationAllowanceConfigurationV1Form extends AbstractPrestationAllowanceConfigurationModelForm implements IModelUpdate<PrestationAllowanceConfiguration<PrestationAllowanceConfigurationV1>, Long> {

  private Long tecid;

  private Long teclock;

  private String label;

  private BigDecimal barrackedAmount;

  private BigDecimal notBarrackedAmount;


  @Override
  public PrestationAllowanceConfiguration<PrestationAllowanceConfigurationV1> update(PrestationAllowanceConfiguration<PrestationAllowanceConfigurationV1> current) {
    return current.setTeclock(teclock)
      .setLabel(label)
      .setConfiguration(PrestationAllowanceConfigurationV1.builder()
        .barrackedAmount(barrackedAmount)
        .notBarrackedAmount(notBarrackedAmount)
        .build())
      .setStartDate(getStartDate());
  }

  @Override
  public Long getTechnicalId() {
    return tecid;
  }


}
