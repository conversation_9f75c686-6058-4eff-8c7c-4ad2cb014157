package lu.fujitsu.ts.cgdis.portal.webapp.controllers.admin.form.person;

import lu.fujitsu.ts.cgdis.portal.core.domain.person.SuspensionPeriod;
import lu.fujitsu.ts.eportal.server.core.domain.IModelUpdate;

public class UpdateSuspensionPeriodForm extends AbstractSuspensionPeriodForm implements IModelUpdate<SuspensionPeriod, Long> {
  @Override
  public SuspensionPeriod update(SuspensionPeriod current) {

    if (current.getTecid() == null) {
      current.setTecid(getTecid());
      current.setTeclock(getTeclock());
    }

    current.setEndDate(getEndDate());
    current.setStartDate(getStartDate());

    return current;
  }
}
