package lu.fujitsu.ts.cgdis.portal.webapp.controllers.admin;

import lu.fujitsu.ts.cgdis.portal.core.domain.*;
import lu.fujitsu.ts.cgdis.portal.core.domain.person.FunctionOperationalStatus;
import lu.fujitsu.ts.cgdis.portal.core.domain.person.PersonLight;
import lu.fujitsu.ts.cgdis.portal.core.logging.LoggingCategory;
import lu.fujitsu.ts.cgdis.portal.core.logging.LoggingDomain;
import lu.fujitsu.ts.cgdis.portal.core.security.ICGDISSecurityConstraint;
import lu.fujitsu.ts.cgdis.portal.core.security.Permission;
import lu.fujitsu.ts.cgdis.portal.core.security.utils.CGDISPortalSecurityHolder;
import lu.fujitsu.ts.cgdis.portal.core.service.IAdminFunctionOperationalBusinessService;
import lu.fujitsu.ts.cgdis.portal.webapp.controllers.AbstractSecuredModifiableBaseController;
import lu.fujitsu.ts.cgdis.portal.webapp.controllers.IPageRequestController;
import lu.fujitsu.ts.cgdis.portal.webapp.controllers.admin.form.functionoperational.CreateFunctionOperationalForm;
import lu.fujitsu.ts.cgdis.portal.webapp.controllers.admin.form.functionoperational.UpdateFunctionOperationalForm;
import lu.fujitsu.ts.cgdis.portal.webapp.utils.rql.RqlLongListValueParser;
import lu.fujitsu.ts.eportal.server.core.domain.SearchCriterion;
import lu.fujitsu.ts.eportal.server.core.exceptions.InvalidRQLExpressionException;
import lu.fujitsu.ts.eportal.server.core.exceptions.NotFoundException;
import lu.fujitsu.ts.eportal.server.core.utils.rql.SearchRqlParser;
import lu.fujitsu.ts.eportal.server.logging.milestones.LogMilestone;
import lu.fujitsu.ts.eportal.server.mvc.utils.rql.RqlParams;
import lu.fujitsu.ts.eportal.server.mvc.utils.rql.RqlRequest;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@RestController
@RequestMapping("/admin/function-operational")
@LogMilestone(category = LoggingCategory.REST_API_CALL, domain = LoggingDomain.FUNCTION_OPERATIONAL)
public class AdminFunctionOperationalController extends AbstractSecuredModifiableBaseController<ICGDISSecurityConstraint, FunctionOperational, Long, IAdminFunctionOperationalBusinessService, CreateFunctionOperationalForm, UpdateFunctionOperationalForm> implements IPageRequestController {

  private IAdminFunctionOperationalBusinessService businessService;


  @Override
  public SearchRqlParser buildRqlParser() {
    SearchRqlParser rqlParser = new SearchRqlParser();
    rqlParser.addCriterionParser("tecid", SearchRqlParser.RQL_LONG_VALUE_PARSER);
    rqlParser.addCriterionParser("label", SearchRqlParser.RQL_STRING_VALUE_PARSER);
    rqlParser.addCriterionParser("portalLabel", SearchRqlParser.RQL_STRING_VALUE_PARSER);
    rqlParser.addCriterionParser("interventionTypeId", SearchRqlParser.RQL_LONG_VALUE_PARSER);
    rqlParser.addCriterionParser("entityId", SearchRqlParser.RQL_LONG_VALUE_PARSER);
    rqlParser.addCriterionParser("subentities", SearchRqlParser.RQL_STRING_VALUE_PARSER);
    rqlParser.addCriterionParser("ids", new RqlLongListValueParser());
    rqlParser.addCriterionParser("showClosedFunctionOperational", SearchRqlParser.RQL_STRING_VALUE_PARSER);
    return rqlParser;
  }

  @Override
  public ICGDISSecurityConstraint getSecurityConstraint() {
    return new CGDISSecurityConstraint(Collections.singletonList(Permission.ROLE_PERMISSION_ADMIN_FUNCTION_OPERATIONAL));
  }

  @Override
  public ICGDISSecurityConstraint getUpdateSecurityConstraint() {
    return new CGDISSecurityConstraint(Collections.singletonList(Permission.ROLE_PERMISSION_ADMIN_FUNCTION_OPERATIONAL_UPDATE));
  }

  @Override
  public ICGDISSecurityConstraint getCreateSecurityConstraint() {
    return new CGDISSecurityConstraint(Collections.singletonList(Permission.ROLE_PERMISSION_ADMIN_FUNCTION_OPERATIONAL_CREATE));
  }

  @Override
  public ICGDISSecurityConstraint getDeleteSecurityConstraint() {
    return new CGDISSecurityConstraint(Collections.singletonList(Permission.ROLE_PERMISSION_ADMIN_FUNCTION_OPERATIONAL_DELETE));
  }

  public ICGDISSecurityConstraint getPersonFunctionOperationalViewConstraint() {
    return new CGDISSecurityConstraint(Collections.singletonList(Permission.ROLE_PERMISSION_USER_MANAGEMENT_OP_FUNCT_VIEW));
  }

  public AdminFunctionOperationalController(IAdminFunctionOperationalBusinessService service) {
    super(service, "label", Sort.Direction.ASC, 10);
    this.businessService = service;
  }

  @GetMapping("/all")
  @LogMilestone(action = "search service plan models with editable information")
  public Page<FunctionOperationalWithEditable> searchEditable(@RqlParams RqlRequest rqlRequest) throws InvalidRQLExpressionException, NotFoundException {
    ICGDISPortalUserDetails connectedUserDetails = CGDISPortalSecurityHolder.getConnectedUserDetails();
    PageRequest pageRequest = getPageRequest(rqlRequest);
    String rql = rqlRequest.getRql();
    List<SearchCriterion> searchCriteria = this.parseRql(rql);
    String showClosedFunctionOperational = "false";
    for (SearchCriterion criterion : searchCriteria) {
      if (criterion.getAttribute().equals("showClosedFunctionOperational")) {
        showClosedFunctionOperational = (String) criterion.getValue();
      }
    }

    return this.businessService.searchWithEditable(this.getSecurityConstraint(), searchCriteria, pageRequest, showClosedFunctionOperational);

  }

  @GetMapping("/list/{interventionId}")
  @LogMilestone(action = "search service plan models with editable information")
  public List<? extends FunctionOperational> getAllByInterventionTypeId(@PathVariable Long interventionId) {
    return this.businessService.getAllByInterventionTypeId(this.getSecurityConstraint(), interventionId);
  }

  /**
   * Get position template with version and editable
   * @param id service positiontemplate id
   * @return the position template with version and editable
   * @throws NotFoundException position template with version and editable not found
   */
  @GetMapping("{id}/editables")
  public FunctionOperationalWithEditable getWithEditables(@PathVariable Long id) throws NotFoundException {
    return this.businessService.getWithEditables(getSecurityConstraint(), id);
  }

  @PutMapping("{id}/delete")
  public void deleteFunctionOperational(@PathVariable Long id) {
    this.businessService.deleteById(getSecurityConstraint(), id);
  }

  /**
   * Get position template with version and editable
   * @param id service positiontemplate id
   * @return the position template with version and editable
   * @throws NotFoundException position template with version and editable not found
   */
  @PutMapping("{id}/validate")
  public boolean validateFunctionOperational(@PathVariable Long id) throws NotFoundException {
    this.businessService.setStatus(getSecurityConstraint(), id, FunctionOperationalStatus.VALIDATED);
    return true;
  }

  /**
   * Get position template with version and editable
   * @param id service positiontemplate id
   * @return the position template with version and editable
   * @throws NotFoundException position template with version and editable not found
   */
  @PutMapping("{id}/close")
  public FunctionOperationalClosingError closeFunctionOperational(@PathVariable Long id) throws NotFoundException {
    return this.businessService.close(getUpdateSecurityConstraint(), id);
  }

  @GetMapping("/hasFunctionOperational/{entityId}")
  public Boolean hasFunctionOperationalByEntityId(@PathVariable Long entityId) {
    return this.businessService.hasFunctionOperationalByEntityId(this.getPersonFunctionOperationalViewConstraint(), entityId);
  }

  @GetMapping("/listByEntityId/{entityId}")
  public List<FunctionOperational> getAllByEntityId(@PathVariable Long entityId, @RequestParam("subentities") Boolean subentities) {
    return this.businessService.getAllByEntityId(this.getPersonFunctionOperationalViewConstraint(), entityId, subentities);
  }

  @GetMapping("/summary")
  public Page<FunctionOperationalSummary> getSummaryBy(@RqlParams RqlRequest rqlRequest) throws InvalidRQLExpressionException, NotFoundException {
    ICGDISPortalUserDetails connectedUserDetails = CGDISPortalSecurityHolder.getConnectedUserDetails();
    PageRequest pageRequest = getPageRequest(rqlRequest);
    String rql = rqlRequest.getRql();
    List<SearchCriterion> searchCriteria = this.parseRql(rql);
    List<Long> ids = new ArrayList<>();
    Long entityId = 0L;
    Boolean subentities = false;
    for (SearchCriterion criterion : searchCriteria) {
      if (criterion.getAttribute().equals("ids")) {
        ids = (List<Long>) criterion.getValue();
      }
      if (criterion.getAttribute().equals("entityId")) {
        entityId = (Long) criterion.getValue();
      }

      if (criterion.getAttribute().equals("subentities")) {
        subentities = "true".equals(criterion.getValue());
      }
    }
    if (ids.isEmpty()){
      return new PageImpl<>(new ArrayList<>(), pageRequest, 0);
    }
    return this.businessService.getSummary(connectedUserDetails, ids, entityId, subentities, pageRequest);
  }


  @GetMapping("/summaryCount")
  public List<FunctionOperationalSummary> getSummaryCount(@RequestParam("ids") List<Long> ids, @RequestParam("entityId") Long entityId, @RequestParam("subentities") Boolean subentities) throws InvalidRQLExpressionException, NotFoundException {
    ICGDISPortalUserDetails connectedUserDetails = CGDISPortalSecurityHolder.getConnectedUserDetails();
    if (ids.isEmpty()){
      return new ArrayList<>();
    }
    return this.businessService.getSummaryCount(connectedUserDetails, ids, entityId, subentities);
  }

  @GetMapping("/personList")
  public Page<PersonLight> getPersonList(@RqlParams RqlRequest rqlRequest) throws InvalidRQLExpressionException, NotFoundException {
    ICGDISPortalUserDetails connectedUserDetails = CGDISPortalSecurityHolder.getConnectedUserDetails();
    PageRequest pageRequest = getPageRequest(rqlRequest);
    String rql = rqlRequest.getRql();
    List<SearchCriterion> searchCriteria = this.parseRql(rql);
    Long tecid = null;
    Long entityId = 0L;
    List<Long> ids = new ArrayList<>();
    Boolean subentities = false;
    for (SearchCriterion criterion : searchCriteria) {
      if (criterion.getAttribute().equals("tecid")) {
        tecid = (Long) criterion.getValue();
      }
      if (criterion.getAttribute().equals("ids")) {
        ids = (List<Long>) criterion.getValue();
      }
      if (criterion.getAttribute().equals("entityId")) {
        entityId = (Long) criterion.getValue();
      }
      if (null == tecid || null == entityId || criterion.getAttribute().equals("subentities")) {
        subentities = "true".equals(criterion.getValue());
      }
    }
    if (null == tecid || null == entityId || ids.isEmpty()){
      return new PageImpl<>(new ArrayList<>(), pageRequest, 0);
    }

      return this.businessService.getPersonList(connectedUserDetails, pageRequest, entityId, tecid, subentities);
  }
}
