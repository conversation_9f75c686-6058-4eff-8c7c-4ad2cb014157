/*
 * Copyright (c) 2012-2017 Fujitsu Technology Solutions, Luxembourg
 * You may not use this file except in compliance with the License granted by Fujitsu to your company.
 * A copy of the License is available upon request by sending an <NAME_EMAIL>
 * Software distributed under this License is provided under an "AS IS" BASIS,  WITHOUT ANY SUPPORT, WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and limitations under the License.
 *
 */

package lu.fujitsu.ts.cgdis.portal.webapp.aop.audit.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Inherited;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * The interface Audit parameter.
 */
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Inherited
@Target({ElementType.TYPE, ElementType.METHOD, ElementType.ANNOTATION_TYPE})
public @interface AuditLogParametersList {

  AuditLogParameter[] paramters();

}
