package lu.fujitsu.ts.cgdis.portal.webapp.validator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.*;
import static java.lang.annotation.RetentionPolicy.RUNTIME;


@Target({METHOD, PARAMETER, FIELD, TYPE, ANNOTATION_TYPE})
@Retention(RUNTIME)
@Constraint(validatedBy = {RequiredIfValidator.class})
@Documented
public @interface RequiredIf {

    String message() default "The field is required";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};


    String fieldName();

    String dependsOnField();

    String dependOnFieldValue();

    @Target({TYPE, ANNOTATION_TYPE})
    @Retention(RUNTIME)
    @Documented
    @interface List {
        RequiredIf[] value();
    }
}
