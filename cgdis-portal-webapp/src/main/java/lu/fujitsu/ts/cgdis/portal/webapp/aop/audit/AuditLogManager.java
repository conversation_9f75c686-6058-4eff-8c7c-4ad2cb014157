package lu.fujitsu.ts.cgdis.portal.webapp.aop.audit;

import lu.fujitsu.ts.cgdis.portal.core.domain.audit.Audit;
import lu.fujitsu.ts.cgdis.portal.core.domain.audit.AuditActionType;
import lu.fujitsu.ts.cgdis.portal.core.domain.audit.AuditType;
import lu.fujitsu.ts.cgdis.portal.webapp.aop.audit.aspect.IAuditAspect;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;


/**
 * The type Audit log manager.
 */
public class AuditLogManager {

  /**
   * The constant LOGGER.
   */
  private static final Logger LOGGER = LoggerFactory.getLogger(AuditLogManager.class);

  /**
   * The Audit classes mapping.
   */
  private Map<AuditType, Map<AuditActionType, IAuditAspect<Audit, ?, ?>>> auditClassesMapping ;

  /**
   * Instantiates a new Audit log manager.
   */
  public AuditLogManager() {
    this.auditClassesMapping = new HashMap<>();
  }


  /**
   * Add.
   *
   * @param auditAspect the audit aspect
   */
  public void add(IAuditAspect<Audit, ?, ?> auditAspect){

    auditClassesMapping.compute(auditAspect.getAuditType(), (auditType1, auditActionTypeClassMap) -> {
      if ( auditActionTypeClassMap == null ){
        auditActionTypeClassMap = new HashMap<>();
      }
      auditActionTypeClassMap.put(auditAspect.getAuditActionType(), auditAspect);

      return auditActionTypeClassMap;
    });

  }


  /**
   * Get audit aspect.
   *
   * @param <T>             the type parameter
   * @param auditType       the audit type
   * @param auditActionType the audit action type
   * @return the audit aspect
   * @throws IllegalAccessException the illegal access exception
   * @throws InstantiationException the instantiation exception
   */
  public <T extends Audit> IAuditAspect<T, ?, ?>  get(AuditType auditType, AuditActionType auditActionType) throws IllegalAccessException, InstantiationException {
    Map<AuditActionType, IAuditAspect<Audit, ?, ?>> auditActionTypeClassMap = this.auditClassesMapping.get(auditType);
    return (IAuditAspect<T, ?, ?>) auditActionTypeClassMap.get(auditActionType);

  }


}
