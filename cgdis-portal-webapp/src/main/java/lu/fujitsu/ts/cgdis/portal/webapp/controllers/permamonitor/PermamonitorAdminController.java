package lu.fujitsu.ts.cgdis.portal.webapp.controllers.permamonitor;

import lu.fujitsu.ts.cgdis.portal.business.config.properties.person.BusinessProperties;
import lu.fujitsu.ts.cgdis.portal.core.domain.BaseModel;
import lu.fujitsu.ts.cgdis.portal.core.domain.CGDISEntity;
import lu.fujitsu.ts.cgdis.portal.core.domain.CGDISSecurityConstraint;
import lu.fujitsu.ts.cgdis.portal.core.domain.audit.AuditActionType;
import lu.fujitsu.ts.cgdis.portal.core.domain.audit.AuditType;
import lu.fujitsu.ts.cgdis.portal.core.domain.enums.CGDISEntityType;
import lu.fujitsu.ts.cgdis.portal.core.domain.permamonitor.*;
import lu.fujitsu.ts.cgdis.portal.core.exception.CGDISTechnicalException;
import lu.fujitsu.ts.cgdis.portal.core.security.Permission;
import lu.fujitsu.ts.cgdis.portal.core.service.permamonitor.IPermDeploymentPlanROBusinessService;
import lu.fujitsu.ts.cgdis.portal.core.service.permamonitor.IPermDeploymentPlanWriteBusinessService;
import lu.fujitsu.ts.cgdis.portal.core.service.permamonitor.IPermServicePlanCategoryROBusinessService;
import lu.fujitsu.ts.cgdis.portal.core.service.permamonitor.IPermServicePlanCategoryWriteBusinessService;
import lu.fujitsu.ts.cgdis.portal.webapp.aop.audit.annotation.AuditLog;
import lu.fujitsu.ts.cgdis.portal.webapp.aop.audit.annotation.AuditLogParameter;
import lu.fujitsu.ts.cgdis.portal.webapp.controllers.IPageRequestController;
import lu.fujitsu.ts.cgdis.portal.webapp.controllers.form.permamonitor.admin.*;
import lu.fujitsu.ts.cgdis.portal.webapp.utils.rql.RqlStringBooleanValueParser;
import lu.fujitsu.ts.eportal.server.core.domain.SearchCriterion;
import lu.fujitsu.ts.eportal.server.core.exceptions.InvalidRQLExpressionException;
import lu.fujitsu.ts.eportal.server.core.exceptions.NotFoundException;
import lu.fujitsu.ts.eportal.server.core.utils.rql.SearchRqlParser;
import lu.fujitsu.ts.eportal.server.core.utils.rql.parsers.RqlEnumValueParser;
import lu.fujitsu.ts.eportal.server.logging.milestones.LogMilestone;
import lu.fujitsu.ts.eportal.server.logging.milestones.LoggableType;
import lu.fujitsu.ts.eportal.server.logging.milestones.LoggableValue;
import lu.fujitsu.ts.eportal.server.mvc.utils.rql.RqlParams;
import lu.fujitsu.ts.eportal.server.mvc.utils.rql.RqlRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Objects;

@RestController
@RequestMapping("/permamonitor/admin")
public class PermamonitorAdminController implements IPageRequestController {
  private static final Logger LOGGER = LoggerFactory.getLogger(PermamonitorAdminController.class);

  private final IPermDeploymentPlanROBusinessService permDeploymentPlanROBusinessService;
  private final IPermDeploymentPlanWriteBusinessService permDeploymentPlanWriteBusinessService;
  private final IPermServicePlanCategoryWriteBusinessService permServicePlanCategoryWriteBusinessService;
  private final IPermServicePlanCategoryROBusinessService permServicePlanCategoryROBusinessService;
  private SearchRqlParser rqlParser;

  @Autowired
  public PermamonitorAdminController(IPermServicePlanCategoryWriteBusinessService permServicePlanCategoryWriteBusinessService, IPermServicePlanCategoryROBusinessService permServicePlanCategoryROBusinessService, IPermDeploymentPlanROBusinessService permDeploymentPlanROBusinessService, IPermDeploymentPlanWriteBusinessService permDeploymentPlanWriteBusinessService, BusinessProperties businessProperties) {
    this.permDeploymentPlanROBusinessService = permDeploymentPlanROBusinessService;
    this.permDeploymentPlanWriteBusinessService = permDeploymentPlanWriteBusinessService;
    this.permServicePlanCategoryROBusinessService = permServicePlanCategoryROBusinessService;
    this.permServicePlanCategoryWriteBusinessService = permServicePlanCategoryWriteBusinessService;

    this.buildRqlParser();
  }

  protected List<SearchCriterion> parseRql(String rql) throws InvalidRQLExpressionException {
    return this.rqlParser.parse(rql);
  }

  public void buildRqlParser() {
    rqlParser = new SearchRqlParser() {
    };
    rqlParser.addCriterionParser("permDeploymentPlanTecid", SearchRqlParser.RQL_LONG_VALUE_PARSER);
    rqlParser.addCriterionParser("subcategoryTecid", SearchRqlParser.RQL_LONG_VALUE_PARSER);
    rqlParser.addCriterionParser("servicePlanEntityName", SearchRqlParser.RQL_STRING_VALUE_PARSER);
    rqlParser.addCriterionParser("servicePlanName", SearchRqlParser.RQL_STRING_VALUE_PARSER);
    rqlParser.addCriterionParser("name", SearchRqlParser.RQL_STRING_VALUE_PARSER);
    rqlParser.addCriterionParser("description", SearchRqlParser.RQL_STRING_VALUE_PARSER);
    rqlParser.addCriterionParser("tecid", SearchRqlParser.RQL_LONG_VALUE_PARSER);
    rqlParser.addCriterionParser("bookmarked", new RqlStringBooleanValueParser());
    rqlParser.addCriterionParser("startDate", SearchRqlParser.RQL_LOCALDATE_VALUE_PARSER);
    rqlParser.addCriterionParser("endDate", SearchRqlParser.RQL_LOCALDATE_VALUE_PARSER);
    rqlParser.addCriterionParser("endDate", SearchRqlParser.RQL_LOCALDATE_VALUE_PARSER);
    rqlParser.addCriterionParser("servicePlanWithoutCategory", new RqlStringBooleanValueParser());
    rqlParser.addCriterionParser("permCategory.name", new RqlEnumValueParser(PermCategoryName.class));
    rqlParser.addCriterionParser("permConfigDpceEntityTecid", SearchRqlParser.RQL_LONG_VALUE_PARSER);

  }

  @GetMapping("/deployment-plan/{deploymentPlanTecid}/{categoryName}/entities")
  @LogMilestone(action = "get linked entities")
  public List<CGDISEntity> getEntitiesLinked(
    @PathVariable("deploymentPlanTecid") Long deploymentPlanTecid,
    @PathVariable("categoryName") PermCategoryName categoryName,
    @RequestParam(required = false, name = "baseEntityTecid") List<Long> baseEntityTecids,
    @RequestParam(required = false, name = "entityType") List<CGDISEntityType> entityTypes,
    @RequestParam(required = false, name = "withGis") Boolean withGis
  ) {
    LOGGER.debug("Getting entities linked to deployment plan {} and category {}, baseEntity tecids {}", deploymentPlanTecid, categoryName, baseEntityTecids);
    return this.permDeploymentPlanROBusinessService.getEntitiesLinkedTo(new CGDISSecurityConstraint(Permission.ROLE_PERMISSION_PERMAMONITOR_ADMIN_VIEW), deploymentPlanTecid, categoryName, baseEntityTecids, entityTypes, withGis);
  }

  @GetMapping("/deployment-plan/{deploymentPlanTecid}/config/is-editable")
  @LogMilestone(action = "is deployment plan editable")
  public Boolean isDeploymentPlanConfigEditable(
    @PathVariable("deploymentPlanTecid") @AuditLogParameter(name = "deploymentPlanTecid") Long deploymentPlanTecid
  ) throws NotFoundException {
    return this.permDeploymentPlanROBusinessService.isDeploymentPlanConfigEditable(new CGDISSecurityConstraint(Permission.ROLE_PERMISSION_PERMAMONITOR_ADMIN_VIEW), deploymentPlanTecid);
  }

  @GetMapping("/deployment-plan/{deploymentPlanTecid}/is-editable")
  @LogMilestone(action = "is deployment plan editable")
  public PermDeploymentPlanFormStatus isDeploymentPlanEditable(
    @PathVariable("deploymentPlanTecid") @AuditLogParameter(name = "deploymentPlanTecid") Long deploymentPlanTecid
  ) throws NotFoundException {
    return this.permDeploymentPlanROBusinessService.isDeploymentPlanEditable(new CGDISSecurityConstraint(Permission.ROLE_PERMISSION_PERMAMONITOR_ADMIN_VIEW), deploymentPlanTecid);
  }


  @GetMapping("/deployment-plans/table-data")
  @LogMilestone(action = "get pa versions table data")
  public Page<PermDeploymentPlan> getPaVersionsTableData(
    @RqlParams RqlRequest rqlRequest
  ) throws InvalidRQLExpressionException {
    String rql = rqlRequest.getRql();
    List<SearchCriterion> searchCriteria = parseRql(rql);
    System.out.println("searchCriteriaaa");
    System.out.println(searchCriteria);

    PageRequest pageRequest = getPageRequest(rqlRequest);
    return this.permDeploymentPlanROBusinessService.getAllDeploymentPlansByFilter(new CGDISSecurityConstraint(Permission.ROLE_PERMISSION_PERMAMONITOR_ADMIN_VIEW), searchCriteria, pageRequest);
  }

  @GetMapping("/service-plans")
  @LogMilestone(action = "get pds config table data")
  public Page<PermServicePlanCategoryWithSubcategory> getPdsConfigTableData(
    @RqlParams RqlRequest rqlRequest
  ) throws InvalidRQLExpressionException {
    String rql = rqlRequest.getRql();
    List<SearchCriterion> searchCriteria = parseRql(rql);

    //    for (SearchCriterion criterion : searchCriteria) {
//      if (criterion.getAttribute().equals("servicePlanEntityName")) {
//        criterion.setAttribute("servicePlan.entity.name");
//      }
//    }
    PageRequest pageRequest = getPageRequest(rqlRequest);
    return this.permServicePlanCategoryROBusinessService.getAllServicePlansCategories(new CGDISSecurityConstraint(Permission.ROLE_PERMISSION_PERMAMONITOR_ADMIN_VIEW), searchCriteria, pageRequest);
  }

  @PostMapping("/deployment-plan/create")
  @AuditLog(auditType = AuditType.PERM_DEPLOYMENT_PLAN, actionType = AuditActionType.ADD)
  public PermDeploymentPlan createDeploymentPlan(
    @RequestBody @Valid @AuditLogParameter(name = "form") PermDeploymentPlanPostForm permDeploymentPlanPostForm
  ) throws NotFoundException {
    return this.permDeploymentPlanWriteBusinessService.createDeploymentPlanAndEndLast(new CGDISSecurityConstraint(Permission.ROLE_PERMISSION_PERMAMONITOR_ADMIN_CREATE_DEPLOYMENT_PLAN), permDeploymentPlanPostForm);

  }

  @PatchMapping("/deployment-plan/update")
  @AuditLog(auditType = AuditType.PERM_DEPLOYMENT_PLAN, actionType = AuditActionType.UPDATE)
  public PermDeploymentPlan updateDeploymentPlan(
    @RequestBody @Valid @AuditLogParameter(name = "form") PermDeploymentPlanPatchForm permDeploymentPlanPatchForm
  ) throws NotFoundException {
    return this.permDeploymentPlanWriteBusinessService.updateDeploymentPlanAndUpdatePrevious(new CGDISSecurityConstraint(Permission.ROLE_PERMISSION_PERMAMONITOR_ADMIN_UPDATE_DEPLOYMENT_PLAN), permDeploymentPlanPatchForm);

  }

  @PatchMapping("/{deploymentPlanTecid}/service-plan/{servicePlanCategoryTecid}/bookmarked")
  @LogMilestone(action = "update servicePlanCategory bookmarked")
  @AuditLog(auditType = AuditType.PERM_SERVICE_PLAN, actionType = AuditActionType.BOOKMARKED_UPDATE)
  public PermServicePlanCategory updatePdsBookmarked(
    @PathVariable("servicePlanCategoryTecid") @AuditLogParameter(name = "servicePlanCategoryTecid") Long servicePlanCategoryTecid,
    @PathVariable("deploymentPlanTecid") @AuditLogParameter(name = "deploymentPlanTecid") Long deploymentPlanTecid,
    @RequestBody @Valid @AuditLogParameter(name = "form") PermServicePlanCategoryPatchIsBookmarkedForm form) throws NotFoundException {
    if (Objects.equals(servicePlanCategoryTecid, form.getTechnicalId())) {
      CGDISSecurityConstraint securityConstraint = new CGDISSecurityConstraint(Permission.ROLE_PERMISSION_PERMAMONITOR_ADMIN_UPDATE_DEPLOYMENT_PLAN_CONFIG);
      if (this.permDeploymentPlanROBusinessService.isDeploymentPlanConfigEditable(securityConstraint, deploymentPlanTecid)) {
        return this.permServicePlanCategoryWriteBusinessService.update(securityConstraint, form);
      } else {
        throw new CGDISTechnicalException("This deployment plan has ended and can not be updated.");
      }
    } else {
      throw new CGDISTechnicalException("URL tecid does not match with body tecid.");

    }
  }

  @PatchMapping("/{deploymentPlanTecid}/service-plan/{servicePlanCategoryTecid}/category")
  @LogMilestone(action = "update servicePlanCategory category")
  @AuditLog(auditType = AuditType.PERM_SERVICE_PLAN, actionType = AuditActionType.PERM_CATEGORY_UPDATE)
  public PermServicePlanCategory updatePdsCategory(
    @PathVariable("servicePlanCategoryTecid") @AuditLogParameter(name = "servicePlanCategoryTecid") Long servicePlanCategoryTecid,
    @PathVariable("deploymentPlanTecid") @AuditLogParameter(name = "deploymentPlanTecid") Long deploymentPlanTecid,
    @RequestBody @Valid @AuditLogParameter(name = "form") PermServicePlanCategoryPatchCategoryForm form) throws NotFoundException {
    if (Objects.equals(servicePlanCategoryTecid, form.getTechnicalId())) {
      CGDISSecurityConstraint securityConstraint = new CGDISSecurityConstraint(Permission.ROLE_PERMISSION_PERMAMONITOR_ADMIN_UPDATE_DEPLOYMENT_PLAN_CONFIG);
      if (this.permDeploymentPlanROBusinessService.isDeploymentPlanConfigEditable(securityConstraint, deploymentPlanTecid)) {
        return this.permServicePlanCategoryWriteBusinessService.update(securityConstraint, form);
      } else {
        throw new CGDISTechnicalException("This deployment plan has ended and can not be updated.");
      }
    } else {
      throw new CGDISTechnicalException("URL tecid does not match with body tecid.");

    }
  }

  @PatchMapping("/{deploymentPlanTecid}/service-plan/{servicePlanCategoryTecid}/subcategory")
  @LogMilestone(action = "update servicePlanCategory subcategory")
  @AuditLog(auditType = AuditType.PERM_SERVICE_PLAN, actionType = AuditActionType.PERM_SUBCAT_UPDATE)
  public PermServicePlanCategoryWithSubcategory updatePdsSubCategory(
    @PathVariable("servicePlanCategoryTecid") @AuditLogParameter(name = "servicePlanCategoryTecid") Long servicePlanCategoryTecid,
    @PathVariable("deploymentPlanTecid") @AuditLogParameter(name = "deploymentPlanTecid") Long deploymentPlanTecid,
    @RequestBody @Valid @AuditLogParameter(name = "form") PermServicePlanCategoryPatchSubcategoryForm form) throws NotFoundException {
    if (Objects.equals(servicePlanCategoryTecid, form.getTechnicalId())) {
      CGDISSecurityConstraint securityConstraint = new CGDISSecurityConstraint(Permission.ROLE_PERMISSION_PERMAMONITOR_ADMIN_UPDATE_DEPLOYMENT_PLAN_CONFIG);
      if (this.permDeploymentPlanROBusinessService.isDeploymentPlanConfigEditable(securityConstraint, deploymentPlanTecid)) {
        return this.permServicePlanCategoryWriteBusinessService.updateSubcategory(securityConstraint, deploymentPlanTecid, new BaseModel(form.getTecid(), form.getTeclock()), form.getSubcategoryTecid());
      } else {
        throw new CGDISTechnicalException("This deployment plan has ended and can not be updated.");
      }
    } else {
      throw new CGDISTechnicalException("URL tecid does not match with body tecid.");

    }
  }

  @PatchMapping("/{deploymentPlanTecid}/service-plan/{servicePlanCategoryTecid}/deletecategory")
  @LogMilestone(action = "delete servicePlanCategory category")
  @AuditLog(auditType = AuditType.PERM_SERVICE_PLAN, actionType = AuditActionType.PERM_CATEGORY_DELETE)
  public void deleteCategory(
    @PathVariable("servicePlanCategoryTecid") @AuditLogParameter(name = "servicePlanCategoryTecid") Long servicePlanCategoryTecid,
    @PathVariable("deploymentPlanTecid") @AuditLogParameter(name = "deploymentPlanTecid") Long deploymentPlanTecid,
    @RequestBody @Valid @AuditLogParameter(name = "form") PermServicePlanCategoryPatchCategoryForm form) throws NotFoundException {
    if (Objects.equals(servicePlanCategoryTecid, form.getTechnicalId())) {
      CGDISSecurityConstraint securityConstraint = new CGDISSecurityConstraint(Permission.ROLE_PERMISSION_PERMAMONITOR_ADMIN_UPDATE_DEPLOYMENT_PLAN_CONFIG);
      if (this.permDeploymentPlanROBusinessService.isDeploymentPlanConfigEditable(securityConstraint, deploymentPlanTecid)) {
        this.permServicePlanCategoryWriteBusinessService.delete(securityConstraint, deploymentPlanTecid, servicePlanCategoryTecid);
      } else {
        throw new CGDISTechnicalException("This deployment plan has ended and can not be updated.");
      }
    } else {
      throw new CGDISTechnicalException("URL tecid does not match with body tecid.");

    }
  }

  @PatchMapping("/{deploymentPlanTecid}/service-plan/{servicePlanCategoryTecid}/deletesubcategory")
  @LogMilestone(action = "delete servicePlanCategory subcategory")
  @AuditLog(auditType = AuditType.PERM_SERVICE_PLAN, actionType = AuditActionType.PERM_SUBCAT_UPDATE)
  public PermServicePlanCategoryWithSubcategory deletePdsSubCategory(
    @PathVariable("servicePlanCategoryTecid") @AuditLogParameter(name = "servicePlanCategoryTecid") Long servicePlanCategoryTecid,
    @PathVariable("deploymentPlanTecid") @AuditLogParameter(name = "deploymentPlanTecid") Long deploymentPlanTecid,
    @RequestBody @Valid @AuditLogParameter(name = "form") PermServicePlanCategoryPatchSubcategoryForm form) throws NotFoundException {
    if (Objects.equals(servicePlanCategoryTecid, form.getTechnicalId())) {
      CGDISSecurityConstraint securityConstraint = new CGDISSecurityConstraint(Permission.ROLE_PERMISSION_PERMAMONITOR_ADMIN_UPDATE_DEPLOYMENT_PLAN_CONFIG);
      if (this.permDeploymentPlanROBusinessService.isDeploymentPlanConfigEditable(securityConstraint, deploymentPlanTecid)) {
        return this.permServicePlanCategoryWriteBusinessService.deleteSubcategory(securityConstraint, deploymentPlanTecid, new BaseModel(form.getTecid(), form.getTeclock()));
      } else {
        throw new CGDISTechnicalException("This deployment plan has ended and can not be updated.");
      }
    } else {
      throw new CGDISTechnicalException("URL tecid does not match with body tecid.");

    }
  }

  @PostMapping("/{deploymentPlanTecid}/service-plan//category/{categoryTecid}")
  @LogMilestone(action = "create servicePlanCategory category")
  @AuditLog(auditType = AuditType.PERM_SERVICE_PLAN, actionType = AuditActionType.PERM_CATEGORY_CREATE)
  public PermServicePlanCategory createPdsCategory(
    @PathVariable("categoryTecid") @LoggableValue Long categoryTecid,
    @PathVariable("deploymentPlanTecid") @AuditLogParameter(name = "deploymentPlanTecid") @LoggableValue Long deploymentPlanTecid,
    @RequestBody @Valid PermServicePlanCategoryCreateCategoryForm form) throws NotFoundException {

    CGDISSecurityConstraint securityConstraint = new CGDISSecurityConstraint(Permission.ROLE_PERMISSION_PERMAMONITOR_ADMIN_UPDATE_DEPLOYMENT_PLAN_CONFIG);
    if (this.permDeploymentPlanROBusinessService.isDeploymentPlanConfigEditable(securityConstraint, deploymentPlanTecid)) {
      return this.permServicePlanCategoryWriteBusinessService.create(securityConstraint, form);
    } else {
      throw new CGDISTechnicalException("This deployment plan has ended and can not be updated.");
    }

  }


  @GetMapping("/deployment-plans")
  @LogMilestone(action = "get all deployment-plans", logResult = @LoggableValue(type = LoggableType.COLLECTION))
  public List<? extends PermDeploymentPlan> getAllPermDeploymentPlans() {
    return this.permDeploymentPlanROBusinessService.getAll(new CGDISSecurityConstraint(Permission.ROLE_PERMISSION_PERMAMONITOR_ADMIN_VIEW));
  }

  @GetMapping("/deployment-plan/{deploymentPlanTecid}")
  @LogMilestone(action = "get all deployment-plans", logResult = @LoggableValue(type = LoggableType.COLLECTION))
  public PermDeploymentPlan getPermDeploymentPlanByTecid(@PathVariable("deploymentPlanTecid") @LoggableValue Long deploymentPlanTecid) throws NotFoundException {
    return this.permDeploymentPlanROBusinessService.get(new CGDISSecurityConstraint(Permission.ROLE_PERMISSION_PERMAMONITOR_ADMIN_VIEW), deploymentPlanTecid);
  }

}
