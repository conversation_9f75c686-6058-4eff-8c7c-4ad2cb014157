package lu.fujitsu.ts.cgdis.portal.webapp.controllers;

import lu.fujitsu.ts.cgdis.portal.core.domain.ICGDISPortalUserDetails;
import lu.fujitsu.ts.cgdis.portal.core.domain.error.ErrorMessage;
import lu.fujitsu.ts.cgdis.portal.core.domain.error.ErrorMessageStatus;
import lu.fujitsu.ts.cgdis.portal.core.domain.error.ErrorMessageType;
import lu.fujitsu.ts.cgdis.portal.core.logging.LoggingCategory;
import lu.fujitsu.ts.cgdis.portal.core.logging.LoggingDomain;
import lu.fujitsu.ts.cgdis.portal.core.security.utils.CGDISPortalSecurityHolder;
import lu.fujitsu.ts.cgdis.portal.core.service.person.IErrorMessageBusinessService;
import lu.fujitsu.ts.cgdis.portal.webapp.controllers.form.error.CreateErrorMessageForm;
import lu.fujitsu.ts.cgdis.portal.webapp.controllers.form.error.UpdateErrorMessageForm;
import lu.fujitsu.ts.eportal.server.core.domain.SearchCriterion;
import lu.fujitsu.ts.eportal.server.core.exceptions.InvalidRQLExpressionException;
import lu.fujitsu.ts.eportal.server.core.exceptions.NotFoundException;
import lu.fujitsu.ts.eportal.server.core.utils.rql.SearchRqlParser;
import lu.fujitsu.ts.eportal.server.core.utils.rql.parsers.RqlEnumValueParser;
import lu.fujitsu.ts.eportal.server.logging.milestones.LogMilestone;
import lu.fujitsu.ts.eportal.server.logging.milestones.LoggableValue;
import lu.fujitsu.ts.eportal.server.mvc.utils.rql.RqlParams;
import lu.fujitsu.ts.eportal.server.mvc.utils.rql.RqlRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

/**
 * The type Error management controller.
 */
@RestController
@RequestMapping("/error-management")
@LogMilestone(category = LoggingCategory.REST_API_CALL, domain = LoggingDomain.ERROR_MESSAGE)
public class ErrorManagementController extends AbstractSecuredModifiableBaseController<ICGDISPortalUserDetails, ErrorMessage, Long, IErrorMessageBusinessService, CreateErrorMessageForm, UpdateErrorMessageForm> {

  private final IErrorMessageBusinessService businessService;

  /**
   * Instantiates a new Person controller.
   *
   * @param service the service
   */
  @Autowired
  public ErrorManagementController(IErrorMessageBusinessService service) {
    super(service, "creationDate", Sort.Direction.DESC, 10);
    this.businessService = service;
  }

  @Override
  public SearchRqlParser buildRqlParser() {
    SearchRqlParser rqlParser = new SearchRqlParser(){};
    rqlParser.addCriterionParser("creationDate", SearchRqlParser.RQL_LOCALDATE_VALUE_PARSER);
    rqlParser.addCriterionParser("type", new RqlEnumValueParser(ErrorMessageType.class));
    rqlParser.addCriterionParser("status", new RqlEnumValueParser(ErrorMessageStatus.class));
    return rqlParser;
  }

  /**
   * Gets error message types.
   *
   * @return the error message types
   */
  @GetMapping("/types")
  @LogMilestone(action = "get error message types")
  public List<String> getErrorMessageTypes() {
    return businessService.getAvailableErrorMessageTypes(CGDISPortalSecurityHolder.getConnectedUserDetails());
  }

  /**
   * Resend message.
   * @param tecid the tecid
   */
  @PutMapping("/{tecid}/resend")
  @LogMilestone(action = "resend message")
  public void resendErrorMessage(@LoggableValue @PathVariable long tecid) throws NotFoundException {
    businessService.resend(tecid);
  }

  @Override
  @DeleteMapping({"/{id}"})
  public void deleteById(@PathVariable("id") Long id) {
    businessService.delete(id);
  }

  @Override
  @GetMapping({"/search"})
  public Page<? extends ErrorMessage> search(@RqlParams RqlRequest rqlRequest) throws InvalidRQLExpressionException {
    PageRequest pageRequest = PageRequest.of(rqlRequest.getPage() == null ? 0 : rqlRequest.getPage(), rqlRequest.getPageSize() == null ? this.defaultPageSize : rqlRequest.getPageSize(), rqlRequest.getDirection() == null ? this.defaultSortDirection : rqlRequest.getDirection(), new String[]{StringUtils.isEmpty(rqlRequest.getOrderBy()) ? this.defaultSortField : rqlRequest.getOrderBy()});
    List<SearchCriterion> criterionList = this.parseRql(rqlRequest.getRql());
    List<SearchCriterion> finalCriterionList = new ArrayList<>();
    for (SearchCriterion criterion : criterionList){
      if(criterion.getAttribute().equals("creationDate")){
        LocalDate creationDate = (LocalDate)criterion.getValue();
        SearchCriterion from = new SearchCriterion("creationDate", creationDate.atStartOfDay(), SearchCriterion.Operator.ge);
        SearchCriterion to = new SearchCriterion("creationDate", creationDate.plusDays(1).atStartOfDay(), SearchCriterion.Operator.lt);
        finalCriterionList.add(from);
        finalCriterionList.add(to);
      }else {
        finalCriterionList.add(criterion);
      }
    }

    return businessService.searchAll(CGDISPortalSecurityHolder.getConnectedUserDetails(), finalCriterionList, pageRequest);
  }

  @Override
  public ICGDISPortalUserDetails getSecurityConstraint() {
    return CGDISPortalSecurityHolder.getConnectedUserDetails();
  }

  @Override
  public ICGDISPortalUserDetails getUpdateSecurityConstraint() {
    return CGDISPortalSecurityHolder.getConnectedUserDetails();
  }

  @Override
  public ICGDISPortalUserDetails getCreateSecurityConstraint() {
    return CGDISPortalSecurityHolder.getConnectedUserDetails();
  }

  @Override
  public ICGDISPortalUserDetails getDeleteSecurityConstraint() {
    return CGDISPortalSecurityHolder.getConnectedUserDetails();
  }
}
