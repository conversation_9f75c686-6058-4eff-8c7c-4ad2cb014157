package lu.fujitsu.ts.cgdis.portal.webapp.controllers.admin.form.positiontemplate;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lu.fujitsu.ts.cgdis.portal.core.domain.BaseModel;
import lu.fujitsu.ts.cgdis.portal.webapp.controllers.admin.form.serviceplanmodel.UpdateServicePlanModelVersionPositionForm;
import lu.fujitsu.ts.cgdis.portal.webapp.validator.FutureDate;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDate;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class AbstractPositionTemplateVersionForm extends BaseModel {

  /**
   * The Version.
   */
  @NotNull
  @Size(min = 1, max = 255)
  private String portalLabel;

  /**
   * The Start date.
   * NOTE: NotNull check on business service
   */
  @FutureDate(plusDays = 1)
  private LocalDate startDate;

  /**
   * The End date.
   */
  @FutureDate(plusDays = 1)
  private LocalDate endDate;

  private Long positionTemplateTecid;


  private Long positionTemplateTeclock;

  @Valid
  @Size(min = 1, max = 10)
  private List<BaseModel> idealFunctions;
  @Valid
  @Size(max = 10)
  private List<BaseModel> partialFunctions;
}
