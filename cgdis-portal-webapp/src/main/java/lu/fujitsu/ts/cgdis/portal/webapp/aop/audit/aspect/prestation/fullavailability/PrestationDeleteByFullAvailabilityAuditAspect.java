package lu.fujitsu.ts.cgdis.portal.webapp.aop.audit.aspect.prestation.fullavailability;

import lu.fujitsu.ts.cgdis.portal.core.domain.audit.AuditActionType;
import lu.fujitsu.ts.cgdis.portal.core.domain.audit.AuditPrestation;
import lu.fujitsu.ts.cgdis.portal.core.domain.serviceplan.prestation.Prestation;
import lu.fujitsu.ts.cgdis.portal.core.pojo.prestation.PrestationFullAvailabilityResultDto;
import lu.fujitsu.ts.cgdis.portal.core.utils.DateTimeUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.util.Map;

@Component
/**
 * Aspect for auditing the deletion of prestations by remaining full availability.
 */
public class PrestationDeleteByFullAvailabilityAuditAspect extends AbstractPrestationByFullAvailabilityAuditAspect<Prestation> {

  private static final Logger LOGGER = LoggerFactory.getLogger(PrestationDeleteByFullAvailabilityAuditAspect.class);

  /**
   * Constructs a new PrestationDeleteByFullAvailabilityAuditAspect.
   */
  public PrestationDeleteByFullAvailabilityAuditAspect() {
    super(AuditActionType.FULL_AV_DELETE, AuditPrestation.class);
  }

  /**
   * Populates the audit after processing the method.
   *
   * @param point the join point representing the method execution
   * @param method the method being executed
   * @param audit the audit object to populate
   * @param parameters the method parameters
   * @param prestation the prestation object
   */
  @Override
  protected void populateAuditAfterProcessingMethod(ProceedingJoinPoint point, Method method, AuditPrestation audit, Map<String, Object> parameters, Prestation prestation) {
    if (prestation != null) {
      mapPrestation(audit, prestation);
    }
  }

  /**
   * Extracts the value from the method result.
   *
   * @param methodResult the result of the method execution
   * @return the extracted value
   */
  @Override
  public Object extractValueFromMethodResult(PrestationFullAvailabilityResultDto methodResult) {
    return methodResult == null || methodResult.getPrestation() == null ? null : methodResult.getPrestation().getPrestationClosed();
  }

  /**
   * Populates the audit before processing the method.
   *
   * @param point the join point representing the method execution
   * @param method the method being executed
   * @param audit the audit object to populate
   * @param parameters the method parameters
   */
  @Override
  protected void populateAuditBeforeProcessingMethod(ProceedingJoinPoint point, Method method, AuditPrestation audit, Map<String, Object> parameters) {
    // No implementation needed
  }

  /**
   * Determines whether an audit log must be created.
   *
   * @param audit the audit object
   * @param parameters the method parameters
   * @param prestation the prestation object
   * @return true if an audit log must be created, false otherwise
   */
  @Override
  protected boolean mustCreateAuditLog(AuditPrestation audit, Map<String, Object> parameters, Prestation prestation) {
    if (prestation == null) {
      return false;
    }
    return DateTimeUtils.isAfterOrEquals(prestation.getStartDateTime(), LocalDateTime.now());
  }
}
