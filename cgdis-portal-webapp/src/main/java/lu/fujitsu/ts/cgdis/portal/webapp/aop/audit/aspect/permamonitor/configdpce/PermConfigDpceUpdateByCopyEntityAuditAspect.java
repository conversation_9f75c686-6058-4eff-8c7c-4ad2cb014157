package lu.fujitsu.ts.cgdis.portal.webapp.aop.audit.aspect.permamonitor.configdpce;

import lu.fujitsu.ts.cgdis.portal.core.domain.audit.AuditActionType;
import lu.fujitsu.ts.cgdis.portal.core.domain.audit.AuditPermConfigDpce;
import lu.fujitsu.ts.cgdis.portal.core.domain.permamonitor.PermCategoryName;
import lu.fujitsu.ts.cgdis.portal.services.permamonitor.IPermCategoryRORepositoryService;
import lu.fujitsu.ts.cgdis.portal.webapp.controllers.form.permamonitor.admin.PermConfigPdceHourCopyForm;
import lu.fujitsu.ts.cgdis.portal.webapp.dto.permamonitor.PermConfigDpceCriticityAdminDto;
import org.aspectj.lang.ProceedingJoinPoint;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.Map;


@Component
public class PermConfigDpceUpdateByCopyEntityAuditAspect extends AbstractPermConfigDpceAuditAspect {

  private static final Logger LOGGER = LoggerFactory.getLogger(PermConfigDpceUpdateByCopyEntityAuditAspect.class);

  private final IPermCategoryRORepositoryService permCategoryRORepositoryService;

  public PermConfigDpceUpdateByCopyEntityAuditAspect(IPermCategoryRORepositoryService permCategoryRORepositoryService) {
    super(AuditActionType.COPY_ENTITY, AuditPermConfigDpce.class);
    this.permCategoryRORepositoryService = permCategoryRORepositoryService;
  }


  @Override
  protected void populateAuditBeforeProcessingMethod(ProceedingJoinPoint point, Method method, AuditPermConfigDpce audit, Map<String, Object> parameters) {
    Long deploymentTecid = (Long) parameters.get("deploymentPlanTecid");
    PermCategoryName categoryName = (PermCategoryName) parameters.get("categoryName");
    Long entityTecid = (Long) parameters.get("toEntityTecid");
    PermConfigPdceHourCopyForm form = (PermConfigPdceHourCopyForm) parameters.get("form");


    audit.setDeploymentPlanTecid(deploymentTecid);
    audit.setCategoryName(categoryName.name());
    audit.setCategoryTecid(permCategoryRORepositoryService.getByName(getUserDetails(), categoryName).get().getTecid());
    audit.setEntityTecid(entityTecid);


  }

  @Override
  protected void mapResult(AuditPermConfigDpce audit, PermConfigDpceCriticityAdminDto result) {
    super.mapResult(audit, result);
  }

  @Override
  protected boolean mustCreateAuditLog(AuditPermConfigDpce audit, Map<String, Object> parameters, PermConfigDpceCriticityAdminDto result) {
    return true;
  }


}
