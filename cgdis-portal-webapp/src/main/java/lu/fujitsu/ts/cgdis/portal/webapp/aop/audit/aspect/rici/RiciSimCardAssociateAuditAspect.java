package lu.fujitsu.ts.cgdis.portal.webapp.aop.audit.aspect.rici;

import lu.fujitsu.ts.cgdis.portal.core.domain.audit.AuditActionType;
import lu.fujitsu.ts.cgdis.portal.core.domain.CGDISSecurityConstraint;
import lu.fujitsu.ts.cgdis.portal.core.domain.audit.AuditActionType;
import lu.fujitsu.ts.cgdis.portal.core.domain.audit.AuditRiciSimCard;
import lu.fujitsu.ts.cgdis.portal.core.domain.rici.RiciPager;
import lu.fujitsu.ts.cgdis.portal.core.domain.rici.RiciSimCard;
import lu.fujitsu.ts.cgdis.portal.core.domain.rici.RiciSimCardPager;
import lu.fujitsu.ts.cgdis.portal.core.security.Permission;
import lu.fujitsu.ts.cgdis.portal.core.service.rici.IRiciPagerROBusinessService;
import lu.fujitsu.ts.eportal.server.core.exceptions.NotFoundException;
import org.aspectj.lang.ProceedingJoinPoint;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.Map;

/**
 * Aspect for auditing RICI SIM card association operations.
 */
@Component
public class RiciSimCardAssociateAuditAspect extends AbstractRiciSimCardAuditAspect<ResponseEntity<RiciSimCardPager>, ResponseEntity<RiciSimCardPager>> {

  private static final Logger LOGGER = LoggerFactory.getLogger(RiciSimCardAssociateAuditAspect.class);

  private final IRiciPagerROBusinessService pagerROService;

  /**
   * Constructs a new RiciSimCardAssignAuditAspect.
   * @param pagerROService Service to fetch Pager details.
   */
  @Autowired
  public RiciSimCardAssociateAuditAspect(IRiciPagerROBusinessService pagerROService) {
    super(AuditActionType.ASSOCIATE, AuditRiciSimCard.class);
    this.pagerROService = pagerROService;
  }

  @Override
  protected void populateAuditAfterProcessingMethod(ProceedingJoinPoint point, Method method, AuditRiciSimCard audit, Map<String, Object> parameters, ResponseEntity<RiciSimCardPager> result) {
    if (result != null && result.getBody() != null) {
      RiciSimCardPager simCardPager = result.getBody();
      if (simCardPager.getSimCard() != null) {
        RiciSimCard simCard = simCardPager.getSimCard();
        audit.setSimCardTecid(simCard.getTecid());
        audit.setIccid(simCard.getIccid());
        audit.setMsisdn(simCard.getMsisdn());
        audit.setPin(simCard.getPin());
        audit.setStatus(simCard.getStatus());
      }
      if (simCardPager.getPager() != null) {
        audit.setAssociatedPagerTecid(simCardPager.getPager().getTecid());
        audit.setAssociatedPagerId(simCardPager.getPager().getPagerId());
      }
    }
  }

  @Override
  protected void populateAuditBeforeProcessingMethod(ProceedingJoinPoint point, Method method, AuditRiciSimCard audit, Map<String, Object> parameters) {
    Long simCardTecid = (Long) parameters.get("simCardTecid");
    Long pagerTecid = (Long) parameters.get("associatedPagerTecid");

    audit.setSimCardTecid(simCardTecid);
    audit.setAssociatedPagerTecid(pagerTecid);

    if (pagerTecid != null) {
      try {
        RiciPager pager = pagerROService.getByTecId(new CGDISSecurityConstraint(Permission.ROLE_PERMISSION_RICI_PAGER_VIEW), pagerTecid);
        if (pager != null) {
          audit.setAssociatedPagerId(pager.getPagerId());
        }
      } catch (NotFoundException e) {
        LOGGER.warn("Pager not found by tecid {} during audit population for association. Pager ID will not be set in audit.", pagerTecid, e);
      } catch (Exception e) {
        LOGGER.error("Error fetching pager by tecid {} for audit.", pagerTecid, e);
      }
    }
  }

  @Override
  protected boolean mustCreateAuditLog(AuditRiciSimCard audit, Map<String, Object> parameters, ResponseEntity<RiciSimCardPager> result) {
    return result != null && result.getStatusCode().is2xxSuccessful() && result.getBody() != null;
  }

  @Override
  protected Long getTecidFromResult(ResponseEntity<RiciSimCardPager> result) {
    if (result != null && result.getBody() != null && result.getBody().getSimCard() != null) {
      return result.getBody().getSimCard().getTecid();
    }
    return null;
  }
}
