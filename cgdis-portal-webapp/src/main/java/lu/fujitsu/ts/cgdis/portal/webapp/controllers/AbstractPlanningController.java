package lu.fujitsu.ts.cgdis.portal.webapp.controllers;

import lu.fujitsu.ts.cgdis.portal.core.domain.planning.Planning;
import lu.fujitsu.ts.cgdis.portal.core.domain.planning.PlanningSummary;
import lu.fujitsu.ts.cgdis.portal.core.security.utils.CGDISPortalSecurityHolder;
import lu.fujitsu.ts.cgdis.portal.core.service.person.IPlanningBusinessService;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDate;

/**
 * The type Abstract planning controller.
 */
public abstract class AbstractPlanningController {

  /**
   * The Planning business service.
   */
  @Autowired
  protected IPlanningBusinessService planningBusinessService;

  /**
   * Get planning.
   * @param personId the person id
   * @param split the split
   * @param from from date
   * @param to to date
   * @return the planning
   */
  protected Planning get(Long personId, boolean split, LocalDate from, LocalDate to) {
    return planningBusinessService.getPlanning(CGDISPortalSecurityHolder.getConnectedUserDetails(), personId, split, from, to);
  }

  /**
   * Summary planning summary.
   * @param personId the person id
   * @param from from date
   * @param to to date
   * @return the planning summary
   */
  protected PlanningSummary summary(Long personId, LocalDate from, LocalDate to) {
    return planningBusinessService.getPlanningSummary(CGDISPortalSecurityHolder.getConnectedUserDetails(), personId, from, to);
  }

}
