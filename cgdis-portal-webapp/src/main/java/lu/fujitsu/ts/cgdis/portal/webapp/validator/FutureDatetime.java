package lu.fujitsu.ts.cgdis.portal.webapp.validator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.*;
import static java.lang.annotation.RetentionPolicy.RUNTIME;


/**
 * The interface Future datetime.
 */
@Target({PARAMETER, METHOD, FIELD, TYPE, ANNOTATION_TYPE})
@Retention(RUNTIME)
@Constraint(validatedBy = {FutureDatetimeValidator.class})
@Documented
public @interface FutureDatetime {

  /**
   * Message string.
   *
   * @return the string
   */
  String message() default "The date must be in the future";

  /**
   * Groups class [ ].
   *
   * @return the class [ ]
   */
  Class<?>[] groups() default {};

  /**
   * Payload class [ ].
   *
   * @return the class [ ]
   */
  Class<? extends Payload>[] payload() default {};

  /**
   * The date must be at least X day after today
   *
   * @return long
   */
  long plusDays() default 0;

  /**
   * Plus hours long.
   *
   * @return the long
   */
  long plusHours() default 0;

  /**
   * Plus minutes long.
   *
   * @return the long
   */
  long plusMinutes() default 0;

  /**
   * Plus seconds long.
   *
   * @return the long
   */
  long plusSeconds() default 0;


  /**
   * The interface List.
   */
  @Target({TYPE, ANNOTATION_TYPE})
    @Retention(RUNTIME)
    @Documented
    @interface List {
    /**
     * Value future datetime [ ].
     *
     * @return the future datetime [ ]
     */
    FutureDatetime[] value();
    }
}
