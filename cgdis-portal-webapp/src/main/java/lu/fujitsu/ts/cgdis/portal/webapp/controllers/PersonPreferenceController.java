package lu.fujitsu.ts.cgdis.portal.webapp.controllers;

import lu.fujitsu.ts.cgdis.portal.core.domain.person.PersonPreference;
import lu.fujitsu.ts.cgdis.portal.core.security.utils.CGDISPortalSecurityHolder;
import lu.fujitsu.ts.cgdis.portal.core.service.person.IPersonPreferenceBusinessService;
import lu.fujitsu.ts.eportal.server.core.utils.rql.SearchRqlParser;
import lu.fujitsu.ts.eportal.server.logging.milestones.LoggableValue;
import lu.fujitsu.ts.eportal.server.mvc.controllers.AbstractSimpleBaseController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * The type Person preference controller.
 */
@RestController
@RequestMapping("/person-preference")
public class PersonPreferenceController extends AbstractSimpleBaseController<PersonPreference, Long, IPersonPreferenceBusinessService> {

  /**
   * Instantiates a new Person preference controller.
   *
   * @param service the service
   */
  @Autowired
  PersonPreferenceController(IPersonPreferenceBusinessService service) {
    super(service, "preferenceKey", Sort.Direction.ASC, 10);
  }

  @Override
  public SearchRqlParser buildRqlParser() {
    SearchRqlParser rqlParser = new SearchRqlParser(){};
    rqlParser.addCriterionParser("tecid", SearchRqlParser.RQL_LONG_VALUE_PARSER);
    rqlParser.addCriterionParser("preferenceKey", SearchRqlParser.RQL_STRING_VALUE_PARSER);
    rqlParser.addCriterionParser("preferenceValue", SearchRqlParser.RQL_STRING_VALUE_PARSER);
    rqlParser.addCriterionParser("personTecid", SearchRqlParser.RQL_LONG_VALUE_PARSER);
    return rqlParser;
  }

  @PutMapping("{preferenceKey}/{preferenceValue}")
  public boolean setPersonPreference(@PathVariable @LoggableValue String preferenceKey, @PathVariable @LoggableValue String preferenceValue) {
    return this.service.setPersonPreference(CGDISPortalSecurityHolder.getConnectedUserDetails().getTecid(),preferenceKey,preferenceValue);
  }

}
