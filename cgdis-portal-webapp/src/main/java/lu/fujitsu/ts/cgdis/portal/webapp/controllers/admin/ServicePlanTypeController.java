package lu.fujitsu.ts.cgdis.portal.webapp.controllers.admin;

import lu.fujitsu.ts.cgdis.portal.core.domain.serviceplan.ServicePlanType;
import lu.fujitsu.ts.cgdis.portal.core.service.serviceplan.IServicePlanTypeBusinessService;
import lu.fujitsu.ts.eportal.server.core.utils.rql.SearchRqlParser;
import lu.fujitsu.ts.eportal.server.mvc.controllers.AbstractSimpleROBaseController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * The Service Plan Type controller.
 */
@RestController
@RequestMapping("/serviceplantype")
public class ServicePlanTypeController extends AbstractSimpleROBaseController<ServicePlanType, Long, IServicePlanTypeBusinessService> {

  /**
   * Instantiates a new Service Plan Type controller.
   *
   * @param service the service
   */
  @Autowired
  public ServicePlanTypeController(IServicePlanTypeBusinessService service) {
    super(service, "label", Sort.Direction.ASC, 10);
  }

  @Override
  public SearchRqlParser buildRqlParser() {
    SearchRqlParser rqlParser = new SearchRqlParser();
    rqlParser.addCriterionParser("tecid", SearchRqlParser.RQL_LONG_VALUE_PARSER);
    rqlParser.addCriterionParser("id", SearchRqlParser.RQL_STRING_VALUE_PARSER);
    rqlParser.addCriterionParser("label", SearchRqlParser.RQL_STRING_VALUE_PARSER);
    return rqlParser;
  }

  @GetMapping("all")
  public List<ServicePlanType> getAll(){
    return (List<ServicePlanType>) service.getAll();
  }
}
