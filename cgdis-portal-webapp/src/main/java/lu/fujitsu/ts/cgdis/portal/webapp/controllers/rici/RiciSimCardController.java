package lu.fujitsu.ts.cgdis.portal.webapp.controllers.rici;

import lombok.extern.slf4j.Slf4j;
import lu.fujitsu.ts.cgdis.portal.core.domain.CGDISSecurityConstraint;
import lu.fujitsu.ts.cgdis.portal.core.domain.audit.AuditActionType;
import lu.fujitsu.ts.cgdis.portal.core.domain.audit.AuditType;
import lu.fujitsu.ts.cgdis.portal.core.domain.rici.RiciPager;
import lu.fujitsu.ts.cgdis.portal.core.domain.csv_import.CsvImportReport;
import lu.fujitsu.ts.cgdis.portal.core.domain.rici.RiciSimCard;
import lu.fujitsu.ts.cgdis.portal.core.domain.rici.RiciSimCardPager;
import lu.fujitsu.ts.cgdis.portal.core.exception.CGDISTechnicalException;
import lu.fujitsu.ts.cgdis.portal.core.logging.LoggingCategory;
import lu.fujitsu.ts.cgdis.portal.core.logging.LoggingDomain;
import lu.fujitsu.ts.cgdis.portal.core.security.Permission;
import lu.fujitsu.ts.cgdis.portal.core.service.rici.*;
import lu.fujitsu.ts.cgdis.portal.export.csv.RiciSimCardExportService;
import lu.fujitsu.ts.cgdis.portal.webapp.aop.audit.annotation.AuditLog;
import lu.fujitsu.ts.cgdis.portal.webapp.aop.audit.annotation.AuditLogParameter;
import lu.fujitsu.ts.cgdis.portal.webapp.controllers.IPageRequestController;
import lu.fujitsu.ts.cgdis.portal.webapp.controllers.form.rici.RiciSimCardForm;
import lu.fujitsu.ts.cgdis.portal.webapp.controllers.form.rici.RiciSimCardPatchForm;
import lu.fujitsu.ts.cgdis.portal.webapp.utils.rql.RqlStringBooleanValueParser;
import lu.fujitsu.ts.eportal.server.core.domain.SearchCriterion;
import lu.fujitsu.ts.eportal.server.core.exceptions.InvalidRQLExpressionException;
import lu.fujitsu.ts.eportal.server.core.exceptions.NotFoundException;
import lu.fujitsu.ts.eportal.server.core.utils.rql.SearchRqlParser;
import lu.fujitsu.ts.eportal.server.logging.milestones.LogMilestone;
import lu.fujitsu.ts.eportal.server.logging.milestones.LoggableType;
import lu.fujitsu.ts.eportal.server.logging.milestones.LoggableValue;
import lu.fujitsu.ts.eportal.server.mvc.utils.rql.RqlParams;
import lu.fujitsu.ts.eportal.server.mvc.utils.rql.RqlRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;


@Slf4j
@RestController
@RequestMapping("/rici/sim-cards")
@LogMilestone(category = LoggingCategory.REST_API_CALL, domain = LoggingDomain.RICI)
public class RiciSimCardController implements IPageRequestController {

  private final IRiciSimCardROBusinessService simCardROBusinessService;
  private final IRiciSimCardWriteBusinessService simCardWriteBusinessService;
  private final IRiciSimCardPagerROBusinessService simCardPagerROBusinessService;
  private final IRiciSimCardPagerWriteBusinessService riciSimCardPagerWriteBusinessService;
  // RiciSimCardWriteRepositoryService is now used by RiciSimCardCSVImportBusinessService
  // IRiciPagerROBusinessService is now used by RiciSimCardCSVImportBusinessService
  // IRiciPagerWriteBusinessService is now used by RiciSimCardCSVImportBusinessService
  private final IRiciSimCardCSVImportBusinessService riciSimCardCSVImportBusinessService; // New service for CSV import
  private SearchRqlParser rqlParser;


  @Autowired
  private RiciSimCardExportService riciSimCardExportService;


  @Autowired
  public RiciSimCardController(
    IRiciSimCardROBusinessService simCardROBusinessService,
    IRiciSimCardWriteBusinessService simCardWriteBusinessService,
    IRiciSimCardPagerROBusinessService simCardPagerROBusinessService,
    IRiciSimCardPagerWriteBusinessService riciSimCardPagerWriteBusinessService,
    IRiciSimCardCSVImportBusinessService riciSimCardCSVImportBusinessService) { // Dependencies for CSV import moved to the service
    this.simCardROBusinessService = simCardROBusinessService;
    this.simCardWriteBusinessService = simCardWriteBusinessService;
    this.simCardPagerROBusinessService = simCardPagerROBusinessService;
    this.riciSimCardPagerWriteBusinessService = riciSimCardPagerWriteBusinessService;
    this.riciSimCardCSVImportBusinessService = riciSimCardCSVImportBusinessService;
    buildRqlParser();
  }

  public void buildRqlParser() {
    rqlParser = new SearchRqlParser() {
    };
    rqlParser.addCriterionParser("showInactiveSims", new RqlStringBooleanValueParser());
    rqlParser.addCriterionParser("iccid", SearchRqlParser.RQL_STRING_VALUE_PARSER);
    rqlParser.addCriterionParser("msisdn", SearchRqlParser.RQL_STRING_VALUE_PARSER);
    rqlParser.addCriterionParser("pin", SearchRqlParser.RQL_STRING_VALUE_PARSER);
    rqlParser.addCriterionParser("status", SearchRqlParser.RQL_STRING_VALUE_PARSER);
    rqlParser.addCriterionParser("pagerId", SearchRqlParser.RQL_STRING_VALUE_PARSER);
//    rqlParser.addCriterionParser("pagerId", SearchRqlParser.RQL_STRING_VALUE_PARSER);
  }


  @GetMapping("all")
  @LogMilestone(action = "get all sim cards", logResult = @LoggableValue(type = LoggableType.PAGE))
  public Page<? extends RiciSimCard> getAllSimCards(@RqlParams RqlRequest rqlRequest) throws InvalidRQLExpressionException {
    PageRequest pageRequest = getPageRequest(rqlRequest);
    List<SearchCriterion> searchCriteria = rqlParser.parse(rqlRequest.getRql());
    CGDISSecurityConstraint securityConstraint = new CGDISSecurityConstraint(Permission.ROLE_PERMISSION_RICI_SIM_CARD_VIEW);
    return simCardROBusinessService.getAllSimCardsNotLogicallyDeleted(securityConstraint, searchCriteria, pageRequest);
  }

  @GetMapping(value = "export/csv", produces = {"text/csv", "application/json"})
  @LogMilestone(action = "export all sim cards with filter")
  public void exportAllSimCards(@RqlParams RqlRequest rqlRequest, HttpServletResponse response
  ) throws InvalidRQLExpressionException {

    PageRequest pageRequest = new PageRequest(0, Integer.MAX_VALUE);
    List<SearchCriterion> searchCriteria = rqlParser.parse(rqlRequest.getRql());
    CGDISSecurityConstraint securityConstraint = new CGDISSecurityConstraint(Permission.ROLE_PERMISSION_RICI_EXPORT);
    Page<RiciSimCard> allSimCardsNotLogicallyDeleted = simCardROBusinessService.getAllSimCardsNotLogicallyDeleted(securityConstraint, searchCriteria, pageRequest);
    List<RiciSimCard> simCards = allSimCardsNotLogicallyDeleted.getContent();
    try {
      String fileName = riciSimCardExportService.generateFilename();
      response.addHeader(HttpHeaders.CONTENT_DISPOSITION, String.format("attachment; filename=\"%s\"", fileName));
      response.setContentType("text/csv");
      riciSimCardExportService.generateSimCardCSV(response.getWriter(), simCards);
    } catch (Exception e) {
      throw new CGDISTechnicalException("Failed to export SIM card CSV.", e);
    }

  }


  /**
   * Endpoint to get SIM cards that are available ('DISPONIBLE') and not actively associated with a pager.
   * Used for populating dropdowns for pager assignment.
   *
   * @return A list of assignable RiciSimCard objects.
   */
  @GetMapping("all/assignable")
  @LogMilestone(action = "get assignable sim cards")
  public ResponseEntity<List<RiciSimCard>> getAssignableSimCards() {
    CGDISSecurityConstraint securityConstraint = new CGDISSecurityConstraint(Permission.ROLE_PERMISSION_RICI_SIM_CARD_VIEW);
    List<RiciSimCard> assignableSimCards = simCardROBusinessService.getAssignableSimCards(securityConstraint);
    return ResponseEntity.ok(assignableSimCards);
  }

  /**
   * Endpoint to get SIM cards that are available ('DISPONIBLE') and not actively associated with a pager (except requesting pager).
   * Used for populating dropdowns for pager assignment.
   *
   * @return A list of assignable RiciSimCard objects.
   */
  @GetMapping("all/assignable/{pagerTecid}")
  @LogMilestone(action = "get assignable sim cards")
  public ResponseEntity<List<RiciSimCard>> getAssignableSimCards(@LoggableValue @PathVariable Long pagerTecid) throws NotFoundException {
    CGDISSecurityConstraint securityConstraint = new CGDISSecurityConstraint(Permission.ROLE_PERMISSION_RICI_SIM_CARD_VIEW);
    List<RiciSimCard> assignableSimCards = simCardROBusinessService.getAssignableSimCardsForPager(securityConstraint, pagerTecid);
    return ResponseEntity.ok(assignableSimCards);
  }


  @GetMapping("{iccid}")
  @LogMilestone(action = "get sim card by iccid")
  public ResponseEntity<RiciSimCard> getSimCardByIccid(@LoggableValue @PathVariable String iccid) throws NotFoundException {
    RiciSimCard simCard = simCardROBusinessService.getByIccid(new CGDISSecurityConstraint(Permission.ROLE_PERMISSION_RICI_SIM_CARD_VIEW), iccid);
    return ResponseEntity.ok(simCard);
  }

  @PostMapping
  @LogMilestone(action = "create sim card")
  @AuditLog(auditType = AuditType.RICI_SIM_CARD, actionType = AuditActionType.CREATE)
  public ResponseEntity<RiciSimCard> createSimCard(@RequestBody @Valid @AuditLogParameter(name = "form") RiciSimCardForm form) {
    RiciSimCard createdSimCard = simCardWriteBusinessService.createSimCard(new CGDISSecurityConstraint(Permission.ROLE_PERMISSION_RICI_SIM_CARD_CREATE), form.create());
    return ResponseEntity.status(HttpStatus.CREATED).body(createdSimCard);
  }

  @PatchMapping("{tecid}")
  @LogMilestone(action = "update sim card")
  @AuditLog(auditType = AuditType.RICI_SIM_CARD, actionType = AuditActionType.UPDATE)
  public ResponseEntity<RiciSimCard> updateSimCard(@PathVariable @AuditLogParameter(name = "tecid") Long tecid, @RequestBody @Valid @AuditLogParameter(name = "form") RiciSimCardPatchForm form) {
    try {
      form.setTecid(tecid);
      RiciSimCard updatedSimCard = simCardWriteBusinessService.updateSimCard(new CGDISSecurityConstraint(Permission.ROLE_PERMISSION_RICI_SIM_CARD_UPDATE), form);
      return ResponseEntity.ok(updatedSimCard);
    } catch (NotFoundException e) {
      return ResponseEntity.notFound().build();
    }
  }

  @DeleteMapping("{tecid}")
  @LogMilestone(action = "delete sim card")
  @AuditLog(auditType = AuditType.RICI_SIM_CARD, actionType = AuditActionType.DELETE)
  public ResponseEntity<Void> deleteSimCard(@PathVariable @AuditLogParameter(name = "tecid") Long tecid) {
    log.debug("Attempting to delete SIM card with tecid: {}", tecid);

    try {
      // Call the business service to handle the deletion logic
      simCardWriteBusinessService.deleteSimCard(
        new CGDISSecurityConstraint(Permission.ROLE_PERMISSION_RICI_SIM_CARD_DELETE),
        tecid
      );

      log.info("Successfully deleted SIM card with tecid: {}", tecid);
      return ResponseEntity.noContent().build(); // 204 No Content
    } catch (NotFoundException e) {
      log.warn("SIM card not found with tecid: {}", tecid);
      return ResponseEntity.notFound().build(); // 404 Not Found
    } catch (Exception e) {
      log.error("Failed to delete SIM card with tecid: {}. Error: {}", tecid, e.getMessage(), e);
      throw new CGDISTechnicalException("Failed to delete SIM card with tecid: " + tecid, e);
    }
  }

  @GetMapping("{tecid}/has-associations")
  @LogMilestone(action = "check sim card associations")
  public ResponseEntity<Boolean> hasAssociations(@LoggableValue @PathVariable long tecid) {
    log.debug("Checking if SIM card with tecid: {} has ever been associated with a pager", tecid);

    try {
      // Get the SIM card first to check if it exists
      RiciSimCard simCard = simCardROBusinessService.get(
        new CGDISSecurityConstraint(Permission.ROLE_PERMISSION_RICI_SIM_CARD_VIEW),
        tecid
      );

      if (simCard == null) {
        return ResponseEntity.notFound().build();
      }

      // Get all pager associations for this SIM card
      List<RiciSimCardPager> simCardPagers = simCardPagerROBusinessService.getSimCardPagersBySimTecid(
        new CGDISSecurityConstraint(Permission.ROLE_PERMISSION_RICI_SIM_CARD_VIEW),
        tecid
      );

      boolean hasAssociations = !simCardPagers.isEmpty();
      return ResponseEntity.ok(hasAssociations);
    } catch (Exception e) {
      log.error("Error checking associations for SIM card with tecid: {}. Error: {}", tecid, e.getMessage(), e);
      throw new CGDISTechnicalException("Error checking associations for SIM card with tecid: " + tecid, e);
    }
  }

  @PatchMapping("{tecid}/deactivate")
  @LogMilestone(action = "deactivate sim card")
  @AuditLog(auditType = AuditType.RICI_SIM_CARD, actionType = AuditActionType.DEACTIVATE)
  public ResponseEntity<RiciSimCard> deactivateSimCard(@PathVariable @AuditLogParameter(name = "tecid") Long tecid) {
    log.debug("Deactivating SIM card with tecid: {}", tecid);
    try {
      RiciSimCard deactivatedSimCard = simCardWriteBusinessService.deactivateSimCard(
        new CGDISSecurityConstraint(Permission.ROLE_PERMISSION_RICI_SIM_CARD_UPDATE),
        tecid
      );
      return ResponseEntity.ok(deactivatedSimCard);
    } catch (NotFoundException e) {
      log.warn("SIM card not found with tecid: {} for deactivation", tecid);
      throw new CGDISTechnicalException("SIM card not found with tecid: " + tecid + " for deactivation", e);
    } catch (Exception e) {
      log.error("Failed to deactivate SIM card with tecid: {}. Error: {}", tecid, e.getMessage(), e);
      throw new CGDISTechnicalException("Failed to deactivate SIM card with tecid: " + tecid, e);
    }
  }

  @PatchMapping("{tecid}/reactivate")
  @LogMilestone(action = "reactivate sim card")
  @AuditLog(auditType = AuditType.RICI_SIM_CARD, actionType = AuditActionType.REACTIVATE)
  public ResponseEntity<RiciSimCard> reactivateSimCard(@PathVariable @AuditLogParameter(name = "tecid") Long tecid) {
    log.debug("Reactivating SIM card with tecid: {}", tecid);
    try {
      RiciSimCard reactivatedSimCard = simCardWriteBusinessService.reactivateSimCard(
        new CGDISSecurityConstraint(Permission.ROLE_PERMISSION_RICI_SIM_CARD_UPDATE),
        tecid
      );
      return ResponseEntity.ok(reactivatedSimCard);
    } catch (Exception e) {
      log.error("Failed to reactivate SIM card with tecid: {}. Error: {}", tecid, e.getMessage(), e);
      throw new CGDISTechnicalException("Failed to reactivate SIM card with tecid: " + tecid, e);
    }
  }

  @PostMapping("{simCardTecid}/associate/{associatedPagerTecid}")
  @LogMilestone(action = "associate sim card to pager")
  @AuditLog(auditType = AuditType.RICI_SIM_CARD, actionType = AuditActionType.ASSOCIATE)
  public ResponseEntity<RiciSimCardPager> associateSimCardWithPager(
    @PathVariable @AuditLogParameter(name = "simCardTecid") Long simCardTecid,
    @PathVariable @AuditLogParameter(name = "associatedPagerTecid") Long associatedPagerTecid) {
    log.debug("Associate SIM card having tecid: {} with pager having tecid: {}", simCardTecid, associatedPagerTecid);
    try {
      CGDISSecurityConstraint simCardUpdateSecurityConstraint = new CGDISSecurityConstraint(Permission.ROLE_PERMISSION_RICI_SIM_CARD_UPDATE);
      // Use the common method from the business service
      RiciSimCardPager simCardPager = riciSimCardPagerWriteBusinessService.associateActiveSimCardWithPagerByIds(
        simCardUpdateSecurityConstraint,
        simCardTecid,
        associatedPagerTecid
      );
      return ResponseEntity.ok(simCardPager);
    } catch (NotFoundException e) {
      log.warn("SIM card or pager not found for association: {}", e.getMessage());
      throw new CGDISTechnicalException("SIM card or pager not found for association. ", e);
    } catch (Exception e) {
      log.error("Failed to associate SIM card with pager. Error: {}", e.getMessage(), e);
      throw new CGDISTechnicalException("Failed to associate SIM card with pager. ", e);
    }
  }

  @GetMapping("{tecid}/pagers/active")
  @LogMilestone(action = "get pager associations")
  public ResponseEntity<RiciSimCardPager> getActivePagerAssociation(@LoggableValue @PathVariable long tecid) {
    try {
      RiciSimCardPager simCardPager = simCardPagerROBusinessService.getActiveSimCardPagerBySimTecid(
        new CGDISSecurityConstraint(Permission.ROLE_PERMISSION_RICI_SIM_CARD_VIEW), // Adjust security constraint as needed
        tecid
      );
      return ResponseEntity.ok(simCardPager); // 200 OK with the result (empty if no active pager)
    } catch (IllegalStateException e) {
      throw new CGDISTechnicalException("Failed to get pager association. ", e);
    }
  }

  @GetMapping("{tecid}/pagers/last")
  @LogMilestone(action = "get pager associations")
  public ResponseEntity<RiciSimCardPager> getLastPagerAssociation(@LoggableValue @PathVariable long tecid) {
    try {
      List<RiciSimCardPager> simCardPagers = simCardPagerROBusinessService.getSimCardPagersBySimTecid(
        new CGDISSecurityConstraint(Permission.ROLE_PERMISSION_RICI_SIM_CARD_VIEW), // Adjust security constraint as needed
        tecid
      );
      // Find the most recently updated pager or return an empty object if none found
      // Sort by startDateTime in descending order (newest first)
      RiciSimCardPager result = simCardPagers.stream()
        .filter(riciSimCardPager -> riciSimCardPager.getStatus() != null && riciSimCardPager.getStartDateTime() != null)
        .min((p1, p2) -> p2.getStartDateTime().compareTo(p1.getStartDateTime()))
        .orElse(new RiciSimCardPager()); // Return empty object if no active pager is found

      return ResponseEntity.ok(result); // 200 OK with the result (empty if no active pager)
    } catch (IllegalStateException e) {
      throw new CGDISTechnicalException("Failed to get pager association. ", e);
    }
  }

  @PostMapping("/import-csv/{lang}")
  @LogMilestone(action = "import sim cards from csv")
  @AuditLog(auditType = AuditType.RICI_SIM_CARD_IMPORT, actionType = AuditActionType.IMPORT_CSV)
  public ResponseEntity<CsvImportReport> importSimCardsFromCsv(
          @RequestParam("file") MultipartFile file,
          @PathVariable String lang) {
    log.info("Received request to import SIM cards from CSV file: {}, language: {}", file.getOriginalFilename(), lang);

    CGDISSecurityConstraint createSimConstraint = new CGDISSecurityConstraint(Permission.ROLE_PERMISSION_RICI_SIM_CARD_CREATE);
    CGDISSecurityConstraint updateSimConstraint = new CGDISSecurityConstraint(Permission.ROLE_PERMISSION_RICI_SIM_CARD_UPDATE);
    CGDISSecurityConstraint viewPagerConstraint = new CGDISSecurityConstraint(Permission.ROLE_PERMISSION_RICI_PAGER_VIEW);
    CGDISSecurityConstraint updatePagerConstraint = new CGDISSecurityConstraint(Permission.ROLE_PERMISSION_RICI_PAGER_UPDATE);

    CsvImportReport report = riciSimCardCSVImportBusinessService.importSimCardsFromCsv(
            file,
            lang, // Pass language
            createSimConstraint,
            updateSimConstraint,
            viewPagerConstraint,
            updatePagerConstraint
    );

    log.info("SIM card import finished for file: {}. Success: {}, Validation Errors: {}, Import Errors: {}. Total duration: {}ms",
            report.getFileName(),
            report.getSuccessfullyImportedCount(),
            report.getValidationErrorCount(),
            report.getImportErrorCount(),
            report.getDurationMillis());

    boolean hasCriticalError = report.getLineInfos().stream()
      .anyMatch(lineInfo -> lineInfo.getValidatorInfo() != null &&
        lineInfo.getValidatorInfo().getLineNumber() == 0 &&
        !lineInfo.getValidatorInfo().isValid());

    if (hasCriticalError) {
      log.warn("CSV import for {} failed with a critical file-level error. Returning HTTP 400.", report.getFileName());
      return ResponseEntity.badRequest().body(report);
    }

    log.info("Returning HTTP 200 with import report for file: {}", report.getFileName());
    return ResponseEntity.ok(report);
  }

}
