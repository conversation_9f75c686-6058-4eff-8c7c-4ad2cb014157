package lu.fujitsu.ts.cgdis.portal.webapp.validator;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.regex.Pattern;

public class PagerMobileValidator implements ConstraintValidator<PagerMobile, String> {

  private static final Logger LOGGER = LoggerFactory.getLogger(PhoneNumberValidator.class);

  private static String PATTERN = "^[+][3][5][2][0-9]{12}$";
  private static Pattern PATTERN_BUILT = Pattern.compile(PATTERN, Pattern.CASE_INSENSITIVE);

  @Override
  public void initialize(PagerMobile pager) {}

  @Override
  public boolean isValid(String pager, ConstraintValidatorContext constraintValidatorContext) {
    LOGGER.debug("check if pager mobile value {} is valid", pager);

    // null/empty values are valid
    boolean isValid = true;

    if (pager != null && pager.length() != 0){
      isValid = PATTERN_BUILT.matcher(pager).find();
    }

    LOGGER.debug("pager mobile value {} is valid: {}", pager, isValid);
    return isValid;
  }
}
