package lu.fujitsu.ts.cgdis.portal.webapp.aop.audit.aspect.timeslot;

import lu.fujitsu.ts.cgdis.portal.core.domain.audit.AuditActionType;
import lu.fujitsu.ts.cgdis.portal.core.domain.audit.AuditTimeSlot;
import lu.fujitsu.ts.cgdis.portal.core.domain.serviceplan.ServicePlan;
import lu.fujitsu.ts.cgdis.portal.core.domain.serviceplan.ServicePlanNoJoin;
import lu.fujitsu.ts.cgdis.portal.core.domain.serviceplan.timeslot.ServicePlanTimeSlot;
import lu.fujitsu.ts.cgdis.portal.core.service.serviceplan.IServicePlanBusinessService;
import lu.fujitsu.ts.cgdis.portal.core.service.serviceplan.IServicePlanNoJoinBusinessService;
import lu.fujitsu.ts.cgdis.portal.core.service.serviceplan.IServicePlanVersionTimeSlotBusinessService;
import lu.fujitsu.ts.cgdis.portal.core.utils.ServicePlanTimeSlotUtils;
import lu.fujitsu.ts.cgdis.portal.webapp.controllers.form.serviceplan.timeslot.SplitServicePlanTimeSlotForm;
import lu.fujitsu.ts.eportal.server.core.exceptions.NotFoundException;
import org.aspectj.lang.ProceedingJoinPoint;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.HashSet;
import java.util.Map;

/**
 * The type Timeslot split audit aspect.
 */
@Component
public class TimeslotSplitAuditAspect extends AbstractTimeslotAuditAspect< Object> {

  /**
   * The constant LOGGER.
   */
  private static final Logger LOGGER = LoggerFactory.getLogger(TimeslotMergeAuditAspect.class);

  /**
   * Instantiates a new Prestation delete audit aspect.
   */
  public TimeslotSplitAuditAspect() {
    super( AuditActionType.SPLIT, AuditTimeSlot.class);
  }

  /**
   * The Prestation business service.
   */
  @Autowired
  private IServicePlanVersionTimeSlotBusinessService timeSlotBusinessService;

  @Autowired
  private IServicePlanNoJoinBusinessService servicePlanBusinessService;

  @Override
  protected void populateAuditAfterProcessingMethod(ProceedingJoinPoint point, Method method, AuditTimeSlot audit, Map<String, Object> parameters, Object result) {

  }

  @Override
  protected void populateAuditBeforeProcessingMethod(ProceedingJoinPoint point, Method method, AuditTimeSlot audit, Map<String, Object> parameters) {
    SplitServicePlanTimeSlotForm form = (SplitServicePlanTimeSlotForm) parameters.get("form");
    ServicePlanTimeSlot slot = null;
    ServicePlanNoJoin plan = null;
    if (form !=null) {
      try {
        plan = servicePlanBusinessService.getByTimeSlotId(getUserDetails(), form.getSlotTecid());
        audit.setServicePlans(new HashSet<>(Collections.singletonList(plan)));
        slot = timeSlotBusinessService.get(getUserDetails(), form.getSlotTecid());
        mapTimeSlot(audit, slot, form.getDate());
        LocalDateTime targetDateTime = form.getDate().atTime(form.getSplitTime());
        if (!ServicePlanTimeSlotUtils.isSlotCurrentTo(form.getDate(), slot, targetDateTime)){
          targetDateTime = targetDateTime.plusDays(1);
        }
//          ServicePlanTimeSlotUtils.getDateTimeForSlotAtTime(form.getDate(), slot, form.getSplitTime());
        audit.setTargetDatetime(targetDateTime);
      } catch (NotFoundException e) {
        LOGGER.debug("Error to find prestation with id " + form.getSlotTecid(), e);
      }
    }
  }

  @Override
  protected boolean mustCreateAuditLog(AuditTimeSlot audit, Map<String, Object> parameters, Object result) {
    return true;
  }

}
