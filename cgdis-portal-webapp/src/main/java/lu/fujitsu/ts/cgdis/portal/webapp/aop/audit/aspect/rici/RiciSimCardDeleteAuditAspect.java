package lu.fujitsu.ts.cgdis.portal.webapp.aop.audit.aspect.rici;

import lu.fujitsu.ts.cgdis.portal.core.domain.audit.AuditActionType;
import lu.fujitsu.ts.cgdis.portal.core.domain.CGDISSecurityConstraint;
import lu.fujitsu.ts.cgdis.portal.core.domain.audit.AuditActionType;
import lu.fujitsu.ts.cgdis.portal.core.domain.audit.AuditRiciSimCard;
import lu.fujitsu.ts.cgdis.portal.core.domain.rici.RiciSimCard;
import lu.fujitsu.ts.cgdis.portal.core.domain.rici.RiciSimCardPager;
import lu.fujitsu.ts.cgdis.portal.core.security.Permission;
import lu.fujitsu.ts.cgdis.portal.core.service.rici.IRiciSimCardPagerROBusinessService;
import lu.fujitsu.ts.cgdis.portal.core.service.rici.IRiciSimCardROBusinessService;
import lu.fujitsu.ts.eportal.server.core.exceptions.NotFoundException;
import org.aspectj.lang.ProceedingJoinPoint;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.Map;

/**
 * Aspect for auditing RICI SIM card delete operations.
 */
@Component
public class RiciSimCardDeleteAuditAspect extends AbstractRiciSimCardAuditAspect<ResponseEntity<Void>,Void> {

  private static final Logger LOGGER = LoggerFactory.getLogger(RiciSimCardDeleteAuditAspect.class);

  private final IRiciSimCardROBusinessService simCardROBusinessService;
  private final IRiciSimCardPagerROBusinessService simCardPagerROBusinessService;


  /**
   * Constructs a new RiciSimCardDeleteAuditAspect.
   * @param simCardROBusinessService Service to fetch SIM Card details.
   * @param simCardPagerROBusinessService Service to fetch SIM Card Pager association details.
   */
  @Autowired
  public RiciSimCardDeleteAuditAspect(IRiciSimCardROBusinessService simCardROBusinessService,
                                      IRiciSimCardPagerROBusinessService simCardPagerROBusinessService) {
    super(AuditActionType.DELETE, AuditRiciSimCard.class);
    this.simCardROBusinessService = simCardROBusinessService;
    this.simCardPagerROBusinessService = simCardPagerROBusinessService;
  }

  @Override
  protected void populateAuditAfterProcessingMethod(ProceedingJoinPoint point, Method method, AuditRiciSimCard audit, Map<String, Object> parameters, ResponseEntity<Void> result) {
    // No additional information to populate after deletion
  }

  @Override
  protected void populateAuditBeforeProcessingMethod(ProceedingJoinPoint point, Method method, AuditRiciSimCard audit, Map<String, Object> parameters) {
    Long tecid = (Long) parameters.get("tecid");
    audit.setSimCardTecid(tecid);

    try {
      // Fetch the SIM card details before deletion to include in the audit
      RiciSimCard simCard = simCardROBusinessService.get(new CGDISSecurityConstraint(Permission.ROLE_PERMISSION_RICI_SIM_CARD_VIEW), tecid);
      if (simCard != null) {
        audit.setIccid(simCard.getIccid());
        audit.setMsisdn(simCard.getMsisdn());
        audit.setPin(simCard.getPin());
        audit.setStatus(simCard.getStatus());

        // Fetch active pager association to log pager details
        try {
          RiciSimCardPager activeAssociation = simCardPagerROBusinessService.getActiveSimCardPagerBySimTecid(
              new CGDISSecurityConstraint(Permission.ROLE_PERMISSION_RICI_SIM_CARD_VIEW), // Or a more specific audit permission
              tecid
          );
          if (activeAssociation != null && activeAssociation.getPager() != null) {
            audit.setAssociatedPagerTecid(activeAssociation.getPager().getTecid());
            audit.setAssociatedPagerId(activeAssociation.getPager().getPagerId());
          }
        } catch (Exception ex) {
          LOGGER.warn("Could not fetch active pager association for SIM card tecid {} during delete audit: {}", tecid, ex.getMessage());
        }
      }
    } catch (NotFoundException e) {
        LOGGER.warn("SIM card not found by tecid {} during audit population for delete. SIM details will not be set in audit.", tecid, e);
    } catch (Exception e) {
      LOGGER.warn("Could not fetch SIM card details for audit before deletion: {}", e.getMessage());
    }
  }

  @Override
  protected boolean mustCreateAuditLog(AuditRiciSimCard audit, Map<String, Object> parameters, ResponseEntity<Void> result) {
    // Only create audit log if deletion was successful (HTTP 204 No Content)
    return result != null && result.getStatusCode().is2xxSuccessful();
  }

  @Override
  protected Long getTecidFromResult(ResponseEntity<Void> result) {
    // For deletion, we already have the tecid from the parameters
    return null;
  }
}
