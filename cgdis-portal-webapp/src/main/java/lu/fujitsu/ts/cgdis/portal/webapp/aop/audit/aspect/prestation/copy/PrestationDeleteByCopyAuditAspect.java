package lu.fujitsu.ts.cgdis.portal.webapp.aop.audit.aspect.prestation.copy;

import lu.fujitsu.ts.cgdis.portal.core.domain.audit.AuditActionType;
import lu.fujitsu.ts.cgdis.portal.core.domain.audit.AuditPrestation;
import lu.fujitsu.ts.cgdis.portal.core.domain.serviceplan.prestation.Prestation;
import lu.fujitsu.ts.cgdis.portal.core.pojo.prestation.copy.PrestationCopyResultDto;
import lu.fujitsu.ts.cgdis.portal.core.utils.DateTimeUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * The type Prestation delete audit aspect.
 */
@Component
public class PrestationDeleteByCopyAuditAspect extends AbstractPrestationByCopyAuditAspect<Prestation> {

  private static final Logger LOGGER = LoggerFactory.getLogger(PrestationDeleteByCopyAuditAspect.class);


  /**
   * Instantiates a new Prestation delete audit aspect.
   */
  public PrestationDeleteByCopyAuditAspect() {
    super( AuditActionType.COPY_DELETE, AuditPrestation.class);
  }

  @Override
  protected void populateAuditAfterProcessingMethod(ProceedingJoinPoint point, Method method,  AuditPrestation audit, Map<String, Object> parameters, Prestation prestation) {
    if (prestation != null ){
        mapPrestation(audit, prestation);
      }

  }

  @Override
  public Object extractValueFromMethodResult(PrestationCopyResultDto methodResult) {
    return methodResult == null ? null : methodResult.getAllClosedPrestations();
  }

  @Override
  protected void populateAuditBeforeProcessingMethod(ProceedingJoinPoint point, Method method,  AuditPrestation audit, Map<String, Object> parameters) {

  }

  @Override
  protected boolean mustCreateAuditLog(AuditPrestation audit, Map<String, Object> parameters, Prestation prestation) {
    if (prestation == null ){
      return false;
    }
    return DateTimeUtils.isAfterOrEquals(prestation.getStartDateTime(), LocalDateTime.now());

  }
}
