package lu.fujitsu.ts.cgdis.portal.webapp.aop.audit.aspect;

import lu.fujitsu.ts.cgdis.portal.core.domain.audit.Audit;
import lu.fujitsu.ts.cgdis.portal.core.domain.audit.AuditActionType;
import lu.fujitsu.ts.cgdis.portal.core.domain.audit.AuditType;
import lu.fujitsu.ts.cgdis.portal.webapp.aop.audit.annotation.AuditLog;
import org.aspectj.lang.ProceedingJoinPoint;

import java.lang.reflect.Method;

/**
 * The interface Audit aspect.
 *
 * @param <A> the type parameter Audit
 * @param <R> the type parameter Result
 */
public interface IAuditAspect<A extends Audit, R, M> {

  /**
   * Gets audit type.
   *
   * @return the audit type
   */
  AuditType getAuditType();

  /**
   * Gets audit action type.
   *
   * @return the audit action type
   */
  AuditActionType getAuditActionType();

  /**
   * Gets audit class.
   *
   * @return the audit class
   */
  Class<A> getAuditClass();

  /**
   * Execute audit object.
   *
   * @param point    the point
   * @param method   the method
   * @param auditLog the audit log
   * @return the object
   * @throws Throwable the throwable
   */
  Object executeAudit(ProceedingJoinPoint point, Method method, AuditLog auditLog) throws Throwable;


  /**
   * Execute audit before proceed method audit aspect execution.
   *
   * @param point    the point
   * @param method   the method
   * @param auditLog the audit log
   * @return the audit aspect execution
   */
  AuditAspectExecution<A> executeAuditBeforeProceedMethod(ProceedingJoinPoint point, Method method, AuditLog auditLog);

  /**
   * Execute audit after proceed method.
   *
   * @param point          the point
   * @param method         the method
   * @param auditLog       the audit log
   * @param auditExecution the audit execution
   * @param result         the result
   */
  void executeAuditAfterProceedMethod(ProceedingJoinPoint point, Method method, AuditLog auditLog, AuditAspectExecution<A> auditExecution, R result);


  /**
   * Extracts a value from the method result.
   *
   * @param methodResult the result of the method execution
   * @return the extracted value type R or Collection<R>
   */
  Object extractValueFromMethodResult(M methodResult);
}
