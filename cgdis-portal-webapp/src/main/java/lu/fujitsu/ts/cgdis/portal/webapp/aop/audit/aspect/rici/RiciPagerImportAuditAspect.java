package lu.fujitsu.ts.cgdis.portal.webapp.aop.audit.aspect.rici;

import lu.fujitsu.ts.cgdis.portal.core.domain.audit.AuditActionType;
import lu.fujitsu.ts.cgdis.portal.core.domain.audit.AuditRiciPagerImport; // Use the new dedicated import audit model
import lu.fujitsu.ts.cgdis.portal.core.domain.audit.AuditType; // Import AuditType
import lu.fujitsu.ts.cgdis.portal.core.domain.csv_import.CsvImportReport;
import lu.fujitsu.ts.cgdis.portal.webapp.aop.audit.aspect.AbstractAuditAspect; // Extend AbstractAuditAspect directly
import org.aspectj.lang.ProceedingJoinPoint;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.lang.reflect.Method;
import java.util.Map;

/**
 * Aspect for auditing RICI Pager import operations.
 */
@Component
public class RiciPagerImportAuditAspect extends AbstractAuditAspect<AuditRiciPagerImport, ResponseEntity<CsvImportReport>, ResponseEntity<CsvImportReport>> {

  private static final Logger LOGGER = LoggerFactory.getLogger(RiciPagerImportAuditAspect.class);

  /**
   * Constructs a new RiciPagerImportAuditAspect.
   */
  public RiciPagerImportAuditAspect() {
    super(AuditType.RICI_PAGER_IMPORT, AuditActionType.IMPORT_CSV, AuditRiciPagerImport.class);
  }

  @Override
  protected void populateAuditAfterProcessingMethod(ProceedingJoinPoint point, Method method, AuditRiciPagerImport audit, Map<String, Object> parameters, ResponseEntity<CsvImportReport> result) {
    if (result != null && result.getBody() != null) {
      CsvImportReport report = result.getBody();
      audit.setFileName(report.getFileName());
      audit.setTotalRecords(report.getTotalRecordsInFile());
      audit.setSuccessfulRecords(report.getSuccessfullyImportedCount());
      audit.setValidationErrors(report.getValidationErrorCount());
      audit.setImportErrors(report.getImportErrorCount());

      // Set overall status based on report outcome
      if (report.getValidationErrorCount() > 0 || report.getImportErrorCount() > 0) {
        audit.setImportStatus("COMPLETED_WITH_ERRORS");
      } else {
        audit.setImportStatus("COMPLETED_SUCCESS");
      }
    } else {
      // If result or body is null, it indicates a critical failure before report generation
      audit.setImportStatus("FAILED_FILE_PROCESSING");
      // Try to get filename from parameters if result is null
      Object fileParam = parameters.get("file");
      if (fileParam instanceof MultipartFile) {
        audit.setFileName(((MultipartFile) fileParam).getOriginalFilename());
      } else {
        audit.setFileName("Unknown file");
      }
      audit.setTotalRecords(0);
      audit.setSuccessfulRecords(0);
      audit.setValidationErrors(0);
      audit.setImportErrors(0);
    }
  }

  @Override
  protected void populateAuditBeforeProcessingMethod(ProceedingJoinPoint point, Method method, AuditRiciPagerImport audit, Map<String, Object> parameters) {
    Object fileParam = parameters.get("file");
    if (fileParam instanceof MultipartFile) {
      MultipartFile file = (MultipartFile) fileParam;
      audit.setFileName(file.getOriginalFilename());
      audit.setImportStatus("INITIATED");
    } else {
      audit.setFileName("Unknown file");
      audit.setImportStatus("INITIATED (file info unavailable)");
    }
    // Initialize counts to null or zero before processing
    audit.setTotalRecords(null);
    audit.setSuccessfulRecords(null);
    audit.setValidationErrors(null);
    audit.setImportErrors(null);
  }

  @Override
  protected boolean mustCreateAuditLog(AuditRiciPagerImport audit, Map<String, Object> parameters, ResponseEntity<CsvImportReport> result) {
    // Always create an audit log for the import attempt if the controller method was invoked and returned a response.
    return result != null && result.getStatusCode().is2xxSuccessful();
  }
}
