package lu.fujitsu.ts.cgdis.portal.webapp.aop.audit.aspect.rici;

import lu.fujitsu.ts.cgdis.portal.core.domain.audit.AuditActionType;
import lu.fujitsu.ts.cgdis.portal.core.domain.CGDISSecurityConstraint;
import lu.fujitsu.ts.cgdis.portal.core.domain.audit.AuditActionType;
import lu.fujitsu.ts.cgdis.portal.core.domain.audit.AuditRiciSimCard;
import lu.fujitsu.ts.cgdis.portal.core.domain.rici.RiciPager;
import lu.fujitsu.ts.cgdis.portal.core.domain.rici.RiciSimCard;
import lu.fujitsu.ts.cgdis.portal.core.domain.rici.RiciSimCardPager;
import lu.fujitsu.ts.cgdis.portal.core.security.Permission;
import lu.fujitsu.ts.cgdis.portal.core.service.rici.IRiciSimCardPagerROBusinessService;
import lu.fujitsu.ts.cgdis.portal.webapp.controllers.form.rici.RiciSimCardPatchForm;
import lu.fujitsu.ts.eportal.server.core.exceptions.NotFoundException;
import org.aspectj.lang.ProceedingJoinPoint;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.Map;

/**
 * Aspect for auditing RICI SIM card update operations.
 */
@Component
public class RiciSimCardUpdateAuditAspect extends AbstractRiciSimCardAuditAspect<ResponseEntity<RiciSimCard>, ResponseEntity<RiciSimCard>> {

  private static final Logger LOGGER = LoggerFactory.getLogger(RiciSimCardUpdateAuditAspect.class);

  private final IRiciSimCardPagerROBusinessService simCardPagerROBusinessService;

  /**
   * Constructs a new RiciSimCardUpdateAuditAspect.
   * @param simCardPagerROBusinessService Service to fetch SIM Card Pager association details.
   */
  @Autowired
  public RiciSimCardUpdateAuditAspect(IRiciSimCardPagerROBusinessService simCardPagerROBusinessService) {
    super(AuditActionType.UPDATE, AuditRiciSimCard.class);
    this.simCardPagerROBusinessService = simCardPagerROBusinessService;
  }

  @Override
  protected void populateAuditAfterProcessingMethod(ProceedingJoinPoint point, Method method, AuditRiciSimCard audit, Map<String, Object> parameters, ResponseEntity<RiciSimCard> result) {
    if (result != null && result.getBody() != null) {
      RiciSimCard simCard = result.getBody();
      audit.setSimCardTecid(simCard.getTecid());
      audit.setIccid(simCard.getIccid());
      audit.setMsisdn(simCard.getMsisdn());
      audit.setPin(simCard.getPin());
      audit.setStatus(simCard.getStatus());

      // If SIM card is associated, fetch and log pager details
      // Note: Status might reflect the new state. If it's no longer associated, this won't fetch.
      // This is generally correct as we audit the state *after* the update.
      try {
        RiciSimCardPager activeAssociation = simCardPagerROBusinessService.getActiveSimCardPagerBySimTecid(
            new CGDISSecurityConstraint(Permission.ROLE_PERMISSION_RICI_SIM_CARD_VIEW), // Or a more specific audit permission
            simCard.getTecid()
        );
        if (activeAssociation != null && activeAssociation.getPager() != null) {
          audit.setAssociatedPagerTecid(activeAssociation.getPager().getTecid());
          audit.setAssociatedPagerId(activeAssociation.getPager().getPagerId());
        }
      } catch (Exception ex) {
        LOGGER.warn("Could not fetch active pager association for SIM card tecid {} during update audit: {}", simCard.getTecid(), ex.getMessage());
      }
    }
  }

  @Override
  protected void populateAuditBeforeProcessingMethod(ProceedingJoinPoint point, Method method, AuditRiciSimCard audit, Map<String, Object> parameters) {
    RiciSimCardPatchForm form = (RiciSimCardPatchForm) parameters.get("form");
    audit.setSimCardTecid(form.getTecid());

    // Only include fields that are being updated
    if (form.getIccid() != null) {
      audit.setIccid(form.getIccid());
    }
    if (form.getMsisdn() != null) {
      audit.setMsisdn(form.getMsisdn());
    }
    if (form.getPin() != null) {
      audit.setPin(form.getPin());
    }
    if (form.getStatus() != null) {
      audit.setStatus(form.getStatus());
    }
  }

  @Override
  protected boolean mustCreateAuditLog(AuditRiciSimCard audit, Map<String, Object> parameters, ResponseEntity<RiciSimCard> result) {
    return result != null && result.getStatusCode().is2xxSuccessful() && result.getBody() != null;
  }

  @Override
  protected Long getTecidFromResult(ResponseEntity<RiciSimCard> result) {
    return result != null && result.getBody() != null ? result.getBody().getTecid() : null;
  }
}
