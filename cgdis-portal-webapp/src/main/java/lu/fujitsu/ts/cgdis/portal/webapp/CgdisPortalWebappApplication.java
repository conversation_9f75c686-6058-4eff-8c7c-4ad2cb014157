package lu.fujitsu.ts.cgdis.portal.webapp;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;


@SpringBootApplication
@EnableAutoConfiguration
public class CgdisPortalWebappApplication extends SpringBootServletInitializer {
  private static final Logger LOGGER = LoggerFactory.getLogger(CgdisPortalWebappApplication.class);

  public static void main(String[] args) {
    SpringApplication.run(CgdisPortalWebappApplication.class, args);
  }

  @Override
  protected SpringApplicationBuilder configure(SpringApplicationBuilder builder) {
    return builder.sources(CgdisPortalWebappApplication.class);
  }

}
