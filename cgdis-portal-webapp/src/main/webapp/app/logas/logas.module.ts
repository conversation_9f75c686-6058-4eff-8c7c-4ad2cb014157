import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SharedModule } from '@app/common/shared/shared.module';
import { FormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { LOGAS_ROUTE } from '@app/logas/logas.route';
import { LogasComponent } from '@app/logas/logas.component';
import { RoutingService } from '@app/routing/routing.service';
import { PageTemplateModule } from '@app/common/template/page-template/page-template.module';
import { NgxSelectModule } from 'ngx-select-ex';
import { FormModule } from '@app/common/modules/form-module/form.module';
import { ToastrModule } from 'ngx-toastr';

@NgModule({
  imports: [
    CommonModule,
    SharedModule,
    ToastrModule,
    FormsModule,
    RouterModule.forChild(LOGAS_ROUTE),
    PageTemplateModule,
    NgxSelectModule,
    FormModule,
  ],
  declarations: [LogasComponent],
  providers: [RoutingService],
})
export class LogasModule {}
