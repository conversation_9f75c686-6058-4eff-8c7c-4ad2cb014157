import { Routes } from '@angular/router';
import { HrfViewComponent } from '@permamonitor/permamonitor/views/hrf-view/hrf-view.component';

export const HRF_VIEW_ROUTES: Routes = [
  {
    path: '',
    component: HrfViewComponent,
    children: [
      { path: '', redirectTo: 'poj', pathMatch: 'full' },
      {
        path: 'table',
        loadComponent: () =>
          import('./hrf-view-table/hrf-view-table.component').then(
            (m) => m.HrfViewTableComponent,
          ),
      },
      {
        path: 'poj',
        loadComponent: () =>
          import('./hrf-view-poj/hrf-view-poj.component').then(
            (m) => m.HrfViewPojComponent,
          ),
      },
      {
        path: 'map',
        loadComponent: () =>
          import('./hrf-view-map/hrf-view-map.component').then(
            (m) => m.HrfViewMapComponent,
          ),
      },
      {
        path: 'live',
        loadComponent: () =>
          import('./hrf-view-live/hrf-view-live.component').then(
            (m) => m.HrfViewLiveComponent,
          ),
      },
      { path: '**', redirectTo: 'poj', pathMatch: 'full' },
    ],
  },
];
