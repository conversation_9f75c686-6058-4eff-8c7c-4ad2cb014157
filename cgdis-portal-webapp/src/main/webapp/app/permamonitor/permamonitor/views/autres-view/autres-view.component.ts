import {ChangeDetectionStrategy, Component} from '@angular/core';
import {MatTabNavPanel} from "@angular/material/tabs";
import {
  PermamonitorViewSelectorComponent
} from "@permamonitor/permamonitor/common/view-selector/view-selector.component";
import {RouterOutlet} from "@angular/router";

@Component({
  selector: 'cgdis-portal-autres-view',
  standalone: true,
  imports: [
    MatTabNavPanel,
    PermamonitorViewSelectorComponent,
    RouterOutlet
  ],
  templateUrl: './autres-view.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class AutresViewComponent {

}
