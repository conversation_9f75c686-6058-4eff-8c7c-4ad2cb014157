import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
} from '@angular/core';
import { PermamonitorViewBaseComponent } from '@permamonitor/permamonitor/views/view-base';
import { Router } from '@angular/router';
import { PermamonitorFiltersComponent } from '@permamonitor/permamonitor/common/filter/filters.component';
import { LiveViewComponent } from '@permamonitor/permamonitor/common/view-selector/live-view/live-view.component';

@Component({
  selector: 'cgdis-portal-incsa-view-live',
  standalone: true,
  imports: [PermamonitorFiltersComponent, LiveViewComponent],
  templateUrl: './incsa-view-live.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class IncsaViewLiveComponent extends PermamonitorViewBaseComponent {
  constructor(
    protected cd: ChangeDetectorRef,
    protected router: Router,
  ) {
    super(cd, router);
  }
}
