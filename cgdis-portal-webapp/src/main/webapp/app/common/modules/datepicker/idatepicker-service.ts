import { DateModel } from '@eportal/core';
import { DatepickerEvents } from './model/datepicker-events';

/**
 * Datepicker service definition
 */
export interface IDatepickerService {
  /**
   * Show the datepicker
   */
  show(): void;

  /**
   * hide the datepicker
   */
  hide(): void;

  /**
   * Return the selected date
   * @return {DateModel}
   */
  getSelectedDate(): DateModel;

  /**
   * Set new selected date
   * @param date
   * @param force force the update of the date
   */
  setSelectedDate(date: DateModel, force?: boolean): void;

  /**
   * Set new start date
   * @param {DateModel} date
   */
  setStartDate(date: DateModel): void;

  /**
   * Set new End date
   * @param {DateModel} date
   */
  setEndDate(date: DateModel): void;

  /**
   * Destroy the datepicker
   */
  destroy(): void;

  /**
   * Set events to datepicker
   * @param {DatepickerEvents} events
   */
  setEvents(events: DatepickerEvents): void;

  /**
   * Set datepicker language
   * @param {string} lang
   */
  setLanguage(lang: string): void;
}
