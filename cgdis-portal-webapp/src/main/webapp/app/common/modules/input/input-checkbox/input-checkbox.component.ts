import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  Input,
  Output,
} from '@angular/core';
import { NgClass } from '@angular/common';

@Component({
  selector: 'cgdis-portal-input-checkbox',
  standalone: true,
  imports: [NgClass],
  templateUrl: './input-checkbox.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class InputCheckboxComponent {
  @Input() checked: boolean = false;
  @Input() label: string = '';
  @Input() disabled: boolean = false;

  @Output() changed = new EventEmitter<boolean>();

  onToggle(): void {
    if (!this.disabled) {
      this.changed.emit(!this.checked);
    }
  }
}
