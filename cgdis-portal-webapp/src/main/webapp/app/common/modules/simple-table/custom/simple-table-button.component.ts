import {
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  Output,
  SimpleChanges,
} from '@angular/core';
import { ConnectedUserService } from '../../../../security/connected-user.service';
import { Subscription } from 'rxjs';

/**
 * This component prevent the click event to be propagated to the row.
 * Should be used by buttons components
 */
@Component({
  selector: 'cgdis-portal-simple-table-button',
  templateUrl: './simple-table-button.component.html',
  styles: [],
})
export class SimpleTableButtonComponent
  implements OnInit, OnDestroy, OnChanges
{
  constructor(private connectedUserService: ConnectedUserService) {}

  @Input() roles: string[];

  @Output() onclick = new EventEmitter();

  visible = true;

  private rolesSubscription: Subscription;

  ngOnInit() {
    this.roleUpdated();
  }

  ngOnDestroy(): void {
    if (this.rolesSubscription != undefined) {
      this.rolesSubscription.unsubscribe();
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.roles && !changes.roles.firstChange) {
      this.roleUpdated();
    }
  }

  roleUpdated() {
    if (this.roles != undefined && this.roles.length > 0) {
      if (this.rolesSubscription != undefined) {
        this.rolesSubscription.unsubscribe();
      }
      this.rolesSubscription = this.connectedUserService
        .hasAnyRolesObservable(this.roles)
        .subscribe((newValue) => {
          this.visible = newValue;
        });
    }
  }

  // @HostListener('click', ['$event'])
  // public onHostClick(event: any): void
  // {console.error('click button')
  //   event.preventDefault(); // prevent row click event
  //   event.stopPropagation();
  //   this.onclick.emit();
  // }

  clickDiv(event: any) {
    this.onclick.emit();
    event.preventDefault(); // prevent row click event
    event.stopPropagation();
  }
}
