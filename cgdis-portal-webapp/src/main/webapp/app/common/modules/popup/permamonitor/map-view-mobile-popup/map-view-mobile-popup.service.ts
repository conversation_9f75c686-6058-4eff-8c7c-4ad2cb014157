import { Injectable } from '@angular/core';
import { DatetimeModel, DatetimeService, RestService } from '@eportal/core';
import { Category } from '@permamonitor/permamonitor/common/constants';
import { PermScheduleWithCounterAndCriticityModel } from '@app/model/permamonitor/counters/perm-schedule-with-counter-and-criticity.model';

@Injectable()
export class MapViewMobilePopupService {
  constructor(
    private _restService: RestService,
    private _dateTimeService: DatetimeService,
  ) {}

  // http://localhost:9000/api/permamonitor/schedules/SAP/2023-12-18/3/counters
  getMapDetails(requestInput: {
    category: Category;
    dateTime: DatetimeModel;
    baseEntityTecid: number;
    isFavorite: boolean;
  }) {
    const params: Record<string, any> = {};

    if (requestInput.baseEntityTecid !== undefined) {
      params.baseEntityTecid = requestInput.baseEntityTecid;
    }

    if (requestInput.isFavorite) {
      params.favorite = requestInput.isFavorite;
    }
    return this._restService
      .one<PermScheduleWithCounterAndCriticityModel>(
        'permamonitor',
        'schedules',
        requestInput.category,
        this._dateTimeService.format(requestInput.dateTime, 'YYYY-MM-DD'),
        this._dateTimeService.format(requestInput.dateTime, 'HH'),
        'counters',
      )
      .get(params);
  }
}
