import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  Inject,
  Input,
  OnD<PERSON>roy,
  OnInit,
  Output,
} from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { SimplePopupDataModel } from '@app/common/modules/popup/simple-popup-data.model';

@Component({
  selector: 'cgdis-portal-image-view-popup',
  templateUrl: './image-view-popup.component.html',
  styles: [],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ImageViewPopupComponent implements OnInit, OnDestroy {
  @Input() imageSrc: string;

  @Output() onClose = new EventEmitter<void>();

  constructor(
    @Inject(MAT_DIALOG_DATA) public data: SimplePopupDataModel<any>,
    public dialogRef: MatDialogRef<ImageViewPopupComponent>,
  ) {}
  ngOnInit() {}
  ngOnDestroy(): void {}
  closePopup() {
    this.dialogRef.close();
  }
}
