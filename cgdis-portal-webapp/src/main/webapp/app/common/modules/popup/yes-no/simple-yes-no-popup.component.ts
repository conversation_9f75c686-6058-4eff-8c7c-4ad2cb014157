import { first } from 'rxjs/operators';
import { Component, HostListener, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { SimpleYesNoPopupData } from './simple-yes-no-popup-data';
import { KeyEventCode } from '../../../shared/directives/key-event-code';
import * as _ from 'lodash';

@Component({
  selector: 'cgdis-portal-simple-yes-no-popup',
  templateUrl: './simple-yes-no-popup.component.html',
})
export class SimpleYesNoPopupComponent implements OnInit {
  /**
   * Wait response from server
   * @type {boolean}
   */
  waiting = false;

  labelButtonYes = 'default.yes';
  labelButtonNo = 'default.no';

  private ongoingSubmit = false;

  constructor(
    @Inject(MAT_DIALOG_DATA) public data: SimpleYesNoPopupData,
    public dialogRef: MatDialogRef<SimpleYesNoPopupComponent>,
  ) {}

  ngOnInit(): void {
    if (this.data.labelButtonYes) {
      this.labelButtonYes = this.data.labelButtonYes;
    }
    if (this.data.labelButtonNo) {
      this.labelButtonNo = this.data.labelButtonNo;
    }
  }

  /**
   * Close dialog with 'yes' button
   */
  public yes(): void {
    if (_.isFunction(this.data.onYes)) {
      this.waiting = true;
      this.data
        .onYes()
        .pipe(first())
        .subscribe(
          () => {
            this.closeYes();
          },
          () => {
            this.stopWaiting();
          },
          () => {
            this.closeYes();
          },
        );
    } else {
      this.closeYes();
    }
  }

  private stopWaiting() {
    this.waiting = false;
  }

  /**
   * Close dialog with 'no' button
   */
  public no(): void {
    if (_.isFunction(this.data.onNo)) {
      this.waiting = true;
      this.data
        .onNo()
        .pipe(first())
        .subscribe(
          () => {
            this.closeNo();
          },
          () => {
            this.stopWaiting();
          },
          () => {
            this.closeNo();
          },
        );
    } else {
      this.closeNo();
    }
  }

  private closeYes(): void {
    this.stopWaiting();
    this.dialogRef.close(true);
  }

  private closeNo(): void {
    this.stopWaiting();
    this.dialogRef.close(false);
  }

  /**
   * Close dialog with 'x' button
   */
  public closePopup(): void {
    this.no();
  }

  /**
   * Yes triggered on enter keydown pressed
   * @param event
   */
  @HostListener('document:keydown', ['$event'])
  public handleKeyboardEvent(event: KeyboardEvent): void {
    if (!this.ongoingSubmit && event.keyCode === KeyEventCode.ENTER) {
      this.ongoingSubmit = true;
      this.yes();
    }
  }
}
