<div class="general-availability">
  <cgdis-portal-spinner [loading]="loading" [fullScreen]="true"></cgdis-portal-spinner>


  <cgdis-portal-general-availability-full-tabs-header *ngIf="!loading" class="general-availability-header" [label]="items.servicePlanLabel" [servicePlanName]="items.servicePlanName" [persons]="items.persons"></cgdis-portal-general-availability-full-tabs-header>

  <div class="scrollable-content_ga" [ngClass]="{'hiddenScrollBar': false }">
<!--    <ng-scrollbar [trackY]="showScrollbar" [trackX]="false" [thumbClass]="'scrollbar-thumb'" (scrollState)="scroll($event)" #scrollRef>-->
      <cgdis-portal-scroll [addtionalViewportClasses]="'general-availability-full-tabs__scroll__viewport'">
        <div class="general-availability-oneday" *ngFor="let oneDay of items.rows; index as $index">
          <cgdis-portal-general-availability-full-tabs-oneday [data]="oneDay" [persons]="items.persons"></cgdis-portal-general-availability-full-tabs-oneday>
        </div>
      </cgdis-portal-scroll>
  </div>
</div>

<cgdis-portal-legend *ngIf="!loading" [items]="legendItems" [legendClasses]="['-inline']"></cgdis-portal-legend>
