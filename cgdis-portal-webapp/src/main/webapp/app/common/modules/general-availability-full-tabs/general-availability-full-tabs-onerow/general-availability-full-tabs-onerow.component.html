<ul class="general-availability-onerow">
  <span class="general-availability-onerow__start"  eportal-time-format [theTime]="timeSlot.startTime" [timeFormat]="'HH:mm'"></span>
  <span class="general-availability-onerow__end"  eportal-time-format [theTime]="timeSlot.endTime" [timeFormat]="'HH:mm'"></span>
  <li class="general-availability-onerow-cellule" *ngFor="let cellule of cellules; index as $index"
    [ngClass]="{'barracked': cellule.isbarrcked,
                'VOLUNTEER': cellule.availability==='VOLUNTEER',
                'PROFESSIONAL': cellule.availability==='PROFESSIONAL'}">
    <div class="general-availability-onerow-cellule-icon">
      <cgdis-portal-icon *ngIf="cellule.isbarrcked" [icon]="'icon-barracked'"></cgdis-portal-icon>
    </div>
  </li>
</ul>
