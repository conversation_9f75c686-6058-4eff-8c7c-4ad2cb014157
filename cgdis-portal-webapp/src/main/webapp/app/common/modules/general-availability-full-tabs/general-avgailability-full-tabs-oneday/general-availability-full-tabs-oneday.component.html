<div class="general-availability-oneday">
  <span class="general-availability-oneday-date first" eportal-date-day-name [theDate]="data.date"></span>
  <span class="general-availability-oneday-date" eportal-date-format [theDate]="data.date" [dateFormat]="'DD/MM'"></span>
  <div *ngIf="data.slots!=null; else templateNoData">
    <div *ngFor="let row of data.slots; index as $index" >
      <cgdis-portal-general-availability-full-tabs-onerow class="general-availability-onerow" [row]="row" [persons]="persons"></cgdis-portal-general-availability-full-tabs-onerow>
    </div>
  </div>
</div>

<ng-template #templateNoData>
  <ul class="general-availability-onerow">
    <span class="general-availability-onerow-nodata" [translate]="'general_availability.no_data'"></span>
  </ul>
</ng-template>




