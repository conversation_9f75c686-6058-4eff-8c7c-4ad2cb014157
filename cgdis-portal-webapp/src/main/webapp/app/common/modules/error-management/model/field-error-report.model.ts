import { ErrorMessage } from "./error-message.model";

/**
 * All errors for  field
 */
export class FieldErrorReport {
  /**
   * Field name
   */
  field: string;

  /**
   * Rejected value
   */
  rejectedValue: any;

  /**
   * All errors for this field
   */
  errors: ErrorMessage[];

  constructor(args: FieldErrorReport) {
    this.field = args.field;
    this.rejectedValue = args.rejectedValue;
    this.errors = args.errors;
  }
}
