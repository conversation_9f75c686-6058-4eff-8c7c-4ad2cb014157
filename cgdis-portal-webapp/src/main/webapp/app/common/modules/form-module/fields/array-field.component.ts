import { Component, Inject, OnInit } from '@angular/core';
import { UntypedFormArray, UntypedFormBuilder } from '@angular/forms';
import { FORM_SERVICE, IFormService } from '../service/iform.service';
import { AbstractGroupComponent } from './abstract-group.component';
import { FormHelperService } from '../service/form-helper.service';

@Component({
  selector: 'cgdis-portal-array-field',
  templateUrl: './array-field.component.html',
})
export class ArrayFieldComponent
  extends AbstractGroupComponent<UntypedFormArray>
  implements OnInit
{
  constructor(
    @Inject(FORM_SERVICE) theFormService: IFormService<any, any>,
    private fb: UntypedFormBuilder,
    formHelper: FormHelperService,
  ) {
    super(theFormService, fb.array([]), formHelper);
  }

  protected executeReset(): void {
    this.formControl.reset();
  }
}
