<cgdis-portal-field
  *ngIf="visible" [fieldFormControl]="formControl"
  [fieldId]="getFieldId()"
  [fieldRequired]="required"
  [iconTooltipKey]="iconTooltipKey"
  [icon]="icon"
  [inline]="inline"
  [labelKey]="labelKey"
  [label]="label" [ngClass]="{'defaultCursor': readonly}"
  role="radiogroup">

  <div *ngFor="let onePossibleValue of possibleValuesTranslated; index as $index; first as $first"
       [id]="getFieldId()"
       [ngClass]="{'defaultCursor': readonly}"
       [ngStyle]="{'pointer-events': readonly ? 'none' : ''}"
       role="radio">

    <input
      [formControl]="formControl"
      [id]="getId($index)"
      [name]="name"
      [ngClass]="{'defaultCursor': readonly}"
      [placeholder]="placeholder"
      [readonly]="readonly"
      [value]="onePossibleValue.value"
      class=""
      type="radio">

    <label [attr.for]="getId($index)" [ngClass]="{'defaultCursor': readonly}" [ngStyle]="readonly ? {'color': 'grey'} : {}"
           style="font-weight: normal">
      {{ onePossibleValue.label }}
    </label>

  </div>

</cgdis-portal-field>


