import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CGDISFullCalendarComponent } from './cgdis-full-calendar.component';

import { DateSelectorModule } from '../date-selector/date-selector.module';
import { AgendaFullCalendarComponent } from './agenda-full-calendar/agenda-full-calendar.component';
import { FullCalendarEventMapperService } from './fullcalendar-impl/full-calendar-event-mapper.service';
import { FullCalendarOptionsWrapperService } from './fullcalendar-impl/full-calendar-options-wrapper.service';

@NgModule({
  imports: [CommonModule, DateSelectorModule],
  declarations: [CGDISFullCalendarComponent, AgendaFullCalendarComponent],
  exports: [CGDISFullCalendarComponent, AgendaFullCalendarComponent],
  providers: [
    FullCalendarEventMapperService,
    FullCalendarOptionsWrapperService,
  ],
})
export class CGDISFullCalendarModule {}
