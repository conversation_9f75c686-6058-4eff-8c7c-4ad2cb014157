import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AllowanceSchedulerManagementComponent } from './allowance-scheduler-management.component';
import { AllowanceSchedulerManagementService } from '@app/common/modules/allowance-scheduler-management/allowance-scheduler-management.service';
import { DefaultFormTemplateModule } from '@app/common/template/default-form-template/default-form-template.module';
import { FormModule } from '@app/common/modules/form-module/form.module';
import { SharedModule } from '@app/common/shared/shared.module';
import { DatatableModule } from '@app/common/modules/datatable/datatable.module';

@NgModule({
  imports: [
    CommonModule,
    DefaultFormTemplateModule,
    FormModule,
    SharedModule,
    DatatableModule,
  ],
  exports: [AllowanceSchedulerManagementComponent],
  declarations: [AllowanceSchedulerManagementComponent],
  providers: [AllowanceSchedulerManagementService],
})
export class AllowanceSchedulerManagementModule {}
