import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ElementRef,
  EventEmitter,
  HostBinding,
  HostListener,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  Output,
  SimpleChanges,
} from '@angular/core';
import { TabsListService } from '../tabs-list.service';
import _ from 'lodash';
import { Subscription } from 'rxjs';

@Component({
  selector: 'cgdis-portal-tabs-list-item',
  templateUrl: './tabs-list-item.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TabsListItemComponent
  implements OnInit, OnDestroy, OnChanges, AfterViewInit
{
  @HostBinding('attr.role.tab') roleTab = 'tab';
  @HostBinding('attr.tabindex') tabIndex = 0;
  @HostBinding('attr.aria-selected') ariaSelected = false;
  @HostBinding('attr.aria-controls') ariaControls: string;
  @HostBinding('attr.class') tabClassesElem: string;

  /**
   * The tab id
   */
  @Input() tabId: string;

  /**
   * This tab is selected by default
   */
  @Input('defaultTab') defaultTab: boolean;

  /**
   * Classes added to the tab
   */
  @Input() tabClasses: string[];

  /**
   * Disable clicks on tabs
   */
  @Input() disableClick: boolean;

  /**
   * Disable item highlight
   */
  @Input() disableHighlight: boolean = false;

  @Output() onFocusItem = new EventEmitter<number>();

  /**
   * Tab selected or not
   * @type {boolean}
   */

  selected = false;
  /**
   * Name of the tab
   */
  name: string;

  private subscription: Subscription;

  constructor(
    private tabsListService: TabsListService,
    private elementRef: ElementRef,
    private cd: ChangeDetectorRef,
  ) {}

  ngOnInit(): void {
    this.subscription = this.subscribeSelected();
    this.name = this.tabsListService.computeTabName(this.tabId);
    this.ariaControls = this.name;
    if (this.defaultTab) {
      this.select();
    }
    this.cd.markForCheck();
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.tabClasses) {
      this.tabClassesElem = _.join(this.tabClasses, ' ');
      this.cd.markForCheck();
    }
  }

  ngAfterViewInit() {
    setTimeout(() => {
      if (this.selected) {
        this.focus(true);
      }
    });
  }

  private subscribeSelected(): Subscription {
    return this.tabsListService.selectedTabsId().subscribe((selectedTab) => {
      this.selected = selectedTab.tabId === this.tabId;
      this.ariaSelected = !this.disableHighlight ? this.selected : false;
      if (this.selected) {
        this.focus(true);
      }
      this.cd.markForCheck();
    });
  }

  /**
   * Set the focus on the tab depending on the parameter
   * @param {boolean} focus
   */
  focus(focus: boolean) {
    if (focus) {
      this.onFocusItem.emit(this.elementRef.nativeElement.offsetLeft);
    }
  }

  select(): void {
    this.tabsListService.selectTabsId(this.tabId);
  }

  @HostListener('click', ['$event'])
  onClick(e: any) {
    if (!this.disableClick) {
      this.select();
    }
  }
}
