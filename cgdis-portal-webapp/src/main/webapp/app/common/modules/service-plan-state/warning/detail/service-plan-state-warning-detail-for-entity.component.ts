import {
  ChangeDetectionStrategy,
  Component,
  Input,
  OnChanges,
  OnInit,
  SimpleChanges,
} from '@angular/core';
import { WarningMessage } from '@app/model/warning/warning-message';

@Component({
  selector: 'cgdis-portal-service-plan-state-warning-detail-for-entity',
  templateUrl: './service-plan-state-warning-detail-for-entity.component.html',
  styles: [],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ServicePlanStateWarningDetailForEntityComponent
  implements OnInit, OnChanges
{
  @Input()
  groupName: string;

  @Input()
  servicePlanNames: string[];

  warningMessages: WarningMessage[];
  constructor() {}

  ngOnInit() {}

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.groupName || changes.servicePlanNames) {
      this.computeWarningMessages();
    }
  }

  private computeWarningMessages() {
    if (
      this.groupName &&
      this.servicePlanNames &&
      this.servicePlanNames.length > 0
    ) {
      this.warningMessages = [];
      this.warningMessages.push(
        new WarningMessage({
          warningType: 'group-unavailable-for-plans',
          params: {
            servicePlan: this.servicePlanNames
              ? this.servicePlanNames.join(', ')
              : '',
            group: this.groupName,
          },
          icon: 'icon-warning',
        }),
      );
    }
  }
}
