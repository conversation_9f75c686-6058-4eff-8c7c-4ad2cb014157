<div class="tile-wrapper align-content-stretch" >

  <div class=" tile-wrapper-item" [ngClass]="{'mb-4':marginArray[$index], 'col-4': !isMobile, 'col-12': isMobile}" [ngStyle]="{'padding': isMobile? '0': '', 'margin-top': isMobile ? '0.5rem' : '0'}" *ngFor="let oneItem of allItems; trackBy: trackById; index as $index; first as $first; last as $last"
       >
    <template tile-group-host></template>

    <div class="tile-group-item-arrows " *ngIf="!disableOrderItem">
      <div *ngIf="$first" class="col-2"></div>
      <div *ngIf="!$first" class="col-2 txtleft" (click)="goToPreviousPosition($index,oneItem)">
        <cgdis-portal-previous-button ></cgdis-portal-previous-button>
      </div>

      <div class="col-8 txtcenter">

      </div>

      <div *ngIf="!$last" class="col-2 txtright"  (click)="goToNextPosition($index,oneItem)">
        <cgdis-portal-next-button ></cgdis-portal-next-button>
      </div>
      <div *ngIf="$last" class="col-2"></div>
    </div>
  </div>

  <!-- Add initialTileGroupItems -->
  <div class="col-4" *ngIf="addItemVisible"   >
    <cgdis-portal-tile [id]="'add-tile'" [showCloseButton]="false" (onClick)="tileAddClicked()">
      <cgdis-portal-link-with-icon class="tile-add-button" [tooltipText]="'tooltip.add-position' | translate" [iconClasses]="['tile-add-button-icon']" [icon]="addButtonIcon" ></cgdis-portal-link-with-icon>
    </cgdis-portal-tile>
  </div>
  <!-- used for avoid clipping when add tile disappear -->
  <div style="min-height: 30rem; width:0;"></div>

</div>
