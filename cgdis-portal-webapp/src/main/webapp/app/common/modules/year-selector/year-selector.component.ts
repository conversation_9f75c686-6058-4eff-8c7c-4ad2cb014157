import { Component, EventEmitter, OnInit, Output } from '@angular/core';
import { DateModel, DateService } from '@eportal/core';

@Component({
  selector: 'cgdis-portal-year-selector',
  templateUrl: './year-selector.component.html',
  styles: [],
})
export class YearSelectorComponent implements OnInit {
  /**
   * Event sent when dates are changed
   * Date sent in parameter is the first day of the week
   * @type {EventEmitter<DateModel>}
   */
  @Output() onDateChanged = new EventEmitter<DateModel>();
  startDate: DateModel;
  endDate: DateModel;
  private yearNumber = 1;
  monthName: string;

  constructor(private dateService: DateService) {}

  ngOnInit() {
    if (this.startDate == undefined) {
      this.updateStartDate(this.dateService.now());
    }
    this.updateStartDate(this.startDate);
  }
  /**
   * Display previous month
   */
  previousYear() {
    let dateTemp = this.dateService.add(
      this.startDate,
      -this.yearNumber,
      'years',
    );
    this.updateStartDate(dateTemp);
  }

  /**
   * Display next month
   */
  nextYear() {
    let dateTemp = this.dateService.add(
      this.startDate,
      this.yearNumber,
      'years',
    );
    this.updateStartDate(dateTemp);
  }

  /**
   * Calculate the end date
   */
  private calculateEndDate() {
    this.endDate = this.dateService.add(
      this.startDate,
      this.yearNumber,
      'years',
    );
  }

  /**
   * Update the start date using the new date in parameter
   * @param {DateModel} newStartDate
   */
  private updateStartDate(newStartDate: DateModel) {
    let startDateUpdated = new DateModel(newStartDate);
    startDateUpdated.month = 1;
    startDateUpdated.day = 1;
    this.startDate = startDateUpdated;
    this.calculateEndDate();
    this.onDateChanged.emit(this.startDate);
  }
}
