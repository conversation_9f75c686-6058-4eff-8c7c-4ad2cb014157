import {
  ChangeDetectorRef,
  Component,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  SimpleChanges,
} from '@angular/core';
import { AbstractDatatableFilter } from './abstract-datatable-filter';
import { DateModel, DateService } from '@eportal/core';
import { DatepickerConfig } from '../../datepicker/model/datepicker-config.model';
import { DatepickerConfigurationsService } from '../../../shared/services/datepicker-configurations.service';
import { FilterConfig, SearchOperator } from '@eportal/components';
import { BreakpointObserver, BreakpointState } from '@angular/cdk/layout';
import { Subscription } from 'rxjs';
import * as _ from 'lodash';

@Component({
  selector: 'cgdis-portal-datatable-datepicker-filter',
  templateUrl: './datatable-datepicker-filter.component.html',
  styles: [],
})
export class DatatableDatepickerFilterComponent
  extends AbstractDatatableFilter
  implements OnInit, OnChanges, On<PERSON><PERSON>roy
{
  /**
   * Values in this config should not be updated
   */
  @Input() initialDatepickerConfig: DatepickerConfig;

  /**
   * Override the start date in config
   */
  @Input() startDate: DateModel;

  /**
   * Override the end date in config
   */
  @Input() endDate: DateModel;

  datepickerConfig: DatepickerConfig;

  startDateForDatepicker: DateModel;

  @Input() initialValue: DateModel;
  /**
   * Date used only for the datepicker
   */
  theDate: DateModel;

  /**
   * Input placeholder
   */
  @Input() placeholder: string = '';

  @Input() readOnly = false;

  isMobile: boolean = false;

  subscriptions: Subscription[] = [];

  constructor(
    private dateService: DateService,
    private datepickerConfigurationsService: DatepickerConfigurationsService,
    private breakpointObserver: BreakpointObserver,
    private cd: ChangeDetectorRef,
  ) {
    super();

    this.subscriptions.push(
      this.breakpointObserver
        .observe(['(max-width: 992px)'])
        .subscribe((result: BreakpointState) => {
          this.isMobile = result.matches;
          this.cd.markForCheck();
          this.updateConfiguration();
        }),
    );
  }

  ngOnInit() {
    if (this.filterConfig == undefined) {
      this.filterConfig = new FilterConfig({
        operator: SearchOperator.eq,
      });
    }
    if (this.initialValue !== undefined) {
      this.theDate = this.initialValue;
    }
    super.ngOnInit();
    this.updateConfiguration();
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((subscription) => {
      subscription.unsubscribe();
    });
  }

  protected valueChanged(newValue: any): void {
    if (
      newValue != undefined &&
      !this.dateService.equals(newValue, this.theDate)
    ) {
      this.theDate = newValue;
    }
  }

  private addEncodeDecodeFunctions() {
    this.filterConfig.encode = (value) =>
      this.dateService.format(value, 'YYYY-MM-DD');
    this.filterConfig.decode = (value) =>
      this.dateService.parse(value, 'YYYY-MM-DD');
  }

  protected prepareFilterConfig() {
    this.addEncodeDecodeFunctions();
  }

  private updateConfiguration() {
    let newConfig: DatepickerConfig =
      this.datepickerConfigurationsService.defaultConfiguration(this.isMobile);
    _.merge(newConfig, this.initialDatepickerConfig);
    this.datepickerConfig = newConfig;
  }

  ngOnChanges(changes: SimpleChanges) {
    if (
      changes.initialDatepickerConfig &&
      !changes.initialDatepickerConfig.firstChange
    ) {
      this.updateConfiguration();
    }

    if (changes.startDate) {
      // No start date if readonly
      this.updateStartdate();
    }
  }

  private updateStartdate() {
    if (this.startDateForDatepicker !== this.startDate) {
      // execute update only if two dates are differents
      this.startDateForDatepicker = this.startDate;
    }
  }

  dateSelected(date: DateModel) {
    this.filterFormControl.setValue(date);
  }
}
