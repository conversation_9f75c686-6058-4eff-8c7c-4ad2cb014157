import {
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
} from '@angular/core';
import _ from 'lodash';

@Component({
  selector: 'cgdis-portal-rating',
  templateUrl: './rating.component.html',
  styles: [],
})
export class RatingComponent implements OnInit, OnChanges {
  /**
   * The rate to display
   */
  @Input() rate: number;

  /**
   * Max number of icons, default 5
   * @type {number}
   */
  @Input() maxRate = 5;

  /**
   * Send the value selected in the rating
   * @type {EventEmitter<number>}
   */
  @Output() onValueSelected = new EventEmitter<number>();

  range: number[];

  private iconFill = 'icon-star-full';

  private iconNotFill = 'icon-star-pas-full';

  constructor() {}

  ngOnInit() {
    this.update();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes != undefined) {
      if (changes.hasOwnProperty('rate') || changes.hasOwnProperty('maxRate')) {
        this.update();
      }
    }
  }

  /**
   * Update the number of icons displayed
   */
  update() {
    this.range = _.range(0, this.maxRate);
  }

  /**
   * return the icon to use for the index
   * @param {number} index
   * @return {string}
   */
  icon(index: number) {
    if (index < this.rate) {
      return this.iconFill;
    }
    return this.iconNotFill;
  }

  /**
   * User have selected a value
   * Note: 'value' is the index (start to 0) so we adding 1
   * @param {number} value
   */
  valueSelected(value: number): void {
    this.onValueSelected.emit(value + 1);
  }
}
