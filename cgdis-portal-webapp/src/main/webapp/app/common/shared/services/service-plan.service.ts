import { first, map } from 'rxjs/operators';
import { Injectable } from '@angular/core';
import {
  DateModel,
  DownloadOptions,
  IPage,
  ProgressDownload,
  RestService,
} from '@eportal/core';
import {
  ServicePlan,
  ServicePlanNoJoin,
} from '../../../model/service-plan.model';
import { Observable } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { Schedule } from '../../../operational/service-plan/schedule/schedule.model';
import { ServicePlanModelVersionWithPositions } from '../../../model/service-plan-model-version-with-positions.model';
import { ServicePlanWithEditable } from '../../../model/service-plan-with-editable.model';
import { ServicePlanVersion } from '../../../model/service-plan-version.model';
import { CompletionStatus } from '../../../model/completion-status.model';
import { VehicleWithoutEntity } from '../../../model/vehicle-without-entity';
import { Entity } from '../../../model/entity.model';
import { BaseModel } from '../../../model/base-model.model';
import { PrestationCopyError } from '../../../model/prestation-copy-error.model';
import { ServicePlanDates } from '../../../model/service-plan-dates.model';
import { ServicePlanWithPermissions } from '../../../model/service-plan-with-permissions.model';
import { ScheduleRow } from '@app/operational/service-plan/schedule/schedule-row.model';
import { Team } from '@app/model/serviceplan/team/team.model';
import { ServicePlanNoJoinAndVersion } from '@app/model/service-plan-no-join-and-version';

@Injectable()
export class ServicePlanService {
  /**
   * Based url to access one service plan
   * @type {string}
   */
  baseUrl = ['service-plan'];
  baseAdminUrl = ['admin', 'service-plan'];
  baseUrlPrestation = ['prestations'];
  baseUrlModel = ['admin', 'service-plan-model'];
  baseUrlFillView = ['service-plan-fill-view'];

  constructor(
    private httpClient: HttpClient,
    private _restService: RestService,
  ) {}

  /**
   * Get one service plan
   * @param {number} tecid: the service plan id
   * @return the service plan
   */
  get(tecid: number): Observable<ServicePlan> {
    return this._restService
      .one<ServicePlan>(...this.baseUrl, String(tecid))
      .get()
      .pipe(
        map((value: ServicePlan) => {
          return value;
        }),
      );
  }

  getFillView(tecid: number): Observable<ServicePlanWithPermissions> {
    return this._restService
      .one<ServicePlanWithPermissions>(
        ...this.baseUrl,
        'fill-view',
        String(tecid),
      )
      .get()
      .pipe(
        map((value: ServicePlanWithPermissions) => {
          return value;
        }),
      );
  }

  /**
   * Get all entities accessible for edit/create a service plan model
   */
  getAllEntities(modelId: BaseModel): Observable<Entity[]> {
    const restResource = this._restService
      .all(...this.baseUrl)
      .all('allEntities', String(modelId.tecid));
    return restResource.get().pipe(
      map((value) => {
        return value;
      }),
    );
  }

  /**
   * Get all positions for a given service plan and a given date
   * @param {number} tecid: the service plan tecid
   * @param date: the date
   * @returns {Observable<ServicePlanModelVersionWithPositions>}: the positions
   */
  getPositions(
    tecid: number,
    date: DateModel,
  ): Observable<ServicePlanModelVersionWithPositions> {
    return this._restService
      .one<ServicePlanModelVersionWithPositions>(
        ...this.baseUrlFillView,
        String(tecid),
        'jobs',
      )
      .get({ date: date.toString() })
      .pipe(
        map((value: ServicePlanModelVersionWithPositions) => {
          return value;
        }),
      );
  }

  /**
   * Get all service plan that have a version enclosing today
   * @returns {ServicePlanWithServicePlanVersion} list of service plan with their current version
   */
  getCurrentServicePlans(): Observable<ServicePlanNoJoinAndVersion[]> {
    const restResource = this._restService.one(
      ...this.baseUrl,
      'current',
      'version',
    );
    return restResource.get().pipe(
      map((value: ServicePlanNoJoinAndVersion[]) => {
        return value;
      }),
    );
  }

  /**
   * Get all service plan that have only versions in the future
   * @returns {ServicePlanWithServicePlanVersion} list of service plan with their nearest future version
   */
  getFutureServicePlans(): Observable<ServicePlanNoJoinAndVersion[]> {
    const restResource = this._restService.one(
      ...this.baseUrl,
      'future',
      'version',
    );
    return restResource.get().pipe(
      map((value: ServicePlanNoJoinAndVersion[]) => {
        return value;
      }),
    );
  }

  getAllNoJoin(): Observable<ServicePlanNoJoin[]> {
    const restResource = this._restService.one(...this.baseUrl, 'all');
    return restResource.get().pipe(
      map((value: ServicePlanNoJoin[]) => {
        return value;
      }),
    );
  }

  /**
   * Get schedule for a given service plan and a givan date
   * @param {number} servicePlanTecid: the service plan tecid
   * @param {DateModel} date: the date
   * @returns {Observable<Schedule>}: the schedule
   */
  getSchedule(servicePlanTecid: number, date: DateModel): Observable<Schedule> {
    return this._restService
      .one<Schedule>(...this.baseUrl, String(servicePlanTecid), 'schedule')
      .get({ date: date })
      .pipe(
        map((value: Schedule) => {
          return value;
        }),
      );
  }

  /**
   * Get schedule availability (number of person available for each (postion, slot)
   * @param servicePlanTecid
   * @param date
   */
  getScheduleAvailability(servicePlanTecid: number, date: DateModel) {
    return this._restService
      .one<ScheduleRow[]>(
        ...this.baseUrl,
        String(servicePlanTecid),
        'schedule-availability',
      )
      .get({ date: date })
      .pipe(
        map((value: ScheduleRow[]) => {
          return {
            data: value,
            requestDate: date,
          };
        }),
      );
  }

  /**
   * Get all positions for a given service plan (in admin view)
   * @param {number} tecid: the service plan tecid
   * @param date: the date
   * @returns {Observable<ServicePlanModelVersionWithPositions>}: the positions
   */
  getPositionsForAdmin(
    tecid: number,
  ): Observable<ServicePlanModelVersionWithPositions> {
    return this._restService
      .one<ServicePlanModelVersionWithPositions>(
        ...this.baseAdminUrl,
        String(tecid),
        'positions',
      )
      .get()
      .pipe(
        map((value: ServicePlanModelVersionWithPositions) => {
          return value;
        }),
      );
  }

  closureServicePlan(tecid: number): Observable<boolean> {
    const restResource = this._restService.one(
      ...this.baseAdminUrl,
      String(tecid),
      'closure',
    );
    return restResource.update(null).pipe(
      map((value: boolean) => {
        return value;
      }),
    );
  }

  /**
   * Get the possible closure date for service plan
   * @returns {Observable<DateModel>}
   */
  getClosureDate(servicePlanId: number): Observable<DateModel> {
    return this._restService
      .one<DateModel>(
        ...this.baseAdminUrl,
        'closure-date',
        String(servicePlanId),
      )
      .get()
      .pipe(
        map((value: DateModel) => {
          return value;
        }),
      );
  }

  getServicePlanWithEditable(
    tecid: number,
  ): Observable<ServicePlanWithEditable> {
    return this._restService
      .one<ServicePlanWithEditable>(
        ...this.baseAdminUrl,
        'editable',
        String(tecid),
      )
      .get()
      .pipe(
        map((value: ServicePlanWithEditable) => {
          return value;
        }),
      );
  }

  /**
   * Get version for a given service plan and a date
   * @param {number} servicePlanTecid: the service plan tecid
   * @param {DateModel} date: the date
   * @returns {Observable<ServicePlanVersion>}: the servicePlanVersion
   */
  getVersion(
    servicePlanTecid: number,
    date: DateModel,
  ): Observable<ServicePlanVersion> {
    return this._restService
      .one<ServicePlanVersion>(
        ...this.baseUrl,
        String(servicePlanTecid),
        'version',
      )
      .get({ date: date })
      .pipe(
        map((value: ServicePlanVersion) => {
          return value;
        }),
      );
  }

  /**
   * Get all existing service plan function
   * @return {Observable<ServicePlan[]>}
   */
  getAll(): Observable<ServicePlan[]> {
    const restResource = this._restService.one<IPage<ServicePlan>>(
      ...this.baseUrl,
    );
    return restResource.get().pipe(
      map((value: IPage<ServicePlan>) => {
        return value.content;
      }),
    );
  }

  /**
   * Get all data for chart in dashboard summary
   * @param {number} servicePlanId
   * @param {DateModel} from
   * @param {DateModel} to
   * @returns {Observable<any>}
   */
  getMonthSummary(
    servicePlanId: number,
    from: DateModel,
    to: DateModel,
  ): Observable<Record<string, CompletionStatus>> {
    return this._restService
      .one<
        Record<string, CompletionStatus>
      >(...this.baseUrl, String(servicePlanId), 'status')
      .get({ from: from, to: to });
  }

  /**
   * Get all service plan vehicles
   * @return {Observable<VehicleWithoutEntity>}
   */
  getVehicles(): Observable<VehicleWithoutEntity[]> {
    const restResource = this._restService.all(
      ...this.baseAdminUrl,
      'vehicles',
    );
    return restResource.get();
  }

  /**
   * Ask for CSV export
   * @param servicePlanTecid
   * @param from
   * @param to
   */
  exportCSV(
    servicePlanTecid: number,
    from: DateModel,
    to: DateModel,
    completeCallback: () => void,
    errorCallback: (e: any) => void,
  ) {
    const fromString = from == undefined ? '' : from.toString();
    const toString = to == undefined ? '' : to.toString();
    this._restService
      .one('export', 'service-plan', String(servicePlanTecid))
      .one('csv')
      .download(
        new DownloadOptions({
          params: { from: fromString, to: toString },
        }),
      )
      .pipe(first())
      .subscribe(
        (progressDownload: ProgressDownload) => {
          // Nothing to do !
        },
        (e) => errorCallback(e),
        () => completeCallback(),
      );
  }

  /**
   * Ask for PDF export
   * @param servicePlanTecid
   * @param from
   * @param to
   */
  exportPDF2(
    servicePlanTecid: number,
    from: DateModel,
    to: DateModel,
    isPortrait: boolean,
    completeCallback: () => void,
    errorCallback: (e: any) => void,
  ) {
    const fromString = from == undefined ? '' : from.toString();
    const toString = to == undefined ? '' : to.toString();
    this._restService
      .one(...this.baseUrl)
      .one('export', 'pdf', String(servicePlanTecid))
      .download(
        new DownloadOptions({
          params: { from: fromString, to: toString, portrait: isPortrait },
        }),
      )
      .pipe(first())
      .subscribe(
        (progressDownload: ProgressDownload) => {
          // Nothing to do !
        },
        (e) => errorCallback(e),
        () => completeCallback(),
      );
  }

  exportPDF(
    servicePlanTecid: number,
    from: DateModel,
    to: DateModel,
    completeCallback: () => void,
    errorCallback: (e: any) => void,
  ) {
    this._restService
      .one('export', 'service-plan', String(servicePlanTecid))
      .one('pdf')
      .download(
        new DownloadOptions({
          progressiveDownload: false,
          params: { from: from.toString(), to: to.toString() },
        }),
      )
      .pipe(first())
      .subscribe(
        (progressDownload: ProgressDownload) => {
          // Nothing to do !
        },
        (e) => errorCallback(e),
        () => completeCallback(),
      );
  }

  /**
   * Copy all prestation of given day to day + 1D
   * @param servicePlanTecid
   * @param date
   */
  public copyDay2(
    servicePlanTecid: number,
    startDate: DateModel,
    targetDate: DateModel,
  ): Observable<PrestationCopyError[]> {
    const restResource = this._restService.one(
      ...this.baseUrlPrestation,
      'copy',
      'day',
      String(servicePlanTecid),
    );
    return restResource
      .get({ startDate: startDate, targetDate: targetDate })
      .pipe(
        map((value: any) => {
          return value;
        }),
      );
  }

  /**
   * Copy all prestation of a whole week to the next one
   * @param servicePlanTecid
   * @param date (first day of the week to copy)
   */
  public copyWeek2(
    servicePlanTecid: number,
    date: DateModel,
  ): Observable<PrestationCopyError[]> {
    const restResource = this._restService.one(
      ...this.baseUrlPrestation,
      'copy',
      'week',
      String(servicePlanTecid),
    );
    return restResource.get({ date: date }).pipe(
      map((value: any) => {
        return value;
      }),
    );
  }

  /**
   * Get start and end dates of the service plan
   * @param servicePlanId
   */
  public getDates(servicePlanId: number): Observable<ServicePlanDates> {
    const restResource = this._restService.one(
      ...this.baseUrl,
      'dates',
      String(servicePlanId),
    );
    return restResource.get().pipe(
      map((value: ServicePlanDates) => {
        return value;
      }),
    );
  }

  getTeams(servicePlanTecid: number): Observable<Team[]> {
    const restResource = this._restService
      .all(...this.baseUrl, String(servicePlanTecid))
      .all('teams');
    return restResource.get();
  }

  sendPdsFilling(
    servicePlanTecid: number,
    from: DateModel,
    to: DateModel,
    message: string,
  ): Observable<void> {
    const restResource = this._restService
      .one('export')
      .one('service-plan', String(servicePlanTecid))
      .one<void>('sendFilling');
    return restResource.update({
      servicePlanId: servicePlanTecid,
      from: from,
      to: to,
      message: message,
    });
  }

  getServicePlansHavingServicePlanAsOptionalOrBackupGroup(
    servicePlanTecid: number,
  ): Observable<ServicePlanNoJoin[]> {
    const restResource = this._restService
      .all(...this.baseAdminUrl, String(servicePlanTecid))
      .all('is-optional-backup-group');
    return restResource.get();
  }
}
