import { ChangeDetectionStrategy, Component, OnInit } from '@angular/core';
import { IconConstants } from '../icon/icon-constants';
import { LinkWithIconComponent } from './link-with-icon.component';
import { ConnectedUserService } from '../../../security/connected-user.service';

@Component({
  selector: 'cgdis-portal-link-with-icon-download',
  templateUrl: './link-with-icon.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class LinkWithIconDownloadComponent
  extends LinkWithIconComponent
  implements OnInit
{
  constructor(connectedUserService: ConnectedUserService) {
    super(connectedUserService);
  }

  ngOnInit() {
    super.ngOnInit();
    this.setIconValue(IconConstants.DOWNLOAD);
  }
}
