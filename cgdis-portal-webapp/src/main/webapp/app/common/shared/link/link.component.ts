import { ChangeDetectionStrategy, Component, Input } from '@angular/core';

@Component({
  selector: 'cgdis-portal-link,[cgdis-portal-link]',
  templateUrl: './link.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class LinkComponent {
  /**
   * Translation key
   */
  @Input() linkKey: string;

  /**
   * Redirect to this view
   */
  @Input() link: string;

  constructor() {}
}
