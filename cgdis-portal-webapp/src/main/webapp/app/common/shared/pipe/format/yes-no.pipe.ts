import { Pipe, PipeTransform } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';

@Pipe({
  name: 'yesNo',
})
export class YesNoPipe implements PipeTransform {
  constructor(private translationService: TranslateService) {}

  private readonly _nullValue = '-';

  transform(value: boolean, nullValue = this._nullValue): string {
    if (value == undefined) {
      return nullValue == undefined ? this._nullValue : nullValue;
    }
    if (value === true) {
      return this.translationService.instant('default.yes');
    }
    return this.translationService.instant('default.no');
  }
}
