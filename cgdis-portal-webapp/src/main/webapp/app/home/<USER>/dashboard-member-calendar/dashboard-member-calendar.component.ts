import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  OnDestroy,
  OnInit,
} from '@angular/core';
import { DateModel, DateService } from '@eportal/core';
import { LegendItem } from '@app/common/modules/legend/legend-item';
import { DashboardDay } from '@app/model/dashboard-day.model';
import { CGDISFullCalendarEvent } from '@app/common/modules/full-calendar/model/cgdis-full-calendar-event.model';
import { ActivatedRoute, Router } from '@angular/router';
import { IconConstants } from '@app/common/shared/icon/icon-constants';
import { ConnectedUserService } from '@app/security/connected-user.service';
import { UserPreference } from '@app/security/model/user-preferences.enum';
import { Subscription } from 'rxjs';
import { BreakpointObserver, BreakpointState } from '@angular/cdk/layout';
import { take } from 'rxjs/operators';

@Component({
  selector: 'cgdis-portal-dashboard-member-calendar',
  templateUrl: './dashboard-member-calendar.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DashboardMemberCalendarComponent implements OnInit, OnDestroy {
  /**
   * First day of the week
   */
  public startDate: DateModel;

  /**
   * All items displayed in the legend
   */
  public legendItems: LegendItem[];
  legendItemsOne: LegendItem[];
  legendItemsSecond: LegendItem[];
  legendItemsThird: LegendItem[];

  /**
   * Number of days to display
   * @type {number}
   */
  public daysNumber = 7;

  public personId: number;

  public isProfessional: boolean;

  public isVolunteer: boolean;

  public data: DashboardDay[];

  public events: CGDISFullCalendarEvent[] = [];

  isMobile: boolean = false;

  subscriptions: Subscription[] = [];

  constructor(
    private dateService: DateService,
    private route: ActivatedRoute,
    private cd: ChangeDetectorRef,
    private connectedUserService: ConnectedUserService,
    private router: Router,
    private breakpointObserver: BreakpointObserver,
  ) {
    this.subscriptions.push(
      this.breakpointObserver
        .observe(['(max-width: 576px)'])
        .subscribe((result: BreakpointState) => {
          this.isMobile = result.matches;
          const definedDaysNumber =
            this.connectedUserService.getCurrentCgdisPortalUser().userPreference
              .defined[UserPreference.CALENDAR_DAYS];
          this.daysNumber =
            definedDaysNumber == undefined ? 7 : parseInt(definedDaysNumber);
          this.cd.markForCheck();
        }),
    );
  }

  ngOnInit() {
    // set days number
    const definedDaysNumber =
      this.connectedUserService.getCurrentCgdisPortalUser().userPreference
        .defined[UserPreference.CALENDAR_DAYS];
    this.daysNumber =
      definedDaysNumber == undefined ? 7 : parseInt(definedDaysNumber);

    if (String(this.daysNumber) === String(7)) {
      this.startDate = this.dateService.firstDayOfWeek();
    } else {
      this.startDate = this.dateService.now();
    }

    this.personId = this.connectedUserService.getConnectedUserId();

    this.connectedUserService
      .getConnectedUser()
      .pipe(take(1))
      .subscribe((user) => {
        this.isProfessional = user.userDetails.professional;
        this.isVolunteer = user.userDetails.volunteer;
      });

    let prefix = '';

    // Init chart legend
    this.legendItems = [
      new LegendItem({
        id: '1',
        labelKey: 'dashboard.members.chart.' + prefix + 'planning',
        classes: ['-planning'],
      }),
      new LegendItem({
        id: '2',
        labelKey: 'dashboard.members.chart.' + prefix + 'availability',
        classes: ['-availability'],
      }),
      new LegendItem({
        id: '3',
        labelKey: 'dashboard.members.chart.' + prefix + 'professional',
        classes: ['-professional'],
      }),
      new LegendItem({
        id: '4',
        labelKey: 'dashboard.members.chart.' + prefix + 'fire',
        classes: [],
        imageId: IconConstants.FIRE,
      }),
      new LegendItem({
        id: '5',
        labelKey: 'dashboard.members.chart.' + prefix + 'ambulance',
        classes: [],
        imageId: IconConstants.AMBULANCE,
      }),
      new LegendItem({
        id: '6',
        labelKey: 'dashboard.members.chart.' + prefix + 'others',
        classes: [],
        imageId: IconConstants.OTHERS,
      }),
      new LegendItem({
        id: '6',
        labelKey: 'dashboard.members.chart.' + prefix + 'exclusive',
        classes: [],
        imageId: IconConstants.LOCK,
      }),
      new LegendItem({
        id: '7',
        labelKey: 'service_plan.versions.type.barracked',
        classes: [],
        imageId: IconConstants.BARRACKED,
      }),
    ];

    this.legendItemsOne = [
      this.legendItems[0],
      this.legendItems[1],
      this.legendItems[2],
    ];
    this.legendItemsSecond = [
      this.legendItems[3],
      this.legendItems[4],
      this.legendItems[5],
    ];
    this.legendItemsThird = [this.legendItems[6], this.legendItems[7]];

    this.subscriptions.push(
      this.connectedUserService.getPreferences().subscribe((preferences) => {
        const definedDaysNumber =
          preferences.defined[UserPreference.CALENDAR_DAYS];
        this.daysNumber =
          definedDaysNumber == undefined ? 7 : parseInt(definedDaysNumber);
        this.cd.markForCheck();
        if (String(this.daysNumber) === String(7)) {
          this.startDate = this.dateService.firstDayOfWeekWithSpecifyDay(
            this.startDate,
            +preferences.defined[UserPreference.FIRST_DAY_OF_WEEK],
          );
        } else {
          this.startDate = this.dateService.now();
        }

        this.cd.markForCheck();
      }),
    );

    this.cd.markForCheck();
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((subscription) => {
      subscription.unsubscribe();
    });
  }

  /**
   * Date change event
   * @param newSelectedDate
   */
  datesChanges(newSelectedDate: DateModel) {
    // Check if date has changed
    if (!this.dateService.equals(this.startDate, newSelectedDate)) {
      this.startDate = newSelectedDate;
      this.cd.markForCheck();
    }
  }

  /**
   * Go to volunteer availability edition
   */
  goToEditAvailability(): void {
    const dateStr = this.dateService.format(this.startDate);
    this.router.navigate(
      ['../availability-planning', { selectedDate: dateStr }],
      {
        relativeTo: this.route,
      },
    );
  }
}
