import { Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { Subscription } from 'rxjs';
import { ConnectedUserService } from '../../../security/connected-user.service';

@Component({
  selector: 'cgdis-portal-dashboard-manager-nav',
  templateUrl: './dashboard-manager-nav.component.html',
})
export class DashboardManagerNavComponent implements OnInit, OnDestroy {
  public canViewVolunteerAvailability = false;
  private canViewVolunteerAvailabilitySubscription: Subscription;

  constructor(private connectedUserService: ConnectedUserService) {}

  ngOnDestroy(): void {
    this.canViewVolunteerAvailabilitySubscription.unsubscribe();
  }

  ngOnInit(): void {
    this.canViewVolunteerAvailabilitySubscription = this.connectedUserService
      .hasAnyRolesObservable(['ROLE_PERMISSION_DASHBOARD_MEMBER'])
      .subscribe((newValue) => {
        this.canViewVolunteerAvailability = newValue;
      });
  }
}
