import { first } from 'rxjs/operators';
import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  On<PERSON><PERSON>roy,
  OnInit,
} from '@angular/core';
import { ConnectedUserService } from '@app/security/connected-user.service';
import { ConnectedUserPreferences } from '@app/security/model/connected-user-preferences.model';
import { UserPreference } from '@app/security/model/user-preferences.enum';
import { PreferenceSummaryService } from './preference-summary.service';
import { TranslateService } from '@ngx-translate/core';
import { DateService, DatetimeService } from '@eportal/core';
import { ToastService } from '@app/common/shared/toasts/CGDISToastService';
import { Subscription } from 'rxjs';
import { TranslationService } from '@app/security/translation.service';
import { BreakpointObserver, BreakpointState } from '@angular/cdk/layout';

@Component({
  selector: 'cgdis-portal-preference-summary',
  templateUrl: './preference-summary.component.html',
  providers: [TranslationService],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PreferenceSummaryComponent implements OnInit, OnDestroy {
  /**
   * The connected user preferences
   */
  preferences: ConnectedUserPreferences;

  public visibleCalendarDays = true;
  public visibleFullAvailbility = false;
  public visibleNotification = true;
  public visibleUpdateStatus = false;
  private rolesSubscription: Subscription;
  private fullAvailabilitySubscription: Subscription;
  // In order to user Object on DOM
  public Object = Object;

  isMobile: boolean = false;

  subscriptions: Subscription[] = [];

  constructor(
    private userService: ConnectedUserService,
    private preferencePopupService: PreferenceSummaryService,
    private translate: TranslateService,
    private dateService: DateService,
    private toastService: ToastService,
    private cd: ChangeDetectorRef,
    private breakpointObserver: BreakpointObserver,
    private datetimeService: DatetimeService,
  ) {
    this.subscriptions.push(
      this.breakpointObserver
        .observe(['(max-width: 992px)'])
        .subscribe((result: BreakpointState) => {
          this.isMobile = result.matches;
          this.cd.markForCheck();
        }),
    );
  }

  ngOnInit(): void {
    this.userService
      .getConnectedUser()
      .pipe(first())
      .subscribe((value) => {
        // the user preferences
        this.preferences = value.userPreference;
        this.cd.markForCheck();
      });

    this.rolesSubscription = this.userService
      .hasAnyRolesObservable(['ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_VIEW'])
      .subscribe((newValue) => {
        this.visibleCalendarDays = newValue;
      });
    this.fullAvailabilitySubscription = this.userService
      .hasAnyRolesObservable([
        'ROLE_PERMISSION_PDS_FILL_ADD_PERSON_FULL_AVAILABILITY',
      ])
      .subscribe((newValue) => {
        this.visibleFullAvailbility = newValue;
      });

    this.visibleUpdateStatus = this.userService.hasAnyRoles([
      'ROLE_PERMISSION_PREFERENCES_UPDATE_VEHICLE',
    ]);
    this.visibleNotification = this.userService.hasAnyRoles([
      'ROLE_ADMIN',
      'ROLE_ADMIN_OPERATIONAL',
      'ROLE_CENTER_CHIEF',
      'ROLE_ZONE_CHIEF',
      'ROLE_PERMANENCE_MANAGEMENT',
      'ROLE_PERMANENCE_MANAGEMENT_LIGHT',
      'ROLE_PERMANENCE_MANAGEMENT_GLOBAL',
      'ROLE_PERSON_MANAGEMENT_GLOBAL',
      'ROLE_PERSON_MANAGEMENT',
      'ROLE_MEMBER',
    ]);
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((subscription) => {
      subscription.unsubscribe();
    });
  }

  /**
   * Set user preference
   * @param key
   * @param value
   */
  setPreference(key: string, value: string): void {
    if (value !== this.preferences.defined[key]) {
      this.preferencePopupService
        .setPersonPreference(key, value)
        .pipe(first())
        .subscribe((success) => {
          if (success) {
            // modify connected user preference
            this.userService.setConnectedUserPreference(key, value);

            // if language, change it !
            if (key === UserPreference.LANGUAGE) {
              this.changeLanguage(value.toLowerCase());
            }

            // show success message
            this.toastService.success(
              'layout.navigation.profile.mypreferences.update-success',
            );
            this.cd.markForCheck();
          } else {
            // show error message
            this.toastService.error(
              'layout.navigation.profile.mypreferences.update-error',
            );
          }
        });
    }
  }

  /**
   * Change the application language
   * @param {string} language
   */
  changeLanguage(language: string): void {
    this.translate.setDefaultLang(language);
    this.translate.use(language);
    this.dateService.setLocale(language);
  }

  changeTooglePreference(key: string, value: string) {
    if (value === 'true') {
      this.setPreference(key, 'false');
    } else {
      this.setPreference(key, 'true');
    }
  }

  changeStatus6(key: string, value: string) {
    if (value != undefined && value != '') {
      this.setPreference(key, null);
    } else {
      this.setPreference(
        key,
        this.datetimeService.format(
          this.datetimeService.now(),
          'YYYY-MM-DD HH:mm:ss',
        ),
      );
    }
  }
}
