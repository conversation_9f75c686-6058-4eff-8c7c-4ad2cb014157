import { Injectable } from '@angular/core';
import { RestService } from '@eportal/core';
import { Observable } from 'rxjs';

@Injectable()
export class Backup112Service {
  constructor(private restService: RestService) {}

  /**
   * Send request for reloading scheduler task
   * @param taskUrl the task url
   */
  public reloadSchedulerTask(): Observable<any> {
    return this.restService.all('scheduler', 'exportBackup112').get();
  }

  /**
   * Send request for reloading scheduler task
   * @param taskUrl the task url
   */
  public changeConfig(): Observable<any> {
    return this.restService.all('configuration', 'backupMode').post({});
  }
}
