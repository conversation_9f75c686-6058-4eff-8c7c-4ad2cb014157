<ng-container *ngFor="let oneFunction of allFunctionsSorted; index as $index; first as $first">

  <cgdis-portal-group-field [name]="groupUid($index)" [uid]="groupUid($index)"  [parentArrayUid]="parentUID"></cgdis-portal-group-field>

  <cgdis-portal-input-field [name]="'tecid'" [parentGroupUid]="groupUid($index)" [initialValue]="oneFunction.tecid"
                            [visible]="false" [disableIfNotVisible]="false"></cgdis-portal-input-field>

  <cgdis-portal-input-field [name]="'teclock'" [parentGroupUid]="groupUid($index)" [initialValue]="oneFunction.teclock"
                            [visible]="false" [disableIfNotVisible]="false"></cgdis-portal-input-field>


  <!--<div class="row " *ngIf="$first">
    <div class="col-6"></div>
    <div *ngIf="containsPartialFulfill()" class="col-3 functions-helper-text smaller" [translate]="'admin.service_plan_model.version.position.function.partialfullfill'"></div>
    <div *ngIf="!containsPartialFulfill()" class="col-3 functions-helper-text smaller"></div>
    <div class="col-3"></div>
  </div>-->

  <div class="row no-gutters form-item displayFunction">
    <label class="col-11 txtleft likeInput" [translate]="oneFunction.label"></label>
    <!--<div *ngIf="containsPartialFulfill()" class="col-3">
      <cgdis-portal-link-with-icon
        *ngIf="oneFunction.partialFulfill"
        [icon]="'icon-valider'"  [smallIcon]="true"
                                   ></cgdis-portal-link-with-icon>
    </div>
    <div *ngIf="!containsPartialFulfill()" class="col-3"></div>-->
    <div class="col-1" *ngIf="canEdit">
      <cgdis-portal-link-with-icon-delete
        class="icon-margin-top-0"
        [rounded]="true"
        [smallIcon]="true"
        [tooltipText]="'tooltip.delete' | translate"
        (click)="delete(oneFunction)"></cgdis-portal-link-with-icon-delete>
    </div>
  </div>

</ng-container>
