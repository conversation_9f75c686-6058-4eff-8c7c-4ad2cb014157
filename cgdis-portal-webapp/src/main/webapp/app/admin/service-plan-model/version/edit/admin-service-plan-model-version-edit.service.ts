import { map } from 'rxjs/operators';
import { Injectable } from '@angular/core';
import { DefaultFormService } from '../../../../common/modules/form-module/service/default-form.service';
import { UpdateAdminServicePlanModelVersionForm } from './update-admin-service-plan-model-version-form.model';
import { ServicePlanModelVersion } from '../../../../model/service-plan-model-version';
import { Observable } from 'rxjs';
import { ToastService } from '../../../../common/shared/toasts/CGDISToastService';
import { RestService } from '@eportal/core';
import { FormError } from '../../../../common/modules/error-management/model/form-error.model';
import { FormErrorService } from '../../../../common/modules/form-module/service/form-error.service';
import { ServicePlanModelVersionEditable } from '../../../../model/service-plan-model-version-editable';
import { ActivatedRoute, Router } from '@angular/router';

@Injectable()
export class AdminServicePlanModelVersionEditService extends DefaultFormService<
  UpdateAdminServicePlanModelVersionForm,
  ServicePlanModelVersion
> {
  private baseUrl = ['admin', 'service-plan-model', 'versions'];

  constructor(
    toastService: ToastService,
    private restService: RestService,
    formErrorService: FormErrorService,
    private route: ActivatedRoute,
    private router: Router,
  ) {
    super(
      toastService,
      'admin.service_plan_model.version.edit.success',
      formErrorService,
    );
  }

  submit(
    form: UpdateAdminServicePlanModelVersionForm,
  ): Observable<ServicePlanModelVersion> {
    return this.restService
      .all<ServicePlanModelVersion>('admin', 'service-plan-model', 'versions')
      .update(form)
      .pipe(
        map((value) => {
          return value;
        }),
      );
  }

  submitSuccess(result: ServicePlanModelVersion): void {
    this.router.navigate(['../../../'], { relativeTo: this.route });
  }

  submitError(formError: FormError): void {}

  /**
   * Get one version
   * @param {number} tecid
   * @return {Observable<ServicePlanModelVersionEditable>}
   */
  get(tecid: number): Observable<ServicePlanModelVersionEditable> {
    return this.restService
      .one<ServicePlanModelVersionEditable>(
        'admin',
        'service-plan-model',
        'versions',
        String(tecid),
        'functions',
      )
      .get()
      .pipe(
        map((value: ServicePlanModelVersionEditable) => {
          return value;
        }),
      );
  }
}
