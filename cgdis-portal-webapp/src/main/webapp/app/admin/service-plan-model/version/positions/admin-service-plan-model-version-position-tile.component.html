<cgdis-portal-group-field [name]="groupName()" [uid]="groupUID" [parentArrayUid]="data.arrayName" [visible]="false" [disableIfNotVisible]="false"></cgdis-portal-group-field>

<cgdis-portal-tile [id]="data.id" (onClose)="closeItem()" [showCloseButton]="canEdit" >


  <cgdis-portal-input-field [name]="'tecid'" #tecId [visible]="false" [disableIfNotVisible]="false"
                            [parentGroupUid]="groupUID"
                            [initialValue]="data.initialTecid"></cgdis-portal-input-field>
  <cgdis-portal-input-field [name]="'teclock'" [visible]="false" [disableIfNotVisible]="false"
                            [parentGroupUid]="groupUID"
                            [initialValue]="data.initialTeclock"></cgdis-portal-input-field>


  <!-- Label -->
  <cgdis-portal-input-field
    [name]="'label'"
    [label]="'Label'"
    [fieldReadonly]="!canEdit"
    [parentGroupUid]="groupUID"
    [fieldRequired]="true"
    [inline]="true"
    [initialValue]="data.initialLabel">

  </cgdis-portal-input-field>


  <cgdis-portal-input-field
    #orderField
    [name]="'order'"
    [label]="'order'"
    [parentGroupUid]="groupUID"
    [initialValue]="data.initialOrder"
    [visible]="false"
    [disableIfNotVisible]="false"
  >

  </cgdis-portal-input-field>


  <cgdis-portal-select-field [name]="!canEdit ? 'positionTemplateIdReadOnly' : 'positionTemplateId'"
                             [initialValue]="initialPositionTemplate"
                             [label]="'Position template'"
                             [fieldReadonly]="!canEdit"
                             [fieldRequired]="!canEdit"
                             [inline]="true"
                             [possibleValues]="positionTemplatePossibleValues"
                             [parentGroupUid]="groupUID"
  ></cgdis-portal-select-field>

  <div class="row">
  <label class="custom-label col-5" [translate]="'admin.service_plan_model.version.position.function.operational_function'.concat(isMobile?'_mobile':'')"></label>
  <cgdis-portal-array-field [name]="'functions'" [uid]="functionsUID" [parentGroupUid]="groupUID"></cgdis-portal-array-field>
  <cgdis-portal-admin-service-plan-model-version-position-tile-function class="col-6" style="padding: 0;"
    [parentUID]="functionsUID"
    [allFunctions]="allFunctions"
    (onDelete)="delete($event)"
    [canEdit]="canEdit">


  </cgdis-portal-admin-service-plan-model-version-position-tile-function>
  </div>

  <div class="row no-gutters" *ngIf="canEdit && allFunctions.length < 4">
    <div class="col-9">

      <cgdis-portal-select-field #functionSelection
                                 [possibleValues]="functionsPossibleValues" (onValueChange)="selectFunction($event)">

      </cgdis-portal-select-field>
    </div>
    <div class="col-3 sameMarginThanFormItem">


      <cgdis-portal-link-with-icon-add class="-center"
                                       [linkOK]="true"
                                       [rounded]="true"
                                       [tooltipText]="'tooltip.add' | translate"
                                       (click)="addFunction()"
                                       [smallIcon]="true"></cgdis-portal-link-with-icon-add>
    </div>
  </div>

</cgdis-portal-tile>
