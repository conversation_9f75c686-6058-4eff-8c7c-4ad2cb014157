import { Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { FORM_SERVICE } from '../../../common/modules/form-module/service/iform.service';
import { AdminServicePlanModelCopyService } from './admin-service-plan-model-copy.service';
import { ServicePlanModel } from '../../../model/service-plan-model';
import { ActivatedRoute, Router } from '@angular/router';
import { Subscription } from 'rxjs';
import _ from 'lodash';
import { TranslateService } from '@ngx-translate/core';
import { NavigationHistoryService } from '../../../common/shared/services/navigation.history.service';
import { BreakpointObserver, BreakpointState } from '@angular/cdk/layout';

@Component({
  selector: 'cgdis-portal-admin-service-plan-model-copy',
  templateUrl: './admin-service-plan-model-copy.component.html',
  providers: [
    AdminServicePlanModelCopyService,
    {
      provide: FORM_SERVICE,
      useExisting: AdminServicePlanModelCopyService,
    },
  ],
})
export class AdminServicePlanModelCopyComponent implements OnInit, OnDestroy {
  /**
   * Service plan model to copy
   */
  copyServicePlanModel: ServicePlanModel;

  /**
   * Model id of the copy model
   */
  id: number;

  private subscriptions: Subscription[] = [];

  constructor(
    private route: ActivatedRoute,
    private navigationHistory: NavigationHistoryService,
    public service: AdminServicePlanModelCopyService,
    private breakpointObserver: BreakpointObserver,
    private router: Router,
    private translateService: TranslateService,
  ) {
    this.subscriptions.push(
      this.breakpointObserver
        .observe(['(max-width: 576px)'])
        .subscribe((result: BreakpointState) => {
          if (result.matches) {
            this.cancel();
          }
        }),
    );
  }

  ngOnInit() {
    this.subscriptions.push(
      this.route.params.subscribe((params) => {
        this.id = params['modelId'];
        this.loadServicePlanModel();
      }),
    );

    this.subscriptions.push(
      this.service.onModelUpdated().subscribe((newServicePlanModel) => {
        this.afterServicePlanModelUpdated(newServicePlanModel);
      }),
    );
  }

  private loadServicePlanModel() {
    this.service.getCopy(this.id).subscribe((value) => {
      // Change the name
      value.name = this.translateService.instant(
        'admin.service_plan_model.copy.new_name',
        { name: value.name },
      );
      this.afterServicePlanModelUpdated(value);
    });
  }

  /**
   * Initialize values using the service plan model
   */
  private afterServicePlanModelUpdated(newServicePlanModel: ServicePlanModel) {
    if (newServicePlanModel != undefined) {
      this.copyServicePlanModel = newServicePlanModel;
      this.service.resetForm();
    }
  }

  ngOnDestroy(): void {
    _.each(this.subscriptions, (oneSubscription) =>
      oneSubscription.unsubscribe(),
    );
  }

  public cancel(): void {
    let param = this.navigationHistory.get(
      'admin-service-plan-model-list-table-Id',
    );
    if (param) {
      this.router.navigate(['list'], {
        relativeTo: this.route.parent,
        queryParams: { filter: param },
      });
    } else {
      this.router.navigate(['list'], { relativeTo: this.route.parent });
    }
  }
}
