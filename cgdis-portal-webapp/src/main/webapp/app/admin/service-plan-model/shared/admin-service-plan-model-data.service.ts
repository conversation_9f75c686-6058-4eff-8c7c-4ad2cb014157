import { Injectable, Injector } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { PositionTemplate } from '@app/model/position-template.model';
import { OperationalFunction } from '@app/model/operational-function.model';

@Injectable()
export class AdminServicePlanModelDataService {
  /**
   * All functions available for positions
   * @type {any[]}
   */
  allFunctionsSubject = new BehaviorSubject<OperationalFunction[]>([]);

  /**
   * All functions available for positions
   * @type {any[]}
   */
  allPositionTemplate = new BehaviorSubject<PositionTemplate[]>([]);

  constructor(private injector: Injector) {}

  public getPositions(): Observable<PositionTemplate[]> {
    return this.allPositionTemplate.asObservable();
  }

  public getFunctions(): Observable<OperationalFunction[]> {
    return this.allFunctionsSubject.asObservable();
  }

  public setPositions(positions: PositionTemplate[]) {
    this.allPositionTemplate.next(positions);
  }

  public setFunctions(positions: OperationalFunction[]) {
    this.allFunctionsSubject.next(positions);
  }
}
