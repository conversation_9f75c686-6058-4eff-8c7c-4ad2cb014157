import { Injectable } from '@angular/core';
import { ServicePlanModelWithEditable } from '../../../model/service-plan-model-with-editable';
import { IAllRestResource, RestService } from '@eportal/core';
import { ServicePlanModel } from '../../../model/service-plan-model';
import { ToastService } from '../../../common/shared/toasts/CGDISToastService';
import { DefaultDeleteService } from '../../../common/shared/services/default/default-delete-service';

@Injectable()
export class AdminServicePlanModelDeleteService extends DefaultDeleteService<ServicePlanModelWithEditable> {
  baseUrl: string[] = ['admin', 'service-plan-model'];

  _restResource: IAllRestResource<ServicePlanModelWithEditable>;

  constructor(
    private restService: RestService,
    toastService: ToastService,
  ) {
    super(toastService);
    this._restResource = restService.all<ServicePlanModel>(...this.baseUrl);
  }

  protected deleteResource(servicePlanModel: ServicePlanModelWithEditable) {
    return this._restResource.one(String(servicePlanModel.tecid));
  }
}
