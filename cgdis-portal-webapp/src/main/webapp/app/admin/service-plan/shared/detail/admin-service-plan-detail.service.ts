import { ServicePlan } from '../../../../model/service-plan.model';
import { DefaultFormService } from '../../../../common/modules/form-module/service/default-form.service';
import { ToastService } from '../../../../common/shared/toasts/CGDISToastService';
import { FormErrorService } from '../../../../common/modules/form-module/service/form-error.service';

export abstract class AdminServicePlanDetailService<
  FORM,
> extends DefaultFormService<FORM, ServicePlan> {
  constructor(
    toastr: ToastService,
    submitSuccessMessage: string = null,
    formErrorService: FormErrorService,
  ) {
    super(toastr, submitSuccessMessage, formErrorService);
  }
}
