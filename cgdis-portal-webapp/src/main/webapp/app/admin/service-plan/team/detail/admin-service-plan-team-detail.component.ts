import { first } from 'rxjs/operators';
import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Inject,
  Input,
  OnDestroy,
  OnInit,
} from '@angular/core';
import { TeamWithMembers } from '@app/model/serviceplan/team/team.model';
import {
  FORM_SERVICE,
  IFormService,
} from '@app/common/modules/form-module/service/iform.service';
import { AdminServicePlanTeamFormsModel } from '@app/admin/service-plan/team/admin-service-plan-team-forms.model';
import { ServicePlanService } from '@app/common/shared/services/service-plan.service';
import { ServicePlanModelVersionWithPositions } from '@app/model/service-plan-model-version-with-positions.model';
import { BreakpointObserver, BreakpointState } from '@angular/cdk/layout';
import { Subscription } from 'rxjs';

@Component({
  selector: 'cgdis-portal-admin-service-plan-team-detail',
  templateUrl: './admin-service-plan-team-detail.component.html',
  styles: [],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AdminServicePlanTeamDetailComponent implements OnInit, OnDestroy {
  @Input() servicePlanId: number;

  @Input() team: TeamWithMembers;

  servicePlanModelVersionWithPositions: ServicePlanModelVersionWithPositions;

  isMobile: boolean = false;

  subscriptions: Subscription[] = [];

  constructor(
    @Inject(FORM_SERVICE)
    formService: IFormService<AdminServicePlanTeamFormsModel, TeamWithMembers>,
    private cd: ChangeDetectorRef,
    private servicePlanService: ServicePlanService,
    private breakpointObserver: BreakpointObserver,
  ) {
    this.subscriptions.push(
      this.breakpointObserver
        .observe(['(max-width: 992px)'])
        .subscribe((result: BreakpointState) => {
          this.isMobile = result.matches;
          this.cd.markForCheck();
        }),
    );
  }

  ngOnInit() {
    this.servicePlanService
      .getPositionsForAdmin(this.servicePlanId)
      .pipe(first())
      .subscribe((value) => {
        this.servicePlanModelVersionWithPositions = value;
        this.cd.markForCheck();
      });
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((subscription) => {
      subscription.unsubscribe();
    });
  }
}
