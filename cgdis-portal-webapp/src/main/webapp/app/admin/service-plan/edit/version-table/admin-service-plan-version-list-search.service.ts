import { map } from 'rxjs/operators';
import { Injectable } from '@angular/core';
import { UntypedFormBuilder } from '@angular/forms';
import { RestService } from '@eportal/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Location } from '@angular/common';
import { ServicePlanVersion } from '../../../../model/service-plan-version.model';
import { CgdisDatatableService } from '../../../../common/modules/datatable/cgdisdatatable-service';
import { SimplePopupService } from '../../../../common/modules/popup/simple-popup.service';
import { Observable } from 'rxjs';

@Injectable()
export class AdminServicePlanVersionListSearchService extends CgdisDatatableService<ServicePlanVersion> {
  constructor(
    public restService: RestService,
    fb: UntypedFormBuilder,
    location: Location,
    router: Router,
    route: ActivatedRoute,
    popupService: SimplePopupService,
  ) {
    super(fb, location, router, route, popupService);
  }

  public initDataList(servicePlanTecid: number): void {
    super.initDataResourceList(
      this.restService.all(
        'serviceplanversion',
        String(servicePlanTecid),
        'versions',
      ),
    );
  }

  /**
   * Get all existing versions of a service plan
   * @return {Observable<ServicePlan[]>}
   */
  isClosed(servicePlanTecid: number): Observable<boolean> {
    const restResource = this.restService.one(
      'admin',
      'service-plan',
      String(servicePlanTecid),
      'is-closed',
    );
    return restResource.get().pipe(
      map((value: boolean) => {
        return value;
      }),
    );
  }
}
