import { Injectable } from '@angular/core';
import { CgdisDatatableService } from '@app/common/modules/datatable/cgdisdatatable-service';
import { Team } from '@app/model/serviceplan/team/team.model';
import { SimplePopupService } from '@app/common/modules/popup/simple-popup.service';
import { UntypedFormBuilder } from '@angular/forms';
import { Location } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { RestService } from '@eportal/core';

@Injectable()
export class AdminServicePlanTeamsListSearchService extends CgdisDatatableService<Team> {
  constructor(
    public restService: RestService,
    fb: UntypedFormBuilder,
    location: Location,
    router: Router,
    route: ActivatedRoute,
    popupService: SimplePopupService,
  ) {
    super(fb, location, router, route, popupService, true);
  }

  public initDataList(servicePlanTecid: number): void {
    super.initDataResourceList(
      this.restService
        .all('admin')
        .all('service-plan', String(servicePlanTecid), 'teams'),
    );
  }

  // protected mapSearchResults(obj: any): IPage<Team> {
  //    const teamIPage = super.mapSearchResults(obj);
  //    teamIPage.content = teamIPage.content.map((item) => this.mapTeam(item));
  //    return teamIPage;
  // }
  //
  // private mapTeam(team: Team): Team {
  //   let newTeam = new Team(team);
  //   if (newTeam.members != undefined ) {
  //     newTeam.members = newTeam.members.map(one => new TeamMember(one));
  //   }
  //
  //   if (newTeam.memberByPositions != undefined ) {
  //     newTeam.memberByPositions.forEach((value, key, map) => map.set(key, new TeamMember(value)));
  //   }
  //   return newTeam;
  // }

  // /**
  //  * Get all existing versions of a service plan
  //  * @return {Observable<ServicePlan[]>}
  //  */
  // getTeams(servicePlanTecid: number): Observable<ServicePlanVersion[]> {
  //   const restResource = this.restService.one('serviceplanversion', String(servicePlanTecid), 'versions');
  //   return restResource.get().map((value: ServicePlanVersion[]) => {
  //     return value['content'];
  //   });
  // }
}
