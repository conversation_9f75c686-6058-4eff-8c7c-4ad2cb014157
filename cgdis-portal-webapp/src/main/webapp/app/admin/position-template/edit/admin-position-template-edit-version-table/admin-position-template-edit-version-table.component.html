<cgdis-portal-button-link *ngIf="isMobile" (click)="showFilter = !showFilter; updateFilterNumber()">
  <span *ngIf="!showFilter" before-icon>{{'service_plan.filter-link' | translate}} ({{numberOfFilters}})</span>
  <span *ngIf="showFilter" before-icon>{{'service_plan.filter-link-toclose' | translate}} ({{numberOfFilters}})</span>
</cgdis-portal-button-link>
<ng-container *ngIf="isMobile">
  <div class="row search-filter" [hidden]="!showFilter">
    <div class="col-md-2">
      <label class="form-label" [translate]="'admin.position_template.version.list.header.label'"></label>
      <cgdis-portal-datatable-text-filter (onValueChanged)="updateFilterNumber()" [filterName]="'label'"
                                          [allowClear]="true"
                                          [datatableService]="searchPositionTemplateVersionService"></cgdis-portal-datatable-text-filter>
    </div>
    <div class="col-md-2">
      <label class="form-label" [translate]="'admin.position_template.version.list.header.startdate_mobile'"></label>
      <cgdis-portal-datatable-datepicker-filter (onValueChanged)="updateFilterNumber()" [filterName]="'startDate'"
                                                [filterConfig]="startDateFilterConfig"
                                                [datatableService]="searchPositionTemplateVersionService"></cgdis-portal-datatable-datepicker-filter>
    </div>
    <div class="col-md-2">
      <label class="form-label" [translate]="'admin.position_template.version.list.header.enddate_mobile'"></label>
      <cgdis-portal-datatable-datepicker-filter (onValueChanged)="updateFilterNumber()" [filterName]="'endDate'"
                                                [filterConfig]="endDateFilterConfig"
                                                [datatableService]="searchPositionTemplateVersionService"></cgdis-portal-datatable-datepicker-filter>
    </div>
  </div>
</ng-container>

<!-- service plan list table-->
<cgdis-portal-panel [roles]="['ROLE_PERMISSION_ADMIN_POSITION_TEMPLATE_VERSION']">
  <cgdis-portal-cgdisdatatable
    [datatableService]="searchPositionTemplateVersionService"
    [id]="'admin-position-template-version-list-table-Id'"
    [class]="'service_plan_model_version__table'"
    [sorts]="[{dir:'asc',prop:'startDate'}]"
    [rowClickable]="true"
    (rowClicked)="goToPositionTemplateVersion($event)"
    *ngIf="showTable"
  >

    <!-- label (string) -->
    <ep-datatable-column [columnName]="'label'" [flexGrow]="1">
      <ng-template epDatatableHeader>
        {{'admin.position_template.version.list.header.label' | translate}}
      </ng-template>
      <ng-template epDatatableCell let-context>
        {{context.row.label}}
      </ng-template>
      <ng-template epDatatableFilter>
        <cgdis-portal-datatable-text-filter [filterName]="'label'"
                                            [placeholder]="''"
                                            [allowClear]="true"
                                            [datatableService]="searchPositionTemplateVersionService"></cgdis-portal-datatable-text-filter>
      </ng-template>
    </ep-datatable-column>

    <!-- startDate (date) -->
    <ep-datatable-column [columnName]="'startDate'" [flexGrow]="1">
      <ng-template epDatatableHeader>
        <span
          [translate]="isMobile ? 'admin.position_template.version.list.header.startdate_mobile' : 'admin.position_template.version.list.header.startdate'"></span>
      </ng-template>
      <ng-template epDatatableCell let-context>
        {{getFormattedDate(context.row.startDate)}}
      </ng-template>
      <!--<ng-template epDatatableFilter>-->
      <!--<cgdis-portal-datatable-text-filter [filterName]="'startDate'"-->
      <!--[datatableService]="searchServicePlanModelVersionService"></cgdis-portal-datatable-text-filter>-->
      <!--</ng-template>-->
      <ng-template epDatatableFilter>
        <cgdis-portal-datatable-datepicker-filter
          [filterName]="'startDate'"
          [filterConfig]="startDateFilterConfig"
          [datatableService]="searchPositionTemplateVersionService"></cgdis-portal-datatable-datepicker-filter>
      </ng-template>
    </ep-datatable-column>

    <!-- endDate (date) -->
    <ep-datatable-column [columnName]="'endDate'" [flexGrow]="1">
      <ng-template epDatatableHeader>
        <span
          [translate]="isMobile ? 'admin.position_template.version.list.header.enddate_mobile' : 'admin.position_template.version.list.header.enddate'"></span>
      </ng-template>
      <ng-template epDatatableCell let-context>
        {{getFormattedDate(context.row.endDate)}}
      </ng-template>
      <ng-template epDatatableFilter>
        <cgdis-portal-datatable-datepicker-filter
          [filterName]="'endDate'"
          [filterConfig]="endDateFilterConfig"
          [datatableService]="searchPositionTemplateVersionService"></cgdis-portal-datatable-datepicker-filter>
      </ng-template>
    </ep-datatable-column>

    <!-- Actions -->
    <ep-datatable-column [columnName]="'actions'" [sortable]="false" [flexGrow]="0.8">
      <ng-template epDatatableHeader>
        {{ 'admin.position_template.list.header.actions' | translate }}
      </ng-template>
      <ng-template let-context epDatatableCell [disableClickOnCell]="true">
        <cgdis-portal-simple-table-button [roles]="['ROLE_PERMISSION_ADMIN_POSITION_TEMPLATE_VERSION_DELETE']"
                                          (onclick)="deletePositionTemplateVersion(context.row)">
          <cgdis-portal-link-with-icon-delete [rounded]="true" [linkDisabled]="!context.row.deletable"
                                              [tooltipText]="'tooltip.delete' | translate"
                                              [smallIcon]="true"></cgdis-portal-link-with-icon-delete>
        </cgdis-portal-simple-table-button>
        <cgdis-portal-simple-table-button [roles]="['ROLE_PERMISSION_ADMIN_POSITION_TEMPLATE_VERSION_CLOSURE']"
                                          (onclick)="closePositionTemplateVersion(context.row)">
          <cgdis-portal-link-with-icon-closure [linkDisabled]="!context.row.closurable" [rounded]="true"
                                               [tooltipText]="'tooltip.closure' | translate"
                                               [smallIcon]="true"></cgdis-portal-link-with-icon-closure>
        </cgdis-portal-simple-table-button>
      </ng-template>
    </ep-datatable-column>


  </cgdis-portal-cgdisdatatable>

  <cgdis-portal-button-primary *ngIf="!closed" [roles]="['ROLE_PERMISSION_ADMIN_POSITION_TEMPLATE_VERSION_CREATE']"
                               (click)="createNewVersion()" [buttonClasses]="['right', 'margin']">
    <span [translate]="'admin.position_template.version.form.button.addversion'"></span>
  </cgdis-portal-button-primary>
</cgdis-portal-panel>

