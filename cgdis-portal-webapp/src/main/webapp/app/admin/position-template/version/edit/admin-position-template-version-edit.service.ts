import { map } from 'rxjs/operators';
import { Injectable } from '@angular/core';
import { DefaultFormService } from '@app/common/modules/form-module/service/default-form.service';
import { ToastService } from '@app/common/shared/toasts/CGDISToastService';
import { RestService } from '@eportal/core';
import { FormErrorService } from '@app/common/modules/form-module/service/form-error.service';
import { ActivatedRoute, Router } from '@angular/router';
import { Observable } from 'rxjs';
import { FormError } from '@app/common/modules/error-management/model/form-error.model';
import { UpdateAdminPositionTemplateVersionForm } from '@app/admin/position-template/version/edit/update-admin-position-template-version-form.model';
import {
  PositionTemplateVersion,
  PositionTemplateVersionWithEditable,
} from '@app/model/position-template-version.model';

@Injectable()
export class AdminPositionTemplateVersionEditService extends DefaultFormService<
  UpdateAdminPositionTemplateVersionForm,
  PositionTemplateVersion
> {
  private baseUrl = ['admin', 'position-template-version'];

  constructor(
    toastService: ToastService,
    private restService: RestService,
    formErrorService: FormErrorService,
    private route: ActivatedRoute,
    private router: Router,
  ) {
    super(
      toastService,
      'admin.service_plan_model.version.edit.success',
      formErrorService,
    );
  }

  submit(
    form: UpdateAdminPositionTemplateVersionForm,
  ): Observable<PositionTemplateVersion> {
    return this.restService
      .all<PositionTemplateVersion>(...this.baseUrl)
      .update(form)
      .pipe(
        map((value) => {
          return value;
        }),
      );
  }

  submitSuccess(result: PositionTemplateVersion): void {
    this.router.navigate(['../../../'], { relativeTo: this.route });
  }

  submitError(formError: FormError): void {}

  /**
   * Get one version
   * @param {number} tecid
   * @return {Observable<ServicePlanModelVersionEditable>}
   */
  get(tecid: number): Observable<PositionTemplateVersionWithEditable> {
    return this.restService
      .one<PositionTemplateVersionWithEditable>(
        'admin',
        'position-template-version',
        String(tecid),
        'editables',
      )
      .get()
      .pipe(
        map((value: PositionTemplateVersionWithEditable) => {
          return value;
        }),
      );
  }
}
