import { map } from 'rxjs/operators';
import { RestService } from '@eportal/core';
import { Observable } from 'rxjs';
import { EntityCategory } from '../../../../model/entity-category.model';
import { Injectable } from '@angular/core';

@Injectable()
export class EntityCategoryService {
  /**
   * Based url to access entity categories
   * @type {string[]}
   */
  baseUrl: string[] = ['entity_category'];

  constructor(private _restService: RestService) {}

  /**
   * Get all entity categories
   * @returns {Observable<EntityCategory[]>}
   */
  getAll(): Observable<EntityCategory[]> {
    return this._restService
      .all(...this.baseUrl, 'all')
      .get()
      .pipe(
        map((value) => {
          return value;
        }),
      );
  }
}
