<cgdis-portal-default-form-template>
  <cgdis-portal-form [customButtonsRow]="true" [formId]="'admin-entity-update-id'" [formClasses]="['row']" (onCancel)="cancel()" [formReadonly]="editForbidden">

    <cgdis-portal-admin-entity-detail [readonly]="editForbidden" [service]="service" [entity]="entity"></cgdis-portal-admin-entity-detail>

    <cgdis-portal-input-field [name]="'tecid'"   [visible]="false" [disableIfNotVisible]="false" [initialValue]="entityTecid" ></cgdis-portal-input-field>
    <cgdis-portal-input-field [name]="'teclock'" [visible]="false" [disableIfNotVisible]="false" [initialValue]="entityTeclock"></cgdis-portal-input-field>

    <ng-container buttons-row>

      <div class="form-actions small">

        <div class="row justify-content-center">
          <div *ngIf="!editForbidden" style="margin: 1em;    width: 10em;">
            <cgdis-portal-button-primary [block]="true" (click)="submit()" >
              <span before-icon [translate]="'default.button.submit'"></span>
            </cgdis-portal-button-primary>
          </div>
          <div style="margin: 1em;    width: 10em;">
            <cgdis-portal-button-cancel (click)="cancel()" [block]="true" >
              <span before-icon  [translate]="'default.button.back'"></span>
            </cgdis-portal-button-cancel>
          </div>
        </div>
      </div>
    </ng-container>

  </cgdis-portal-form>
</cgdis-portal-default-form-template>

