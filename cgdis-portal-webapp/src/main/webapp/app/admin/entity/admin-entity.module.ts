import { NgModule } from '@angular/core';
import { SharedModule } from '../../common/shared/shared.module';
import { AdminEntityComponent } from './admin-entity.component';
import { ADMIN_ENTITIES_ROUTE } from './admin-entity.route';
import { RouterModule } from '@angular/router';
import { AdminEntityListComponent } from './list/admin-entity-list.component';
import { FormModule } from '../../common/modules/form-module/form.module';
import { SimpleTableModule } from '../../common/modules/simple-table/simple-table.module';
import { DefaultFormTemplateModule } from '../../common/template/default-form-template/default-form-template.module';
import { ConfigModule } from '@eportal/core';
import { TileGroupModule } from '../../common/modules/tile-group/tile-group.module';
import { InputModule } from '../../common/modules/input/input.module';
import { DatatableModule } from '../../common/modules/datatable/datatable.module';
import { EpDatatableModule } from '@eportal/components';
import { ReactiveFormsModule } from '@angular/forms';
import { AdminEntityDetailComponent } from './shared/detail/admin-entity-detail.component';
import { AdminEntityEditComponent } from './edit/admin-entity-edit.component';

@NgModule({
  imports: [
    SharedModule,
    RouterModule.forChild(ADMIN_ENTITIES_ROUTE),
    FormModule,
    SimpleTableModule,
    DefaultFormTemplateModule,
    ConfigModule,
    TileGroupModule,
    InputModule,
    DatatableModule,
    EpDatatableModule,
    ReactiveFormsModule,
  ],
  declarations: [
    AdminEntityComponent,
    AdminEntityListComponent,
    AdminEntityDetailComponent,
    AdminEntityEditComponent,
  ],
  exports: [AdminEntityComponent],
})
export class AdminEntityModule {}
