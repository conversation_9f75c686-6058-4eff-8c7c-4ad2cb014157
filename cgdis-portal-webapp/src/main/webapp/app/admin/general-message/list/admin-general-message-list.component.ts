import { ChangeDetector<PERSON><PERSON>, Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { AdminGeneralMessageListService } from '@app/admin/general-message/list/admin-general-message-list.service';
import { DatetimeService } from '@eportal/core';
import { ConnectedUserService } from '@app/security/connected-user.service';
import { TranslateService } from '@ngx-translate/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Location } from '@angular/common';
import { NavigationHistoryService } from '@app/common/shared/services/navigation.history.service';
import { GeneralMessage } from '@app/model/general-message.model';
import { BreakpointObserver, BreakpointState } from '@angular/cdk/layout';
import { Subscription } from 'rxjs';

@Component({
  selector: 'cgdis-portal-admin-general-message-list',
  templateUrl: './admin-general-message-list.component.html',
  providers: [AdminGeneralMessageListService],
})
export class AdminGeneralMessageListComponent implements OnInit, OnD<PERSON>roy {
  isMobile: boolean = false;

  subscriptions: Subscription[] = [];

  constructor(
    public service: AdminGeneralMessageListService,
    private connectedUserService: ConnectedUserService,
    private translateService: TranslateService,
    private router: Router,
    private location: Location,
    private dateTimeService: DatetimeService,
    private navigationHistory: NavigationHistoryService,
    private route: ActivatedRoute,
    private breakpointObserver: BreakpointObserver,
    private cd: ChangeDetectorRef,
  ) {
    this.subscriptions.push(
      this.breakpointObserver
        .observe(['(max-width: 576px)'])
        .subscribe((result: BreakpointState) => {
          this.isMobile = result.matches;
          this.cd.markForCheck();
          this.cd.markForCheck();
        }),
    );
  }

  ngOnInit(): void {
    this.navigationHistory.clearAll();
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((subscription) => {
      subscription.unsubscribe();
    });
  }

  /**
   * Go the export profile detail
   * @param dataClicked: the export profile clicked
   */
  public goToGeneralMessage(dataClicked: GeneralMessage): void {
    this.navigationHistory.add(
      'admin-export-list-table-Id',
      NavigationHistoryService.getParameterByName(
        'filter',
        this.location.path(),
      ),
    );
    this.router.navigate(['../detail', dataClicked.tecid], {
      relativeTo: this.route,
    });
  }
}
