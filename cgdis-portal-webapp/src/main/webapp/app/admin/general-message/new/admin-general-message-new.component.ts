import { Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { AdminGeneralMessageNewService } from '@app/admin/general-message/new/admin-general-message-new.service';
import { FORM_SERVICE } from '@app/common/modules/form-module/service/iform.service';
import { ActivatedRoute, Router } from '@angular/router';
import { NavigationHistoryService } from '@app/common/shared/services/navigation.history.service';
import { BreakpointObserver, BreakpointState } from '@angular/cdk/layout';
import { Subscription } from 'rxjs';

@Component({
  selector: 'cgdis-portal-admin-general-message-new',
  templateUrl: './admin-general-message-new.component.html',
  providers: [
    AdminGeneralMessageNewService,
    {
      provide: FORM_SERVICE,
      useExisting: AdminGeneralMessageNewService,
    },
  ],
})
export class AdminGeneralMessageNewComponent implements OnInit, OnDestroy {
  subscriptions: Subscription[] = [];

  constructor(
    private route: ActivatedRoute,
    private navigationHistory: NavigationHistoryService,
    private breakpointObserver: BreakpointObserver,
    private router: Router,
  ) {
    this.subscriptions.push(
      this.breakpointObserver
        .observe(['(max-width: 576px)'])
        .subscribe((result: BreakpointState) => {
          if (result.matches) {
            this.cancel();
          }
        }),
    );
  }

  ngOnInit(): void {}

  ngOnDestroy(): void {
    this.subscriptions.forEach((subscription) => {
      subscription.unsubscribe();
    });
  }

  public cancel(): void {
    let param = this.navigationHistory.get('admin-export-list-table-Id');
    if (param) {
      this.router.navigate(['list'], {
        relativeTo: this.route.parent,
        queryParams: { filter: param },
      });
    } else {
      this.router.navigate(['list'], { relativeTo: this.route.parent });
    }
  }
}
