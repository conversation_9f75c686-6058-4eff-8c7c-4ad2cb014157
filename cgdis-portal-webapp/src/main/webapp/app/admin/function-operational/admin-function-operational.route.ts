import { Routes } from '@angular/router';
import { RoleGuard } from '@app/security/guards/role.guard';
import { IconConstants } from '@app/common/shared/icon/icon-constants';
import { AdminFunctionOperationalComponent } from '@app/admin/function-operational/admin-function-operational.component';
import { AdminFunctionOperationalListComponent } from '@app/admin/function-operational/list/admin-function-operational-list.component';
import { AdminFunctionOperationalEditComponent } from '@app/admin/function-operational/edit/admin-function-operational-edit.component';
import { AdminFunctionOperationalNewComponent } from '@app/admin/function-operational/new/admin-function-operational-new.component';

export const ADMIN_FUNCTION_OPERATIONAL_ROUTE: Routes = [
  {
    path: '',
    component: AdminFunctionOperationalComponent,
    canActivate: [RoleGuard],
    canActivateChild: [RoleGuard],
    data: {
      expectedRoles: ['ROLE_PERMISSION_ADMIN_FUNCTION_OPERATIONAL'],
    },
    children: [
      { path: '', redirectTo: 'list', pathMatch: 'full' },
      {
        path: 'list',
        component: AdminFunctionOperationalListComponent,
        canActivate: [RoleGuard],
        data: {
          expectedRoles: ['ROLE_PERMISSION_ADMIN_FUNCTION_OPERATIONAL'],
          titleKey: 'admin.function_operational.list.title',
          pageHeaderIcons: [
            {
              icon: IconConstants.ADD,
              link: '../../../../admin/function-operational/new',
              rounded: true,
              tooltipText: 'tooltip.new',
              roles: ['ROLE_PERMISSION_ADMIN_FUNCTION_OPERATIONAL_CREATE'],
            },
          ],
        },
      },
      {
        path: 'new',
        component: AdminFunctionOperationalNewComponent,
        canActivate: [RoleGuard],
        data: {
          expectedRoles: ['ROLE_PERMISSION_ADMIN_FUNCTION_OPERATIONAL_CREATE'],
          titleKey: 'admin.function_operational.creation.title',
        },
      },
      {
        path: 'detail/:functionOperationalId',
        component: AdminFunctionOperationalEditComponent,
        canActivate: [RoleGuard],
        data: {
          expectedRoles: ['ROLE_PERMISSION_ADMIN_FUNCTION_OPERATIONAL'],
          titleKey: 'admin.function_operational.update.title',
        },
      },
    ],
  },
];
