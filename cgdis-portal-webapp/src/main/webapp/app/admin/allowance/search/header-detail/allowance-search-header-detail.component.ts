import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Input,
  OnChanges,
  OnInit,
  SimpleChanges,
} from '@angular/core';
import { DateModel } from '@eportal/core';
import { take } from 'rxjs/operators';
import { PrestationAllowanceService } from '@app/admin/allowance/prestation-allowance.service';

@Component({
  selector: 'cgdis-portal-allowance-search-header-detail',
  styleUrls: ['./allowance-search-header-detail.component.scss'],
  templateUrl: './allowance-search-header-detail.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AllowanceSearchHeaderDetailComponent implements OnInit, OnChanges {
  @Input() public entityId: number;
  @Input() public personId: number;
  @Input() public dateRange: { startDate: DateModel; endDate: DateModel };

  public totalAllowances: number;
  public hasAllowances: boolean;
  public totalDurations: number;
  public loading = false;

  private _counter = 0;

  constructor(
    private allowanceService: PrestationAllowanceService,
    private cd: ChangeDetectorRef,
  ) {}

  ngOnInit() {}

  ngOnChanges(changes: SimpleChanges): void {
    const currentCounter = ++this._counter;
    if (this.entityId != undefined && this.dateRange != undefined) {
      this.loading = true;
      this.allowanceService
        .getTotalAllowancesByEntity(
          this.entityId,
          this.dateRange.startDate,
          this.dateRange.endDate,
          this.personId,
        )
        .pipe(take(1))
        .subscribe({
          next: (value: {
            duration: number;
            allowance: number;
            hasAllowance: boolean;
          }) => {
            if (this._counter === currentCounter) {
              if (value != undefined) {
                this.totalDurations = value.duration;
                this.totalAllowances = value.allowance;
                this.hasAllowances = value.hasAllowance;
              } else {
                this.totalDurations = 0;
                this.totalAllowances = 0;
                this.hasAllowances = false;
              }

              this.cd.markForCheck();
            }
          },
          complete: () => {
            this.loading = false;
            this.cd.markForCheck();
          },
        });
    }
  }
}
