import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import { DateModel } from '@eportal/core';

@Injectable()
export class PrestationAllowanceFilterService {
  private _filters = new BehaviorSubject<{
    entityId?: number;
    startDate?: DateModel;
    endDate?: DateModel;
  }>({});

  constructor() {}

  filters() {
    return this._filters.asObservable();
  }

  setFilters(filters: {
    entityId?: number;
    startDate?: DateModel;
    endDate?: DateModel;
  }) {
    this._filters.next(filters);
  }
}
