import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  OnDestroy,
  OnInit,
} from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { BoxService } from '@app/common/shared/services/box.service';
import { NavigationHistoryService } from '@app/common/shared/services/navigation.history.service';
import { TranslateService } from '@ngx-translate/core';
import { ConnectedUserService } from '@app/security/connected-user.service';
import { Subscription } from 'rxjs';
import { Box } from '@app/model/box.model';
import * as _ from 'lodash';

@Component({
  selector: 'cgdis-portal-admin-box-view',
  templateUrl: './admin-box-view.component.html',
  styles: [],
  providers: [BoxService],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AdminBoxViewComponent implements OnInit, OnDestroy {
  /**
   * Service vehicle to display
   */
  box: Box;

  private subscriptions: Subscription[] = [];

  constructor(
    private route: ActivatedRoute,
    public service: BoxService,
    private navigationHistory: NavigationHistoryService,
    private translateService: TranslateService,
    private connectedUserService: ConnectedUserService,
    private router: Router,
    private cd: ChangeDetectorRef,
  ) {}

  public ngOnInit(): void {
    this.box = new Box({});
    this.subscriptions.push(
      this.route.params.subscribe((params) => {
        this.loadBox(params['selectedBoxTecid']);
      }),
    );
  }

  public ngOnDestroy(): void {
    _.each(this.subscriptions, (oneSubscription) =>
      oneSubscription.unsubscribe(),
    );
  }

  /**
   * Load box information
   * @param boxId: the box id
   */
  private loadBox(boxId: number): void {
    this.subscriptions.push(
      this.service.get(boxId).subscribe((value) => {
        if (value != undefined) {
          this.box = value;
        }
        this.cd.markForCheck();
      }),
    );
  }

  /**
   * Go back to the list of boxs
   */
  public back(): void {
    let param = this.navigationHistory.get('admin-box-list-table-Id');
    if (param) {
      this.router.navigate(['list'], {
        relativeTo: this.route.parent,
        queryParams: { filter: param },
      });
    } else {
      this.router.navigate(['list'], { relativeTo: this.route.parent });
    }
  }
}
