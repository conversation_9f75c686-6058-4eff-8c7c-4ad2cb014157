import { Component, OnDestroy, OnInit } from '@angular/core';
import { FORM_SERVICE } from '../../../common/modules/form-module/service/iform.service';
import { ActivatedRoute, Router } from '@angular/router';
import { AdminExportNewService } from './admin-export-new.service';
import { NavigationHistoryService } from '../../../common/shared/services/navigation.history.service';
import { BreakpointObserver, BreakpointState } from '@angular/cdk/layout';
import { Subscription } from 'rxjs';
import { ExportProfile } from '@app/model/export-profile.model';

@Component({
  selector: 'cgdis-portal-admin-export-new',
  templateUrl: './admin-export-new.component.html',
  providers: [
    AdminExportNewService,
    {
      provide: FORM_SERVICE,
      useExisting: AdminExportNewService,
    },
  ],
})
export class AdminExportNewComponent implements OnInit, OnD<PERSON>roy {
  subscriptions: Subscription[] = [];

  protected exportProfile = new ExportProfile({});

  constructor(
    public service: AdminExportNewService,
    private route: ActivatedRoute,
    private navigationHistory: NavigationHistoryService,
    private breakpointObserver: BreakpointObserver,
    private router: Router,
  ) {
    this.subscriptions.push(
      this.breakpointObserver
        .observe(['(max-width: 576px)'])
        .subscribe((result: BreakpointState) => {
          if (result.matches) {
            this.cancel();
          }
        }),
    );
  }

  ngOnInit(): void {}

  ngOnDestroy(): void {
    this.subscriptions.forEach((subscription) => {
      subscription.unsubscribe();
    });
  }

  /**
   * Redirection on cancel with search params
   */
  public cancel(): void {
    let param = this.navigationHistory.get('admin-export-list-table-Id');
    if (param) {
      this.router.navigate(['list'], {
        relativeTo: this.route.parent,
        queryParams: { filter: param },
      });
    } else {
      this.router.navigate(['list'], { relativeTo: this.route.parent });
    }
  }
}
