import { Routes } from '@angular/router';
import { ServicePlanComponent } from './service-plan.component';
import { ServicePlanListComponent } from './list/service-plan-list.component';
import { ServicePlanDetailsComponent } from './details/service-plan-details.component';
import { RoleGuard } from '../../security/guards/role.guard';

export const SERVICE_PLAN_ROUTE: Routes = [
  {
    path: '',
    component: ServicePlanComponent,
    canActivate: [RoleGuard],
    canActivateChild: [RoleGuard],
    data: {
      expectedRoles: ['ROLE_PERMISSION_PDS_LIST'],
    },
    children: [
      {
        path: '',
        component: ServicePlanListComponent,
        canActivate: [RoleGuard],
        data: {
          expectedRoles: ['ROLE_PERMISSION_PDS_LIST_VIEW'],
        },
      },
      {
        path: 'detail/:servicePlanId',
        component: ServicePlanDetailsComponent,
        canActivate: [RoleGuard],
        data: {
          expectedRoles: [
            'ROLE_PERMISSION_PDS_FILL_',
            'ROLE_PERMISSION_PDS_FILL_VIEW',
          ],
          selectedDate: undefined,
        },
      },
    ],
  },
];
