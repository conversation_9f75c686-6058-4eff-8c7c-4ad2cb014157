import { Injectable } from '@angular/core';
import { IOneRestResource, IPage, RestService } from '@eportal/core';
import { Observable } from 'rxjs';
import { PersonLight } from '../../../model/person/person-light.model';

@Injectable()
export class AvailabilityPlanningLogAsService {
  private baseUrl = ['logas'];

  constructor(private restService: RestService) {}

  getPersonsAvailable(search: string): Observable<IPage<PersonLight>> {
    const resource: IOneRestResource<IPage<PersonLight>> = this.restService.one(
      ...this.baseUrl,
      'volunteeravailabilities',
    );
    let map: Record<string, string> = {};
    if (search != undefined) {
      map['search'] = search;
    }
    return resource.get(map);
  }
}
