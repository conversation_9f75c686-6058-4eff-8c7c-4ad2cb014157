import { Injectable } from '@angular/core';
import { IPage, RestService, SearchRequest } from '@eportal/core';
import { News } from '../news-summary/news';
import { Observable } from 'rxjs';

@Injectable()
export class NewsService {
  constructor(private restService: RestService) {}

  get(newsId: string) {
    return this.restService.one<News>('news', newsId, 'detail').get();
  }

  searchNews(
    language: string,
    maxNews: number,
    offset?: number,
  ): Observable<IPage<News>> {
    let searchRequest = new SearchRequest();
    searchRequest.pageSize = maxNews;
    searchRequest.pageNumber = offset;
    return this.restService
      .all<News>('news', String(language))
      .search(searchRequest);
  }
}
