<cgdis-portal-page-template>
  <cgdis-portal-page-header [titleKey]="'news.list.title'"></cgdis-portal-page-header>

  <cgdis-portal-panel class="news-view">

    <ul *ngIf="allNews" class="news-list" (swiperight)="swipeToPrevious()" (swipeleft)="swipeToNext()">

      <cgdis-portal-spinner [loading]="loading" ></cgdis-portal-spinner>

      <li *ngFor="let oneNews of allNews; trackBy: trackById" (click)="goToNewsDetail(oneNews)">
        <cgdis-portal-news-list-item [news]="oneNews"></cgdis-portal-news-list-item>
      </li>
    </ul>

    <div class="news-list news-list__empty align-items-center" *ngIf="allNews == undefined || allNews.length==0">
      <cgdis-portal-spinner [loading]="loading" ></cgdis-portal-spinner>
      <span class="col">{{'news.list.empty' | translate }}</span>
    </div>

    <nav class="news-list__pagination" *ngIf="allNews !== undefined && allNews.length!==0">
      <button [matTooltip]="'tooltip.previous' | translate" class="page-item" [disabled]="!hasPrevious" (click)="goPrevious()">
        <a>❮</a>
      </button>
      <button [matTooltip]="'tooltip.next' | translate" class="page-item" [disabled]="!hasNext" (click)="goNext()">
        <a>❯</a>
      </button>
    </nav>

  </cgdis-portal-panel>
</cgdis-portal-page-template>


