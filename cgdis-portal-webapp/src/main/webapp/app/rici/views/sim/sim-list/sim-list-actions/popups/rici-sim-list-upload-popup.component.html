<cgdis-portal-popup-template
  (onClose)="closePopup()"
  [data]="data">

  <ng-container popupTitle>
    <h4 class="title">
      {{ "rici.sim-list.popup.upload.title" | translate }}
    </h4>
  </ng-container>

  <ng-container popupContent>
    <div class="popup-content-wrapper">
      <div class="initial-state-content"> <!-- Simplified: always in 'initial' state from its own perspective -->
        <p>{{ "rici.sim-list.popup.upload.note" | translate }}</p>

        <div class="compact-columns-info">
          <p>
            <strong>{{ "rici.sim-list.popup.upload.mandatoryFieldsLabel" | translate }}:</strong>
            {{ "rici.sim-list.popup.upload.column.iccid.name" | translate }},
            {{ "rici.sim-list.popup.upload.column.msisdn.name" | translate }},
            {{ "rici.sim-list.popup.upload.column.pin.name" | translate }}
          </p>
          <p>
            <strong>{{ "rici.sim-list.popup.upload.optionalFieldsLabel" | translate }}:</strong>
            {{ "rici.sim-list.popup.upload.column.pager_serial_number.name" | translate }}
          </p>
        </div>

        <div class="download-example">
          <a [href]="exampleCsvUrl" target="_blank" download>
            {{ "rici.sim-list.popup.upload.downloadExample" | translate }}
          </a>
        </div>

        <cgdis-portal-file-upload
          [contentType]="contentType"
          [multiple]="multiple"
          [maxFiles]="maxFiles"
          (fileSelected)="onFileSelected($event)"
          class="file-upload-spacing">
        </cgdis-portal-file-upload>
        <div *ngIf="generalErrorMessage" class="general-error-message alert alert-danger">
          {{ generalErrorMessage | translate }}
        </div>
      </div>
    </div>
  </ng-container>

  <ng-container popupButtons>
    <cgdis-portal-validate-cancel-buttons
      (clickCancel)="closePopup()"
      (clickValidate)="handleImportAction()"
      [labelButtonValidate]="'rici.sim-list.popup.upload.button.import'">
    </cgdis-portal-validate-cancel-buttons>
  </ng-container>
</cgdis-portal-popup-template>
