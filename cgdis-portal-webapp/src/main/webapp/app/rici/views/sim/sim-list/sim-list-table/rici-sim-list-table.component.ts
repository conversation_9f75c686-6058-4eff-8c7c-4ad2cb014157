import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  <PERSON><PERSON><PERSON>roy,
  OnInit,
} from '@angular/core'; // Import ChangeDetectorRef, OnDestroy
import { PageTemplateModule } from '@app/common/template/page-template/page-template.module';
import { SharedModule } from '@app/common/shared/shared.module';
import { RiciSimListTableService } from '@rici/views/sim/sim-list/sim-list-table/rici-sim-list-table.service';
import { DatatableModule } from '@app/common/modules/datatable/datatable.module';
import {
  EpDatatableModule,
  FilterConfig,
  SearchOperator,
} from '@eportal/components';
import { NavigationHistoryService } from '@app/common/shared/services/navigation.history.service';
import { RiciSimCard } from '@app/model/rici/rici-sim-card.model';
import { SimpleTableModule } from '@app/common/modules/simple-table/simple-table.module';
import { UntypedFormControl } from '@angular/forms';
import { Router } from '@angular/router';
import { FieldGroupOption } from '@app/common/modules/form-module/model/field-group-option.model';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { MatTooltipModule } from '@angular/material/tooltip'; // Import MatTooltipModule
import { TranslateService } from '@ngx-translate/core';
import { RiciSimCardStatus } from '@rici/common/enums/sim-card-status.enum';
import { SimpleYesNoPopupData } from '@app/common/modules/popup/yes-no/simple-yes-no-popup-data';

// Removed PagerCacheService import

@Component({
  selector: 'cgdis-portal-rici-sim-list-table',
  standalone: true,
  imports: [
    PageTemplateModule,
    SharedModule,
    DatatableModule,
    EpDatatableModule,
    SimpleTableModule,
    MatTooltipModule, // Add MatTooltipModule here
  ],
  templateUrl: './rici-sim-list-table.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class RiciSimListTableComponent implements OnInit, OnDestroy {
  statuses: FieldGroupOption<any, any>[];
  protected showInactiveSimsFilterConfig = new FilterConfig({
    inUrl: true,
    operator: SearchOperator.eq,
    defaultValue: false,
  });
  protected columnSearchFilterLikeConfig = new FilterConfig({
    operator: SearchOperator.like,
  });
  protected showInactiveSimsFormControlToggle = new UntypedFormControl(false);
  // Type for row data
  protected rowType: RiciSimCard; // This model now includes activePagerId and lastPagerId

  // fetchingPagerId map is no longer needed
  private destroy$ = new Subject<void>(); // Subject to manage subscriptions
  private showDeactivatedSims = false;

  constructor(
    protected riciSimListTableService: RiciSimListTableService,
    private router: Router,
    private cdr: ChangeDetectorRef, // Inject ChangeDetectorRef
    private translateService: TranslateService,
    private navigationHistoryService: NavigationHistoryService,
    // Removed PagerCacheService injection
  ) {}

  ngOnInit(): void {
    this.fetchStatuses();
    this.trackActiveFilters();

    // Restore filter state from navigation history if available
    const savedFilterState =
      this.navigationHistoryService.get('showInactiveSims');
    if (savedFilterState === 'true') {
      this.showInactiveSimsFormControlToggle.setValue(true);
    } else {
      // Check URL parameters if navigation history is empty
      const urlFilterState =
        NavigationHistoryService.getParameterByName('filter');
      if (urlFilterState) {
        try {
          const filterObj = JSON.parse(urlFilterState);
          if (filterObj.criteria && Array.isArray(filterObj.criteria)) {
            const showInactiveFilter = filterObj.criteria.find(
              (c: any) => c.attribute === 'showInactiveSims',
            );
            if (showInactiveFilter && showInactiveFilter.value === true) {
              this.showInactiveSimsFormControlToggle.setValue(true);
            }
          }
        } catch (e) {
          console.error('Error parsing filter from URL', e);
        }
      }
    }

    // Subscribe to data changes to preload pager IDs for visible rows
    this.riciSimListTableService
      .getResults()
      .pipe(takeUntil(this.destroy$))
      .subscribe((data) => {
        // No need to preload pager IDs here anymore
        // Data is augmented by the service during the search call
      });

    // Subscribe to filter changes to save in navigation history
    this.showInactiveSimsFormControlToggle.valueChanges
      .pipe(takeUntil(this.destroy$))
      .subscribe((value) => {
        this.navigationHistoryService.add('showInactiveSims', value.toString());
      });
  }

  // --- Action Handlers ---

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  openDeletePopup(row: any): void {
    if (!this.canDelete(row)) {
      return;
    } // Prevent opening if not allowed
    this.riciSimListTableService.openConfirmationPopupAndRefreshTable(
      new SimpleYesNoPopupData({
        title: 'rici.sim-list.popup.delete.title',
        message: 'rici.sim-list.popup.delete.note',
        messageParams: {
          iccid: row.iccid,
        },
        messageHtml: true,
        labelButtonYes: 'default.button.submit',
        labelButtonNo: 'default.button.cancel',
      }),
      () => this.riciSimListTableService.deleteSimCard(row.tecid),
      undefined,
      'rici.sim-list.popup.delete.success',
      'rici.sim-list.popup.delete.error.error',
    );
  }

  openDeactivatePopup(row: RiciSimCard): void {
    if (!this.canDeactivate(row)) {
      return;
    } // Prevent opening if not allowed

    this.riciSimListTableService.openConfirmationPopupAndRefreshTable(
      new SimpleYesNoPopupData({
        title: 'rici.sim-list.popup.deactivate.title',
        message: 'rici.sim-list.popup.deactivate.confirmation',
        messageParams: {
          iccid: row.iccid,
        },
        messageHtml: true,
        labelButtonYes: 'default.button.submit',
        labelButtonNo: 'default.button.cancel',
      }),
      () => this.riciSimListTableService.deactivateSimCard(row.tecid),
      undefined,
      'rici.sim-list.popup.deactivate.success',
      'rici.sim-list.popup.deactivate.error.error',
    );
  }

  // --- Status and Association Logic ---

  // fetchLastPagerId and preloadPagerIdsForVisibleRows are no longer needed
  // as the data is fetched upfront by the service.

  // --- Button State Helper Functions ---

  // Deletion is not allowed in one cases:
  // 1. If status is ASSOCIEE
  // If cas was associated before and in other state than ASSOCIEE,

  openReactivatePopup(row: RiciSimCard): void {
    if (!this.canReactivate(row)) {
      return;
    } // Prevent opening if not allowed

    this.riciSimListTableService.openConfirmationPopupAndRefreshTable(
      new SimpleYesNoPopupData({
        title: 'rici.sim-list.popup.reactivate.title',
        message: 'rici.sim-list.popup.reactivate.confirmation',
        messageParams: {
          iccid: row.iccid,
        },
        messageHtml: true,
        labelButtonYes: 'default.button.submit',
        labelButtonNo: 'default.button.cancel',
      }),
      () => this.riciSimListTableService.reactivateSimCard(row.tecid),
      undefined,
      'rici.sim-list.popup.reactivate.success',
      'rici.sim-list.popup.reactivate.error.error',
    );
  }

  // deletion is allowed but in the background logical deletion is performed
  canDelete(row: RiciSimCard): boolean {
    if (row?.status === RiciSimCardStatus.ASSOCIEE || row.lastPagerId) {
      return false; // Case 1: Cannot delete if currently associated
    }
    // In all other cases, deletion is allowed
    return true;
  }

  // Condition 2 & 3: Deactivation allowed if previously used ('ASSOCIEE' or 'DISPONIBLE') but not currently 'ASSOCIEE'.
  canDeactivate(row: RiciSimCard): boolean {
    if (row?.status === RiciSimCardStatus.ASSOCIEE || row.activePagerTecid) {
      return false; // Case 1: Cannot delete if currently associated
    }
    return true;
    // Deactivate allowed only if Delete is *not* allowed AND status is ASSOCIEE or DISPONIBLE
    // return row?.status === RiciSimCardStatus.DISPONIBLE;
    // return !this.canDelete(row);
  }

  // Condition 4: Reactivation allowed if 'INACTIVE'.
  canReactivate(row: RiciSimCard): boolean {
    return row?.status === RiciSimCardStatus.INACTIVE;
  }

  // Visibility: Delete icon always visible (but maybe disabled).
  showDeleteIcon(row: RiciSimCard): boolean {
    return true;
  }

  // Visibility: Deactivate icon visible unless INACTIVE.
  showDeactivateIcon(row: RiciSimCard): boolean {
    return row?.status !== RiciSimCardStatus.INACTIVE;
  }

  // --- Tooltip Logic ---

  // Visibility: Reactivate icon visible only if INACTIVE.
  showReactivateIcon(row: RiciSimCard): boolean {
    return row?.status === RiciSimCardStatus.INACTIVE;
  }

  needsPagerIdForTooltip(row: RiciSimCard): boolean {
    return this.canDeactivate(row) || this.canReactivate(row);
  }

  // --- Other Methods ---

  getTooltip(row: RiciSimCard): string {
    if (!this.needsPagerIdForTooltip(row)) {
      return ''; // No tooltip needed
    }

    // lastPagerId is now directly available on the row object
    const lastPagerId = row.lastPagerId;
    const activePagerId = row.activePagerId;
    let tooltipMessage = '';

    if (lastPagerId || activePagerId) {
      // Data is available
      activePagerId
        ? (tooltipMessage =
            `\n` +
            this.translateService.instant(
              'rici.sim-list.actions.active-pager-tooltip',
            ) +
            `: ${activePagerId}`)
        : (tooltipMessage =
            `\n` +
            this.translateService.instant(
              'rici.sim-list.actions.last-pager-tooltip',
            ) +
            `: ${lastPagerId}`);
    } else {
      // Data is not available (either never associated or fetch failed - service handles fetch)
      // Return appropriate text, maybe indicating no history
      tooltipMessage = this.translateService.instant(
        'rici.sim-list.popup.deactivate.no-pager-history',
      ); // Re-use existing key
    }
    return tooltipMessage;
  }

  fetchStatuses() {
    this.statuses = [];

    this.statuses.push(
      new FieldGroupOption({
        value: RiciSimCardStatus.ASSOCIEE.valueOf(),
        label: RiciSimCardStatus.ASSOCIEE.valueOf(),
      }),
    );

    this.statuses.push(
      new FieldGroupOption({
        value: RiciSimCardStatus.DISPONIBLE.valueOf(),
        label: RiciSimCardStatus.DISPONIBLE.valueOf(),
      }),
    );

    if (this.showDeactivatedSims) {
      this.statuses.push(
        new FieldGroupOption({
          value: RiciSimCardStatus.INACTIVE.valueOf(),
          label: RiciSimCardStatus.INACTIVE.valueOf(),
        }),
      );
    }
  }

  trackActiveFilters() {
    this.riciSimListTableService.filtersValues<any>().subscribe((value) => {
      this.showDeactivatedSims = value.showInactiveSims;
      this.fetchStatuses();
    });
  }

  openHistory(row: any) {
    if (row?.iccid) {
      // Create the full URL with the base href and hash
      const baseHref =
        document.getElementsByTagName('base')[0]?.getAttribute('href') || '/';
      const url =
        baseHref +
        '#/audit-management/rici/sim-card?iccid=' +
        encodeURIComponent(row.iccid);

      // Open in the same window
      this.router.navigate(['/audit-management/rici/sim-card'], { queryParams: { iccid: row.iccid } });
    }
  }

  openPagerDetails(activePagerTecid: number) {
    this.router.navigate(['/rici/pagers/edit', activePagerTecid], {
      queryParams: {
        redirectSimList: true,
      },
    });
  }

  getPagerIdForRow(row: RiciSimCard): string | null | undefined {
    // activePagerId is now directly available on the row object
    // Only display if the status is ASSOCIEE
    if (row?.status === RiciSimCardStatus.ASSOCIEE) {
      return row.activePagerId;
    }
    return null; // Return null if not associated or no active pager ID found
  }

  onRowClick(row: RiciSimCard) {
    if (row && row.iccid) {
      // Save the current filter state before navigating
      const showInactiveValue = this.showInactiveSimsFormControlToggle.value;

      // Clear any previous values to avoid conflicts
      this.navigationHistoryService.clearAll();

      // Add the current filter state to navigation history
      this.navigationHistoryService.add(
        'showInactiveSims',
        showInactiveValue.toString(),
      );

      // Navigate to edit page
      this.router.navigate(['/rici/sim-card/edit', row.iccid]);
    } else {
      console.error(
        'Cannot navigate to edit page: row or iccid is missing',
        row,
      );
    }
  }
}
