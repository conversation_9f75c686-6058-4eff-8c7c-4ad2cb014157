<cgdis-portal-popup-template
  (onClose)="closePopup()"
  [data]="data">

  <!--  <cgdis-portal-spinner [loading]="waiting" popupContent>-->
  <!--  </cgdis-portal-spinner>-->

  <ng-container popupTitle>
    <h4 class="title">
      {{ "rici.sim-list.popup.download.title" | translate }}

    </h4>
  </ng-container>
  <ng-container popupContent>
    <div class="popup-content">
      <span>      {{ "rici.sim-list.popup.download.note" | translate }}
      </span>
    </div>
    <cgdis-portal-validate-cancel-buttons (clickCancel)="closePopup()"
                                          (clickValidate)="validateAction()"></cgdis-portal-validate-cancel-buttons>
  </ng-container>
</cgdis-portal-popup-template>
