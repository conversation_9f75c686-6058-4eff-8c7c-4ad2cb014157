import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  OnInit,
} from '@angular/core';
import { CustomValidatorsService } from '@app/common/modules/form-module/validator/custom-validators.service';
import {
  FormBuilder,
  FormsModule,
  ReactiveFormsModule,
  ValidatorFn,
} from '@angular/forms';
import { FORM_SERVICE } from '@app/common/modules/form-module/service/iform.service';
import { SimplePopupService } from '@app/common/modules/popup/simple-popup.service';
import { ActivatedRoute, Router } from '@angular/router';
import { ToastService } from '@app/common/shared/toasts/CGDISToastService';
import { MatDialog } from '@angular/material/dialog';
import { CommonModule } from '@angular/common';
import { MatTooltipModule } from '@angular/material/tooltip';
import { NgxSelectModule } from 'ngx-select-ex';
import { TranslateModule } from '@ngx-translate/core';
import { FormModule } from '@app/common/modules/form-module/form.module';
import { PageTemplateModule } from '@app/common/template/page-template/page-template.module';
import { DefaultFormTemplateModule } from '@app/common/template/default-form-template/default-form-template.module';
import { SharedModule } from '@app/common/shared/shared.module';
import { FieldOption } from '@app/common/modules/form-module/model/field-option.model';
import { SimCardEditionService } from './service/sim-card-edition.service';
import { finalize, take } from 'rxjs/operators';
import { StatusHistoryPopupComponent } from './popups/status-history-popup/status-history-popup.component';
import {
  RiciSimCardPagerStatus,
  RiciSimCardStatus,
  SimCardStatusOptions,
} from '@rici/common/enums/sim-card-status.enum';
import { RestService } from '@eportal/core'; // Import RestService
import { RiciSimCardPager } from '@app/model/rici/rici-sim-card-pager.model';
import { RiciPagerAssignmentType } from '@rici/common/enums/pager.enum';
import {
  PagerAssociationPopupData,
  PagerAssociationPopupDataContent,
} from '@rici/views/sim/sim-create/popup/pager-association-popup/pager-association-popup-data';
import { PagerAssociationPopupComponent } from '@rici/views/sim/sim-create/popup/pager-association-popup/pager-association-popup.component';
import { RiciSimCard } from '@app/model/rici/rici-sim-card.model'; // Import RiciSimCardPager
// Removed PagerCacheService import

@Component({
  selector: 'cgdis-portal-sim-card-edition',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatTooltipModule,
    NgxSelectModule,
    TranslateModule,
    FormModule,
    PageTemplateModule,
    DefaultFormTemplateModule,
    SharedModule,
  ],
  templateUrl: './sim-card-edition.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    SimCardEditionService,
    {
      provide: FORM_SERVICE,
      useExisting: SimCardEditionService,
    },
  ],
})
export class SimCardEditionComponent implements OnInit {
  loading = true;
  simCardTecId: number;
  simCard: RiciSimCard;
  iccid: number;
  iccidValue: number;
  msisdnValue: number;
  pinValue: number;
  statusValue: string;
  pagerIdValue: string;
  pagerSerialValue: string;
  pagerOwnerValue: string;
  pagerOwnerMatriculeValue: string;
  pagerTecIdValue: number; // To store the tecid of the associated pager
  isAssociated: boolean = false; // Flag to track if SIM is associated with a pager

  /**
   * Validators
   */
  validateNumeric: ValidatorFn[];

  statusOptions: FieldOption<string>[] = SimCardStatusOptions.map(
    (option) => new FieldOption({ label: option.label, value: option.value }),
  );

  constructor(
    private popupService: SimplePopupService,
    private fb: FormBuilder,
    private simCardEditionService: SimCardEditionService,
    private router: Router,
    private route: ActivatedRoute,
    private toastr: ToastService,
    private validatorService: CustomValidatorsService,
    private cdr: ChangeDetectorRef,
    private dialog: MatDialog,
    private restService: RestService, // Inject RestService
    // Removed PagerCacheService injection
  ) {
    this.iccid = this.route.snapshot.params['id'];
  }

  /**
   * Filter status options based on current status
   * - If status is ASSOCIEE, we don't allow changing to INACTIVE
   * - If status is DISPONIBLE or INACTIVE, we don't allow changing to ASSOCIEE
   */
  get filteredStatusOptions(): FieldOption<string>[] {
    if (this.statusValue === RiciSimCardStatus.ASSOCIEE) {
      // Si le statut est ASSOCIEE, on ne permet que ASSOCIEE et DISPONIBLE
      return this.statusOptions.filter(
        (option) => option.value !== RiciSimCardStatus.INACTIVE,
      );
    } else if (
      this.statusValue === RiciSimCardStatus.DISPONIBLE ||
      this.statusValue === RiciSimCardStatus.INACTIVE
    ) {
      // Si le statut est DISPONIBLE ou INACTIVE, on ne permet que DISPONIBLE et INACTIVE
      return this.statusOptions.filter(
        (option) => option.value !== RiciSimCardStatus.ASSOCIEE,
      );
    }
    return this.statusOptions;
  }

  openPagerAssociationPopup() {
    // Check if there are already open dialogs and close them first
    if (this.popupService.openDialogs().length > 0) {
      this.popupService.closeAll();
    }

    const popupData = new PagerAssociationPopupData({
      message: 'rici.pagerAssociation.subtitle',
      content: new PagerAssociationPopupDataContent({
        simCard: this.simCard,
        onAssociationSuccess: (result) => {
          // Removed logic to update PagerCacheService
          // Navigate to the SIM card list or detail view after successful association
          this.router.navigate(['/rici']);
        },
      }),
    });

    const popupDialog = this.popupService.open(PagerAssociationPopupComponent, {
      data: popupData,
      disableClose: true, // Don't allow closing by clicking outside
    });

    popupDialog
      .afterClosed()
      .pipe(take(1))
      .subscribe({
        next: (result) => {
          if (result) {
            console.log('Pager association result:', result);
            // Removed logic to update PagerCacheService
            // Navigate to the SIM card list or detail view
            this.router.navigate(['/rici']);
          } else {
            // User cancelled the association, still navigate to the list
            this.router.navigate(['/rici']);
          }
        },
      });
  }

  ngOnInit(): void {
    this.iccidValue = 121364;
    // Init validator
    this.validateNumeric = [this.validatorService.validateNumeric()];
    // Load the SIM card data
    if (this.iccid) {
      this.loadSimCardData(this.iccid);
    } else {
      this.router.navigate(['/rici']);
    }
  }

  loadSimCardData(iccid: number): void {
    this.loading = true;
    this.simCardEditionService
      .getSimCardById(iccid)
      .pipe(
        finalize(() => {
          this.loading = false;
          this.cdr.markForCheck();
        }),
      )
      .subscribe({
        next: (simCard) => {
          this.simCard = simCard;
          this.iccidValue = simCard.iccid;
          this.msisdnValue = simCard.msisdn;
          this.pinValue = simCard.pin;
          this.statusValue = simCard.status;
          this.simCardTecId = simCard.tecid;

          // Store the original status in the service for validation
          this.simCardEditionService.originalStatus = simCard.status;

          // If simCardTecId is set, load the pager data
          if (this.simCardTecId) {
            this.loadPagerData(this.simCardTecId);
          } else {
            console.error(
              'simCardTecId is not set after loading SIM card data',
            );
            this.router.navigate(['/rici']);
          }

          this.cdr.markForCheck();
        },
        error: (error) => {
          console.error('Error loading SIM card data', error);
          this.router.navigate(['/rici']);
        },
      });
  }

  loadPagerData(simTecId: number): void {
    this.loading = true;
    // Directly fetch the active pager association using RestService (removed generic type)
    this.restService
      .one('rici', 'sim-cards', simTecId.toString(), 'pagers', 'active')
      .get()
      .pipe(
        finalize(() => {
          this.loading = false;
          this.cdr.markForCheck();
        }),
      )
      .subscribe({
        // Explicitly type the response object
        next: (simCardPager: RiciSimCardPager) => {
          // Handle empty pager object
          if (
            simCardPager &&
            simCardPager.tecid !== null &&
            simCardPager.status !== RiciSimCardPagerStatus.DEACTIVATED &&
            simCardPager.pager
          ) {
            this.pagerIdValue = simCardPager.pager.pagerId || '';
            this.pagerSerialValue = simCardPager.pager.serialNumber || '';
            this.pagerTecIdValue = simCardPager.pager.tecid; // Store the pager's tecid

            const activeAssignment = simCardPager.pager.activeAssignment;
            if (
              activeAssignment &&
              activeAssignment.assignmentType ===
                RiciPagerAssignmentType.PERSON &&
              'assignedPerson' in activeAssignment &&
              activeAssignment.assignedPerson
            ) {
              const assignedPerson = activeAssignment.assignedPerson;
              this.pagerOwnerValue = `${assignedPerson.firstName} ${assignedPerson.lastName}`;
              this.pagerOwnerMatriculeValue =
                assignedPerson.cgdisRegistrationNumber;
            } else {
              this.pagerOwnerValue = '';
              this.pagerOwnerMatriculeValue = '';
            }

            // Set the associated flag to true if we have pager data
            this.isAssociated = true;
          } else {
            this.pagerIdValue = '';
            this.pagerSerialValue = '';
            this.pagerOwnerValue = '';
            this.pagerOwnerMatriculeValue = '';
            this.isAssociated = false;
            console.warn(
              'No pager data found or pager not assigned to a person for simTecId:',
              simTecId,
            );
          }

          this.cdr.markForCheck();
        },
        error: (error) => {
          console.error('Error loading PAGER data', error);
          // Consider navigating back or showing an error message to the user
          // this.router.navigate(['/rici']);
        },
      });
  }

  onSubmit() {
    this.simCardEditionService
      .onSuccessSubmit()
      .pipe()
      .subscribe({
        next: (result) => {
          this.loading = false;
          console.log('SIM card updated successfully', result);
          this.navigateBackWithFilters();
        },
        error: (error) => {
          this.loading = false;
          console.error('Error updating SIM card:', error);
        },
      });
  }

  onCancel() {
    // Navigate back to the list view with preserved filters
    this.navigateBackWithFilters();
  }

  onAudit() {
    if (this.iccidValue) {
      const url = this.router.serializeUrl(
        this.router.createUrlTree(['/audit-management/rici/sim-card'], {
          queryParams: { iccid: this.iccidValue },
        }),
      );

      // Open in the same window
      this.router.navigateByUrl(url);
    } else {
      this.toastr.error('rici.sim.edit.error.no-iccid');
    }
  }

  onHistoriqueStatut() {
    if (!this.simCardTecId) {
      this.toastr.error('rici.sim.edit.error.no-tecid');
      return;
    }

    this.dialog.open(StatusHistoryPopupComponent, {
      data: {
        tecid: this.simCardTecId,
        iccid: this.iccidValue,
      },
      width: '500px',
      panelClass: 'simple-popup',
    });
  }

  onPagerDetails() {
    if (this.isAssociated && this.pagerTecIdValue) {
      this.router.navigate(['/rici/pagers/edit', this.pagerTecIdValue]);
    } else {
      this.toastr.info('rici.sim.edit.no-pager-associated');
    }
  }

  /**
   * Navigate back to the list view with preserved filters from NavigationHistoryService
   */
  private navigateBackWithFilters(): void {
    const filterValue =
      this.simCardEditionService.navigationHistoryService.get(
        'showInactiveSims',
      );

    if (filterValue === 'true') {
      // Create filter object for navigation
      const filterObj = {
        criteria: [
          {
            operator: 'eq',
            attribute: 'showInactiveSims',
            value: true,
          },
        ],
        pageNumber: 0,
        pageSize: 10,
      };

      // Navigate with filter as query param
      this.router.navigate(['/rici'], {
        queryParams: { filter: JSON.stringify(filterObj) },
      });
    } else {
      // Navigate without filter
      this.router.navigate(['/rici']);
    }
  }
}
