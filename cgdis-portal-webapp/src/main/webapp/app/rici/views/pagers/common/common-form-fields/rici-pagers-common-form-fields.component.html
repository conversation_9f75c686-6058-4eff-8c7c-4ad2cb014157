@if (formService && !isLoading) {
  @if (warningMessages.length) {
    <cgdis-portal-warning-message [warningMessages]="warningMessages"></cgdis-portal-warning-message>

  }
  <div class="row form-section">
    <div class="col-12">
      <div class="row justify-content-between ">
        <div class="d-flex gap-1">
          <h3>{{ 'rici.pagers.details' | translate }}
            <span class="ms-2 redirect-links"> <!-- Added margin for spacing -->



          </span>
          </h3>
          <span class="form-header-link">
                  <!-- Audit Link - Disabled for creation -->
              <span>
                <cgdis-portal-button-link
                  [disabled]="!currentRiciPager"
                  (click)="onAudit()"
                  id="audit-link">{{ 'rici.schemas.form.schema.audit' | translate }}
                </cgdis-portal-button-link>
              </span>
        </span>
          <span class="form-header-link">
                  <!-- <PERSON>t Link - Disabled for creation -->
               <span>
                <cgdis-portal-button-link
                  [disabled]="!currentRiciPager?.simCard"
                  (click)="onSimCard()"
                  id="sim-card-link">{{ 'rici.pagers.simCardLink' | translate }}
                </cgdis-portal-button-link>

              </span>
        </span>
          <span class="form-header-link">
            <span>
                <cgdis-portal-button-link
                  [disabled]="!($any(currentRiciPager?.activeAssignment)?.assignedPerson)"
                  (click)="onAudit()"
                  id="pager-owner-link">{{ 'rici.pagers.pagerOwnerLink' | translate }}
                </cgdis-portal-button-link>
              </span>
        </span>
        </div>

        <br>
        <div class="col-2">
          @if (currentRiciPager && currentRiciPager?.riciPagerProgramming) {
            <div class="ml-auto programming-details">
              <cgdis-portal-input-field-with-icon [fieldReadonly]="true"
                                                  [iconTooltipKey]="'rici.pagers.form.programming.status-tooltip'"
                                                  [icon]="'icon-question-circle'"
                                                  [initialValue]="currentRiciPager?.riciPagerProgramming.programmingStatusView.calculatedProgrammingStatus"
                                                  [labelKey]="'rici.pagers.form.programming.status'"></cgdis-portal-input-field-with-icon>
              <cgdis-portal-datepicker-field
                [fieldReadonly]="true"
                [initialValue]="currentRiciPager?.riciPagerProgramming.lastProgrammingSentDate"
                [labelKey]="'rici.pagers.form.programming.programming-date'"
              ></cgdis-portal-datepicker-field>
              <cgdis-portal-datepicker-field [fieldReadonly]="true"
                                             [initialValue]="currentRiciPager?.riciPagerProgramming.lastSuccessfulUpdateDate"

                                             [labelKey]="'rici.pagers.form.programming.last-update'"></cgdis-portal-datepicker-field>
            </div>
          }
        </div>
      </div>


    </div>
    <!-- Column 1 -->
    <div class="col-md-6">


      <div class="form-field-container mb-3">
        <cgdis-portal-input-field
          [fieldReadonly]="true"
          [iconTooltipKey]="'rici.pagers.pagerStatusTooltip'"
          [icon]="'icon-question-circle'"
          [id]="'pagerStatus'"
          [initialValue]="currentRiciPager? currentRiciPager.status : RiciPagerStatus.INACTIVE"
          [labelKey]="'rici.pagers.pagerStatus'"
          [name]="'pagerStatus'"
        ></cgdis-portal-input-field>
      </div>

      <div class="form-field-container mb-3">
        <cgdis-portal-input-field
          (onValueChange)="onPagerIdChange($event)"
          [fieldMaxLength]="10"
          [fieldMinLength]="10"
          [fieldRequired]="isPagerIdFieldRequired"
          [iconTooltipKey]="'rici.pagers.pagerIdTooltip'"
          [icon]="'icon-question-circle'"
          [initialValue]="currentRiciPager?.pagerId"
          [id]="'pagerId'"
          [placeholder]="'-'"
          [labelKey]="'rici.pagers.pagerId'"
          [name]="'pagerId'"
        ></cgdis-portal-input-field>
      </div>

      <div class="form-field-container mb-3">
        <cgdis-portal-input-field
          (onValueChange)="onSerialNumberChange($event)"
          [fieldMaxLength]="14"
          [fieldMinLength]="14"
          [initialValue]="currentRiciPager?.serialNumber"
          [fieldRequired]="true"
          [iconTooltipKey]="'rici.pagers.serialNumberTooltip'"
          [icon]="'icon-question-circle'"
          [placeholder]="'-'"

          [id]="'serialNumber'"
          [labelKey]="'rici.pagers.serialNumber'"
          [name]="'serialNumber'"
        ></cgdis-portal-input-field>
      </div>

      <div class="form-field-container mb-3">
        <cgdis-portal-select-field
          (onValueChange)="onManufacturerChange($event)"
          [fieldRequired]="true"
          [iconTooltipKey]="'rici.pagers.manufacturerTooltip'"
          [icon]="'icon-question-circle'"
          [id]="'manufacturer'"
          [labelKey]="'rici.pagers.manufacturer'"
          [name]="'manufacturer'"
          [placeholder]="'-'"

          [initialValue]="currentRiciPager?.manufacturer"
          [possibleValues]="manufacturerOptions"
        ></cgdis-portal-select-field>
      </div>

      <div class="form-field-container mb-3">
        <cgdis-portal-select-field
          (onValueChange)="onModelChange($event)"
          [fieldRequired]="true"
          [fieldReadonly]="!hasManufacturer "
          [iconTooltipKey]="'rici.pagers.modelTooltip'"
          [icon]="'icon-question-circle'"
          [id]="'model'"
          [labelKey]="'rici.pagers.model'"
          [placeholder]="'-'"
          [name]="'model'"
          [initialValue]="currentRiciPager?.model"
          [possibleValues]="modelOptions"
        ></cgdis-portal-select-field>
      </div>
      <div class="form-field-container mb-3 maintenance-password-container">
        <div class="form-field-container flex-grow-1">
          <cgdis-portal-input-field
            [fieldReadonly]="true"
            [iconTooltipKey]="'rici.pagers.maintenancePasswordTooltip'"
            [icon]="'icon-question-circle'"
            [id]="'maintenancePassword'"
            [labelKey]="'rici.pagers.maintenancePassword'"
            [name]="'maintenancePassword'"
            [initialValue]="currentRiciPager?.riciPagerMaintenance?.maintenancePassword"
            [placeholder]="'-'"
          ></cgdis-portal-input-field>
        </div>
        <cgdis-portal-link-with-icon
          (click)="onResetMaintenancePassword()"
          [icon]="'icon-reload2'"
          [linkDisabled]="!currentRiciPager"
          [iconClasses]="['reset-password-icon', !currentRiciPager ? 'disabled-link' : '']"
          [tooltipText]=" currentRiciPager ? null:'rici.pagers.resetPasswordTooltipDisabled' | translate ">
        </cgdis-portal-link-with-icon>
      </div>

      <div class="form-field-container mb-3">
        <cgdis-portal-datepicker-field
          [fieldRequired]="true"
          (onValueChange)="onDeliveryDateChange($event)"
          [iconTooltipKey]="'rici.pagers.deliveryDateTooltip'"
          [icon]="'icon-question-circle'"
          [id]="'deliveryDate'"
          [initialValue]="currentRiciPager ? currentRiciPager.deliveryDate :today"
          [labelKey]="'rici.pagers.deliveryDate'"
          [name]="'deliveryDate'"
        ></cgdis-portal-datepicker-field>
      </div>

    </div>

    <!-- Column 3 -->
    <div class="col-md-6">
      <br>
      <cgdis-portal-warning-message *ngIf="currentRiciPager?.simCard?.status ===RiciSimCardStatus.INACTIVE"
                                    [warningMessages]="simInactiveWarning"></cgdis-portal-warning-message>


      <div class="form-field-container mb-3">
        <cgdis-portal-select-field
          (onValueChange)="onSimCardSelected($event)"
          [allowClear]="true"
          [fieldRequired]="false"
          [iconTooltipKey]="'rici.pagers.simAllocationTooltip'"
          [icon]="'icon-question-circle'"
          [initialValue]="simTecid"
          [labelKey]="'rici.pagers.simAllocation'"
          [name]="'simCardIccid'"
          [placeholderKey]="'rici.pagers.searchSimPlaceholder'"
          [possibleValues]="simCardOptions"
        ></cgdis-portal-select-field>
      </div>

      <div class="form-field-container mb-3">
        <cgdis-portal-select-field
          (onValueChange)="onMsisdnSelected($event)"
          [fieldRequired]="false"
          [allowClear]="true"
          [iconTooltipKey]="'rici.pagers.simAllocationMsisdnTooltip'"
          [icon]="'icon-question-circle'"
          [initialValue]="simTecid"
          [labelKey]="'rici.pagers.simAllocationMsisdn'"
          [name]="'simCardMsisdn'"
          [placeholderKey]="'rici.pagers.searchMsisdnPlaceholder'"
          [possibleValues]="msisdnOptions"
        ></cgdis-portal-select-field>
      </div>

      <div class="form-field-container mb-3">
        <cgdis-portal-select-field
          (onValueChange)="onAssignmentTypeChange($event)"
          [fieldRequired]="true"
          [iconTooltipKey]="'rici.pagers.assignmentTooltip'"
          [icon]="'icon-question-circle'"
          [id]="'assignmentType'"
          [initialValue]="currentRiciPager ? currentRiciPager.activeAssignment.assignmentType : RiciPagerAssignmentType.STOCK"
          [labelKey]="'rici.pagers.assignment'"
          [name]="'assignmentType'"
          [possibleValues]="assignmentOptions"
        ></cgdis-portal-select-field>
      </div>
      <!-- Conditional Fields based on Assignment Type - Moved to Column 3 -->
      <ng-container [ngSwitch]="formService.formValue()?.assignmentType?.valueOf()">

        <!-- Assignement: Utilisateur -->
        <div *ngSwitchCase="RiciPagerAssignmentType.PERSON">
          <div class="form-field-container mb-3">
            <cgdis-portal-autocomplete-field
              (onValueChange)="onUserSelected($event)"
              (ontTextTyped)="onPersonTyped($event)"
              [fieldRequired]="true"

              [multiple]="false"
              [iconTooltipKey]="'rici.pagers.pagerOwnerTooltip'"
              [icon]="'icon-question-circle'"
              [id]="'assignedToPersonTecid'"
              [labelKey]="'rici.pagers.pagerOwner'"
              [initialValue]="personInitialValue"
              [placeholderKey]="'rici.pagers.searchUserPlaceholder'"
              [possibleValues]="personPossibleValues"
            ></cgdis-portal-autocomplete-field>
            <cgdis-portal-input-field
              [fieldReadonly]="true"
              [id]="'pagerOwnerMatricule'"
              [initialValue]="selectedUser?.cgdisRegistrationNumber ? selectedUser?.cgdisRegistrationNumber : '-'"
              [labelKey]="'rici.pagers.matricule'"
              [name]="'pagerOwnerMatricule'"
            ></cgdis-portal-input-field>
            <cgdis-portal-input-field
              [fieldReadonly]="true"
              [id]="'individualRic'"
              [initialValue]="selectedUser?.pagerRic ? selectedUser?.pagerRic : '-'"
              [labelKey]="'rici.pagers.individualRic'"
              [name]="'individualRic'"
            ></cgdis-portal-input-field>
          </div>
        </div>

        <!-- Assignement: Pool CGDIS -->
        <div *ngSwitchCase="RiciPagerAssignmentType.POOL_CIS">
          <div class="form-field-container mb-3">
            <cgdis-portal-select-field
              (onValueChange)="onEntitySelected($event)"
              [fieldRequired]="true"
              [iconTooltipKey]="'rici.pagers.entityTooltip'"
              [icon]="'icon-question-circle'"
              [id]="'assignedToEntityTecid'"
              [labelKey]="'rici.pagers.entity'"
              [initialValue]="$any(currentRiciPager?.activeAssignment)?.assignedEntity?.tecid"
              [placeholderKey]="'rici.pagers.searchEntityPlaceholder'"
              [possibleValues]="entityOptions"
            ></cgdis-portal-select-field>
          </div>
        </div>
      </ng-container>
      @if (currentRiciPager && personHasChanged) {
        <cgdis-portal-input-field
          [labelKey]="'rici.pagers.change-person-reason'"
          [name]="'changeReason'"
        ></cgdis-portal-input-field>
      }

    </div> <!-- End Column 3 -->


  </div> <!-- End Row -->
    <!--          Hidden fields   -->
  <cgdis-portal-input-field
    [fieldRequired]="false"
    [disableIfNotVisible]="false"
    [visible]="false"
    [id]="'simCardTecid'"
    [initialValue]="currentRiciPager?.simCard?.tecid"
    [name]="'simCardTecid'"
  ></cgdis-portal-input-field>
  <cgdis-portal-input-field
    [fieldRequired]="false"
    [disableIfNotVisible]="false"
    [visible]="false"
    [id]="'assignedToPersonTecid'"
    [name]="'assignedToPersonTecid'"
  ></cgdis-portal-input-field>
  <cgdis-portal-input-field
    [fieldRequired]="false"
    [disableIfNotVisible]="false"
    [visible]="false"
    [id]="'assignedToEntityTecid'"
    [name]="'assignedToEntityTecid'"
  ></cgdis-portal-input-field>
}
