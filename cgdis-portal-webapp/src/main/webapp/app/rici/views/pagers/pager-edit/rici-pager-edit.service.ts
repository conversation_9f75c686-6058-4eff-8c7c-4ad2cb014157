import { RestService } from '@eportal/core';
import { Injectable } from '@angular/core';
import { DefaultFormService } from '@app/common/modules/form-module/service/default-form.service';
import { FormError } from '@app/common/modules/error-management/model/form-error.model';
import { Observable } from 'rxjs';
import { FormErrorService } from '@app/common/modules/form-module/service/form-error.service';
import { ToastService } from '@app/common/shared/toasts/CGDISToastService';
import { TranslateService } from '@ngx-translate/core';
import { ActivatedRoute, Router } from '@angular/router';
import { RiciPager } from '@app/model/rici/rici-pager.model';
import { PagerEditFormModel } from '@rici/views/pagers/pager-edit/model/pager-edit-form.model';

@Injectable()
export class RiciPagerEditService extends DefaultFormService<
  PagerEditFormModel,
  RiciPager // Replace 'any' with the expected response type from the backend
> {
  constructor(
    formErrorService: FormErrorService,
    toastr: ToastService,
    private restService: RestService,
    protected translationService: TranslateService,
    private router: Router,
    private route: ActivatedRoute,
  ) {
    super(
      toastr,
      translationService.instant('rici.pagers.form.update.toast'),
      formErrorService,
    );
  }

  submit(parameter: PagerEditFormModel): Observable<RiciPager> {
    return this.restService.one('rici', 'pagers').patch(parameter);
    // throw new Error('Method not implemented.');
  }

  submitSuccess(result: any): void {}

  submitError(formError: FormError): void {
    throw new Error('Method not implemented.');
  }

  getPagerById(id: number) {
    return this.restService
      .one<RiciPager>('rici', 'pagers', id.toString())
      .get();
  }
}
