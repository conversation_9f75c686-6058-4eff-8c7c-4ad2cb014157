import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  Output,
  SimpleChanges,
} from '@angular/core';
import { NgSwitchCase } from '@angular/common';
import { FormModule } from '@app/common/modules/form-module/form.module';
import { SharedModule } from '@app/common/shared/shared.module';
import { TranslateModule } from '@ngx-translate/core';
import {
  RiciPagerAssignmentType,
  RiciPagerStatus,
} from '@rici/common/enums/pager.enum';
import { FieldOption } from '@app/common/modules/form-module/model/field-option.model';
import { firstValueFrom, forkJoin, Observable, Subject } from 'rxjs';
import { Validators, ValidatorFn } from '@angular/forms'; // Import Validators
import { DateModel } from '@eportal/core';
import { Router } from '@angular/router';
import { ToastService } from '@app/common/shared/toasts/CGDISToastService';
import { debounceTime, take, takeUntil } from 'rxjs/operators';
import { PagerCatalog } from '@rici/common/enums/pager-catalog';
import { RiciPagersCommonFormFieldsService } from '@rici/views/pagers/common/common-form-fields/rici-pagers-common-form-fields.service';
import { DefaultFormService } from '@app/common/modules/form-module/service/default-form.service';
import { RiciPager } from '@app/model/rici/rici-pager.model';
import { EntityFilterModule } from '@app/common/modules/entity-filter/entity-filter.module';
import { Entity } from '@app/model/entity.model';
import { has } from 'lodash';
import { WarningMessage } from '@app/model/warning/warning-message';
import { $e } from 'codelyzer/angular/styles/chars';
import { RiciPagerAssignmentPersonModel } from '@app/model/rici/rici-pager-assignment-person.model';
import { PersonInfoRiciModel } from '@rici/views/pagers/models/personInfoRici.model';
import { RiciSimCardStatus } from '@rici/common/enums/sim-card-status.enum';

@Component({
  selector: 'cgdis-portal-rici-pagers-common-form-fields',
  standalone: true,
  imports: [
    FormModule,
    NgSwitchCase,
    SharedModule,
    TranslateModule,
    EntityFilterModule,
  ],
  templateUrl: './rici-pagers-common-form-fields.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [RiciPagersCommonFormFieldsService],
})
export class RiciPagersCommonFormFieldsComponent
  implements OnInit, OnDestroy, OnChanges
{
  loading = false;
  isPagerStatusReadonly: boolean = true;
  // Options for dropdowns
  manufacturerOptions: FieldOption<string>[] = []; // Renamed from producerOptions
  modelOptions: FieldOption<string>[] = [];
  statusOptions: FieldOption<string>[] = [];
  assignmentOptions: FieldOption<string>[] = [];

  @Output() wasUpdated = new EventEmitter<boolean>(false);

  @Input() currentRiciPager: RiciPager;
  // Observables for dropdown options
  simCardOptions$: Observable<FieldOption<number>[]>;
  simCardOptions: FieldOption<number>[];
  entityOptions: FieldOption<number>[];
  msisdnOptions: FieldOption<number>[];
  // Enum access for template
  readonly RiciPagerAssignmentType = RiciPagerAssignmentType;
  // Today's date for initial values - will be used by datepicker
  today: DateModel = {
    year: new Date().getFullYear(),
    month: new Date().getMonth() + 1,
    day: new Date().getDate(),
  };
  currentUser: PersonInfoRiciModel = null;
  selectedUser: PersonInfoRiciModel = null;
  selectedEntity: any = null;
  @Input() isLoading: boolean = false;
  @Input() formService: DefaultFormService<any, any>;
  searchPersonSubject = new Subject<string>();
  personPossibleValues: FieldOption<PersonInfoRiciModel>[] = [];
  personInitialValue: PersonInfoRiciModel[] = [];
  simTecid: number = null;
  simInactiveWarning: WarningMessage[] = [
    new WarningMessage({
      warningType: 'sim-inactive',
    }),
  ];

  protected hasManufacturer = false;
  protected personHasChanged: boolean = false;
  protected readonly RiciPagerStatus = RiciPagerStatus;
  protected readonly has = has;
  protected warningMessages: WarningMessage[] = [];
  protected readonly $e = $e;
  protected readonly RiciSimCardStatus = RiciSimCardStatus;
  protected isPagerIdFieldRequired: boolean = false; // For visual indication
  // To manage subscriptions
  private unsubscribe$ = new Subject<void>();
  private selectedSim: any = null;
  private isUpdatingFields = false; // Flag to prevent infinite loop
  private personSearchIndex = 0;
  private isInitializing = false;

  constructor(
    private router: Router,
    private toastr: ToastService,
    private cdr: ChangeDetectorRef,
    private riciPagersCommonFormFieldsService: RiciPagersCommonFormFieldsService,
  ) {}

  ngOnInit(): void {
    this.loading = true;
    // Flag to track that we're in initial setup to prevent other handlers from interfering
    this.isInitializing = true;
    this.setupProducersAndModels();
    this.setupStatusAndAssignmentDropdownOptions();
    this.simTecid = this.currentRiciPager?.simCard?.tecid ?? null;
    this.setupSimCardData();
    this.setupEntityData();
    // Pass currentRiciPager tecid if available, especially for edit mode
    this.loadPersons('', this.currentRiciPager?.tecid);
    // Ensure the pager status is set to INACTIVE and readonly initially
    this.isPagerStatusReadonly = true;

    this.loading = false;

    this.searchPersonSubject
      .pipe(debounceTime(250), takeUntil(this.unsubscribe$))
      .subscribe({
        next: (personValue) => {
          // Pass currentRiciPager tecid if available
          this.loadPersons(personValue, this.currentRiciPager?.tecid);
        },
      });

    // Release initialization flag
    this.isInitializing = false;

    // Determine initial assignment type and set Pager ID validators
    let initialAssignmentType = this.formService.formValue()?.assignmentType?.valueOf();
    if (this.currentRiciPager && this.currentRiciPager.activeAssignment) {
      initialAssignmentType = this.currentRiciPager.activeAssignment.assignmentType;
    } else if (!initialAssignmentType && !this.currentRiciPager) { // Create mode, no form value yet
      initialAssignmentType = RiciPagerAssignmentType.STOCK; // Default for creation
    }
    if (initialAssignmentType) {
      this.updatePagerIdValidators(initialAssignmentType);
    }

    this.cdr.markForCheck();
  }

  private updatePagerIdValidators(assignmentType: RiciPagerAssignmentType): void {
    const pagerIdControl = this.formService.getFormGroup()?.get('pagerId');
    if (!pagerIdControl) {
      return;
    }

    const minLengthValidator = Validators.minLength(10);
    const maxLengthValidator = Validators.maxLength(10);
    
    let newValidatorsArray: ValidatorFn[] = [minLengthValidator, maxLengthValidator];
    this.isPagerIdFieldRequired = false; // Default to not required visually

    if (assignmentType === RiciPagerAssignmentType.PERSON || assignmentType === RiciPagerAssignmentType.POOL_CIS) {
      newValidatorsArray.push(Validators.required);
      this.isPagerIdFieldRequired = true; // Set visual required flag
    }

    pagerIdControl.setValidators(newValidatorsArray);
    pagerIdControl.updateValueAndValidity(); // Update the control's validity status
    this.cdr.markForCheck(); // Ensure change detection picks up isPagerIdFieldRequired for the template
  }

  wasUpdatedHandler(): void {
    this.wasUpdated.emit(true);
  }

  wasUpdatedHandlerReset(): void {
    this.wasUpdated.emit(false);
  }

  findAssignedPerson(tecid: number) {
    return this.personPossibleValues.find(
      (value) => value.value.tecid === tecid,
    );
  }

  warnPagerOwnerChangeOnlyPerson() {
    if (
      this.currentRiciPager &&
      'assignedPerson' in this.currentRiciPager.activeAssignment
    ) {
      this.personHasChanged = true;
      this.warningMessages.push(
        new WarningMessage({
          warningType: 'pager-owner-change-only-person',
          params: {
            personName: this.currentRiciPager.activeAssignment.assignedPerson,
          },
        }),
      );
      this.cdr.markForCheck();
    }
  }

  warnPagerOwnerChangePersonWithSIM() {
    if (
      this.currentRiciPager &&
      'assignedPerson' in this.currentRiciPager.activeAssignment
    ) {
      this.warningMessages.push(
        new WarningMessage({
          warningType: 'pager-owner-change-person-with-sim',
          params: {
            personName: this.currentRiciPager.activeAssignment.assignedPerson,
          },
        }),
      );
      this.cdr.markForCheck();
    }
  }

  warnPagerOwnerChangeOnlyWithSIM() {
    if (
      this.currentRiciPager &&
      'assignedPerson' in this.currentRiciPager.activeAssignment
    ) {
      this.warningMessages.push(
        new WarningMessage({
          warningType: 'pager-owner-change-only-with-sim',
          params: {
            personName: this.currentRiciPager.activeAssignment.assignedPerson,
          },
        }),
      );
      this.cdr.markForCheck();
    }
  }

  initModels() {
    if (this.currentRiciPager) {
      this.modelOptions = PagerCatalog.ProducerModels[
        this.currentRiciPager.manufacturer as PagerCatalog.Producer
      ]
        .sort()
        .map(
          (m) =>
            new FieldOption({
              // Use manufacturer
              label: m,
              value: m,
            }),
        );
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.currentRiciPager) {
      this.initModels();
      if (this.currentRiciPager !== undefined) {
        this.hasManufacturer = true;
      }
      if (
        this.currentRiciPager &&
        'assignedPerson' in this.currentRiciPager.activeAssignment
      ) {
        this.loadPersons(
          this.currentRiciPager.activeAssignment.assignedPerson
            .cgdisRegistrationNumber,
          this.currentRiciPager?.tecid,
        );
        this.formService
          .getFormGroup()
          .get('assignedToPersonTecid')
          ?.setValue(
            this.currentRiciPager.activeAssignment.assignedPerson.tecid,
          );
      }
      // Update Pager ID validators when currentRiciPager changes (e.g., in edit mode)
      if (this.currentRiciPager && this.currentRiciPager.activeAssignment) {
        this.updatePagerIdValidators(this.currentRiciPager.activeAssignment.assignmentType);
      } else if (this.currentRiciPager && !this.currentRiciPager.activeAssignment) {
        // If pager exists but has no assignment (e.g. new pager being edited after initial save with STOCK)
        // Or if it's an old pager that somehow has no assignment type. Default to STOCK rules.
        this.updatePagerIdValidators(RiciPagerAssignmentType.STOCK);
      }
    }
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
  }

  // --- Selection Handlers ---
  async onSimCardSelected(option: any): Promise<void> {
    // Skip processing during initialization
    if (this.isInitializing) return;

    if (
      this.currentRiciPager &&
      this.currentRiciPager.simCard?.tecid !== option
    ) {
      this.wasUpdatedHandler();
    }

    if (this.isUpdatingFields) return;

    this.isUpdatingFields = true;
    try {
      let selectedTecid: number | null = null;
      if (option) {
        // Extract the tecid (value) reliably
        selectedTecid = typeof option === 'number' ? option : option?.value;

        // Set the simCardTecid form control value directly
        this.formService
          .getFormGroup()
          .get('simCardTecid')
          ?.setValue(selectedTecid, { emitEvent: false });
        // console.log(`Set simCardTecid form control to: ${selectedTecid}`);

        // Update both ICCID and MSISDN dropdowns to reflect the selection
        this.formService
          .getFormGroup()
          .get('simCardIccid')
          ?.setValue(selectedTecid, { emitEvent: false }); // Prevent re-triggering
        this.formService
          .getFormGroup()
          .get('simCardMsisdn')
          ?.setValue(selectedTecid, { emitEvent: false }); // Prevent re-triggering

        // Store the selected SIM data for display or other uses
        if (typeof option === 'object' && option && (option as any).simData) {
          this.selectedSim = (option as any).simData;
        } else if (selectedTecid !== null) {
          // Fetch if only value was provided
          this.selectedSim = await this.getSimDataByValue(selectedTecid);
        } else {
          this.selectedSim = null;
        }
        // console.log('Selected SIM Data:', this.selectedSim);
      } else {
        // Handle case where selection is cleared (e.g., user deselects)
        selectedTecid = null;
        this.formService.getFormGroup().get('simCardTecid')?.setValue(null);
        this.selectedSim = null;
        this.formService
          .getFormGroup()
          .get('simCardIccid')
          ?.setValue(null, { emitEvent: false });
        this.formService
          .getFormGroup()
          .get('simCardMsisdn')
          ?.setValue(null, { emitEvent: false });
        // console.log('Cleared simCardTecid form control');
      }

      // Update status field based on new selection (no setTimeout needed)
      this.updatePagerStatusBasedOnInputs();
      this.cdr.markForCheck();
    } finally {
      this.isUpdatingFields = false;
    }
  }

  onPersonTyped(personValue: any): void {
    this.searchPersonSubject.next(personValue);
  }

  async onUserSelected(
    option: PersonInfoRiciModel[] | PersonInfoRiciModel,
  ): Promise<void> {
    const selectionValue = Array.isArray(option) ? option[0] : option;

    this.formService
      .getFormGroup()
      .get('assignedToPersonTecid')
      ?.setValue(selectionValue?.tecid);
    // Mettre à jour selectedUser pour l'affichage (Matricule, RIC) et la valeur propre de l'autocomplétion
    this.selectedUser = selectionValue || null;
    if (this.selectedUser) {
      this.personInitialValue = [
        this.personPossibleValues.find(
          (onePerson) => onePerson.value.tecid === this.selectedUser.tecid,
        )?.value,
      ].filter(Boolean); // Utiliser filter(Boolean) pour supprimer undefined si non trouvé
    } else {
      this.personInitialValue = [];
    }

    // Gérer les avertissements et l'indicateur personHasChanged
    if (this.currentUser) {
      // S'il y avait un utilisateur initial (currentUser n'est pas null)
      if (
        this.selectedUser &&
        this.currentUser.tecid !== this.selectedUser.tecid
      ) {
        // Changement de l'utilisateur initial (currentUser) vers une personne différente (selectedUser)
        this.warnPagerOwnerChangeOnlyPerson();
        this.personHasChanged = true;
      } else if (!this.selectedUser) {
        // Changement de l'utilisateur initial (currentUser) vers aucune personne (selectedUser est null)
        this.warnPagerOwnerChangeOnlyPerson(); // Ou un avertissement différent comme "Personne effacée"
        this.personHasChanged = true;
      } else if (
        this.selectedUser &&
        this.currentUser.tecid === this.selectedUser.tecid
      ) {
        // Re-sélection de la même personne initiale
        this.personHasChanged = false;
        this.warningMessages = [];
      }
      // Si selectedUser est null et currentUser était aussi null, personHasChanged et warnings restent tels quels ou par défaut à false/vide.
      // Si selectedUser n'est pas null, et currentUser était null (l'affectation initiale n'était pas une personne), ce bloc est sauté, géré par le 'else' ci-dessous.
    } else {
      // Pas d'utilisateur initial (currentUser est null - ex: mode création, ou mode édition depuis une affectation non-personne)
      if (this.selectedUser) {
        // Une personne est maintenant sélectionnée
        this.personHasChanged = true; // C'est un changement depuis "aucune personne"
        this.warningMessages = []; // Effacer les avertissements non pertinents
      } else {
        // Aucune personne sélectionnée, et pas d'utilisateur initial
        this.personHasChanged = false;
        this.warningMessages = [];
      }
    }

    this.wasUpdatedHandler();

    this.cdr.markForCheck();
  }

  selectEntity(entityValue: { entity: Entity; allUnderEntity: boolean }): void {
    this.formService
      .getFormGroup()
      .get('assignedToEntityTecid')
      ?.setValue(entityValue.entity.tecid);
  }

  onEntitySelected(option: FieldOption<number> | number | null) {
    this.formService
      .getFormGroup()
      .get('assignedToEntityTecid')
      ?.setValue(option);
    if (
      this.currentRiciPager &&
      'assignedEntity' in this.currentRiciPager.activeAssignment &&
      this.currentRiciPager?.activeAssignment?.assignedEntity?.tecid !== option
    ) {
      this.formService
        .getFormGroup()
        .get('assignedToEntityTecid')
        ?.setValue(option);
      this.wasUpdatedHandler();
    }
    this.cdr.markForCheck();
  }

  async onMsisdnSelected(option: any): Promise<void> {
    // Skip processing during initialization
    if (this.isInitializing) return;

    if (
      this.currentRiciPager &&
      this.currentRiciPager?.simCard?.tecid !== option
    ) {
      this.wasUpdatedHandler();
    }
    if (this.isUpdatingFields) return;

    this.isUpdatingFields = true;
    try {
      let selectedTecid: number | null = null;
      if (option) {
        // Extract the tecid (value) reliably
        selectedTecid = typeof option === 'number' ? option : option?.value;

        // Set the simCardTecid form control value directly
        this.formService
          .getFormGroup()
          .get('simCardTecid')
          ?.setValue(selectedTecid);
        // console.log(`Set simCardTecid form control to: ${selectedTecid}`);

        // Update both ICCID and MSISDN dropdowns to reflect the selection
        this.formService
          .getFormGroup()
          .get('simCardMsisdn')
          ?.setValue(selectedTecid, { emitEvent: false }); // Prevent re-triggering
        this.formService
          .getFormGroup()
          .get('simCardIccid')
          ?.setValue(selectedTecid, { emitEvent: false }); // Prevent re-triggering

        this.formService
          .getFormGroup()
          .get('simCardTecid')
          ?.setValue(selectedTecid, { emitEvent: false }); // Prevent re-triggering

        // Store the selected SIM data for display or other uses
        if (typeof option === 'object' && option && (option as any).simData) {
          this.selectedSim = (option as any).simData;
        } else if (selectedTecid !== null) {
          // Fetch if only value was provided
          this.selectedSim = await this.getSimDataByValue(selectedTecid);
        } else {
          this.selectedSim = null;
        }
        // console.log('Selected SIM Data:', this.selectedSim);
      } else {
        // Handle case where selection is cleared (e.g., user deselects)
        selectedTecid = null;
        this.formService.getFormGroup().get('simCardTecid')?.setValue(null);
        this.selectedSim = null;
        this.formService
          .getFormGroup()
          .get('simCardMsisdn')
          ?.setValue(null, { emitEvent: false });
        this.formService
          .getFormGroup()
          .get('simCardIccid')
          ?.setValue(null, { emitEvent: false });
        // console.log('Cleared simCardTecid form control');
      }

      // Update status field based on new selection (no setTimeout needed)
      this.updatePagerStatusBasedOnInputs();
      this.cdr.markForCheck();
    } finally {
      this.isUpdatingFields = false;
    }
  }

  onCancel(): void {
    this.router.navigate(['/rici/pagers']); // Navigate back to the list
  }

  onAudit(): void {
    if (this.currentRiciPager) {
      const url = this.router.serializeUrl(
        this.router.createUrlTree(['/audit-management/rici/pager'], {
          queryParams: { pagerId: this.currentRiciPager.pagerId },
        }),
      );

      // Open in a new tab/window
      window.open('#' + url, '_blank');
    } else {
      this.toastr.error('rici.sim.edit.error.no-iccid');
    }
  }

  onSimCard() {
    if (
      this.currentRiciPager &&
      this.currentRiciPager.simCard &&
      this.currentRiciPager.simCard.iccid
    ) {
      this.router.navigate([
        '/rici/sim-card/edit',
        this.currentRiciPager.simCard.iccid,
      ]);
    } else {
      this.toastr.error('rici.pagers.form.error.no-sim-iccid'); // Clé de traduction à ajouter si nécessaire
      console.error(
        'Cannot navigate to SIM card details: ICCID is missing.',
        this.currentRiciPager,
      );
    }
  }

  onResetMaintenancePassword(): void {
    // console.log('Reset Maintenance Password clicked - Not implemented yet');
    // this.toastr.info('rici.pagers.resetPasswordComingSoon'); // Use a specific translation key
    if (!this.currentRiciPager?.tecid) {
      // this.toastr.error('rici.pagers.error.noTecid');
      return;
    }

    this.riciPagersCommonFormFieldsService
      .resetMaintenancePassword(this.currentRiciPager.tecid)
      .subscribe({
        next: (response) => {
          this.toastr.success('rici.pagers.resetPassword.success', {
            password: response.maintenancePassword,
          });
          this.formService
            .getFormGroup()
            .get('maintenancePassword')
            ?.setValue(response.maintenancePassword);
        },
        error: (error) => {
          this.toastr.error('rici.pagers.resetPassword.error');
        },
      });
    this.cdr.markForCheck();
  }

  onSerialNumberChange($event: any) {
    if (
      this.currentRiciPager &&
      this.currentRiciPager.serialNumber !== $event
    ) {
      this.wasUpdatedHandler();
    }
  }

  onModelChange($event: any) {
    if (this.currentRiciPager && this.currentRiciPager.model !== $event) {
      this.wasUpdatedHandler();
    }
  }

  onDeliveryDateChange($event: any) {
    if (
      this.currentRiciPager &&
      this.currentRiciPager.deliveryDate !== $event
    ) {
      this.wasUpdatedHandler();
    }
  }

  protected onAssignmentTypeChange($event: RiciPagerAssignmentType): void {
    this.warningMessages = [];
    console.log('Assignment Type Changed:', $event);
    const previousAssignmentType =
      this.currentRiciPager?.activeAssignment?.assignmentType;
    const isUpdate = !!this.currentRiciPager; // Check if it's an update scenario
    console.log('Previous Assignment Type:', previousAssignmentType);
    console.log('isUpdate:', isUpdate);
    console.log(
      'this.currentRiciPager.activeAssignment:',
      this.currentRiciPager.activeAssignment,
    );
    // Check if the assignment type actually changed
    if (previousAssignmentType !== $event) {
      if (isUpdate) {
        // Only trigger update handler if it's an existing pager being modified
        this.wasUpdatedHandler();
      }

      // Reset dependent fields regardless of create/update
      const formGroup = this.formService.getFormGroup();
      formGroup.get('assignedToPersonTecid')?.setValue(null);
      formGroup.get('assignedToEntityTecid')?.setValue(null);

      // Guard against accessing properties of undefined currentRiciPager in create mode
      if (
        isUpdate && // Only check for person change warning in update mode
        this.currentRiciPager.activeAssignment && // Ensure activeAssignment exists
        'assignedPerson' in this.currentRiciPager.activeAssignment
      ) {
        if ($event === RiciPagerAssignmentType.DECLASSED) {
          this.warningMessages = [];
          this.warnPagerOwnerChangePersonWithSIM();
          this.personHasChanged = false;
        }
        if ($event === RiciPagerAssignmentType.STOCK) {
          if (
            this.currentRiciPager.activeAssignment.assignedPerson &&
            this.currentRiciPager.simCard
          ) {
            this.warningMessages = [];
            this.warnPagerOwnerChangePersonWithSIM();
          } else {
            if (this.currentRiciPager.activeAssignment.assignedPerson) {
              this.warningMessages = [];
              this.warnPagerOwnerChangeOnlyPerson();
            }
            if (this.currentRiciPager.simCard) {
              this.warningMessages = [];
              this.warnPagerOwnerChangeOnlyWithSIM();
            }
          }
          this.personHasChanged = true;
        }
        if ($event === RiciPagerAssignmentType.POOL_CIS) {
        }
      }
    } else {
      if ($event === RiciPagerAssignmentType.PERSON) {
        console.log('currentUser:', this.currentUser);
        console.log('selectedUser:', this.selectedUser);

        if (this.currentUser.tecid !== this.selectedUser.tecid) {
          this.warnPagerOwnerChangeOnlyPerson();
          this.personHasChanged = true;
        } else {
          this.personHasChanged = false;
          this.warningMessages = [];
          this.cdr.markForCheck();
        }
      }
    }

    // Clear local component state for selected user/entity
    // this.selectedUser = null;
    // this.selectedEntity = null;
    // this.cdr.markForCheck();
    // wait a tick so that the formControl('assignmentType') has been updated
    // AND pagerId validators have been updated
    this.updatePagerIdValidators($event);

    setTimeout(() => {
      this.updatePagerStatusBasedOnInputs();
      this.cdr.markForCheck();
    });
  }

  /**
   * Handles changes to the Pager ID input field.
   */
  protected onPagerIdChange($event: any): void {
    // Force a delay to ensure form values are updated before checking status
    if (this.currentRiciPager && this.currentRiciPager.pagerId !== $event) {
      this.wasUpdatedHandler();
    }
    setTimeout(() => {
      this.updatePagerStatusBasedOnInputs();
      this.cdr.markForCheck();
    });
  }

  /**
   * Handles changes to the Manufacturer select field.
   * Updates the Model options and resets/sets the Model value.
   * Triggered by (onValueChange) from the template.
   */
  protected onManufacturerChange($event: any): void {
    if (
      this.currentRiciPager &&
      this.currentRiciPager.manufacturer !== $event
    ) {
      this.wasUpdatedHandler();
    }

    // Renamed from onProducerChange
    const manufacturer = $event?.valueOf(); // Renamed variable

    if (
      manufacturer &&
      PagerCatalog.ProducerModels[manufacturer as PagerCatalog.Producer]
    ) {
      this.hasManufacturer = true;
      // Use manufacturer
      this.modelOptions = PagerCatalog.ProducerModels[
        manufacturer as PagerCatalog.Producer
      ]
        .sort()
        .map(
          (m) =>
            new FieldOption({
              // Use manufacturer
              label: m,
              value: m,
            }),
        );
      // Set the first model as the default value if available
      const firstModelValue =
        this.modelOptions.length > 0 ? this.modelOptions[0].value : null;
      // this.formService.setValueByUid('model', firstModelValue); // Use service method if preferred

      if (this.currentRiciPager && this.currentRiciPager.model) {
        this.formService
          .getFormGroup()
          .get('model')
          ?.setValue(this.currentRiciPager.model);

        if (
          this.currentRiciPager &&
          this.currentRiciPager.manufacturer !== $event
        ) {
          this.formService
            .getFormGroup()
            .get('model')
            ?.setValue(firstModelValue);
        }
      } else {
        this.formService.getFormGroup().get('model')?.setValue(firstModelValue);
      }
      // Note: Readonly state for model is handled in the template based on manufacturer value
    } else {
      this.modelOptions = [];
      // this.formService.setValueByUid('model', null); // Use service method if preferred
      this.formService.getFormGroup().get('model')?.setValue(null); // Reset value
      // Note: Readonly state for model is handled in the template based on manufacturer value
    }
    // Template binding [fieldReadonly] and form service handle validity/state.
    this.cdr.markForCheck(); // Use markForCheck for OnPush strategy
  }

  private setupEntityData() {
    this.riciPagersCommonFormFieldsService
      .getEntityOptions()
      .subscribe((value) => {
        this.entityOptions = value;
      });
  }

  private setupStatusAndAssignmentDropdownOptions(): void {
    this.statusOptions = Object.values(RiciPagerStatus).map(
      (s) =>
        new FieldOption({
          I18NLabel: `rici.pagers.status.${s}`, // Use translation keys
          value: s,
        }),
    );

    this.assignmentOptions = Object.values(RiciPagerAssignmentType).map(
      (a) =>
        new FieldOption({
          I18NLabel: `rici.pagers.assignmentType.${a}`, // Use I18NLabel for translation keys
          value: a,
        }),
    );
  }

  // // Helper to get form controls in the template
  // get f() {

  private setupProducersAndModels(): void {
    this.manufacturerOptions = Object.values(PagerCatalog.Producer)
      .sort()
      .map(
        (p) =>
          new FieldOption({
            label: p,
            value: p,
          }),
      );
    // Model options will be populated when manufacturer changes
  }

  private setupSimCardData(): void {
    const tecid = this.currentRiciPager?.tecid ?? null;

    forkJoin({
      iccids: this.riciPagersCommonFormFieldsService.getSimCardOptions(tecid),
      msisdns: this.riciPagersCommonFormFieldsService.getMsisdnOptions(tecid),
    })
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe(({ iccids, msisdns }) => {
        // Store options
        this.simCardOptions = iccids;
        this.msisdnOptions = msisdns;

        // Use setTimeout to ensure DOM has updated with new options
        // setTimeout(() => {
        if (this.simTecid) {
          // If we have a SIM card, add it to the options
          this.simCardOptions.push(
            new FieldOption({
              label: this.currentRiciPager.simCard.iccid,
              value: this.simTecid,
            }),
          );
          this.msisdnOptions.push(
            new FieldOption({
              label: this.currentRiciPager.simCard.msisdn,
              value: this.simTecid,
            }),
          );
        }

        // Update status and mark for check after a small delay
        this.updatePagerStatusBasedOnInputs();
        this.cdr.markForCheck();
        // }, 0);
      });
  }

  private loadPersons(personValue: string, pagerTecid?: number) {
    const currentIndex = ++this.personSearchIndex;
    this.riciPagersCommonFormFieldsService
      .getPersons(personValue, pagerTecid)
      .subscribe({
        next: (page) => {
          if (currentIndex === this.personSearchIndex) {
            this.personPossibleValues = page.content
              .filter(person => person.pagerRic && person.pagerRic.trim() !== '')
              .map((person) => {
                return new FieldOption<PersonInfoRiciModel>({
                  label: `${person.lastName} ${person.firstName} (${person.cgdisRegistrationNumber})`,
                  value: person,
                });
              });
            if (
              this.personPossibleValues.length &&
              this.currentRiciPager &&
              'assignedPerson' in this.currentRiciPager.activeAssignment
            ) {
              this.personInitialValue = [
                this.personPossibleValues.find(
                  (onePerson) =>
                    onePerson.value.tecid ===
                    (
                      this.currentRiciPager
                        ?.activeAssignment as RiciPagerAssignmentPersonModel
                    )?.assignedPerson.tecid,
                )?.value,
              ];
              this.selectedUser =
                this.currentRiciPager.activeAssignment.assignedPerson;
              this.currentUser = this.selectedUser;
            }

            console.error(
              'set person possible values',
              this.personPossibleValues.length,
            );
            this.cdr.markForCheck();
          }
        },
      });
  }

  /**
   * Updates the readonly state and value of the 'pagerStatus' field
   * based on the presence of 'pagerId' and 'simCardTecid' and the current 'assignmentType'.
   */
  private updatePagerStatusBasedOnInputs(): void {
    const pagerIdValue = this.formService.formValue()?.pagerId?.valueOf();
    const simCardIccidValue = this.formService
      .formValue()
      ?.simCardIccid?.valueOf();
    const simCardMsisdnValue = this.formService
      .formValue()
      ?.simCardMsisdn?.valueOf();
    const assignmentTypeValue = this.formService
      .formValue()
      ?.assignmentType?.valueOf();

    const hasPagerId = !!pagerIdValue;
    const hasSimCard = !!(simCardIccidValue || simCardMsisdnValue);

    let newStatus: RiciPagerStatus = RiciPagerStatus.INACTIVE;
    let isReadonly: boolean = true;

    // Check if conditions for ACTIVE status are met
    if (
      hasPagerId &&
      hasSimCard &&
      assignmentTypeValue !== RiciPagerAssignmentType.LOST &&
      assignmentTypeValue !== RiciPagerAssignmentType.DECLASSED
    ) {
      // Explicitly allow PERSON, STOCK, and POOL_CIS for ACTIVE status
      if (
        assignmentTypeValue === RiciPagerAssignmentType.PERSON ||
        assignmentTypeValue === RiciPagerAssignmentType.STOCK ||
        assignmentTypeValue === RiciPagerAssignmentType.POOL_CIS
      ) {
        isReadonly = false;
        newStatus = RiciPagerStatus.ACTIVE;
      }
    }

    // Update form control and readonly state
    const pagerStatusControl = this.formService
      .getFormGroup()
      .get('pagerStatus');
    if (pagerStatusControl?.value !== newStatus) {
      pagerStatusControl.setValue(newStatus, { emitEvent: false }); // Avoid infinite loops
    }
    this.isPagerStatusReadonly = isReadonly;

    this.cdr.markForCheck(); // Ensure UI updates
  }

  // Helper method to get SIM data by value
  private async getSimDataByValue(value: number): Promise<any> {
    try {
      // Get the SIM options directly from the service
      const simOptions = await firstValueFrom(
        this.simCardOptions$.pipe(take(1)),
      );
      if (simOptions) {
        const matchingSim = simOptions.find((sim) => sim.value === value);
        return matchingSim ? (matchingSim as any).simData : null;
      }
    } catch (error) {
      console.error('Error getting SIM data:', error);
    }
    return null;
  }
}
