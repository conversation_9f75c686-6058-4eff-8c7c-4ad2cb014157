// Inherit styles from shared components. Add specific overrides if necessary.
// Example: Adjust spacing or alignment if needed
// .create-pager-form {
//   // Custom styles for the form container
// }

// Style for the read-only fields like Matricule CGDIS
.readonly-field {
  padding-top: 0.5rem; // Adjust as needed for alignment
  font-style: italic;
  color: var(--text-muted); // Use appropriate theme variable
}

// Ensure columns have appropriate padding
.col-md-4 { // Changed from col-md-6
    padding-left: 15px;
    padding-right: 15px;
}

// Spacing for form sections or rows if needed
.form-section {
    margin-bottom: 1rem;
}

// Style for the audit link (disabled look)
.audit-link-disabled {
    color: var(--text-muted); // Use theme variable for disabled text
    pointer-events: none; // Prevent clicking
    cursor: default;
    text-decoration: none; // Remove underline if present
}

// Align maintenance password elements
.maintenance-password-container {
    display: flex;
    align-items: center; // Align items vertically
    gap: 10px; // Space between input and button

    .form-field-container {
        flex-grow: 1; // Allow input field to take available space
        margin-bottom: 0 !important; // Remove default margin if needed
    }
}
