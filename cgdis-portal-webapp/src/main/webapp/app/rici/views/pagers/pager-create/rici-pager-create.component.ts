import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  <PERSON><PERSON><PERSON><PERSON>,
  OnInit,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatTooltipModule } from '@angular/material/tooltip';
import { NgxSelectModule } from 'ngx-select-ex';
import { TranslateModule } from '@ngx-translate/core';
import { Router } from '@angular/router';
import { Subject } from 'rxjs';
import { FormModule } from '@app/common/modules/form-module/form.module';
import { PageTemplateModule } from '@app/common/template/page-template/page-template.module';
import { DefaultFormTemplateModule } from '@app/common/template/default-form-template/default-form-template.module';
import { SharedModule } from '@app/common/shared/shared.module';
import { FORM_SERVICE } from '@app/common/modules/form-module/service/iform.service';
import { PagerCreationService } from './service/pager-creation.service';
import { RiciPagersCommonFormFieldsComponent } from '@rici/views/pagers/common/common-form-fields/rici-pagers-common-form-fields.component';
import { RiciPagersCommonFormFieldsService } from '@rici/views/pagers/common/common-form-fields/rici-pagers-common-form-fields.service';

@Component({
  selector: 'cgdis-portal-rici-pager-create', // Changed selector
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatTooltipModule,
    NgxSelectModule,
    TranslateModule,
    FormModule,
    PageTemplateModule,
    DefaultFormTemplateModule,
    SharedModule,
    RiciPagersCommonFormFieldsComponent,
  ],
  templateUrl: './rici-pager-create.component.html',
  styleUrls: ['./rici-pager-create.component.scss'], // Added styleUrls
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    PagerCreationService,
    RiciPagersCommonFormFieldsService,
    {
      provide: FORM_SERVICE,
      useExisting: PagerCreationService,
    },
  ],
})
export class RiciPagerCreateComponent implements OnInit, OnDestroy {
  loading = false;

  private _unsubscribe$ = new Subject<void>();

  constructor(
    private router: Router,
    private cdr: ChangeDetectorRef,
    public pagerCreationService: PagerCreationService,
    private riciPagersCommonFormFieldsService: RiciPagersCommonFormFieldsService,
  ) {}

  ngOnInit(): void {
    this.loading = true;
    this.loading = false;
    this.riciPagersCommonFormFieldsService.test();
    this.cdr.markForCheck();
  }

  onCancel(): void {
    this.riciPagersCommonFormFieldsService.navigateBackWithFilters();
    // this.router.navigate(['/rici/pagers']); // Navigate back to the list
  }

  onValider(): void {
    // The form submission is handled by the DefaultFormService via the cgdis-portal-form component's submit event
    // No specific logic needed here unless you want to perform actions *before* the service's submit is called.
    console.log(
      'Validation button clicked, form service will handle submission.',
    );
  }

  ngOnDestroy(): void {
    this._unsubscribe$.next();
    this._unsubscribe$.complete();
  }
}
