<cgdis-portal-page-template>
  <cgdis-portal-page-header [subtitleAlign]="true" [titleKey]="'rici.pagers.creation.title'">
    <!-- Optional: Add subtitle or breadcrumbs if needed -->
  </cgdis-portal-page-header>

  <cgdis-portal-panel>

    <div *ngIf="loading" class="d-flex justify-content-center my-5">
      <cgdis-portal-spinner [loading]="loading"></cgdis-portal-spinner>
    </div>

    <div *ngIf="!loading">
      <cgdis-portal-default-form-template>
        <cgdis-portal-form
          (onCancel)="onCancel()"
          (onSubmitEmitter)="onValider()"
          [formClasses]="['create-pager-form']"
          [hideCancelAction]="false"
          formId="pager-creation-form"> <!-- Use onSubmitEmitter if service handles submit -->

          <cgdis-portal-rici-pagers-common-form-fields
            [formService]="pagerCreationService"></cgdis-portal-rici-pagers-common-form-fields>
        </cgdis-portal-form>
      </cgdis-portal-default-form-template>
    </div>
  </cgdis-portal-panel>
</cgdis-portal-page-template>
