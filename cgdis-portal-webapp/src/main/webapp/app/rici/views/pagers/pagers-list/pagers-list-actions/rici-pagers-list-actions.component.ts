import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { SharedModule } from '@app/common/shared/shared.module';
import { Router, RouterLink } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { MatDialog } from '@angular/material/dialog';
import { SimplePopupService } from '@app/common/modules/popup/simple-popup.service';
import { RiciPagersListTableService } from '@rici/views/pagers/pagers-list/pagers-list-table/rici-pagers-list-table.service';
import { ToastService } from '@app/common/shared/toasts/CGDISToastService';
import { finalize, take } from 'rxjs/operators';
import { CsvImportReport } from '@app/model/rici/csv-import-report.model';
import { RiciPagersListUploadPopupComponent } from './popups/rici-pagers-list-upload-popup.component';
import { RiciPagersListImportReportPopupComponent } from './popups/rici-pagers-list-import-report-popup/rici-pagers-list-import-report-popup.component';
import { SimpleLoadingPopupComponent } from '@rici/views/sim/sim-list/sim-list-actions/popups/simple-loading-popup.component'; // Reusing SIM's loading popup
import { CommonModule } from '@angular/common';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';

@Component({
  selector: 'cgdis-portal-rici-pagers-list-actions',
  standalone: true,
  imports: [
    SharedModule,
    RouterLink,
    TranslateModule,
    CommonModule,
    MatProgressSpinnerModule,
  ],
  templateUrl: './rici-pagers-list-actions.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  styleUrl: './rici-pagers-list-actions.component.scss',
})
export class RiciPagersListActionsComponent {
  @Input() isAdmin: boolean = false;

  constructor(
    private dialog: MatDialog,
    private router: Router, // Keep if other actions navigate
    private popupService: SimplePopupService,
    private pagersListService: RiciPagersListTableService,
    private toastService: ToastService,
  ) {}

  importCSV() {
    const uploadDialogRef = this.popupService.open(
      RiciPagersListUploadPopupComponent,
      {
        data: {
          contentType: ['text/csv', 'application/vnd.ms-excel'],
          multiple: false,
          maxFiles: 1,
        },
        width: '500px',
        panelClass: 'simple-popup',
        disableClose: true,
      },
    );

    uploadDialogRef
      .afterClosed()
      .pipe(take(1))
      .subscribe((selectedFile: File | undefined) => {
        if (selectedFile) {
          const loadingDialogRef = this.dialog.open(
            SimpleLoadingPopupComponent,
            {
              disableClose: true,
              width: '250px',
              data: { message: 'rici.pagers.list.popup.upload.loading' }, // Adjusted translation key
            },
          );

          this.pagersListService
            .importPagersCsv(selectedFile)
            .pipe(
              take(1),
              finalize(() => {
                loadingDialogRef.close();
              }),
            )
            .subscribe({
              next: (report: CsvImportReport) => {
                this.openReportPopup(report);
                if (report.successfullyImportedCount > 0) {
                  this.pagersListService.refreshData();
                }
              },
              error: (err) => {
                console.error('Error importing Pager CSV:', err);
                if (
                  err.error &&
                  typeof err.error === 'object' &&
                  err.error.fileName &&
                  err.error.lineInfos
                ) {
                  this.openReportPopup(err.error as CsvImportReport);
                  // } else {
                  //   this.toastService.error(
                  //     err.error?.message ||
                  //       err.message ||
                  //       'rici.pagers.list.popup.upload.error.generic', // Adjusted translation key
                  //   );
                }
              },
            });
        } else {
          console.log('Pager CSV import cancelled or no file selected.');
        }
      });
  }

  private openReportPopup(report: CsvImportReport): void {
    this.popupService.open(RiciPagersListImportReportPopupComponent, {
      data: report,
      width: '850px',
      panelClass: 'simple-popup',
      disableClose: false,
    });
  }
}
