@if (functionCodesModels && functionCodesToneColors && functionCodesTones) {

  <div class="row">
    <h3 class=" section-title first">{{ 'rici.schemas.form.schema.title' | translate }}
    </h3>

    <span class="form-header-link">
         <cgdis-portal-button-link
           [disabled]="!currentRicSchema"
           (click)="onAudit()"
           id="schema-audit-link">{{ 'rici.schemas.form.schema.audit' | translate }}
    </cgdis-portal-button-link>
    </span>


  </div>
  <div class="row">

    <cgdis-portal-input-field [class]="'col-4'"
                              [fieldMaxLength]="50"
                              [fieldRequired]="true"
                              [iconTooltipKey]="'rici.schemas.form.schema.name-tooltip'"
                              [icon]="'icon-question-circle'"
                              [labelKey]="'rici.schemas.form.schema.name'"
                              [name]="'schemaName'"
                              [initialValue]="currentRicSchema ? currentRicSchema.schemaName : ''"
    ></cgdis-portal-input-field>
    <cgdis-portal-input-field [class]="'col-4'"
                              [fieldMaxLength]="10"
                              [fieldRequired]="true"
                              [iconTooltipKey]="'rici.schemas.form.schema.alias-tooltip'"
                              [icon]="'icon-question-circle'"
                              [labelKey]="'rici.schemas.form.schema.alias'"
                              [name]="'schemaAlias'"
                              [initialValue]="currentRicSchema ? currentRicSchema.schemaAlias : ''"

    ></cgdis-portal-input-field>
    <cgdis-portal-number-field [class]="'col-4'"
                               [fieldMaxLength]="3"
                               [fieldRequired]="true"
                               [iconTooltipKey]="'rici.schemas.form.schema.suffix-tooltip'"
                               [icon]="'icon-question-circle'"
                               [labelKey]="'rici.schemas.form.schema.suffix'"
                               [name]="'ricSuffix'"
                               [initialValue]="currentRicSchema ? currentRicSchema.ricSuffix : ''"


    ></cgdis-portal-number-field>

  </div>
  <div class="row">
    <cgdis-portal-input-field [class]="'col-12'"
                              [fieldMaxLength]="250"
                              [fieldRequired]="true"
                              [iconTooltipKey]="'rici.schemas.form.schema.description-tooltip'"
                              [icon]="'icon-question-circle'"
                              [labelKey]="'rici.schemas.form.schema.description'"
                              [name]="'description'"
                              [initialValue]="currentRicSchema ? currentRicSchema.description : ''"

    ></cgdis-portal-input-field>
  </div>
  <div class="row">
    <h3 class="col-3 section-title">{{ 'rici.schemas.form.functions.title' | translate }}
      <cgdis-portal-icon [icon]="'icon-question-circle'"
                         [small]="true"
                         [tooltipTranslationKey]="'rici.schemas.form.functions.title-tooltip'"></cgdis-portal-icon>
    </h3>
  </div>

  <cgdis-portal-rici-schemas-form-function-group
    [functionCodesModels]="functionCodesModels" [functionCodesToneColors]="functionCodesToneColors"
    [functionCodesTones]="functionCodesTones"
    [functionKey]="'A'"
    [currentRicSchema]="currentRicSchema"></cgdis-portal-rici-schemas-form-function-group>
  <cgdis-portal-rici-schemas-form-function-group
    [functionCodesModels]="functionCodesModels" [functionCodesToneColors]="functionCodesToneColors"
    [functionCodesTones]="functionCodesTones"
    [functionKey]="'B'"
    [currentRicSchema]="currentRicSchema"></cgdis-portal-rici-schemas-form-function-group>
  <cgdis-portal-rici-schemas-form-function-group
    [functionCodesModels]="functionCodesModels" [functionCodesToneColors]="functionCodesToneColors"
    [functionCodesTones]="functionCodesTones"
    [functionKey]="'C'"
    [currentRicSchema]="currentRicSchema"></cgdis-portal-rici-schemas-form-function-group>
  <cgdis-portal-rici-schemas-form-function-group
    [functionCodesModels]="functionCodesModels" [functionCodesToneColors]="functionCodesToneColors"
    [functionCodesTones]="functionCodesTones"
    [functionKey]="'D'"
    [currentRicSchema]="currentRicSchema"></cgdis-portal-rici-schemas-form-function-group>
}
