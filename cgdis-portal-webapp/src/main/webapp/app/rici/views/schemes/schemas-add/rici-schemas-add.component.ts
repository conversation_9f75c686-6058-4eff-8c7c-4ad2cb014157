import { ChangeDetectionStrategy, Component } from '@angular/core';
import { DefaultFormTemplateModule } from '@app/common/template/default-form-template/default-form-template.module';
import { FormModule } from '@app/common/modules/form-module/form.module';
import { PageTemplateModule } from '@app/common/template/page-template/page-template.module';
import { SharedModule } from '@app/common/shared/shared.module';
import { Router } from '@angular/router';
import { FORM_SERVICE } from '@app/common/modules/form-module/service/iform.service';
import { RiciSchemasAddService } from '@rici/views/schemes/schemas-add/rici-schemas-add.service';
import { RiciSchemasFunctionCodesService } from '@rici/views/schemes/common/rici-schemas-function-codes.service';
import { RiciSchemasCommonFormFieldsComponent } from '@rici/views/schemes/common/common-form-fields/rici-schemas-common-form-fields.component';

@Component({
  selector: 'cgdis-portal-rici-schemas-add',
  standalone: true,
  imports: [
    DefaultFormTemplateModule,
    FormModule,
    PageTemplateModule,
    SharedModule,
    RiciSchemasCommonFormFieldsComponent,
  ],
  templateUrl: './rici-schemas-add.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    RiciSchemasAddService,
    RiciSchemasFunctionCodesService,
    {
      provide: FORM_SERVICE,
      useExisting: RiciSchemasAddService,
    },
  ],
})
export class RiciSchemasAddComponent {
  constructor(private router: Router) {}

  cancel() {
    this.router.navigate(['rici/schemas']);
  }
}
