import { Injectable } from '@angular/core';
import { CgdisDatatableService } from '@app/common/modules/datatable/cgdisdatatable-service';
import { RiciRicSchema } from '@rici/views/schemes/models/RiciRicSchema';
import { RestService } from '@eportal/core';
import { UntypedFormBuilder } from '@angular/forms';
import { Location } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { SimplePopupService } from '@app/common/modules/popup/simple-popup.service';

@Injectable()
export class RiciSchemasListTableService extends CgdisDatatableService<RiciRicSchema> {
  constructor(
    private restService: RestService,
    fb: UntypedFormBuilder,
    location: Location,
    router: Router,
    route: ActivatedRoute,
    popupService: SimplePopupService,
  ) {
    super(fb, location, router, route, popupService);
    super.initDataResourceList(
      restService.all<RiciRicSchema>('rici', 'schema', 'all'),
    );
  }

  deleteRicSchema(tecid: number) {
    return this.restService.one('rici', 'schema', tecid.toString()).delete();
  }
}
