<cgdis-portal-page-template>
  <!-- Header -->
  <cgdis-portal-page-header [subtitleAlign]="true" [titleKey]="'rici.pager-assignment-and-groups.list.title'"
                            [useDisplayFlex]="true">
    <span class="page-header-subtitle-flex" page-header-subtitle>
      <div class="entity-selector-wrapper">
                   <cgdis-portal-datatable-entity-filter
                     [datatableService]="riciPagersAssignmentTableService"
                     [entityPermission]="['ROLE_PERMISSION_RICI_PAGER_VIEW']"
                     [external]="true"
                     [filterConfig]="entityFilterConfig"
                     [filterName]="'entityTecid'"
                     [initPrimaryEntity]="!initialEntityTecid"
                     [initialEntityByTecid]="initialEntityTecid"
                     [underDisable]="true"
                   ></cgdis-portal-datatable-entity-filter>
      </div>
        <div class="alert-group-selector-wrapper">
          @if (alertGroups !== undefined && alertGroups.length > 1) {
            <ngx-select
              [items]="alertGroups"
              [optionValueField]="'value'"
              [optionTextField]="'label'"
              (selectionChanges)="setAlertGroupFilter($event)"
              [defaultValue]="initialAlertGroup ? [initialAlertGroup.value] : [alertGroups[0].value]"
              class="cgdis-portal-datatable-select-filter external"
            ></ngx-select>
          }

      </div>

    </span>
  </cgdis-portal-page-header>
  <cgdis-portal-rici-pagers-assignment-table
    (entityAlertGroupsOutput)="getAlertGroups($event)"
    [alertGroupFilter]="alertGroupFilter"></cgdis-portal-rici-pagers-assignment-table>


</cgdis-portal-page-template>
