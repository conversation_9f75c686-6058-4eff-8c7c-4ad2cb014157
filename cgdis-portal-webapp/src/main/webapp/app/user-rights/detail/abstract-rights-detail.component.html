<cgdis-portal-spinner [loading]="showSpinner"></cgdis-portal-spinner>
<cgdis-portal-form [customButtonsRow]="true">
  <div class="details">
    <div *ngIf="hasAnyRoles(['ROLE_PERMISSION_USER_RIGHTS_UPDATE'])" class="row">
      <div class="col-10 col-sm-5">
        <ngx-select (select)="selectRight($event)"
                    [allowClear]="true"
                    [class]="'permission-select'"
                    [items]="allRights"
                    [optionTextField]="'label'"
                    [optionValueField]="'value'"
                    [searchCallback]="this.search"
                    placeholder="{{ 'user-rights.'+rightType+'.select-permission' | translate}}"
                    style="background: transparent;"
        ></ngx-select>
      </div>
      <div class="col-2">
        <cgdis-portal-link-with-icon-add (click)="addRightToOne()"
                                         [additionnalLinkClasses]="['-vertical-align-middle']"
                                         [linkDisabled]="!hasAnyRoles(['ROLE_PERMISSION_USER_RIGHTS_UPDATE'])"
                                         [linkOK]="true"
                                         [rounded]="true"
                                         [smallIcon]="true"
                                         [tooltipText]="'tooltip.add' | translate"></cgdis-portal-link-with-icon-add>
      </div>
    </div>

    <div class="row">

      <ul *ngIf="currentSecurityRoles && currentSecurityRoles.length > 0; else noData" class="col-12 pt-3"
          style="list-style-type: none;">
        <div class="permission-search">
          <input [(ngModel)]="searchRight" placeholder="{{'user-rights.'+rightType+'.search-permission' | translate}}"
                 type="text" />
          <button (click)="searchRight = '';" *ngIf="searchRight !== null && searchRight.trim() != ''">
            <i class="fa fa-times"></i>
          </button>
        </div>
        <cgdis-portal-array-field [name]="'securityRole'" [uid]="'securityRole'"></cgdis-portal-array-field>
        <ng-container
          *ngFor="let securityRole of currentSecurityRoles; trackBy: trackBySecurityRole ; index as currentSecurityRolesIndex">
          <li *ngIf="isSearchResult(securityRole)" class="granted-permission">
            <div class="row">
              <div class="col-4 ">
                <span [matTooltip]="getTooltipText(securityRole)"
                      [ngClass]="{'parent-permission': securityRole.parentPermission || securityRole.parentPermissionDeletion}"
                >{{ (rightType === 'role' ? 'user-rights.permission.' + securityRole.permission + '.name' : 'user-rights.role.' + securityRole.role + '.name') | translate }}</span>
              </div>
              <p
                [innerHTML]="(rightType === 'role' ? 'user-rights.permission.' + securityRole.permission+'.description' :  'user-rights.role.' + securityRole.role+'.description')| translate"
                class="col-5  permission-description"></p>

              <div class="col-2">

                <cgdis-portal-radio-field
                  (onValueChange)="updateInheritance(securityRole, $event)"
                  [fieldReadonly]="!hasAnyRoles(['ROLE_PERMISSION_USER_RIGHTS_UPDATE'])"
                  [initialValue]="securityRole.entityInheritance"
                  [labelKey]="'user-rights.role.entityinheritance'"
                  [name]="'role_'+currentSecurityRolesIndex"
                  [parentArrayUid]="'securityRole'"
                  [possibleValues]="inheritanceValue"
                  [uid]="'role_'+one+'_'+currentSecurityRolesIndex"></cgdis-portal-radio-field>
              </div>

              <cgdis-portal-link-with-icon-delete (click)="delete(securityRole)"
                                                  [linkDisabled]="!hasAnyRoles(['ROLE_PERMISSION_USER_RIGHTS_DELETE'])"
                                                  [rounded]="true"
                                                  [smallIcon]="true"
                                                  [tooltipText]="'tooltip.delete' | translate"
                                                  class="col-1"
                                                  style="text-align: right;"></cgdis-portal-link-with-icon-delete>
            </div>
          </li>
        </ng-container>
        <li *ngIf="!hasSearchResult()" style="text-align: center;">
          <span>{{ 'user-rights.' + rightType + '.no-search-result' | translate }}</span>
        </li>
      </ul>
      <span *ngIf="hasSearchResult()" [translate]="'user-rights.'+rightType+'.dependency'"
            class="col-4 disclaimer-parent-permission"></span>
      <ng-template #noData>
        <div style="width: 100%; text-align: center; padding: 1rem;">
          <span>{{ 'user-rights.' + rightType + '.no-permissions' | translate }}</span>
        </div>
      </ng-template>
    </div>

  </div>
</cgdis-portal-form>

