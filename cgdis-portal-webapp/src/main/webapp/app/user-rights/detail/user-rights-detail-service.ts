import { Observable } from 'rxjs';
import { SecurityRole } from '../../model/security-role.model';
import { FieldOption } from '../../common/modules/form-module/model/field-option.model';

export interface UserRightsDetailService {
  /**
   * Get all possible permissions
   * @return {Observable<any[]>}
   */
  getAllRights(): Observable<any[]>;

  /**
   * Get all permissions for given role
   * @param {string} one
   * @return {Observable<any[]>}
   */
  getAllForOne(one: string): Observable<any[]>;

  /**
   * Add permission to role
   * @param {string} right
   * @param {string} one
   */
  addRightToOne(right: string, one: string): Observable<any>;

  /**
   * Delete a security role
   * @param {number} securityRole
   */
  delete(securityRole: SecurityRole): Observable<boolean>;

  /**
   * Update inheritance of rights
   * @param securityRole
   */
  updateInheritance(securityRole: SecurityRole): Observable<any>;

  /**
   * Map the rights to list of field options
   * @param rights
   * @param currentSecurityRoles
   */
  mapRightsToField(
    rights: any,
    currentSecurityRoles: SecurityRole[],
  ): FieldOption<string>[];

  getDependencyKeyForSecurityRole(securityRole: SecurityRole): string;
  getDeletionDependencyKeyForSecurityRole(securityRole: SecurityRole): string;
  getRightNameKey(right: SecurityRole): string;
}
