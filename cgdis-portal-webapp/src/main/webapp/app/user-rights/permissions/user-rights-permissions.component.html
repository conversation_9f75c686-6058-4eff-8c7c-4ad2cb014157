<cgdis-portal-page-template>

  <cgdis-portal-page-header [subtitleAlign]="true" [titleKey]="'layout.navigation.menu.items.user_rights.permissions'">
    <div page-header-subtitle>
      <!-- Entity filter -->
      <div style="display: inline-block">
        <ngx-select (selectionChanges)="selectRight($event)" [items]="allRights"
                    [allowClear]="true"
                    [optionTextField]="'label'"
                    [optionValueField]="'value'"
                    class="select-permissions"
                    placeholder="{{ 'user-rights.role.select-permission' | translate}}"
                    style="background: transparent;"
        ></ngx-select>
      </div>
    </div>

  </cgdis-portal-page-header>

  <cgdis-portal-panel>
    <div class="localSpinner">
      <cgdis-portal-cgdisdatatable
        [datatableService]="securityRoleService"
        [id]="'user-rights-permissions-list'">

        <ng-template #template let-row="row">
          <div class="person-detail-row">
            <cgdis-portal-user-rights-permissions-detail
              [one]="row.permission"></cgdis-portal-user-rights-permissions-detail>
          </div>
        </ng-template>

        <cgdis-portal-datatable-text-filter [customFormControl]="formControl"
                                            [datatableService]="securityRoleService"
                                            [filterName]="'permission'"
                                            [hidden]="true"></cgdis-portal-datatable-text-filter>

        <!-- role name -->
        <ep-datatable-column [columnName]="'permission'" [flexGrow]="45" [sortable]="false">
          <ng-template epDatatableHeader>
            <span [translate]="'user-rights.permission.column'"></span>
          </ng-template>
          <ng-template epDatatableCell let-context>
            {{ ('user-rights.permission.' + context.row.permission + '.name' | translate) | defaultValue:'-' }}

          </ng-template>
        </ep-datatable-column>

        <ep-datatable-column [columnName]="'description'" [flexGrow]="100" [sortable]="false">
          <ng-template epDatatableCell let-context>

            <span [innerHTML]="('user-rights.permission.' + context.row.permission+'.description' |translate) | defaultValue:'-' "
                  class="description"></span>
          </ng-template>
        </ep-datatable-column>

      </cgdis-portal-cgdisdatatable>
    </div>
  </cgdis-portal-panel>

</cgdis-portal-page-template>
