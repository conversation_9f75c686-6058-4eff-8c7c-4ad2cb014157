<svg display="none" xmlns="http://www.w3.org/2000/svg">

  <symbol id="icon-administratif" viewBox="0 0 512 512">
    <path
      d="m483 236c5-7 9-13 11-20 10-33-9-69-41-79-15-4-25-18-24-33 0-16-6-32-17-44-13-12-28-18-45-18-8 0-14 1-20 3-3 1-7 2-10 2-11 0-22-5-28-14-4-6-9-11-15-16-10-7-24-11-36-11-20 0-39 10-51 27-6 9-18 14-28 14-4 0-7-1-10-2-6-2-13-3-19-3-18 0-33 6-46 18-11 12-17 28-17 44 0 16-9 29-24 33-7 2-13 5-19 10-13 11-22 25-25 41-2 17 3 33 13 46 9 13 9 29 0 41-4 7-7 13-10 20-6 16-4 33 4 48 8 14 20 26 36 31 15 4 25 17 24 33 0 16 6 32 17 44 13 12 28 18 45 18 8 0 14-1 20-3 3-1 7-2 10-2 11 0 22 5 28 14 4 6 9 11 15 16 10 7 24 11 36 11 21 0 39-10 51-27 6-9 18-14 28-14 3 0 7 1 10 2 6 2 13 3 21 3 17 0 32-6 45-18 11-12 17-28 17-44 0-16 9-29 24-33 7-2 13-5 19-10 28-21 33-61 12-89-10-11-10-27-1-39z m-19 93c-4 8-11 14-20 17-26 8-45 33-45 61 0 9-3 17-9 24-6 6-15 10-24 10-5 0-8-1-12-2-6-2-13-3-19-3-21 0-39 10-51 27-2 3-5 6-9 8-6 4-12 6-19 6-11 0-22-5-28-14-11-17-30-27-51-27-7 0-13 1-19 3-3 1-7 2-12 2-9 0-18-4-24-10-6-6-10-15-10-24 0-28-18-53-44-61-5-1-8-3-11-5-15-11-18-33-7-48 17-22 17-52 0-75-2-3-4-6-5-10-3-8-2-18 2-26 4-8 11-14 20-17 27-7 46-32 46-60 0-9 3-18 10-24 6-6 15-10 24-10 5 0 8 1 12 2 6 2 13 3 19 3 21 0 39-10 51-27 2-3 5-6 9-8 6-4 12-6 19-6 11 0 22 5 28 14 11 17 30 27 51 27 7 0 13-1 19-3 3-1 7-2 12-2 9 0 18 4 24 10 6 6 10 15 10 24 0 28 18 53 44 61 5 1 8 3 11 5 13 10 17 30 9 44l-1 1-3 2c-17 23-17 53 0 75 2 3 4 6 5 10 3 8 2 18-2 26z m-146-130c-3 0-7 1-9 3l-1 1-74 73-4-4-5-4-21-21c-3-3-6-4-10-4-5 0-9 2-11 4-3 3-4 6-4 10 0 4 2 8 4 10l41 41c3 3 6 4 10 4 5 0 9-2 11-4l84-85c6-6 6-15 0-21-2-1-6-3-11-3z" />
  </symbol>

  <symbol id="icon-sim" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M8 6.5V8.5M11 6.5V8.5M14 6.5V8.5M8.2 3H13.462C14.0268 3 14.3092 3 14.5699 3.07183C14.8009 3.13546 15.0186 3.24008 15.2126 3.3807C15.4315 3.53943 15.608 3.75994 15.9608 4.20098L18.2988 7.12348C18.5587 7.44834 18.6886 7.61078 18.781 7.79166C18.863 7.95213 18.9229 8.12295 18.9592 8.29948C19 8.49845 19 8.70646 19 9.1225V17.8C19 18.9201 19 19.4802 18.782 19.908C18.5903 20.2843 18.2843 20.5903 17.908 20.782C17.4802 21 16.9201 21 15.8 21H8.2C7.0799 21 6.51984 21 6.09202 20.782C5.71569 20.5903 5.40973 20.2843 5.21799 19.908C5 19.4802 5 18.9201 5 17.8V6.2C5 5.0799 5 4.51984 5.21799 4.09202C5.40973 3.71569 5.71569 3.40973 6.09202 3.21799C6.51984 3 7.0799 3 8.2 3Z"
      fill="none" stroke-linecap="round" stroke-linejoin="round" stroke-opacity="1"
      stroke-width="2" />
  </symbol>

  <symbol id="icon-sim-slash" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M8 8.5V8M14 6.5V8.34961M8.65039 3H13.462C14.0268 3 14.3092 3 14.5699 3.07183C14.8009 3.13546 15.0186 3.24008 15.2126 3.3807C15.4315 3.53943 15.608 3.75994 15.9608 4.20098L18.2988 7.12348C18.5587 7.44834 18.6886 7.61078 18.781 7.79166C18.863 7.95213 18.9229 8.12295 18.9592 8.29948C19 8.49845 19 8.70646 19 9.1225V13.3496M3 3L5.01811 5.01811M21 21L18.9819 18.9819M5.01811 5.01811C5 5.32914 5 5.71125 5 6.2V17.8C5 18.9201 5 19.4802 5.21799 19.908C5.40973 20.2843 5.71569 20.5903 6.09202 20.782C6.51984 21 7.0799 21 8.2 21H15.8C16.9201 21 17.4802 21 17.908 20.782C18.2843 20.5903 18.5903 20.2843 18.782 19.908C18.9049 19.6668 18.9585 19.3837 18.9819 18.9819M5.01811 5.01811L8 8M18.9819 18.9819L8 8"
      fill="none" stroke-linecap="round" stroke-linejoin="round" stroke-opacity="1"
      stroke-width="2" />
  </symbol>

  <!--<svg id="icon-allowance" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 122.88 63.89">
    <path d="M0 0h122.88v63.89H0V0zm64.44 35.32h-4.79c.04 1.36.41 2.36 1.11 2.99.66.63 1.81.96 3.36.96 1.03 0 1.92-.15 2.69-.48l.66 4.13c-1.36.29-2.69.48-4.02.48-2.77 0-4.98-.7-6.6-2.14-1.62-1.44-2.51-3.43-2.58-5.94h-2.54v-2.47h2.54v-1.88h-2.54v-2.43h2.58c.18-2.54 1.14-4.5 2.84-5.94 1.7-1.44 3.98-2.14 6.78-2.14 1.07 0 2.29.15 3.58.48l-.66 4.13c-.81-.33-1.66-.48-2.54-.48-1.4 0-2.51.33-3.24.96-.74.63-1.18 1.62-1.33 2.99h4.76v2.43h-4.79v1.88h4.79v2.47h-.06zm-2.98-22.16c10.4 0 18.8 8.41 18.8 18.8 0 10.4-8.41 18.8-18.8 18.8-10.4 0-18.8-8.41-18.8-18.8s8.4-18.8 18.8-18.8zM20.54 9.73h81.7c0 5.2 4.24 9.44 9.44 9.44v25.25c-5.2 0-9.44 4.24-9.44 9.44h-81.7c0-5.2-4.24-9.44-9.44-9.44V19.17c5.2 0 9.44-4.24 9.44-9.44z" fill-rule="evenodd" clip-rule="evenodd"/>
  </svg>-->

  <svg id="icon-reload2" viewBox="0 0 256.676 256.676">
    <path
      d="M240.455,170.828l-40-29V70.441H89.781v30.439L19.221,50.441L89.781,0v30.441h150.674V170.828z M237.455,206.236	l-70.559-50.441v30.441H56.221V114.85l-40-29v140.387h150.676v30.44L237.455,206.236z" />
  </svg>

  <svg id="icon-question-circle" viewBox="0 0 512 512">
    <path
      d="M504 256c0 137-111 248-248 248S8 393 8 256C8 119.1 119 8 256 8s248 111.1 248 248zM262.7 90c-54.5 0-89.3 23-116.5 63.8-3.5 5.3-2.4 12.4 2.7 16.3l34.7 26.3c5.2 3.9 12.6 3 16.7-2.1 17.9-22.7 30.1-35.8 57.3-35.8 20.4 0 45.7 13.1 45.7 33 0 15-12.4 22.7-32.5 34C247.1 238.5 216 254.9 216 296v4c0 6.6 5.4 12 12 12h56c6.6 0 12-5.4 12-12v-1.3c0-28.5 83.2-29.6 83.2-106.7 0-58-60.2-102-116.5-102zM256 338c-25.4 0-46 20.6-46 46 0 25.4 20.6 46 46 46s46-20.6 46-46c0-25.4-20.6-46-46-46z" />
  </svg>

  <svg id="icon-allowance" viewBox="0 0 37 40">
    <path
      d="M 20.1875 31.664062 L 20.1875 28.898438 C 23.679688 28.507812 25.828125 26.445312 25.828125 23.46875 C 25.890625 21.992188 25.257812 20.5625 24.109375 19.59375 C 23.078125 18.789062 21.820312 18.320312 20.507812 18.234375 L 18.460938 17.929688 C 17.617188 17.820312 16.820312 17.507812 16.132812 17.023438 C 15.632812 16.5625 15.375 15.898438 15.4375 15.234375 C 15.4375 13.375 16.757812 12.257812 18.976562 12.257812 C 20.484375 12.179688 21.960938 12.703125 23.0625 13.710938 L 23.34375 13.953125 L 25.1875 12.171875 L 24.882812 11.898438 C 23.601562 10.726562 21.945312 10.015625 20.1875 9.898438 L 20.1875 7.132812 L 17.8125 7.132812 L 17.8125 9.921875 C 14.875 10.078125 12.601562 12.460938 12.671875 15.3125 C 12.601562 16.695312 13.171875 18.039062 14.226562 18.976562 C 15.273438 19.78125 16.53125 20.289062 17.859375 20.4375 L 20.039062 20.773438 C 20.859375 20.78125 21.648438 21.054688 22.28125 21.546875 C 22.820312 22.070312 23.09375 22.78125 23.039062 23.515625 C 23.039062 25.375 21.539062 26.492188 19.023438 26.492188 C 17.210938 26.601562 15.445312 25.914062 14.21875 24.617188 L 13.9375 24.34375 L 12.007812 26.210938 L 12.289062 26.484375 C 13.742188 27.953125 15.71875 28.835938 17.8125 28.9375 L 17.8125 31.664062 Z M 20.1875 31.664062 " />
  </svg>

  <symbol id="icon-barracked" viewBox="0 0 512 512">
    <path
      d="m483 236c5-7 9-13 11-20 10-33-9-69-41-79-15-4-25-18-24-33 0-16-6-32-17-44-13-12-28-18-45-18-8 0-14 1-20 3-3 1-7 2-10 2-11 0-22-5-28-14-4-6-9-11-15-16-10-7-24-11-36-11-20 0-39 10-51 27-6 9-18 14-28 14-4 0-7-1-10-2-6-2-13-3-19-3-18 0-33 6-46 18-11 12-17 28-17 44 0 16-9 29-24 33-7 2-13 5-19 10-13 11-22 25-25 41-2 17 3 33 13 46 9 13 9 29 0 41-4 7-7 13-10 20-6 16-4 33 4 48 8 14 20 26 36 31 15 4 25 17 24 33 0 16 6 32 17 44 13 12 28 18 45 18 8 0 14-1 20-3 3-1 7-2 10-2 11 0 22 5 28 14 4 6 9 11 15 16 10 7 24 11 36 11 21 0 39-10 51-27 6-9 18-14 28-14 3 0 7 1 10 2 6 2 13 3 21 3 17 0 32-6 45-18 11-12 17-28 17-44 0-16 9-29 24-33 7-2 13-5 19-10 28-21 33-61 12-89-10-11-10-27-1-39z m-19 93c-4 8-11 14-20 17-26 8-45 33-45 61 0 9-3 17-9 24-6 6-15 10-24 10-5 0-8-1-12-2-6-2-13-3-19-3-21 0-39 10-51 27-2 3-5 6-9 8-6 4-12 6-19 6-11 0-22-5-28-14-11-17-30-27-51-27-7 0-13 1-19 3-3 1-7 2-12 2-9 0-18-4-24-10-6-6-10-15-10-24 0-28-18-53-44-61-5-1-8-3-11-5-15-11-18-33-7-48 17-22 17-52 0-75-2-3-4-6-5-10-3-8-2-18 2-26 4-8 11-14 20-17 27-7 46-32 46-60 0-9 3-18 10-24 6-6 15-10 24-10 5 0 8 1 12 2 6 2 13 3 19 3 21 0 39-10 51-27 2-3 5-6 9-8 6-4 12-6 19-6 11 0 22 5 28 14 11 17 30 27 51 27 7 0 13-1 19-3 3-1 7-2 12-2 9 0 18 4 24 10 6 6 10 15 10 24 0 28 18 53 44 61 5 1 8 3 11 5 13 10 17 30 9 44l-1 1-3 2c-17 23-17 53 0 75 2 3 4 6 5 10 3 8 2 18-2 26z m-146-130c-3 0-7 1-9 3l-1 1-74 73-4-4-5-4-21-21c-3-3-6-4-10-4-5 0-9 2-11 4-3 3-4 6-4 10 0 4 2 8 4 10l41 41c3 3 6 4 10 4 5 0 9-2 11-4l84-85c6-6 6-15 0-21-2-1-6-3-11-3z" />
  </symbol>

  <symbol id="icon-aidant" viewBox="0 0 512 512">
    <path
      d="m455 101l-98 0 0-33c0-19-15-33-32-33l-137 0c-18 0-32 15-32 33l0 33-103 0c-27 0-49 23-49 50l0 271c0 28 23 49 49 49l402 0c30 0 55-25 55-55l0-259c-1-30-26-56-55-56z m-267-33l136 0 0 33-136 0z m288 350c0 12-10 22-22 22l-401 0c-9 0-16-7-16-16l0-272c0-10 7-17 16-17l402 0c12 0 22 10 22 23l0 260z m-152-148l-51 0 0-50c0-9-7-16-16-16-9 0-16 7-16 16l0 50-51 0c-9 0-16 8-16 17 0 9 7 16 16 16l51 0 0 50c0 9 7 17 16 17 9 0 16-8 16-17l0-50 51 0c9 0 16-7 16-16 0-9-7-17-16-17z" />
  </symbol>

  <symbol id="icon-ambulance" viewBox="0 0 200 200">
    <path
      d="M 71.9375,6.84375 L 71.9375,51.09375 L 33.625,28.96875 L 8,73.375 L 46.3125,95.46875 L 8,117.59375 L 33.625,162.03125 L 71.9375,139.90625 L 71.9375,184.15625 L 123.25,184.15625 L 123.25,139.9375 L 161.53125,162.03125 L 187.1875,117.59375 L 148.875,95.5 L 187.1875,73.375 L 161.53125,28.96875 L 123.25,51.0625 L 123.25,6.84375 L 71.9375,6.84375 z"
      id="rect13822"
      style="opacity:1;fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:#395475;stroke-width:1.70000005;stroke-linecap:round;stroke-linejoin:miter;stroke-miterlimit:4;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1" />
    <path
      d="M 75.09375,10 L 75.09375,56.53125 L 34.78125,33.25 L 12.28125,72.21875 L 52.59375,95.5 L 12.28125,118.75 L 34.78125,157.71875 L 75.09375,134.4375 L 75.09375,181 L 120.09375,181 L 120.09375,134.46875 L 160.375,157.71875 L 182.875,118.75 L 142.5625,95.5 L 182.875,72.21875 L 160.375,33.25 L 120.09375,56.5 L 120.09375,10 L 75.09375,10 z"
      id="rect14795"
      style="opacity:1;fill:#395475;fill-opacity:1;fill-rule:nonzero;stroke:none;stroke-width:1.70000005;stroke-linecap:round;stroke-linejoin:miter;stroke-miterlimit:4;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1" />
    <path
      d="M 92.517151,28.749341 C 92.408986,25.487473 95.364102,22.444786 98.522226,22.444786 C 100.78169,22.444786 103.56391,24.163507 103.49852,27.028713 C 102.70837,61.652668 100.60467,131.89502 99.809866,166.84929 C 99.785836,167.9063 98.454636,167.43783 98.406332,166.35356 C 96.861909,131.68636 93.680464,63.830863 92.517151,28.749341 z"
      id="path2161"
      style="fill:#ffffff;fill-opacity:1;fill-rule:evenodd;stroke:#395475;stroke-width:0.60000002;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:4;stroke-dasharray:none;stroke-opacity:1" />
    <path
      d="M 94.036939,33.435356 C 96.542562,33.435356 97.667652,34.958914 99.217717,36.299367 C 100.71504,37.594206 101.88918,38.77889 101.88918,40.05277 C 101.88918,41.391534 100.90645,40.990382 99.865157,41.482074 C 99.316637,41.74108 99.365585,42.300792 98.469657,42.300792 C 96.316099,42.300792 95.45867,40.274406 92.51715,40.274406 C 90.093039,40.274406 86.944591,43.179145 86.944591,48.506596 C 86.944591,56.552685 90.047493,60.158311 93.720316,62.944591 L 94.100263,72.506596 C 90.174142,70.860159 80.042216,61.931473 80.042216,49.519789 C 80.042216,38.718116 87.055227,33.435356 94.036939,33.435356 z"
      id="path4102"
      style="fill:#ffffff;fill-opacity:1;fill-rule:evenodd;stroke:#395475;stroke-width:0.60000002;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:4;stroke-dasharray:none;stroke-opacity:1" />
    <path
      d="M 94.986808,36.126649 C 96.778096,35.842684 96.597917,36.918831 97.39314,37.614775 C 95.554706,37.669411 95.147378,36.898305 94.986808,36.126649 z"
      id="path5075"
      style="fill:#395475;fill-opacity:1;fill-rule:evenodd;stroke:none;stroke-width:1px;stroke-linecap:butt;stroke-linejoin:miter;stroke-opacity:1" />
    <path
      d="M 102.52243,66.934037 L 102.26913,76.05277 C 102.26913,81.62722 87.514512,91.407424 87.514512,101.38259 C 87.514512,109.06464 93.688655,113.98417 95.968338,115.12401 L 95.683377,107.08179 C 95.683377,107.08179 92.897098,105.20276 92.897098,102.01583 C 92.897098,97.506158 109.86807,85.891676 109.86807,77.002639 C 109.86807,70.699933 105.56201,68.348285 102.52243,66.934037 z"
      id="path8971"
      style="fill:#ffffff;fill-opacity:1;fill-rule:evenodd;stroke:#395475;stroke-width:0.60000002;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:4;stroke-dasharray:none;stroke-opacity:1" />
    <path
      d="M 101.35093,111.54617 C 101.35093,111.54617 107.14512,115.37689 107.14512,120.18997 C 107.14512,124.30746 103.98764,128.07412 100.25762,131.96931 C 95.900204,136.51968 94.980033,142.21601 97.551452,148.17942 L 97.773087,153.0554 C 95.905013,151.53561 92.960422,146.78869 92.960422,138.87071 C 92.960422,129.33988 101.12929,126.47258 101.12929,117.68866 L 101.35093,111.54617 z"
      id="path11880"
      style="fill:#ffffff;fill-opacity:1;fill-rule:evenodd;stroke:#395475;stroke-width:0.60000002;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:4;stroke-dasharray:none;stroke-opacity:1" />
    <path
      d="M 100.21108,151.25066 C 100.21108,151.25066 102.90238,152.69861 102.90238,156.47493 C 102.90238,160.02958 98.451601,163.27556 97.773087,163.75726 C 97.11579,164.2239 96.863869,163.61619 97.39314,163.12401 C 97.939619,162.61583 100.17942,159.61423 100.17942,156.15831 L 100.21108,151.25066 z"
      id="path12851"
      style="fill:#ffffff;fill-rule:evenodd;stroke:#395475;stroke-width:0.60000002;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:4;stroke-dasharray:none;stroke-opacity:1" />
  </symbol>


  <symbol id="icon-person" viewBox="-42 0 512 512.002">
    <path
      d="m210.351562 246.632812c33.882813 0 63.222657-12.152343 87.195313-36.128906 23.972656-23.972656 36.125-53.304687 36.125-87.191406 0-33.875-12.152344-63.210938-36.128906-87.191406-23.976563-23.96875-53.3125-36.121094-87.191407-36.121094-33.886718 0-63.21875 12.152344-87.191406 36.125s-36.128906 53.308594-36.128906 87.1875c0 33.886719 12.15625 63.222656 36.132812 87.195312 23.976563 23.96875 53.3125 36.125 87.1875 36.125zm0 0" />
    <path
      d="m426.128906 393.703125c-.691406-9.976563-2.089844-20.859375-4.148437-32.351563-2.078125-11.578124-4.753907-22.523437-7.957031-32.527343-3.308594-10.339844-7.808594-20.550781-13.371094-30.335938-5.773438-10.15625-12.554688-19-20.164063-26.277343-7.957031-7.613282-17.699219-13.734376-28.964843-18.199219-11.226563-4.441407-23.667969-6.691407-36.976563-6.691407-5.226563 0-10.28125 2.144532-20.042969 8.5-6.007812 3.917969-13.035156 8.449219-20.878906 13.460938-6.707031 4.273438-15.792969 8.277344-27.015625 11.902344-10.949219 3.542968-22.066406 5.339844-33.039063 5.339844-10.972656 0-22.085937-1.796876-33.046874-5.339844-11.210938-3.621094-20.296876-7.625-26.996094-11.898438-7.769532-4.964844-14.800782-9.496094-20.898438-13.46875-9.75-6.355468-14.808594-8.5-20.035156-8.5-13.3125 0-25.75 2.253906-36.972656 6.699219-11.257813 4.457031-21.003906 10.578125-28.96875 18.199219-7.605469 7.28125-14.390625 16.121094-20.15625 26.273437-5.558594 9.785157-10.058594 19.992188-13.371094 30.339844-3.199219 10.003906-5.875 20.945313-7.953125 32.523437-2.058594 11.476563-3.457031 22.363282-4.148437 32.363282-.679688 9.796875-1.023438 19.964844-1.023438 30.234375 0 26.726562 8.496094 48.363281 25.25 64.320312 16.546875 15.746094 38.441406 23.734375 65.066406 23.734375h246.53125c26.625 0 48.511719-7.984375 65.0625-23.734375 16.757813-15.945312 25.253906-37.585937 25.253906-64.324219-.003906-10.316406-.351562-20.492187-1.035156-30.242187zm0 0" />
  </symbol>

  <symbol id="icon-logo" viewBox="200 2 200 129">
    <g
      id="g10"
      transform="matrix(1.3333333,0,0,-1.3333333,0,132.28)">
      <g
        id="g12"
        transform="scale(0.1)">
        <path
          d="M 3968.5,559.363 V 992.129 L 0,992.129 V 559.363 L 65.1953,494.168 0,428.984 V 0 h 3968.5 v 428.984 l -65.18,65.184 65.18,65.195"
          id="path14"
          style="fill:#2d3c7d;fill-opacity:1;fill-rule:nonzero;stroke:none" />
        <path
          d="M 3878.31,901.934 V 796.063 h 90.19 V 992.129 H 0 V 796.063 H 90.1953 V 901.934 H 3878.31"
          id="path16"
          style="fill:#ed1c24;fill-opacity:1;fill-rule:nonzero;stroke:none" />
        <path
          d="M 90.1953,90.1953 V 196.063 H 0 V 0 h 3968.5 v 196.063 h -90.19 V 90.1953 H 90.1953"
          id="path18"
          style="fill:#00adef;fill-opacity:1;fill-rule:nonzero;stroke:none" />
        <path
          d="M 828.25,737.293 V 250.156"
          id="path20"
          style="fill:none;stroke:#ffffff;stroke-width:12.75339985;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:10;stroke-dasharray:none;stroke-opacity:1" />
        <path
          d="m 331.102,605.223 c 0.371,-0.352 1.093,-0.633 1.613,-0.633 h 116.883 116.894 c 0.52,0 1.238,0.281 1.602,0.633 l 52.441,50.293 c 0.879,0.839 0.879,2.226 0,3.078 l -51.965,49.832 c -0.886,0.851 -2.332,0.851 -3.211,0 l -46.355,-44.453 c -0.879,-0.848 -2.324,-0.848 -3.203,0 l -29.336,28.125 c -0.852,0.82 -1.328,1.925 -1.328,3.078 v 7.82 c 0,1.067 0.41,2.09 1.14,2.891 l 22.442,24.25 c 0.918,0.996 0.754,2.429 -0.364,3.211 l -57.417,39.816 c -0.735,0.508 -1.934,0.508 -2.668,0 l -57.422,-39.816 c -1.114,-0.782 -1.282,-2.215 -0.371,-3.211 l 22.441,-24.25 c 0.742,-0.801 1.145,-1.824 1.145,-2.891 v -7.82 c 0,-1.153 -0.469,-2.258 -1.333,-3.078 l -29.324,-28.125 c -0.886,-0.848 -2.324,-0.848 -3.215,0 l -46.343,44.453 c -0.891,0.851 -2.328,0.851 -3.215,0 l -51.961,-49.832 c -0.879,-0.852 -0.879,-2.239 0,-3.078 l 52.43,-50.293"
          id="path22"
          style="fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none" />
        <path
          d="m 430.645,346.211 -64.028,48.074 c -0.515,0.391 -0.828,0.977 -0.879,1.602 l -4.707,53.82 c -0.054,0.801 -0.73,1.68 -1.511,1.961 l -69.305,25.273 c -1.738,0.625 -3.176,-0.312 -3.188,-2.097 l -0.386,-61.192 c -0.012,-1.027 0.847,-2.031 1.914,-2.226 l 47.398,-12.871 c 1.301,-0.235 1.445,-0.977 0.324,-1.653 l -59.863,-26.511 c -1.133,-0.676 -1.453,-2.09 -0.715,-3.157 l 44.731,-64.636 c 0.574,-0.84 1.843,-1.223 2.82,-0.852 l 106.785,40.617 c 2.063,0.782 2.336,2.508 0.61,3.848"
          id="path24"
          style="fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none" />
        <path
          d="m 678.238,555.75 -35.437,41.027 c -0.653,0.996 -2.063,1.356 -3.133,0.789 l -55.727,-29.363 c -0.691,-0.371 -1.472,-0.558 -2.261,-0.558 H 449.598 317.52 c -0.793,0 -1.567,0.187 -2.258,0.558 l -55.723,29.363 c -1.086,0.567 -2.488,0.207 -3.137,-0.789 L 220.965,555.75 c -0.402,-0.605 -0.43,-1.621 -0.07,-2.254 l 36.996,-41.25 c 0.894,-1.582 2.625,-1.758 3.855,-0.391 l 22.356,16.454 c 0.64,0.722 1.707,0.976 2.644,0.625 L 387.41,490.789 c 0.852,-0.312 1.426,-1.074 1.485,-1.941 l 4.101,-60.352 c 0.078,-1.164 1.121,-2.109 2.332,-2.109 l 54.27,-0.215 54.277,0.215 c 1.199,0 2.258,0.945 2.336,2.109 l 4.102,60.352 c 0.054,0.867 0.632,1.629 1.484,1.941 l 100.664,38.145 c 0.926,0.351 1.988,0.097 2.644,-0.625 l 22.356,-16.454 c 1.219,-1.367 2.957,-1.191 3.855,0.391 l 36.993,41.25 c 0.363,0.633 0.324,1.649 -0.071,2.254"
          id="path26"
          style="fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none" />
        <path
          d="m 523.633,284.637 -73.164,27.656 c -0.481,0.176 -1.25,0.176 -1.731,0 l -73.164,-27.656 c -0.82,-0.313 -1.496,-1.258 -1.496,-2.098 v -29.523 c 0,-0.508 0.317,-1.231 0.695,-1.59 l 17.665,-16.934 c 0.375,-0.371 1.125,-0.664 1.664,-0.664 h 55.496 55.507 c 0.54,0 1.282,0.293 1.661,0.664 l 17.668,16.934 c 0.379,0.359 0.679,1.082 0.679,1.59 v 29.523 c 0,0.84 -0.664,1.785 -1.48,2.098"
          id="path28"
          style="fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none" />
        <path
          d="m 468.563,346.211 64.023,48.074 c 0.519,0.391 0.832,0.977 0.867,1.602 l 4.719,53.82 c 0.051,0.801 0.73,1.68 1.516,1.961 l 69.292,25.273 c 1.75,0.625 3.176,-0.312 3.188,-2.097 l 0.398,-61.192 c 0.008,-1.027 -0.859,-2.031 -1.914,-2.226 L 563.25,398.555 c -1.301,-0.235 -1.445,-0.977 -0.32,-1.653 l 59.863,-26.511 c 1.121,-0.676 1.441,-2.09 0.711,-3.157 l -44.727,-64.636 c -0.578,-0.84 -1.855,-1.223 -2.824,-0.852 l -106.785,40.617 c -2.063,0.782 -2.332,2.508 -0.605,3.848"
          id="path30"
          style="fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none" />
        <path
          d="m 1089.73,250.156 h -85.35 v 27.735 h 24.66 v 121.386 h -24.66 v 27.735 h 85.35 V 399.277 H 1065.2 V 277.891 h 24.53 v -27.735"
          id="path32"
          style="fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none" />
        <path
          d="m 1225.58,250.156 -64.4,126.375 V 250.156 h -35.26 v 176.856 h 46.76 l 59.93,-119.231 v 119.231 h 35.4 V 250.156 h -42.43"
          id="path34"
          style="fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none" />
        <path
          d="m 1455.64,279.422 c -7.58,-11.336 -16.88,-19.656 -27.92,-24.981 -11.03,-5.32 -24.6,-7.988 -40.69,-7.988 -54.78,0 -82.17,30.703 -82.17,92.129 0,31.438 6.94,54.648 20.83,69.641 13.89,14.988 33.91,22.488 60.06,22.488 17.29,0 31.39,-2.918 42.29,-8.75 10.9,-5.84 19.21,-13.484 24.92,-22.938 l -27.98,-21.718 c -4.09,6.133 -7.84,10.554 -11.25,13.281 -3.41,2.723 -7.29,4.773 -11.62,6.133 -4.35,1.367 -9.54,2.051 -15.6,2.051 -14.48,0 -25.51,-4.629 -33.09,-13.868 -7.58,-9.25 -11.37,-24.687 -11.37,-46.32 0,-20.195 3.55,-35.273 10.66,-45.234 7.12,-9.969 18.47,-14.95 34.06,-14.95 10.47,0 18.64,2 24.47,6.004 5.84,4.004 11.14,9.328 15.91,15.977 l 28.49,-20.957"
          id="path36"
          style="fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none" />
        <path
          d="m 1486.39,250.156 v 176.856 h 114.88 v -31.828 h -78.84 v -37.696 h 66.44 v -31.941 h -66.44 v -42.422 h 78.84 v -32.969 h -114.88"
          id="path38"
          style="fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none" />
        <path
          d="m 1738.8,250.156 -64.41,126.375 V 250.156 h -35.26 v 176.856 h 46.77 l 59.92,-119.231 v 119.231 h 35.4 V 250.156 h -42.42"
          id="path40"
          style="fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none" />
        <path
          d="m 1926.14,338.836 c 0,15.422 -1.51,26.855 -4.53,34.309 -3.03,7.449 -8.16,12.8 -15.4,16.035 -7.24,3.242 -17.98,4.863 -32.2,4.863 h -13.8 V 283.125 h 13.8 c 14.22,0 24.93,1.641 32.14,4.922 7.2,3.281 12.32,8.75 15.39,16.414 3.07,7.668 4.6,19.121 4.6,34.375 z m 37.19,-0.254 c 0,-21.297 -2.94,-38.418 -8.82,-51.367 -5.88,-12.949 -14.74,-22.363 -26.58,-28.242 -11.84,-5.879 -28.49,-8.817 -49.96,-8.817 h -53.92 v 176.856 h 53.41 c 20.87,0 37.46,-3.028 49.77,-9.082 12.31,-6.047 21.4,-15.481 27.28,-28.301 5.88,-12.824 8.82,-29.836 8.82,-51.047"
          id="path42"
          style="fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none" />
        <path
          d="m 2073.33,250.156 h -85.35 v 27.735 h 24.65 v 121.386 h -24.65 v 27.735 h 85.35 V 399.277 H 2048.8 V 277.891 h 24.53 v -27.735"
          id="path44"
          style="fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none" />
        <path
          d="m 2109.51,250.156 v 176.856 h 114.87 v -31.828 h -78.84 v -37.696 h 66.45 v -31.941 h -66.45 v -42.422 h 78.84 v -32.969 h -114.87"
          id="path46"
          style="fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none" />
        <path
          d="m 2413.36,394.297 c 0,3.484 -1.46,6.387 -4.35,8.691 -2.9,2.293 -6.69,3.446 -11.38,3.446 -5.95,0 -10.45,-1.454 -13.47,-4.344 -3.03,-2.902 -4.54,-6.856 -4.54,-11.887 0,-3.496 1,-6.941 3,-10.351 2.01,-3.407 5,-7.364 9.01,-11.883 5.54,3.66 9.95,6.961 13.23,9.902 3.27,2.938 5.51,5.535 6.71,7.801 1.19,2.258 1.79,5.129 1.79,8.625 z m -30.41,-65.938 c -8.01,-5.031 -13.6,-9.902 -16.75,-14.629 -3.15,-4.726 -4.73,-10.117 -4.73,-16.164 0,-7.664 2.85,-13.652 8.57,-17.957 5.7,-4.296 13.49,-6.457 23.39,-6.457 10.98,0 20.6,3.243 28.87,9.719 z m 66.82,-78.203 -9.2,10.996 c -6.13,-4.179 -12.86,-7.675 -20.19,-10.48 -7.33,-2.813 -16.49,-4.219 -27.47,-4.219 -20.19,0 -36.14,4.641 -47.85,13.926 -11.72,9.289 -17.57,21.769 -17.57,37.441 0,11.164 2.44,20.403 7.34,27.727 4.9,7.324 14.76,15.594 29.58,24.793 -5.03,6.133 -9.32,12.433 -12.9,18.906 -3.58,6.477 -5.37,12.988 -5.37,19.551 0,13.633 4.51,24.023 13.55,31.183 9.03,7.157 21.93,10.731 38.72,10.731 14.05,0 25.59,-3.152 34.63,-9.453 9.02,-6.309 13.54,-15.203 13.54,-26.707 0,-6.133 -1.33,-11.926 -3.96,-17.383 -2.64,-5.449 -6.26,-10.559 -10.86,-15.332 -4.6,-4.766 -11.64,-10.266 -21.09,-16.484 l 33.61,-39.485 c 5.44,7.414 9.79,17.207 13.03,29.395 l 29.01,-6.649 c -4.35,-17.968 -11.84,-33.136 -22.49,-45.488 l 27.73,-32.969 h -41.79"
          id="path48"
          style="fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none" />
        <path
          d="m 2721.68,301.395 c 0,-17.715 -5.81,-31.309 -17.44,-40.762 -11.63,-9.453 -28.77,-14.18 -51.44,-14.18 -15.67,0 -29.28,2.598 -40.82,7.793 -11.54,5.195 -20.94,13.586 -28.17,25.176 l 27.72,21.211 c 4.09,-5.801 7.97,-10.137 11.63,-13.035 3.66,-2.903 8.01,-5.157 13.03,-6.77 5.03,-1.621 10.82,-2.43 17.38,-2.43 9.8,0 17.44,1.68 22.94,5.047 5.49,3.36 8.24,8.496 8.24,15.403 0,3.914 -0.96,7.195 -2.88,9.832 -1.91,2.636 -4.79,4.922 -8.62,6.836 -3.84,1.914 -10.48,4.113 -19.93,6.582 -9.2,2.472 -17.87,4.961 -26.01,7.472 -8.13,2.52 -15.16,5.77 -21.08,9.774 -5.92,4.004 -10.59,9.035 -14,15.078 -3.4,6.055 -5.1,13.672 -5.1,22.883 0,16.777 5.66,29.871 16.99,39.285 11.33,9.414 27.56,14.121 48.68,14.121 30.59,0 52.22,-10.477 64.91,-31.434 l -27.47,-21.464 c -4.85,7.488 -10.3,12.859 -16.36,16.101 -6.04,3.234 -13.24,4.856 -21.59,4.856 -8.35,0 -15.14,-1.516 -20.38,-4.543 -5.23,-3.016 -7.86,-7.774 -7.86,-14.247 0,-5.703 2.73,-10.07 8.18,-13.097 5.45,-3.028 14.96,-6.063 28.5,-9.129 7.49,-1.965 14.9,-4.074 22.23,-6.328 7.32,-2.258 13.86,-5.391 19.62,-9.395 5.75,-4.004 10.37,-9.238 13.86,-15.715 3.49,-6.472 5.24,-14.785 5.24,-24.921"
          id="path50"
          style="fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none" />
        <path
          d="m 2756.37,250.156 v 176.856 h 114.87 v -31.828 h -78.84 v -37.696 h 66.45 v -31.941 h -66.45 v -42.422 h 78.84 v -32.969 h -114.87"
          id="path52"
          style="fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none" />
        <path
          d="m 3050.15,279.422 c -7.57,-11.336 -16.88,-19.656 -27.92,-24.981 -11.03,-5.32 -24.6,-7.988 -40.69,-7.988 -54.77,0 -82.17,30.703 -82.17,92.129 0,31.438 6.95,54.648 20.83,69.641 13.89,14.988 33.91,22.488 60.06,22.488 17.29,0 31.39,-2.918 42.3,-8.75 10.9,-5.84 19.21,-13.484 24.91,-22.938 l -27.98,-21.718 c -4.09,6.133 -7.84,10.554 -11.25,13.281 -3.41,2.723 -7.28,4.773 -11.62,6.133 -4.35,1.367 -9.54,2.051 -15.6,2.051 -14.48,0 -25.5,-4.629 -33.09,-13.868 -7.58,-9.25 -11.37,-24.687 -11.37,-46.32 0,-20.195 3.56,-35.273 10.66,-45.234 7.12,-9.969 18.47,-14.95 34.06,-14.95 10.48,0 18.64,2 24.47,6.004 5.84,4.004 11.14,9.328 15.91,15.977 l 28.49,-20.957"
          id="path54"
          style="fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none" />
        <path
          d="m 3186.78,338.582 c 0,20.957 -2.97,36.223 -8.89,45.813 -5.92,9.578 -15.95,14.375 -30.09,14.375 -14.14,0 -24.17,-4.797 -30.1,-14.375 -5.91,-9.59 -8.87,-24.856 -8.87,-45.813 0,-20.957 2.99,-36.23 9,-45.809 6.01,-9.582 16,-14.375 29.97,-14.375 14.14,0 24.17,4.793 30.09,14.375 5.92,9.579 8.89,24.852 8.89,45.809 z m 37.17,0 c 0,-30.75 -6.49,-53.797 -19.48,-69.129 -12.99,-15.332 -31.88,-23 -56.67,-23 -26.15,0 -45.39,8.067 -57.69,24.211 -12.32,16.141 -18.47,38.789 -18.47,67.918 0,29.309 6.15,51.984 18.47,68.039 12.3,16.063 31.54,24.09 57.69,24.09 26.32,0 45.6,-8.027 57.82,-24.09 12.22,-16.055 18.33,-38.73 18.33,-68.039"
          id="path56"
          style="fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none" />
        <path
          d="m 3404.69,324.012 c 0,-27.684 -5.63,-47.578 -16.87,-59.668 -11.25,-12.098 -29.86,-18.145 -55.84,-18.145 -25.73,0 -44.28,5.977 -55.64,17.949 -11.38,11.965 -17.06,31.926 -17.06,59.864 v 103 h 36.03 V 326.57 c 0,-12.011 1.14,-21.465 3.45,-28.367 2.29,-6.894 5.95,-11.973 10.98,-15.207 5.03,-3.242 12.36,-4.851 21.99,-4.851 13.71,0 23.29,3.64 28.75,10.925 5.45,7.278 8.17,19.61 8.17,36.993 v 100.949 h 36.04 v -103"
          id="path58"
          style="fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none" />
        <path
          d="m 3541.12,372.957 c 0,9.023 -2.28,15.078 -6.83,18.145 -4.57,3.066 -12.08,4.601 -22.56,4.601 h -27.98 v -47.16 h 26.44 c 8.1,0 14.32,0.773 18.67,2.305 4.33,1.535 7.47,3.918 9.38,7.16 1.92,3.23 2.88,8.211 2.88,14.949 z m 6.01,-122.801 -45.11,66.961 h -18.27 v -66.961 h -36.04 v 176.856 h 64.27 c 22.74,0 39.38,-4.094 49.9,-12.278 10.52,-8.171 15.78,-21.589 15.78,-40.242 0,-15.078 -3.17,-27.09 -9.52,-36.035 -6.34,-8.945 -15.35,-14.66 -27.02,-17.129 l 47.28,-71.172 h -41.27"
          id="path60"
          style="fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none" />
        <path
          d="m 3743.08,301.395 c 0,-17.715 -5.81,-31.309 -17.44,-40.762 -11.63,-9.453 -28.77,-14.18 -51.43,-14.18 -15.68,0 -29.28,2.598 -40.82,7.793 -11.55,5.195 -20.94,13.586 -28.18,25.176 l 27.73,21.211 c 4.09,-5.801 7.97,-10.137 11.63,-13.035 3.66,-2.903 8.01,-5.157 13.03,-6.77 5.03,-1.621 10.82,-2.43 17.38,-2.43 9.79,0 17.44,1.68 22.94,5.047 5.49,3.36 8.24,8.496 8.24,15.403 0,3.914 -0.97,7.195 -2.88,9.832 -1.91,2.636 -4.8,4.922 -8.62,6.836 -3.84,1.914 -10.48,4.113 -19.94,6.582 -9.19,2.472 -17.87,4.961 -26,7.472 -8.14,2.52 -15.17,5.77 -21.09,9.774 -5.91,4.004 -10.58,9.035 -13.99,15.078 -3.41,6.055 -5.11,13.672 -5.11,22.883 0,16.777 5.67,29.871 17,39.285 11.32,9.414 27.55,14.121 48.68,14.121 30.58,0 52.21,-10.477 64.91,-31.434 l -27.47,-21.464 c -4.85,7.488 -10.3,12.859 -16.36,16.101 -6.04,3.234 -13.24,4.856 -21.59,4.856 -8.35,0 -15.15,-1.516 -20.38,-4.543 -5.24,-3.016 -7.86,-7.774 -7.86,-14.247 0,-5.703 2.72,-10.07 8.18,-13.097 5.45,-3.028 14.95,-6.063 28.5,-9.129 7.49,-1.965 14.9,-4.074 22.22,-6.328 7.33,-2.258 13.87,-5.391 19.62,-9.395 5.75,-4.004 10.37,-9.238 13.87,-15.715 3.49,-6.472 5.23,-14.785 5.23,-24.921"
          id="path62"
          style="fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none" />
        <path
          d="m 1135.8,585.223 c -2.81,-5.028 -6.01,-9.668 -9.59,-13.926 -3.58,-4.258 -7.83,-7.969 -12.77,-11.121 -4.95,-3.145 -10.72,-5.617 -17.32,-7.403 -6.6,-1.796 -14.33,-2.687 -23.19,-2.687 -25.72,0 -44.68,7.707 -56.87,23.125 -12.18,15.422 -18.267,38.418 -18.267,69.004 0,14.824 1.453,27.961 4.347,39.426 2.9,11.453 7.37,21.074 13.41,28.875 6.05,7.793 13.74,13.711 23.07,17.754 9.33,4.054 20.42,6.074 33.28,6.074 8.26,0 15.64,-0.809 22.11,-2.43 6.48,-1.613 12.16,-3.918 17.06,-6.894 4.89,-2.989 9.15,-6.563 12.78,-10.735 3.62,-4.18 6.79,-8.816 9.52,-13.933 l -14.83,-9.454 c -2.21,4.09 -4.67,7.813 -7.41,11.18 -2.72,3.371 -5.94,6.281 -9.64,8.75 -3.71,2.473 -7.97,4.395 -12.78,5.754 -4.82,1.367 -10.41,2.051 -16.81,2.051 -10.05,0 -18.61,-1.692 -25.68,-5.051 -7.07,-3.367 -12.82,-8.309 -17.25,-14.824 -4.43,-6.512 -7.66,-14.5 -9.7,-23.965 -2.06,-9.453 -3.08,-20.313 -3.08,-32.578 0,-12.266 1.02,-23.125 3.08,-32.586 2.04,-9.453 5.32,-17.441 9.83,-23.957 4.51,-6.512 10.35,-11.453 17.51,-14.824 7.15,-3.368 15.84,-5.047 26.06,-5.047 7.16,0 13.35,0.847 18.6,2.558 5.23,1.7 9.72,3.914 13.47,6.641 3.75,2.723 6.93,5.77 9.53,9.141 2.59,3.359 4.87,6.746 6.83,10.156 l 14.7,-9.074"
          id="path64"
          style="fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none" />
        <path
          d="m 1292.27,642.215 c 0,12.265 -0.9,23.144 -2.68,32.648 -1.79,9.5 -4.69,17.481 -8.69,23.953 -4.01,6.477 -9.29,11.399 -15.84,14.766 -6.57,3.359 -14.57,5.051 -24.03,5.051 -9.54,0 -17.59,-1.672 -24.15,-4.992 -6.56,-3.321 -11.86,-8.223 -15.91,-14.696 -4.05,-6.476 -6.96,-14.453 -8.76,-23.957 -1.78,-9.5 -2.67,-20.418 -2.67,-32.773 0,-12.266 0.89,-23.125 2.67,-32.586 1.8,-9.453 4.71,-17.441 8.76,-23.957 4.05,-6.512 9.35,-11.453 15.91,-14.824 6.56,-3.368 14.61,-5.047 24.15,-5.047 9.46,0 17.46,1.679 24.03,5.047 6.55,3.371 11.83,8.281 15.84,14.757 4,6.473 6.9,14.461 8.69,23.965 1.78,9.493 2.68,20.379 2.68,32.645 z m 18.66,0 c 0,-14.227 -1.23,-27.031 -3.71,-38.399 -2.47,-11.375 -6.47,-21.043 -12.01,-29.003 -5.53,-7.969 -12.76,-14.083 -21.66,-18.34 -8.9,-4.258 -19.74,-6.387 -32.52,-6.387 -12.78,0 -23.6,2.129 -32.46,6.387 -8.86,4.257 -16.05,10.39 -21.59,18.398 -5.54,8.008 -9.56,17.676 -12.08,29.004 -2.51,11.34 -3.77,24.113 -3.77,38.34 0,14.14 1.26,26.894 3.77,38.273 2.52,11.367 6.54,21.035 12.08,29.004 5.54,7.969 12.73,14.102 21.59,18.399 8.86,4.304 19.68,6.453 32.46,6.453 12.78,0 23.62,-2.129 32.52,-6.387 8.9,-4.266 16.13,-10.352 21.66,-18.269 5.54,-7.93 9.54,-17.579 12.01,-28.946 2.48,-11.379 3.71,-24.219 3.71,-38.527"
          id="path66"
          style="fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none" />
        <path
          d="m 1451.15,680.672 c 0,6.223 -0.72,11.523 -2.17,15.91 -1.45,4.395 -3.79,7.969 -7.03,10.742 -3.23,2.762 -7.47,4.785 -12.71,6.063 -5.24,1.281 -11.69,1.914 -19.36,1.914 h -32.71 v -71.172 h 31.94 c 8.01,0 14.7,0.664 20.07,1.984 5.36,1.317 9.67,3.426 12.9,6.328 3.24,2.891 5.56,6.661 6.96,11.309 1.41,4.637 2.11,10.281 2.11,16.922 z m 8.44,-126.883 -45.24,74.883 h -37.18 v -74.883 h -17.25 v 176.856 h 50.21 c 9.72,0 18.26,-0.86 25.63,-2.559 7.36,-1.711 13.56,-4.492 18.58,-8.371 5.03,-3.875 8.82,-8.945 11.38,-15.203 2.56,-6.262 3.84,-13.957 3.84,-23.067 0,-7.843 -0.84,-14.629 -2.5,-20.382 -1.66,-5.75 -4.08,-10.625 -7.28,-14.629 -3.2,-4.004 -7.12,-7.227 -11.75,-9.649 -4.65,-2.43 -9.96,-4.285 -15.91,-5.555 l 48.17,-77.441 h -20.7"
          id="path68"
          style="fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none" />
        <path
          d="m 1611.82,674.93 c 0,7.668 -0.95,14.074 -2.87,19.23 -1.92,5.145 -4.68,9.258 -8.31,12.324 -3.62,3.075 -8.02,5.262 -13.22,6.582 -5.2,1.317 -11.11,1.981 -17.76,1.981 h -30.03 v -83.055 h 26.71 c 7.92,0 14.73,0.645 20.45,1.914 5.7,1.278 10.41,3.516 14.11,6.707 3.71,3.196 6.45,7.559 8.24,13.106 1.8,5.527 2.68,12.597 2.68,21.211 z m 18.53,0.508 c 0,-10.731 -1.23,-19.852 -3.7,-27.344 -2.47,-7.5 -6.24,-13.614 -11.31,-18.34 -5.07,-4.727 -11.44,-8.152 -19.1,-10.281 -7.67,-2.129 -16.7,-3.196 -27.09,-3.196 h -29.52 v -62.488 h -17.25 v 176.856 h 48.94 c 20.27,0 35.18,-4.59 44.72,-13.743 9.54,-9.16 14.31,-22.976 14.31,-41.464"
          id="path70"
          style="fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none" />
        <path
          d="m 1793.23,600.047 c 0,-9.121 -1.62,-16.824 -4.85,-23.125 -3.25,-6.309 -7.63,-11.465 -13.17,-15.469 -5.53,-4.004 -12.06,-6.894 -19.55,-8.68 -7.5,-1.796 -15.51,-2.687 -24.02,-2.687 -8.95,0 -16.79,0.891 -23.52,2.687 -6.73,1.786 -12.62,4.227 -17.69,7.344 -5.07,3.106 -9.46,6.836 -13.17,11.18 -3.7,4.348 -7,9.074 -9.9,14.18 l 14.83,8.82 c 1.7,-3.41 3.91,-6.797 6.65,-10.156 2.72,-3.371 6.04,-6.418 9.96,-9.141 3.91,-2.727 8.49,-4.941 13.74,-6.641 5.23,-1.711 11.26,-2.558 18.07,-2.558 6.22,0 12.03,0.644 17.44,1.914 5.41,1.277 10.12,3.281 14.13,6.004 4,2.734 7.15,6.203 9.46,10.422 2.29,4.218 3.45,9.265 3.45,15.136 0,5.457 -0.9,10.078 -2.69,13.868 -1.79,3.789 -4.51,7.07 -8.17,9.843 -3.68,2.762 -8.31,5.176 -13.94,7.215 -5.61,2.043 -12.26,4.043 -19.93,6.008 -10.23,2.637 -19.03,5.215 -26.45,7.734 -7.41,2.508 -13.54,5.528 -18.4,9.071 -4.86,3.527 -8.45,7.832 -10.8,12.902 -2.34,5.066 -3.51,11.473 -3.51,19.227 0,7.324 1.19,14.015 3.57,20.07 2.39,6.043 5.99,11.219 10.8,15.515 4.82,4.309 10.84,7.649 18.08,10.04 7.25,2.382 15.72,3.574 25.43,3.574 7.32,0 13.97,-0.75 19.94,-2.235 5.96,-1.496 11.33,-3.625 16.1,-6.386 4.77,-2.774 9.03,-6.114 12.78,-10.032 3.74,-3.925 7.11,-8.347 10.09,-13.289 l -14.44,-9.718 c -2.21,3.839 -4.72,7.355 -7.54,10.546 -2.81,3.196 -6,5.938 -9.59,8.243 -3.57,2.297 -7.59,4.062 -12.07,5.304 -4.47,1.231 -9.48,1.856 -15.02,1.856 -6.72,0 -12.6,-0.774 -17.62,-2.305 -5.03,-1.535 -9.2,-3.703 -12.53,-6.515 -3.32,-2.813 -5.79,-6.219 -7.4,-10.223 -1.63,-4.004 -2.44,-8.477 -2.44,-13.418 0,-4.777 0.75,-8.75 2.24,-11.945 1.48,-3.204 3.98,-5.965 7.47,-8.309 3.5,-2.344 8.14,-4.473 13.94,-6.387 5.79,-1.914 12.98,-3.945 21.59,-6.074 9.11,-2.297 17.29,-4.766 24.53,-7.414 7.25,-2.637 13.4,-5.918 18.47,-9.832 5.06,-3.918 8.94,-8.75 11.63,-14.504 2.67,-5.75 4.02,-12.969 4.02,-21.66"
          id="path72"
          style="fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none" />
        <path
          d="m 2035.05,690.898 c -2.31,4.512 -4.89,8.497 -7.74,11.954 -2.85,3.445 -6.15,6.336 -9.9,8.679 -3.75,2.344 -8.01,4.114 -12.78,5.305 -4.77,1.191 -10.14,1.797 -16.1,1.797 -9.63,0 -17.89,-1.602 -24.79,-4.797 -6.9,-3.191 -12.56,-7.988 -16.99,-14.375 -4.44,-6.387 -7.69,-14.356 -9.78,-23.895 -2.09,-9.543 -3.13,-20.656 -3.13,-33.351 0,-11.934 0.98,-22.617 2.94,-32.07 1.96,-9.461 5.19,-17.469 9.71,-24.024 4.51,-6.562 10.46,-11.59 17.82,-15.078 7.38,-3.496 16.47,-5.242 27.29,-5.242 3.57,0 7.02,0.195 10.35,0.574 3.32,0.383 6.6,0.977 9.84,1.789 3.24,0.809 6.54,1.836 9.9,3.066 3.37,1.239 6.88,2.665 10.54,4.286 v 49.707 h -47.15 v 15.715 h 64.4 v -75.137 c -4.94,-2.551 -9.6,-4.817 -13.99,-6.77 -4.39,-1.961 -8.84,-3.601 -13.35,-4.922 -4.52,-1.316 -9.29,-2.324 -14.31,-3.007 -5.03,-0.672 -10.61,-1.016 -16.75,-1.016 -13.96,0 -25.74,2.148 -35.32,6.457 -9.59,4.297 -17.38,10.449 -23.39,18.457 -6,8.008 -10.33,17.703 -12.97,29.07 -2.64,11.379 -3.96,24.172 -3.96,38.399 0,14.23 1.32,27.004 3.96,38.332 2.64,11.336 6.9,20.957 12.78,28.887 5.87,7.917 13.5,14.003 22.88,18.269 9.36,4.258 20.78,6.387 34.23,6.387 8.35,0 15.73,-0.828 22.11,-2.489 6.39,-1.66 11.97,-4.003 16.74,-7.031 4.77,-3.027 8.9,-6.621 12.4,-10.801 3.49,-4.171 6.56,-8.73 9.19,-13.671 l -14.68,-9.454"
          id="path74"
          style="fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none" />
        <path
          d="m 2194.45,680.672 c 0,6.223 -0.72,11.523 -2.17,15.91 -1.45,4.395 -3.79,7.969 -7.03,10.742 -3.23,2.762 -7.47,4.785 -12.71,6.063 -5.24,1.281 -11.69,1.914 -19.36,1.914 h -32.71 v -71.172 h 31.94 c 8.01,0 14.7,0.664 20.07,1.984 5.36,1.317 9.67,3.426 12.9,6.328 3.24,2.891 5.57,6.661 6.96,11.309 1.41,4.637 2.11,10.281 2.11,16.922 z m 8.44,-126.883 -45.24,74.883 h -37.18 v -74.883 h -17.25 v 176.856 h 50.22 c 9.71,0 18.25,-0.86 25.62,-2.559 7.36,-1.711 13.56,-4.492 18.58,-8.371 5.03,-3.875 8.82,-8.945 11.38,-15.203 2.56,-6.262 3.84,-13.957 3.84,-23.067 0,-7.843 -0.84,-14.629 -2.5,-20.382 -1.66,-5.75 -4.08,-10.625 -7.28,-14.629 -3.2,-4.004 -7.12,-7.227 -11.75,-9.649 -4.65,-2.43 -9.96,-4.285 -15.91,-5.555 l 48.17,-77.441 h -20.7"
          id="path76"
          style="fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none" />
        <path
          d="m 2297.91,620.488 h 63 l -31.57,93.407 z m -40.64,-66.699 61.34,176.856 h 22.24 l 61.72,-176.856 h -18.79 l -17.25,50.984 h -73.86 l -17.25,-50.984 h -18.15"
          id="path78"
          style="fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none" />
        <path
          d="m 2548.49,553.789 -87.65,157.813 V 553.789 h -16.74 v 176.856 h 25.04 l 82.67,-148.868 v 148.868 h 17.13 V 553.789 h -20.45"
          id="path80"
          style="fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none" />
        <path
          d="m 2728.27,642.469 c 0,13.371 -1.13,24.679 -3.39,33.926 -2.25,9.238 -5.92,16.738 -10.99,22.492 -5.06,5.75 -11.68,9.883 -19.87,12.39 -8.17,2.512 -18.18,3.77 -30.03,3.77 H 2641.5 V 569.512 h 22.49 c 11.58,0 21.47,1.211 29.65,3.64 8.17,2.422 14.82,6.496 19.93,12.2 5.11,5.703 8.84,13.222 11.18,22.558 2.35,9.324 3.52,20.84 3.52,34.559 z m 18.65,-0.254 c 0,-17.293 -1.77,-31.629 -5.3,-42.996 -3.54,-11.379 -8.73,-20.43 -15.59,-27.16 -6.85,-6.727 -15.33,-11.454 -25.43,-14.18 -10.09,-2.723 -21.7,-4.09 -34.82,-4.09 h -41.65 v 176.856 h 41.4 c 13.88,0 25.91,-1.497 36.1,-4.473 10.17,-2.988 18.63,-7.93 25.36,-14.824 6.73,-6.907 11.74,-15.996 15.01,-27.285 3.28,-11.29 4.92,-25.235 4.92,-41.848"
          id="path82"
          style="fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none" />
        <path
          d="m 2791.15,612.949 v 16.231 h 79.47 v -16.231 h -79.47"
          id="path84"
          style="fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none" />
        <path
          d="m 3023.35,642.469 c 0,13.371 -1.14,24.679 -3.39,33.926 -2.26,9.238 -5.92,16.738 -10.99,22.492 -5.07,5.75 -11.69,9.883 -19.87,12.39 -8.17,2.512 -18.18,3.77 -30.03,3.77 h -22.49 V 569.512 h 22.49 c 11.58,0 21.47,1.211 29.65,3.64 8.17,2.422 14.82,6.496 19.93,12.2 5.11,5.703 8.84,13.222 11.18,22.558 2.35,9.324 3.52,20.84 3.52,34.559 z m 18.65,-0.254 c 0,-17.293 -1.77,-31.629 -5.3,-42.996 -3.54,-11.379 -8.73,-20.43 -15.59,-27.16 -6.85,-6.727 -15.33,-11.454 -25.43,-14.18 -10.1,-2.723 -21.7,-4.09 -34.82,-4.09 h -41.65 v 176.856 h 41.39 c 13.89,0 25.92,-1.497 36.11,-4.473 10.17,-2.988 18.63,-7.93 25.36,-14.824 6.73,-6.907 11.74,-15.996 15.01,-27.285 3.28,-11.29 4.92,-25.235 4.92,-41.848"
          id="path86"
          style="fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none" />
        <path
          d="m 3215.94,627.645 c 0,-14.481 -1.11,-26.68 -3.32,-36.61 -2.22,-9.922 -5.82,-17.93 -10.81,-24.015 -4.98,-6.094 -11.47,-10.477 -19.48,-13.165 -8.01,-2.683 -17.76,-4.023 -29.26,-4.023 -12.01,0 -22.11,1.387 -30.28,4.152 -8.18,2.762 -14.74,7.235 -19.68,13.418 -4.95,6.172 -8.48,14.2 -10.61,24.082 -2.13,9.883 -3.2,21.934 -3.2,36.161 v 103 h 17.26 V 630.203 c 0,-12.265 0.72,-22.527 2.17,-30.801 1.44,-8.261 3.96,-14.922 7.54,-19.988 3.58,-5.07 8.33,-8.691 14.25,-10.859 5.91,-2.18 13.36,-3.262 22.29,-3.262 9.03,0 16.5,1.082 22.43,3.262 5.92,2.168 10.61,5.789 14.06,10.859 3.44,5.066 5.87,11.707 7.28,19.93 1.41,8.222 2.11,18.34 2.11,30.351 v 100.95 h 17.25 v -103"
          id="path88"
          style="fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none" />
        <path
          d="m 3402.47,585.223 c -2.81,-5.028 -6.01,-9.668 -9.59,-13.926 -3.58,-4.258 -7.83,-7.969 -12.77,-11.121 -4.95,-3.145 -10.72,-5.617 -17.32,-7.403 -6.6,-1.796 -14.33,-2.687 -23.19,-2.687 -25.72,0 -44.68,7.707 -56.87,23.125 -12.18,15.422 -18.27,38.418 -18.27,69.004 0,14.824 1.46,27.961 4.35,39.426 2.9,11.453 7.37,21.074 13.41,28.875 6.05,7.793 13.74,13.711 23.07,17.754 9.33,4.054 20.42,6.074 33.28,6.074 8.26,0 15.64,-0.809 22.11,-2.43 6.48,-1.613 12.16,-3.918 17.06,-6.894 4.89,-2.989 9.15,-6.563 12.78,-10.735 3.62,-4.18 6.79,-8.816 9.52,-13.933 l -14.83,-9.454 c -2.21,4.09 -4.67,7.813 -7.41,11.18 -2.72,3.371 -5.94,6.281 -9.64,8.75 -3.71,2.473 -7.97,4.395 -12.78,5.754 -4.82,1.367 -10.41,2.051 -16.81,2.051 -10.05,0 -18.61,-1.692 -25.68,-5.051 -7.07,-3.367 -12.82,-8.309 -17.25,-14.824 -4.43,-6.512 -7.66,-14.5 -9.7,-23.965 -2.06,-9.453 -3.08,-20.313 -3.08,-32.578 0,-12.266 1.02,-23.125 3.08,-32.586 2.04,-9.453 5.32,-17.441 9.83,-23.957 4.51,-6.512 10.35,-11.453 17.51,-14.824 7.15,-3.368 15.84,-5.047 26.06,-5.047 7.16,0 13.35,0.847 18.6,2.558 5.23,1.7 9.72,3.914 13.47,6.641 3.75,2.723 6.93,5.77 9.53,9.141 2.59,3.359 4.87,6.746 6.83,10.156 l 14.7,-9.074"
          id="path90"
          style="fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none" />
        <path
          d="m 3473.27,620.488 h 63 l -31.56,93.407 z m -40.64,-66.699 61.34,176.856 h 22.24 l 61.72,-176.856 h -18.79 l -17.25,50.984 h -73.85 l -17.26,-50.984 h -18.15"
          id="path92"
          style="fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none" />
        <path
          d="m 3619.46,553.789 v 176.856 h 17.25 V 569.512 h 82.93 v -15.723 h -100.18"
          id="path94"
          style="fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none" />
      </g>
    </g>
  </symbol>

  <symbol id="icon-magic" viewBox="0 0 512 512">
    <path
      d="m501 445l-239-238c-7-7-16-11-25-11-11 0-20 4-26 11-8 6-12 15-12 26 0 11 4 20 11 26l239 239c7 7 16 10 26 10 9 0 20-3 27-10 6-8 10-17 10-27 0-9-4-19-11-26z m-25 28c-1 1-3 1-4 0l-185-185 4-4 185 185c1 2 1 3 0 4z m-243-243c1-1 3-1 3-1 1 0 2 0 2 0l29 30-4 4-29-29c-1-1-1-2-1-2-1-1 0-1 0-2z m-82-2c0-9-9-17-18-17l-115 0c-9 0-17 7-17 17 0 10 7 18 17 18l115 0c10 0 18-8 18-18z m170 0c0 10 7 18 17 18l116 0c9 0 17-7 17-18 0-9-7-17-17-17l-116 0c-10 0-17 7-17 17z m-97 74c-9 0-17 7-17 17l0 116c0 9 8 18 17 18 9 0 18-8 18-18l0-116c0-9-8-17-18-17z m0-148c9 0 18-8 18-18l0-116c0-9-8-17-18-17-9 0-17 7-17 17l0 116c1 10 8 18 17 18z m-134-84c-3-4-8-7-12-5-4 0-8 2-11 5-4 3-6 7-6 12 0 5 2 9 6 12l81 82c4 3 9 5 13 5 4 0 8-2 11-5 3-3 5-7 5-11 0-5-2-9-5-12z m70 205c-4 0-8 3-12 6l-81 81c-5 4-6 9-6 13 0 4 2 8 6 11 3 3 8 5 12 5 4 0 9-2 11-5l82-81c3-3 5-7 5-12 0-5-2-9-5-12-3-3-8-6-12-6z m130-95c5 0 9-2 12-5l82-82c3-3 5-7 5-12 0-5-2-9-5-12-3-3-8-6-12-6-4 0-8 3-12 6l-81 83c-4 3-6 8-6 12 0 4 2 8 6 11 2 3 7 5 11 5z" />
  </symbol>

  <symbol id="icon-file-lock" viewBox="0 0 50 50">
    <path d="M21.8,37.6H6.4c-1.8,0-3.1-1.5-2.9-3.2l2.6-21.6c0.2-1.5,1.4-2.6,2.9-2.6h31.8c1.8,0,3.1,1.5,2.9,3.2l-1.2,10.5l-2.7-1.4
	l1.1-9.3H8.9L6.4,34.5h15.1L21.8,37.6z" />
    <path d="M1.7,19.3c-0.7,0-1.3-0.6-1.3-1.3V2.5c0-1.2,1-2.2,2.2-2.2h9c1.2,0,2.2,1,2.2,2.2v2.7h24c0.6,0,1,0.2,1.3,0.7
	c0.7,1-0.1,2.4-1.2,2.4H13c-1.2,0-2.2-1-2.2-2.2V3.4H2.9v14.5C2.9,18.6,2.5,19.2,1.7,19.3z" />
    <g>
      <path d="M34.8,49.6c-8.1,0-14.7-6.6-14.7-14.7s6.6-14.7,14.7-14.7S49.5,26.9,49.5,35S42.9,49.6,34.8,49.6z M34.8,22.8
		c-6.7,0-12.2,5.5-12.2,12.2s5.5,12.2,12.2,12.2C41.5,47.1,47,41.7,47,35S41.5,22.8,34.8,22.8z" />
    </g>
    <g>
      <rect height="2.6" width="1.7" x="34.4" y="36.2" />
      <path d="M40.4,31.9h-0.9v-1.7c0-2.4-1.9-4.3-4.3-4.3S31,27.8,31,30.2v1.7h-0.9c-0.5,0-0.9,0.4-0.9,0.9v6.9c0,1.9,1.5,3.4,3.4,3.4
		h5.2c1.9,0,3.4-1.5,3.4-3.4v-6.9C41.3,32.3,40.9,31.9,40.4,31.9z M32.7,30.2c0-1.4,1.2-2.6,2.6-2.6c1.4,0,2.6,1.2,2.6,2.6v1.7h-5.2
		V30.2z M39.6,39.7c0,0.9-0.8,1.7-1.7,1.7h-5.2c-0.9,0-1.7-0.8-1.7-1.7v-6h8.6V39.7z" />
    </g>
  </symbol>

  <symbol id="icon-others" viewBox="0 0 512 512">
    <path
      d="m112 111c1 0 54-26 115-40l0 22-15 0c-32 0-57 26-57 58l0 345c0 7 6 13 13 13l102 0c32 0 58-26 58-57l0-301c0-32-26-58-58-58l-15 0 0-28c40-7 81-7 111 11 38 22 75 65 10 198-40 79-38 199-38 204l27-1c0-1-3-119 33-191 58-116 51-193-20-234-35-19-80-21-124-14l0-2c0-6-4-12-10-13l-99-21-5 27 78 16c-62 14-114 39-118 41z m158 9c18 0 31 14 31 31l0 301c0 17-14 30-31 30l-89 0 0-38 104 0 0-26-104 0 0-267c0-18 15-31 31-31z" />
  </symbol>

  <symbol id="icon-permamonitor" viewBox="0 0 512 512">
    <path
      d="M19.075,90.39H6.094V67.9H19.075V90.39Zm2.109,1.055v-24.6a1.055,1.055,0,0,0-1.055-1.055H5.039a1.054,1.054,0,0,0-1.054,1.055v24.6A1.055,1.055,0,0,0,5.039,92.5h15.09a1.055,1.055,0,0,0,1.055-1.055ZM87.862,90.39H74.881V27.935H87.862V90.39Zm2.109,1.055V26.881a1.055,1.055,0,0,0-1.055-1.055H73.827a1.055,1.055,0,0,0-1.055,1.055V91.445A1.055,1.055,0,0,0,73.827,92.5H88.916a1.055,1.055,0,0,0,1.055-1.055ZM51.953,52.952h12.98V90.39H51.953V52.952ZM65.988,92.5a1.055,1.055,0,0,0,1.054-1.055V51.9a1.054,1.054,0,0,0-1.054-1.055H50.9A1.054,1.054,0,0,0,49.843,51.9V91.445A1.055,1.055,0,0,0,50.9,92.5ZM29.023,43.711H42V90.39H29.023V43.711ZM43.059,92.5a1.055,1.055,0,0,0,1.054-1.055V42.657A1.055,1.055,0,0,0,43.059,41.6H27.969a1.055,1.055,0,0,0-1.055,1.055V91.445A1.055,1.055,0,0,0,27.969,92.5ZM83.787,14.847a5.119,5.119,0,1,1,5.119-5.12,5.125,5.125,0,0,1-5.119,5.12ZM58.443,39.862a5.119,5.119,0,1,1,5.118-5.119,5.124,5.124,0,0,1-5.118,5.119ZM35.514,30.621A5.118,5.118,0,1,1,40.632,25.5a5.124,5.124,0,0,1-5.118,5.117ZM12.585,54.808A5.118,5.118,0,1,1,17.7,49.69a5.123,5.123,0,0,1-5.118,5.118ZM83.787,2.5A7.223,7.223,0,0,0,77.96,14L62.789,28.972a7.217,7.217,0,0,0-10.581,2.122l-9.669-3.9a7.229,7.229,0,1,0-12.707,2.767l-13.1,13.815a7.239,7.239,0,1,0,1.531,1.451l13.1-13.814a7.219,7.219,0,0,0,10.387-2.262l9.668,3.895A7.228,7.228,0,1,0,64.27,30.473L79.441,15.5a7.226,7.226,0,1,0,4.346-13Z"
      fill="#000000" stroke="#000000" stroke-width="2"
      transform="scale(5)" />
  </symbol>

  <symbol id="icon-play" viewBox="0 0 512 512">
    <path
      d="M582 358c25,14 75,33 75,65 0,33 -51,52 -75,66 -66,38 -131,76 -197,113 -24,15 -66,49 -94,32 -28,-16 -19,-69 -19,-97 0,-76 0,-152 0,-227 0,-29 -9,-82 19,-98 29,-16 70,18 95,33 65,37 131,75 196,113zm-159 -341c225,0 407,182 407,406 0,225 -182,407 -407,407 -224,0 -406,-182 -406,-407 0,-224 182,-406 406,-406zm0 31c207,0 375,168 375,375 0,207 -168,375 -375,375 -207,0 -375,-168 -375,-375 0,-207 168,-375 375,-375zm143 337c-65,-37 -131,-75 -196,-113 -79,-46 -66,-53 -66,38 0,75 0,151 0,227 0,91 -14,84 65,38 66,-38 131,-76 197,-113 79,-46 79,-31 0,-77z"
      fill="transparent"
      stroke="#000000"
      stroke-width="30"
      transform="scale(0.55)"
      xmlns="http://www.w3.org/2000/svg" />
  </symbol>

  <symbol id="icon-link" viewBox="0 0 512 512">
    <path
      d="M7.05025 1.53553C8.03344 0.552348 9.36692 0 10.7574 0C13.6528 0 16 2.34721 16 5.24264C16 6.63308 15.4477 7.96656 14.4645 8.94975L12.4142 11L11 9.58579L13.0503 7.53553C13.6584 6.92742 14 6.10264 14 5.24264C14 3.45178 12.5482 2 10.7574 2C9.89736 2 9.07258 2.34163 8.46447 2.94975L6.41421 5L5 3.58579L7.05025 1.53553Z"
      transform="scale(33)"
    />
    <path
      d="M7.53553 13.0503L9.58579 11L11 12.4142L8.94975 14.4645C7.96656 15.4477 6.63308 16 5.24264 16C2.34721 16 0 13.6528 0 10.7574C0 9.36693 0.552347 8.03344 1.53553 7.05025L3.58579 5L5 6.41421L2.94975 8.46447C2.34163 9.07258 2 9.89736 2 10.7574C2 12.5482 3.45178 14 5.24264 14C6.10264 14 6.92742 13.6584 7.53553 13.0503Z"
      transform="scale(33)"
    />
    <path d="M5.70711 11.7071L11.7071 5.70711L10.2929 4.29289L4.29289 10.2929L5.70711 11.7071Z" fill="#ffffff"
          transform="scale(33)"
    />
  </symbol>

  <symbol id="icon-back" viewBox="0 0 512 512">
    <path
      d="M48.6,23H15.4c-0.9,0-1.3-1.1-0.7-1.7l9.6-9.6c0.6-0.6,0.6-1.5,0-2.1l-2.2-2.2c-0.6-0.6-1.5-0.6-2.1,0  L2.5,25c-0.6,0.6-0.6,1.5,0,2.1L20,44.6c0.6,0.6,1.5,0.6,2.1,0l2.1-2.1c0.6-0.6,0.6-1.5,0-2.1l-9.6-9.6C14,30.1,14.4,29,15.3,29  h33.2c0.8,0,1.5-0.6,1.5-1.4v-3C50,23.8,49.4,23,48.6,23z"
      transform="scale(7)" xmlns="http://www.w3.org/2000/svg"
    />
  </symbol>


  <symbol id="icon-pause" viewBox="0 0 512 512">
    <path
      d="m81.73 7.8711c5.7305 0 10.398 4.6719 10.398 10.398v63.449c0 5.7305-4.6719 10.398-10.398 10.398h-11.449c-5.7305 0-10.398-4.6719-10.398-10.398l-0.003906-63.449c0-5.7305 4.6719-10.398 10.398-10.398h11.449m0.003907-5.3711h-11.449c-8.7109 0-15.77 7.0586-15.77 15.77v63.449c0 8.7109 7.0586 15.77 15.77 15.77h11.449c8.7109 0 15.77-7.0586 15.77-15.77v-63.449c0-8.7109-7.0586-15.77-15.77-15.77z"
      transform="scale(5)" xmlns="http://www.w3.org/2000/svg" />
    <path
      d="m29.719 7.8711c5.7305 0 10.398 4.6719 10.398 10.398v63.449c0 5.7305-4.6719 10.398-10.398 10.398h-11.449c-5.7305 0-10.398-4.6719-10.398-10.398v-63.449c0-5.7305 4.6719-10.398 10.398-10.398h11.449m0-5.3711h-11.449c-8.7109 0-15.77 7.0586-15.77 15.77v63.449c0 8.7109 7.0586 15.77 15.77 15.77h11.449c8.7109 0 15.77-7.0586 15.77-15.77v-63.449c0-8.7109-7.0586-15.77-15.77-15.77z"
      transform="scale(5)" xmlns="http://www.w3.org/2000/svg" />
  </symbol>

  <symbol id="icon-news" viewBox="0 0 512 512">
    <path
      d="m104 25c-6 0-12 7-12 12l0 138-76 0c-7 0-12 8-12 13l0 246c0 30 26 56 56 56l392 0c30 0 56-26 56-56l0-398c0-6-6-12-12-12l-392 0z m14 26l365 0 0 385c0 17-14 31-31 31l-346 0c7-9 12-19 12-31z m50 37c-7 0-12 8-12 13l0 88c0 6 6 12 12 12l126 0c6 0 12-6 12-12l0-88c0-6-6-13-12-13z m176 13c-7 0-12 6-12 12 0 6 6 12 12 12l88 0c7 0 12-6 12-12 0-6-6-12-12-12z m-164 12l102 0 0 64-102 0z m164 51c-7 0-12 6-12 13 0 6 6 12 12 12l88 0c7 0 12-6 12-12 0-7-6-13-12-13z m-315 38l63 0 0 234c0 17-14 31-32 31-17 0-31-14-31-31z m139 38c-7 0-12 6-12 12 0 6 6 13 12 13l265 0c7 0 12-7 12-13 0-7-6-12-12-12z m0 69c-7 0-12 6-12 12 0 7 6 12 12 12l265 0c7 0 12-6 12-12 0-7-6-12-12-12z m0 69c-7 0-12 6-12 13 0 7 6 12 12 12l265 0c7 0 12-6 12-12 0-8-6-13-12-13z" />
  </symbol>

  <symbol id="icon-audit" viewBox="0 0 16 16">
    <path
      d="M1.5 1.25a.75.75 0 011.5 0v1.851A7 7 0 111 8a.75.75 0 011.5 0 5.5 5.5 0 101.725-4H5.75a.75.75 0 010 1.5h-3.5a.75.75 0 01-.75-.75v-3.5z"
      xmlns="http://www.w3.org/2000/svg" />
    <path
      d="M8.25 4a.75.75 0 01.75.75v3.763l1.805.802a.75.75 0 01-.61 1.37l-2.25-1A.75.75 0 017.5 9V4.75A.75.75 0 018.25 4z"
      xmlns="http://www.w3.org/2000/svg" />
  </symbol>


  <symbol id="icon-repair" preserveAspectRatio="xMidYMid meet"
          viewBox="0 0 600 600">
    <g transform="scale(2.8) translate(-1.4, -1.8)">
      <path
        d="M230.505,102.78c-0.365-3.25-4.156-5.695-7.434-5.695c-10.594,0-19.996-6.218-23.939-15.842  c-4.025-9.855-1.428-21.346,6.465-28.587c2.486-2.273,2.789-6.079,0.705-8.721c-5.424-6.886-11.586-13.107-18.316-18.498  c-2.633-2.112-6.502-1.818-8.787,0.711c-6.891,7.632-19.27,10.468-28.836,6.477c-9.951-4.187-16.232-14.274-15.615-25.101  c0.203-3.403-2.285-6.36-5.676-6.755c-8.637-1-17.35-1.029-26.012-0.068c-3.348,0.37-5.834,3.257-5.723,6.617  c0.375,10.721-5.977,20.63-15.832,24.667c-9.451,3.861-21.744,1.046-28.621-6.519c-2.273-2.492-6.074-2.798-8.725-0.731  c-6.928,5.437-13.229,11.662-18.703,18.492c-2.133,2.655-1.818,6.503,0.689,8.784c8.049,7.289,10.644,18.879,6.465,28.849  c-3.99,9.505-13.859,15.628-25.156,15.628c-3.666-0.118-6.275,2.345-6.68,5.679c-1.016,8.683-1.027,17.535-0.049,26.289  c0.365,3.264,4.268,5.688,7.582,5.688c10.07-0.256,19.732,5.974,23.791,15.841c4.039,9.855,1.439,21.341-6.467,28.592  c-2.473,2.273-2.789,6.07-0.701,8.709c5.369,6.843,11.537,13.068,18.287,18.505c2.65,2.134,6.504,1.835,8.801-0.697  c6.918-7.65,19.295-10.481,28.822-6.482c9.98,4.176,16.258,14.262,15.645,25.092c-0.201,3.403,2.293,6.369,5.672,6.755  c4.42,0.517,8.863,0.773,13.32,0.773c4.23,0,8.461-0.231,12.692-0.702c3.352-0.37,5.834-3.26,5.721-6.621  c-0.387-10.716,5.979-20.626,15.822-24.655c9.514-3.886,21.752-1.042,28.633,6.512c2.285,2.487,6.063,2.789,8.725,0.73  c6.916-5.423,13.205-11.645,18.703-18.493c2.135-2.65,1.832-6.503-0.689-8.788c-8.047-7.284-10.656-18.879-6.477-28.839  c3.928-9.377,13.43-15.673,23.65-15.673l1.43,0.038c3.318,0.269,6.367-2.286,6.768-5.671  C231.476,120.379,231.487,111.537,230.505,102.78z M115.616,182.27c-36.813,0-66.654-29.841-66.654-66.653  s29.842-66.653,66.654-66.653s66.654,29.841,66.654,66.653c0,12.495-3.445,24.182-9.428,34.176l-29.186-29.187  c2.113-4.982,3.229-10.383,3.228-15.957c0-10.915-4.251-21.176-11.97-28.893c-7.717-7.717-17.978-11.967-28.891-11.967  c-3.642,0-7.267,0.484-10.774,1.439c-1.536,0.419-2.792,1.685-3.201,3.224c-0.418,1.574,0.053,3.187,1.283,4.418  c0,0,14.409,14.52,19.23,19.34c0.505,0.505,0.504,1.71,0.433,2.144l-0.045,0.317c-0.486,5.3-1.423,11.662-2.196,14.107  c-0.104,0.103-0.202,0.19-0.308,0.296c-0.111,0.111-0.213,0.218-0.32,0.328c-2.477,0.795-8.937,1.743-14.321,2.225l0.001-0.029  l-0.242,0.061c-0.043,0.005-0.123,0.011-0.229,0.011c-0.582,0-1.438-0.163-2.216-0.94c-5.018-5.018-18.862-18.763-18.862-18.763  c-1.242-1.238-2.516-1.498-3.365-1.498c-1.979,0-3.751,1.43-4.309,3.481c-3.811,14.103,0.229,29.273,10.546,39.591  c7.719,7.718,17.981,11.968,28.896,11.968c5.574,0,10.975-1.115,15.956-3.228l29.503,29.503  C141.125,178.412,128.825,182.27,115.616,182.27z"
        xmlns="http://www.w3.org/2000/svg" />

    </g>


  </symbol>

  <symbol id="icon-nouveau-plan" viewBox="0 0 512 512">
    <path
      d="m280 121c11 0 21-9 21-22 0-8-4-15-11-18l0-61c0-6-4-10-10-10-7 0-11 4-11 10l0 62c-6 4-11 10-11 18 0 12 9 21 22 21z m-121-55l82 0c6 0 10-5 10-11 0-6-4-10-10-10l-82 0c-6 0-11 4-11 10 0 6 6 11 11 11z m-37 55c11 0 21-9 21-22 0-7-5-14-11-17l0-62c0-6-4-10-10-10-6 0-10 4-10 10l0 62c-7 4-12 10-12 18 0 12 10 21 22 21z m0 65l-42 0c-6 0-11 5-11 12l0 42c0 6 5 11 11 11l42 0c6 0 11-5 11-11l0-42c0-5-5-12-11-12z m0 55l-42 0 0-42 42 0z m99-55l-42 0c-6 0-11 5-11 12l0 42c0 6 5 11 11 11l42 0c6 0 11-5 11-11l0-42c1-5-4-12-11-12z m0 55l-42 0 0-42 42 0z m59 11l42 0c6 0 11-5 11-11l0-42c0-6-5-12-11-12l-42 0c-7 0-12 6-12 12l0 42c0 6 5 11 12 11z m0-53l42 0 0 42-42 0z m-158 74l-42 0c-6 0-11 6-11 12l0 42c0 6 5 11 11 11l42 0c6 0 11-5 11-11l0-42c0-6-5-12-11-12z m0 54l-42 0 0-42 42 0z m99-54l-42 0c-6 0-11 6-11 12l0 42c0 6 5 11 11 11l42 0c6 0 11-5 11-11l0-42c1-6-4-12-11-12z m0 54l-42 0 0-42 42 0z m176-77l0-174c0-18-14-31-30-31l-48 0c-6 0-10 4-10 10 0 6 4 11 10 11l48 0c6 0 10 5 10 10l0 64-352 0 0-64c0-6 5-10 10-10l54 0c6 0 10-5 10-11 0-6-4-10-10-10l-54 0c-18 0-31 14-31 31l0 278c0 18 14 31 31 31l226 0c6 64 60 114 124 114 69 0 125-57 125-125 0-65-49-118-113-124z m-372 104l0-193 352 0 0 89c-62 4-111 53-116 115l-226 0c-6 0-10-5-10-11z m360 127c-59 0-107-48-107-107 0-60 48-108 107-108 59 0 108 48 108 108 0 59-49 107-108 107z m47-117l-36 0 0-36c0-5-4-11-10-11-6 0-10 5-10 11l0 36-36 0c-5 0-10 4-10 10 0 6 4 10 10 10l36 0 0 36c0 5 4 10 10 10 6 0 10-4 10-10l0-36 36 0c5 0 10-4 10-10 0-6-5-10-10-10z" />
  </symbol>

  <symbol id="icon-chauffeur" viewBox="0 0 512 512">
    <path
      d="m500 339l0-40c0-15-8-29-23-35l-28-16-45-84c-2-3-5-5-9-5l-12 0-5-38c-1-5-5-9-10-9l-33 0c-5 0-9 4-10 9l-6 38-12 0 0-40c0-9-7-19-18-19l-269 0c-9 0-17 8-17 19l0 232c0 6 4 10 10 10l32 0c5 22 24 37 47 37 23 0 42-16 47-37l208 0c5 22 24 37 47 37 24 0 42-16 47-37l58 0c6 0 10-4 10-10-3-7-5-11-9-12z m-159-207l15 0 4 26-22 0z m47 48l35 65-116 0 0-65z m-296 195c-15 0-26-11-26-27 0-15 11-25 26-25 16 0 27 11 27 26 0 16-11 26-27 26z m47-36c-5-22-23-37-47-37-22 0-42 16-47 37l-21 0 0-73 15 0c6 0 10-4 10-10 0-6-4-10-10-10l-14 0 0-124 261 0 0 124-206 0c-6 0-10 4-10 10 0 6 4 10 10 10l206 0 0 73z m206 0l-39 0 0-73 132 0 30 17c6 3 10 8 10 16l0 40-39 0c-5-22-23-37-47-37-22 0-41 15-47 37z m47 36c-15 0-26-11-26-27 0-15 11-26 26-26 16 0 27 11 27 26 0 16-11 27-27 27z m-248-161l22 0 0-19 19 0 0-22-19 0 0-21-22 0 0 21-20 0 0 22 20 0z" />
  </symbol>

  <symbol id="icon-conge" viewBox="0 0 512 512">
    <path
      d="m229 163c0-1 0-2-1-4-1-5-2-12-4-19-9-29-26-49-52-56-7-2-14-3-24-3-37 0-86 21-106 48-27 35-20 73-12 105 3 12 6 23 8 34 5 20 9 38 14 58 6 12 9 26 13 41 6 27 12 55 23 82 15 35 41 55 74 55 4 0 7 0 11-1 40-5 66-33 70-75 2-22 1-44-5-64-9-33-12-62-9-90 5-38 5-74 0-111z m-171 64c-9-34-10-58 8-82 15-19 54-36 84-36 6 0 11 1 15 2 16 5 26 17 34 37 2 6 2 12 3 16 0 2 1 3 1 4 4 31 5 60 2 93-9-71-36-97-49-107l-2-13c-1-7-9-12-16-11-7 1-12 8-11 15l1 10c-43 27-49 121-50 144-3-12-6-25-9-37-4-12-8-23-11-35z m157 198c-3 30-18 47-46 50-26 3-44-9-56-38-11-24-16-50-22-78-3-14-6-28-10-43-1-2-1-4-2-6 2 3 6 5 10 5 7 0 13-6 13-13 1-35 11-107 37-125 9 8 32 34 38 104 1 7 7 13 16 12 3 0 6-2 8-4-1 25 2 52 10 81 5 18 6 36 4 55z m158-417c-4 0-7 0-11 1-25 4-46 21-59 48-3 6-5 14-7 19 0 1-1 2-1 4-10 36-16 71-17 110-2 28-8 56-21 88-8 19-13 40-14 63-2 43 19 74 58 85 7 2 15 3 23 3 19 0 47-8 69-45 16-25 25-52 35-78 5-13 10-26 15-40 8-17 15-35 22-51 5-11 9-24 14-35 14-30 26-67 3-106-17-36-69-66-109-66z m29 287c-9 26-18 50-32 74-16 26-36 37-62 29-26-7-40-26-38-56 1-19 5-37 13-53 11-28 18-53 21-79 2 2 4 4 7 5 7 2 15-3 17-10 16-69 42-90 52-96 22 20 23 88 18 129-1 7 4 14 13 15 1 0 1 0 2 0 3 0 6-1 9-3-1 2-2 4-2 6-7 11-12 25-18 39z m53-128c-5 11-11 23-15 35-4 11-9 21-13 32 2-22 10-116-29-149l3-9c2-7-2-16-9-18-7-2-15 2-17 10l-4 12c-15 7-45 30-65 98 2-33 6-62 16-91 0-1 1-2 1-4 1-4 3-9 5-14 9-20 22-31 39-33 2 0 4 0 6 0 29 0 72 25 87 50 14 25 8 48-5 81z" />
  </symbol>

  <symbol id="icon-organisationnel" viewBox="0 0 512 512">
    <path
      d="m460 421c9-9 14-21 14-34 0-21-15-41-36-46l0-31c0-6-4-10-10-10l-161 0 0-24c70-5 124-63 124-134 0-74-60-135-135-135-75 0-134 62-134 136 0 71 54 129 124 135l0 23-161 0c-6 0-10 4-10 10l0 31c-21 5-35 24-35 45 0 13 5 26 14 34-26 5-46 28-46 54l0 19c0 6 4 10 10 10l133 0c6 0 10-4 10-10l0-19c0-26-19-49-44-54 9-9 14-21 14-34 0-22-15-41-37-46l0-19 152 0 0 19c-22 5-36 24-36 46 0 13 5 25 14 34-25 5-45 28-45 54l0 18c0 6 4 10 10 10l133 0c6 0 10-4 10-10l0-19c0-27-19-49-44-54 9-9 14-21 14-34 0-21-15-41-36-46l0-19 150 0 0 19c-22 5-36 24-36 46 0 13 5 25 14 34-25 5-45 27-45 54l0 19c0 6 4 10 10 10l133 0c6 0 10-4 10-10l0-19c2-25-18-48-42-53z m-374-8c-14 0-26-12-26-26 0-14 12-26 26-26 14 0 26 12 26 26 0 14-12 26-26 26z m55 69l-111 0 0-8c0-18 15-34 33-34l43 0c20 0 35 16 35 34z m91-284l49 0c21 0 41 12 50 31-43 37-107 37-150 0 10-19 30-31 51-31z m65-80c0 21-18 40-40 40-21 0-40-19-40-40 0-22 19-40 40-40 22 0 40 18 40 40z m-15 59l-9 0c27-7 46-31 46-58 0-34-27-62-61-62-34 0-61 28-61 62 0 27 19 52 46 58l-7 0c-27 0-52 13-67 36-15-19-23-43-23-69 0-30 12-58 32-79 20-22 48-34 79-34 62 0 114 51 114 113 0 25-9 49-23 69-15-23-40-36-66-36z m-26 236c-14 0-26-12-26-26 0-13 12-25 25-26 15 1 27 12 27 26 0 14-12 26-26 26z m55 69l-110 0 0-8c0-18 15-34 33-34l44 0c18 0 33 15 33 34z m93-41l45 0c18 0 33 16 33 34l0 8-111 0 0-8c0-18 15-34 33-34z m-3-53c1-14 13-26 26-26 14 0 26 12 26 26 0 14-12 26-26 26-14-1-26-13-26-26z" />
  </symbol>

  <symbol id="icon-droit-utilisateurs" viewBox="0 0 512 512">
    <path
      d="m339 11c-4-6-11-9-18-9l-138 0c-7 0-14 3-18 9-4 6-6 14-4 20l20 87c2 11 13 18 23 18l96 0c11 0 21-7 23-18l20-87c2-7 0-15-4-20z m-161 11c1-2 2-4 5-4l138 0c3 0 5 2 5 4 1 1 2 3 1 6l-21 87c-1 3-3 5-7 5l-96 0c-3 0-6-2-7-5l-21-87c1-3 2-5 3-6z m116 156c-2-8-10-14-20-14l-47 0c-9 0-17 6-19 15l-51 221c-1 7 0 14 5 18l79 87c4 4 9 6 14 6 5 0 11-2 14-6l79-86c4-5 6-12 4-19z m39 230l-70 76-7 8-6-8-71-77-3-4 1-4 49-211 1-6 48 0 3 6 57 211 1 4z" />
  </symbol>

  <symbol id="icon-duplicat" viewBox="0 0 512 512">
    <path
      d="m132 9l0 371 371 0 0-371z m320 320l-269 0 0-269 269 0z m-72 78l-51 0 0 45-269 0 0-269 46 0 0-51-97 0 0 371 371 0z" />
  </symbol>

  <symbol id="icon-planning-disponibilites" viewBox="0 0 512 512">
    <path
      d="m469 58l-60 0c-8 0-14 7-14 14 0 7 6 13 14 13l60 0c7 0 13 6 13 13l0 83-453 0 0-83c0-7 6-13 13-13l70 0c7 0 13-6 13-13 0-7-6-14-13-14l-70 0c-22 0-39 18-39 40l0 359c0 21 17 40 40 40l427 0c22 0 40-18 40-40l0-359c-1-22-18-40-41-40z m13 399c0 7-6 13-13 13l-427 0c-7 0-13-6-13-13l0-250 453 0z m-280-372l105 0c7 0 14-6 14-13 0-7-7-14-14-14l-105 0c-7 0-14 7-14 14 0 7 7 13 14 13z m-47 71c15 0 27-13 27-28 0-10-6-19-14-24l0-78c0-8-6-14-13-14-8 0-14 6-14 14l0 78c-8 6-14 14-14 24 0 15 12 28 28 28z m202 0c16 0 28-13 28-28 0-10-6-19-14-24l0-78c0-8-6-14-14-14-7 0-13 6-13 14l0 78c-8 6-14 14-14 24 0 15 12 28 27 28z" />
  </symbol>

  <symbol id="icon-edit" viewBox="0 0 512 512">
    <path
      d="m502 99c0-49-40-89-89-89-24 0-46 9-63 26l-275 276c-2 2-4 4-5 7l-59 155c-4 10 1 23 13 27 5 2 10 2 14 0l155-59c3-1 5-3 7-5l275-275c18-17 27-39 27-63z m-436 347l30-81 51 51z m119-52l-67-67 217-217 67 67z m261-261l-15 15-67-67 15-15c18-19 49-19 67 0 19 18 20 49 0 67z" />
  </symbol>

  <symbol id="icon-datepicker" viewBox="0 0 512 512">
    <path
      d="m502 99c0-49-40-89-89-89-24 0-46 9-63 26l-275 276c-2 2-4 4-5 7l-59 155c-4 10 1 23 13 27 5 2 10 2 14 0l155-59c3-1 5-3 7-5l275-275c18-17 27-39 27-63z m-436 347l30-81 51 51z m119-52l-67-67 217-217 67 67z m261-261l-15 15-67-67 15-15c18-19 49-19 67 0 19 18 20 49 0 67z" />
  </symbol>

  <symbol id="icon-plans-service" viewBox="0 0 512 512">
    <path
      d="m444 468l-371 0c-21 0-37-17-34-38l30-253c2-17 16-30 33-30l372 0c21 0 37 18 34 38l-30 253c-1 17-16 30-34 30z m-371-36l371 0 30-251-372 0z m-55-178c-8 0-15-7-15-15l0-182c0-14 11-26 26-26l105 0c14 0 26 11 26 25l0 32 280 0c6 0 13 3 16 8 8 13-1 28-14 28l-291 0c-15 0-26-11-26-26l0-31-93 0 0 170c0 9-5 16-14 17z" />
  </symbol>

  <symbol id="icon-plans-travail" viewBox="0 0 512 512">
    <path
      d="m214 17c-24 0-42 19-42 42l0 42-127 0c-23 0-42 19-42 42l0 309c0 24 20 42 42 42l422 0c23 0 42-19 42-42l0-309c0-24-20-42-42-42l-127 0 0-42c0-24-19-42-42-42z m0 28l84 0c8 0 14 7 14 15l0 42-112 0 0-42c0-8 6-15 14-15z m-169 84l422 0c8 0 14 7 14 15l0 31-192 68c-3-1-7-2-11-2l-42 0c-5 0-8 1-12 2l-192-68 0-31c0-8 5-15 13-15z m435 76l0 247c0 8-6 14-14 14l-421 0c-8 0-14-6-14-14l0-247 171 61c-1 3-2 7-2 11l0 14c0 20 16 35 34 35l42 0c20 0 35-16 35-35l0-14c0-4-1-8-2-11z m-246 65l42 0c5 0 8 3 8 7l0 14c0 4-3 7-8 7l-42 0c-4 0-7-3-7-7l0-14c1-4 3-7 7-7z" />
  </symbol>

  <symbol id="icon-exit" viewBox="0 0 512 512">
    <path
      d="m279 364c5 4 10 6 15 6 7 0 13-3 18-9l76-88c8-9 8-22 0-31l-76-88c-8-11-24-12-33-2-11 8-12 24-3 33l42 48-153 0c-13 0-25 12-25 25 0 13 12 25 25 25l153 0-42 47c-9 10-8 24 3 34z m176-186c5 12 19 19 31 13 13-5 19-19 14-31-39-95-132-158-236-158-140 0-254 115-254 255 0 140 114 254 254 254 104 0 197-61 236-158 5-12-1-26-14-31-12-6-26 1-31 13-32 78-107 128-191 128-113 0-206-92-206-206 0-114 94-206 206-206 84 0 159 50 191 127z" />
  </symbol>

  <symbol id="icon-gestion-personnes" viewBox="0 0 512 512">
    <path
      d="m175 201c10-23 32-37 56-37l51 0c24 0 47 14 56 37-47 42-117 42-163 0z m175-12c-14-24-40-39-68-39l-51 0c-28 0-54 16-68 39 0 0-9 13-2 19 22 21 53 36 87 38l0 30-169 0c-4 0-7 4-7 9l0 34c-21 5-36 23-36 45 0 15 7 28 19 37-28 2-52 26-52 55l0 19c0 4 3 7 7 7l137 0c5 0 8-3 8-7l0-19c0-29-23-53-51-54 13-8 20-21 20-37 0-23-16-41-38-44l0-26 163 0 0 27c-22 4-38 22-38 44 0 15 7 28 19 37-28 1-51 25-51 54l0 18c0 4 3 7 7 7l138 0c4 0 7-3 7-7l0-19c0-29-23-53-50-54 12-8 19-21 19-37 0-23-16-41-38-44l0-27 162 0 0 27c-22 4-38 22-38 44 0 15 7 28 20 37-29 1-52 25-52 54l0 19c0 4 3 7 7 7l137 0c4 0 7-3 7-7l0-19c0-29-23-53-50-54 12-8 19-21 19-37 0-23-16-41-38-44l0-35c0-4-3-7-7-7l-165 0 0-31c37-2 69-19 92-43 0-1 1-2-6-16z m-210 266l0 11-122 0 0-11c0-22 18-38 38-38l44 0c23 0 40 16 40 38z m-30-91c0 16-14 29-30 29-17 0-30-13-30-29 0-17 13-30 30-30 16 0 30 13 30 30z m206 91l0 11-120 0 0-11c0-22 17-38 37-38l45 0c22 0 38 16 38 38z m-30-91c0 16-14 29-30 29-16 0-30-13-30-29 0-17 14-29 29-30 17 0 31 13 31 30z m147-30c17 0 30 13 30 30 0 16-13 29-30 29-16 0-30-13-30-29 0-17 14-30 30-30z m61 132l-122 0 0-11c0-22 17-38 38-38l45 0c21 0 38 17 38 38l0 11z m-237-321c33 0 60-26 60-59 0-33-26-59-60-59-33 0-60 26-60 59 0 33 26 59 60 59z m0-105c25 0 45 19 45 44 0 25-19 44-45 44-25 0-45-19-45-44 0-24 19-44 45-44z" />
  </symbol>

  <symbol id="icon-secourist" viewBox="0 0 512 512">
    <path
      d="m443 143l-108 0 0-30c0-8-6-14-13-14l-20 0 0-73c0-8-6-14-13-14l-65 0c-7 0-13 6-13 14l0 73-20 0c-7 0-13 6-13 14l0 30-109 0c-32 0-58 26-58 58l0 243c0 32 26 58 58 58l374 0c32 0 58-26 58-58l0-244c0-31-26-57-58-57z m30 300c0 17-13 30-30 30l-374 0c-17 0-30-13-30-30l0-243c0-17 13-30 30-30l108 0 0 6c0 7 6 13 13 13l132 0c7 0 13-6 13-13l0-6 108 0c17 0 30 13 30 30z m-235-404l36 0 0 59-36 0z m70 87l0 37-104 0 0-37z m-132 84c-29 0-52 23-52 52 0 29 23 52 52 52 29 0 52-23 52-52 0-29-23-52-52-52z m0 77c-13 0-24-12-24-25 0-13 11-24 24-24 13 0 25 11 25 24 1 13-11 25-25 25z m56 46c-35-10-74-10-110 0-23 7-39 27-39 51l0 30c0 7 6 13 13 13l161 0c7 0 13-6 13-13l0-30c0-24-15-44-38-51z m-122 51c0-11 7-22 18-25 31-9 65-9 95 0 11 3 19 14 19 25l0 15-133 0 0-15z m303-108l-92 0c-8 0-14-6-14-13 0-7 6-13 14-13l92 0c7 0 13 6 13 13 0 7-6 13-13 13z m0 75l-92 0c-8 0-14-6-14-13 0-7 6-13 14-13l92 0c7 0 13 6 13 13 0 7-6 13-13 13z m0 75l-92 0c-8 0-14-6-14-13 0-7 6-14 14-14l92 0c7 0 13 7 13 14 0 7-6 13-13 13z" />
  </symbol>

  <symbol id="icon-fire" viewBox="0 0 512 512">
    <path
      d="m433 228l-5-32-42 50c-6-101-123-203-128-207l-39-34 14 49c1 2 15 56-36 94-33 25-50 55-59 80-1-9-3-18-4-26l-8-61-22 57c-38 104-39 184-3 238 41 60 117 69 158 69 55 0 98-19 129-54 69-79 46-217 45-223z m-274 77c0 0 0-1 0-2-1-13-6-84 55-130 37-28 47-61 50-85 7 8 15 15 22 24 44 51 90 125 59 183l-42 81 1 0-1 0-20 40 123-148c2 41-2 116-40 163-25 29-61 43-107 43-36 0-100-7-133-55-25-35-29-85-12-151 7 33 13 42 19 50l35 40z" />
  </symbol>

  <symbol id="icon-star-full" viewBox="0 0 512 512">
    <path d="m256 15l78 159 175 25-126 124 30 174-157-82-157 82 30-174-126-124 175-25z" />
  </symbol>

  <symbol id="icon-star-pas-full" viewBox="0 0 512 512">
    <path
      d="m256 71l56 113 6 12 13 2 126 18-91 89-10 9 2 13 22 125-113-60-11-5-11 6-113 60 22-126 2-13-10-9-91-89 126-18 13-2 6-12 56-113m0-56l-78 159-175 25 127 123-30 174 157-82 157 82-30-174 127-123-177-25z" />
  </symbol>

  <symbol id="icon-time" viewBox="0 0 512 512">
    <path
      d="m256 4c-139 0-252 113-252 252 0 139 113 252 252 252 139 0 252-113 252-252 0-139-113-252-252-252z m18 468l0-44-35 0 0 44c-106-9-190-93-199-198l44 0 0-35-44 0c9-106 93-190 198-199l0 44 35 0 0-44c106 9 190 93 199 198l-44 0 0 35 44 0c-9 106-93 190-198 199z m-18-219l-70-124-30 17 78 143 131-1 0-36z" />
  </symbol>

  <svg height="1em" id="icon-time-real" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M256 0a256 256 0 1 1 0 512A256 256 0 1 1 256 0zM232 120V256c0 8 4 15.5 10.7 20l96 64c11 7.4 25.9 4.4 33.3-6.7s4.4-25.9-6.7-33.3L280 243.2V120c0-13.3-10.7-24-24-24s-24 10.7-24 24z" />
  </svg>

  <svg fill="#000000" height="400px" id="icon-person2" viewBox="0 0 28 28" width="400px"
       xmlns="http://www.w3.org/2000/svg">
    <path
      d="M14 25.953c6.527 0 11.953 -5.414 11.953 -11.953 0 -6.527 -5.438 -11.953 -11.965 -11.953C7.449 2.047 2.047 7.473 2.047 14c0 6.539 5.414 11.953 11.953 11.953m0 -1.992C8.469 23.961 4.05 19.531 4.05 14c0 -5.52 4.407 -9.961 9.938 -9.961 5.519 0 9.96 4.441 9.973 9.961 0.011 5.532 -4.441 9.961 -9.961 9.961m0 -10.617c1.699 0.011 3.058 -1.442 3.058 -3.352 0 -1.781 -1.359 -3.269 -3.058 -3.269s-3.058 1.488 -3.058 3.27c0 1.91 1.359 3.34 3.058 3.352M8.539 19.988h10.91c0.469 0 0.704 -0.317 0.704 -0.739 0 -1.313 -1.969 -4.699 -6.152 -4.699S7.848 17.938 7.848 19.25c0 0.422 0.234 0.739 0.692 0.739" />
  </svg>

  <symbol id="icon-euro" viewBox="0 0 512 512">
    <path
      d="m502.56 358.4c-34.56 41.759-87.04 68.16-145.92 68.16-82.24 0-152.32-53.12-178.72-126.72h286.72v-42.24l-292.16 0.003906c-0.48047-5.7598-0.96094-12-0.96094-17.76 0-6.72 0.48047-12.96 0.96094-19.68h291.68v-42.24l-280.32-0.003906c25.92-71.04 93.6-121.6 174.08-121.6 57.92 0 110.4 26.4 144.96 67.84l21.12-40.32c-41.76-43.2-100.48-70.4-164.48-70.4-103.68 0-190.72 69.44-217.6 163.84h-72.96v42.24h64.32c-0.48047 6.72-0.96094 13.04-0.96094 19.68 0 5.76 0.48047 12 0.96094 17.76l-64.32 0.003906v42.24h71.04c26.88 95.04 114.88 164.8 218.88 164.8 64 0 122.72-26.4 164.48-68.96z" />
  </symbol>

  <symbol id="icon-valider" viewBox="0 0 512 512">
    <path
      d="m499 110c-18-18-46-18-64 0l-232 231-123-123c-18-17-46-17-65 0-18 18-17 46 0 65l154 153c9 9 21 14 33 14 12 0 24-5 32-14l265-262c17-18 17-47 0-64z" />
  </symbol>

  <symbol id="icon-chevron" viewBox="0 0 512 512">
    <path
      d="m256 214l174 167c4 4 11 4 16 0l30-30c5-4 5-11 1-16l-213-204c-2-2-5-3-8-3-3 0-6 1-8 3l-213 204c-4 5-4 12 0 16l31 30c4 4 11 4 16 0z" />
  </symbol>

  <symbol id="icon-pro" viewBox="0 0 512 512">
    <path
      d="m256 507l-5-2c-2-1-191-70-202-196l0-1c0-65 9-95 18-120 4-14 8-25 8-41 1-36-10-50-10-50l-9-9 10-10 56-67 11 9c0 0 22 18 51 18 22 0 42-8 63-26l9-7 9 8c21 18 41 26 63 26 29 0 51-17 51-17l11-10 9 12 56 65-10 9c0 0-10 14-9 49 0 16 4 27 8 41 9 25 18 55 18 120l0 1c-10 126-200 195-202 196z m-177-200c8 94 148 157 177 169 29-11 169-75 177-168 0-60-9-88-16-109-5-16-9-30-10-49-1-31 5-50 10-60l-31-39c-12 7-33 17-58 17-25 0-49-9-72-25-23 16-47 25-72 25-25 0-46-10-58-17l-32 38c5 10 11 30 10 59-1 20-5 34-10 50-6 22-15 49-15 109z m249 42c-1 0-2 0-3-1l-69-36-69 36c-2 1-5 1-7-1-2-2-3-4-3-7l13-77-55-54c-2-2-3-5-2-7 1-3 3-4 6-5l77-12 34-69c2-5 10-5 13 0l35 69 77 12c3 0 5 2 6 5 1 3 0 5-2 7l-55 54 13 77c0 3-1 5-3 7-3 2-4 2-6 2z m-72-52c1 0 2 0 3 1l59 31-11-66c0-2 0-5 2-6l48-46-65-9c-2 0-4-2-5-4l-31-61-30 60c-1 2-3 4-5 4l-65 9 48 46c2 2 2 4 2 6l-11 66 59-31c0 0 1 0 2 0z" />
  </symbol>

  <symbol id="icon-volontaire" viewBox="0 0 512 512">
    <path
      d="m173 493c3 3 7 5 11 5l216 0c7 0 12-4 14-10 0-1 3-10 5-14 3-11 7-23 10-36 5-21 9-42 12-61 2-13 3-25 3-36l0-173c0-24-19-43-43-43-3 0-6 0-9 1-2 1-5 1-5 1l0-17c0-24-19-43-43-43-3 0-6 0-8 1-4-1-8 0-8 0l0-17c0-23-20-43-43-43l-7 0c-18 0-33 10-40 26-1 2-2 6-2 6-6-2-11-3-17-3-23 0-43 19-43 43l0 241-42-39c-30-28-73-23-95 12l-5 9c-3 5-3 12 1 16z m-108-184c12-19 33-22 50-7l65 62c9 8 25 2 25-11l0-273c0-8 6-14 14-14 8 0 14 6 14 14l0 144c0 20 29 20 29 0l0-173c0-8 6-14 14-14l8 0c8 0 14 6 14 14l0 188c0 19 29 19 29 0l0-130c0-9 6-15 14-15 8 0 14 6 14 15l0 154c0 20 29 20 29 0l0-97c0-8 6-14 14-14 9 0 15 6 15 14l0 173c0 10-1 20-2 33-1 18-5 38-11 58-3 12-6 24-9 35-1 4-1 4-1 4l-199 0z" />
  </symbol>

  <symbol id="icon-stagiaire" viewBox="0 0 512 512">
    <path
      d="m173 493c3 3 7 5 11 5l216 0c7 0 12-4 14-10 0-1 3-10 5-14 3-11 7-23 10-36 5-21 9-42 12-61 2-13 3-25 3-36l0-173c0-24-19-43-43-43-3 0-6 0-9 1-2 1-5 1-5 1l0-17c0-24-19-43-43-43-3 0-6 0-8 1-4-1-8 0-8 0l0-17c0-23-20-43-43-43l-7 0c-18 0-33 10-40 26-1 2-2 6-2 6-6-2-11-3-17-3-23 0-43 19-43 43l0 241-42-39c-30-28-73-23-95 12l-5 9c-3 5-3 12 1 16z m-108-184c12-19 33-22 50-7l65 62c9 8 25 2 25-11l0-273c0-8 6-14 14-14 8 0 14 6 14 14l0 144c0 20 29 20 29 0l0-173c0-8 6-14 14-14l8 0c8 0 14 6 14 14l0 188c0 19 29 19 29 0l0-130c0-9 6-15 14-15 8 0 14 6 14 15l0 154c0 20 29 20 29 0l0-97c0-8 6-14 14-14 9 0 15 6 15 14l0 173c0 10-1 20-2 33-1 18-5 38-11 58-3 12-6 24-9 35-1 4-1 4-1 4l-199 0z" />
  </symbol>

  <symbol id="icon-reload" viewBox="0 0 512 512">
    <path
      d="m465 219c-20-58-64-105-120-129-47-19-99-22-148-8l17-47c5-15-3-30-17-34-14-4-28 2-34 15l-39 107c0 1 0 1-1 2-2 7-1 14 2 21 1 2 2 5 4 6 2 2 5 4 8 6l103 49c13 6 29 0 35-13 7-14 1-29-12-35l-51-25c37-10 76-8 111 6 43 18 75 54 90 98 13 38 13 81-2 120-27 65-118 130-218 87-58-24-97-79-100-142-1-15-14-25-28-24-14 0-25 13-24 28 6 85 55 156 132 189 27 11 55 17 84 17 88-1 168-53 204-133 20-52 22-109 4-161z" />
  </symbol>

  <symbol id="icon-ressources" viewBox="0 0 512 512">
    <path
      d="m467 228c6-9 10-21 10-33 0-34-27-63-62-63-34 0-63 28-63 63 0 12 4 24 10 33-6 1-12 3-18 8-8-13-20-21-36-24 7-10 10-23 10-36 0-36-28-64-64-64-36 0-65 28-65 64 0 13 5 26 11 36-16 2-28 10-36 24-5-4-11-6-19-8 7-9 11-21 11-33 0-34-28-63-63-63-35 0-59 28-59 63 0 12 4 24 10 33-25 4-43 26-42 52l3 52c1 15 13 27 29 29 20 3 42 4 62 4 21 0 42-1 63-4 1 0 2-1 3-1 1 16 13 28 28 29 22 2 43 3 65 3 21 0 43-1 64-3 16-1 28-13 29-29 1 0 2 1 3 1 21 3 42 4 63 4 20 0 42-1 62-4 16-1 28-14 29-29l3-52c2-27-16-49-41-52z m-371-82c27 0 48 22 48 49 0 26-21 48-48 48-26 0-48-23-48-48 0-26 22-49 48-49z m62 199c-20 2-40 3-62 3-20 0-41-1-60-3-8 0-16-7-16-14l-3-52c-1-19 14-35 32-36l7-1c5 4 13 8 19 10 6 3 13 4 21 4 16 0 30-5 40-14l7 1c7 1 13 2 17 5-2 5-2 10-2 16l3 81c-1 0-2 0-3 0z m97-218c28 0 49 21 49 49 0 28-21 49-49 49-28 0-49-21-49-49 0-28 22-49 49-49z m83 136l-3 94c-1 9-7 16-16 16-20 2-41 3-63 3-22 0-43-1-63-3-9-1-16-7-16-16l-3-94c-1-19 13-36 33-38 0 0 7-1 8-1 11 9 26 15 41 15 15 0 30-6 41-15 1 0 8 1 8 1 19 2 34 19 33 38z m77-117c26 0 48 22 48 49 0 26-22 48-48 48-27 0-48-22-48-48 0-27 21-49 48-49z m79 132l-4 52c-1 8-7 14-15 15-19 2-40 3-61 3-21 0-41-1-62-3-1 0-3-1-4-1l3-81c1-5-1-11-2-16 5-3 10-5 17-5l7-1c10 9 24 14 40 14 15 0 29-5 40-14l7 1c20 1 35 17 34 36z" />
  </symbol>

  <symbol id="icon-bell" viewBox="-5 -5 120 120">
    <path
      d="m88.594 76.723c0 2.4648-1.9883 4.4531-4.4531 4.4531h-68.285c-2.4336 0-4.4531-1.9883-4.4531-4.4531s2.0195-4.4531 4.4531-4.4531c2.4648 0 4.4531-1.9883 4.4531-4.4531l0.003906-28.203c0-14.875 10.984-27.168 25.234-29.332v-3.3281c0-2.4648 2.0195-4.4531 4.4531-4.4531 2.4648 0 4.4531 1.9883 4.4531 4.4531v3.3555c14.281 2.1367 25.238 14.43 25.238 29.305v28.203c0 2.4648 2.0195 4.4531 4.4531 4.4531 2.4648 0 4.4531 1.9883 4.4531 4.4531z"
      xmlns="http://www.w3.org/2000/svg" />
    <path d="m64.555 85.625c-1.3945 6.7695-7.3633 11.875-14.547 11.875-7.1562 0-13.152-5.1055-14.547-11.875z"
          xmlns="http://www.w3.org/2000/svg" />
  </symbol>
  <symbol fill="none" id="icon-bell2" viewBox="0 0 12 12"
          xmlns="http://www.w3.org/2000/svg">
    <path
      d="M4.501 8.5H2.803c-0.629 0 -0.944 0 -1.01 -0.049 -0.074 -0.055 -0.092 -0.087 -0.1 -0.179 -0.007 -0.082 0.185 -0.398 0.571 -1.028C2.662 6.592 3 5.644 3 4.3c0 -0.743 0.316 -1.455 0.879 -1.98S5.205 1.5 6 1.5s1.559 0.295 2.122 0.82S9 3.558 9 4.3c0 1.343 0.339 2.292 0.737 2.943 0.386 0.631 0.579 0.947 0.572 1.028 -0.009 0.092 -0.026 0.124 -0.101 0.179 -0.066 0.049 -0.381 0.049 -1.01 0.049H7.5m-2.999 0L4.5 9a1.5 1.5 0 1 0 3 0v-0.5m-2.999 0H7.5"
      stroke="#ffffff" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" />
  </symbol>
  <symbol fill="none" id="icon-bell2-slash" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M3 3L21 21M9.37747 3.56325C10.1871 3.19604 11.0827 3 12 3C13.5913 3 15.1174 3.59 16.2426 4.6402C17.3679 5.69041 18 7.11479 18 8.6C18 10.3566 18.2892 11.7759 18.712 12.9122M17 17H15M6.45339 6.46451C6.15686 7.13542 6 7.86016 6 8.6C6 11.2862 5.3238 13.1835 4.52745 14.4866C3.75616 15.7486 3.37051 16.3797 3.38485 16.5436C3.40095 16.7277 3.43729 16.7925 3.58603 16.9023C3.71841 17 4.34762 17 5.60605 17H9M9 17V18C9 19.6569 10.3431 21 12 21C13.6569 21 15 19.6569 15 18V17M9 17H15"
      stroke="#ffffff" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" />
  </symbol>
  <symbol fill="#ffffff" id="icon-rici-range" viewBox="0 0 477.6 477.6"
          xml:space="preserve" xmlns="http://www.w3.org/2000/svg">
<g>
	<g>
		<path
      d="M82.95,139.6h112.7c7.5,0,13.5-6,13.5-13.5V13.5c0-7.5-6-13.5-13.5-13.5H82.95c-7.5,0-13.5,6-13.5,13.5v112.7    C69.45,133.6,75.45,139.6,82.95,139.6z M96.45,27h85.7v85.7h-85.7V27z" />
    <path
      d="M82.95,308.6h112.7c7.5,0,13.5-6,13.5-13.5V182.5c0-7.5-6-13.5-13.5-13.5H82.95c-7.5,0-13.5,6-13.5,13.5v112.7    C69.45,302.6,75.45,308.6,82.95,308.6z M96.45,196h85.7v85.7h-85.7V196z" />
    <path
      d="M394.55,54.3h-102.5l29-29c5.3-5.3,5.3-13.8,0-19.1s-13.8-5.3-19.1,0l-52,52c-5.3,5.3-5.3,13.8,0,19.1l52,52    c2.6,2.6,6.1,4,9.5,4s6.9-1.3,9.5-4c5.3-5.3,5.3-13.8,0-19.1l-29-29h89.1v313h-172v-42.8c0-7.5-6-13.5-13.5-13.5H82.95    c-7.5,0-13.5,6-13.5,13.5v112.7c0,7.5,6,13.5,13.5,13.5h112.7c7.5,0,13.5-6,13.5-13.5v-42.8h185.5c7.5,0,13.5-6,13.5-13.5v-340    C408.05,60.3,402.05,54.3,394.55,54.3z M182.05,450.6h-85.7v-85.7h85.7V450.6z" />
	</g>
</g>
</symbol>

  <svg fill="#ffffff" id="icon-rici-schema" viewBox="0 0 297 297"
       xml:space="preserve" xmlns="http://www.w3.org/2000/svg">
<g>
	<path
    d="M181.116,195.694c-14.442,0-26.191,11.749-26.191,26.191s11.749,26.191,26.191,26.191   c14.442,0,26.191-11.749,26.191-26.191S195.558,195.694,181.116,195.694z M181.116,228.309c-3.542,0-6.424-2.882-6.424-6.424   s2.882-6.424,6.424-6.424c3.542,0,6.424,2.882,6.424,6.424S184.658,228.309,181.116,228.309z" />
  <path
    d="M246.347,32.616h-39.04v-6.424c0-5.458-4.425-9.884-9.884-9.884h-24.668C168.847,6.751,159.447,0,148.5,0   s-20.347,6.751-24.255,16.308H99.577c-5.458,0-9.884,4.425-9.884,9.884v6.424h-39.04c-5.458,0-9.884,4.425-9.884,9.884v244.617   c0,5.458,4.425,9.884,9.884,9.884h195.694c5.458,0,9.884-4.425,9.884-9.884V42.499C256.23,37.041,251.805,32.616,246.347,32.616z    M109.46,36.075h22.732c5.458,0,9.884-4.425,9.884-9.884c0-3.542,2.882-6.424,6.424-6.424s6.424,2.882,6.424,6.424   c0,5.458,4.425,9.884,9.884,9.884h22.732v12.849h-78.08V36.075z M236.463,277.233H60.537V52.383h29.156v6.424   c0,5.458,4.425,9.884,9.884,9.884h97.847c5.458,0,9.884-4.425,9.884-9.884v-6.424h29.156V277.233z" />
  <path
    d="M115.884,121.708l9.319,9.319c1.93,1.929,4.459,2.895,6.989,2.895c2.53,0,5.059-0.965,6.989-2.895   c3.86-3.86,3.86-10.118,0-13.978l-9.319-9.319l9.319-9.319c3.86-3.86,3.86-10.118,0-13.978c-3.861-3.859-10.117-3.859-13.978,0   l-9.319,9.319l-9.319-9.319c-3.861-3.859-10.117-3.859-13.978,0c-3.86,3.86-3.86,10.118,0,13.978l9.319,9.319l-9.319,9.319   c-3.86,3.86-3.86,10.118,0,13.978c1.93,1.929,4.459,2.895,6.989,2.895c2.53,0,5.059-0.965,6.989-2.895L115.884,121.708z" />
  <path
    d="M147.833,192.778c-18.435-13.7-22.451-45.445-22.49-45.763c-0.642-5.413-5.548-9.289-10.961-8.654   c-5.421,0.633-9.304,5.541-8.671,10.963c0.192,1.645,4.992,40.49,30.331,59.32c1.771,1.317,3.839,1.952,5.888,1.952   c3.02,0,6.001-1.379,7.941-3.989C153.126,202.225,152.214,196.034,147.833,192.778z" />
</g>
</svg>
  <symbol id="icon-plus" viewBox="0 0 512 512">
    <path
      d="m509 256c0 12-10 22-22 22l-209 0 0 208c0 13-11 22-22 22-11 0-22-10-22-22l0-208-208 0c-13 0-22-11-22-22 0-11 10-22 22-22l208 0 0-208c0-13 11-22 22-22 11 0 22 10 22 22l0 208 208 0c13-1 23 10 23 22z" />
  </symbol>

  <symbol id="icon-jour" viewBox="0 0 512 512">
    <path
      d="m406 132l33-34c4-4 4-11 0-15-4-4-11-4-15 0l-35 33c-4 4-4 11 0 15 5 5 12 5 17 1z m-291 3c6 0 11-5 11-11 0-3-1-6-3-8l-34-33c-4-4-11-4-15 0-4 4-4 11 0 15l34 34c1 2 4 3 7 3z m133-66c4 4 11 4 15 0 3-2 4-4 4-8l0-47c0-6-5-11-11-11-6 0-11 5-11 11l0 47c0 4 1 6 3 8z m158 312c-5-4-12-4-16 0-4 4-4 11 0 15l34 34c4 4 11 4 15 0 4-4 4-11 0-15z m-300 0l-33 34c-4 4-4 11 0 15 4 4 11 4 15 0l34-34c2-2 3-5 3-8 0-6-5-11-11-11-2 0-5 2-8 4z m139 71l0 47c0 6 5 11 11 11 6 0 11-5 11-11l0-47c0-3-1-7-3-9-4-4-11-4-15 0-3 2-4 6-4 9z m-169-202c4-4 4-11 0-16-2-2-5-3-8-3l-48 0c-6 0-11 6-11 12 0 6 5 11 11 11l48 0c3-1 6-2 8-4z m368 3l48 0c6 0 11-5 11-11 0-6-5-12-11-12l-48 0c-3 0-6 1-8 3-4 5-4 12 0 16 2 3 5 4 8 4z m-188-95c54 0 99 44 99 99 0 55-44 99-99 99-54 0-99-44-99-99 0-55 45-99 99-99m0-23c-67 0-121 54-121 121 0 67 54 121 121 121 67 0 122-54 122-121 0-67-54-121-122-121z" />
  </symbol>

  <symbol id="icon-matin" viewBox="0 0 512 512">
    <path
      d="m227 137l15-14 0 113c0 8 6 14 14 14 8 0 14-6 14-14l0-113 15 14c6 6 15 6 20 0 6-6 6-15 0-20l-39-39c-6-6-15-6-20 0l-39 39c-6 6-6 15 0 20 5 6 14 6 20 0z m274 295l-123 0c-6-67-66-117-133-110-59 5-106 52-111 110l-123 0c-6 0-11 5-11 11 0 7 5 12 11 12l490 0c6 0 11-5 11-12 0-6-5-11-11-11z m-344 0c6-50 48-89 99-89 51 0 93 38 99 89z m250-122l33-34c4-4 4-11 0-16-4-4-11-4-16 0l-34 34c-4 4-4 11 0 16 5 4 12 4 17 0z m-302 0c3 2 6 3 9 3 6 0 11-5 11-11 0-3-1-6-3-8l-34-34c-4-4-11-4-16 0-4 4-4 11 0 16z" />
  </symbol>

  <symbol id="icon-nuit" viewBox="0 0 512 512">
    <path
      d="m285 445c-107 0-193-86-193-192 0-68 35-129 93-165 5-3 13-2 17 2 4 4 6 10 3 16-11 25-18 51-18 76 0 97 79 175 175 175 21 0 40-3 60-10 6-2 12 0 15 5 4 5 3 12 0 17-37 49-92 76-152 76z m-121-304c-29 30-44 70-44 113 0 91 74 165 165 165 37 0 70-12 99-34-7 1-14 1-22 1-111 0-202-91-202-203 0-14 1-28 4-42z" />
  </symbol>

  <symbol id="icon-soiree" viewBox="0 0 512 512">
    <path
      d="m246 245c6 6 15 6 20 0l39-39c6-6 6-16 0-21-6-6-15-6-20 0l-15 15 0-113c0-9-6-15-14-15-8 0-14 6-14 14l0 113-15-15c-6-6-15-6-20 0-6 6-6 16 0 21z m255 186l-123 0c-6-67-66-117-133-110-59 5-106 52-111 110l-123 0c-6 0-11 5-11 11 0 7 5 12 11 12l490 0c6 0 11-5 11-12 0-6-5-11-11-11z m-344 0c6-50 48-89 99-89 51 0 93 38 99 89z m250-122l33-34c4-4 4-11 0-16-4-4-11-4-16 0l-34 34c-4 4-4 11 0 16 5 4 12 4 17 0z m-302 0c3 2 6 3 9 3 6 0 11-5 11-11 0-3-1-6-3-8l-34-34c-4-4-11-4-16 0-4 4-4 11 0 16z" />
  </symbol>

  <svg id="icon-gear" viewBox="0 0 50 50">
    <path
      d="M48.8,23.2l-2.5-1.3c-0.1-0.5-0.2-1.1-0.3-1.6l2-2.3c0.4-0.5,0.5-1.1,0.3-1.7c-0.9-2.5-2.2-4.8-3.8-6.8 c-0.5-0.5-1.1-0.8-1.8-0.6l-2.6,0.6c-0.6-0.6-1.4-1.3-2.1-1.8v-3c0-0.6-0.4-1.2-0.9-1.5c-0.3-0.2-0.5-0.3-0.7-0.4 c-1.9-1-3.9-1.8-6-2.3c-0.6-0.2-1.4,0.1-1.8,0.6l-1.4,2.2c-1.4-0.2-2.7-0.2-4-0.1l-1.8-2.3c-0.5-0.5-1.1-0.7-1.7-0.5 c-2.2,0.5-4.2,1.3-6.3,2.3c-0.6,0.3-1,1-0.9,1.6l0.2,2.6c-1.4,0.9-2.5,2-3.7,3.2L6.3,9.6c-0.6-0.1-1.4,0.2-1.7,0.7 c-0.7,1-1.3,1.9-1.7,2.8S1.9,15,1.6,16c-0.3,0.6-0.1,1.4,0.4,1.8l1.9,2c-0.4,1.5-0.5,3.2-0.5,4.8L1,25.8c-0.6,0.4-0.9,1-0.8,1.7 C0.4,29.7,1,31.9,1.8,34c0.3,0.6,0.8,1,1.4,1.1l2.9,0.3c0.6,1.2,1.4,2.3,2.3,3.2L7.4,41c-0.3,0.6-0.1,1.4,0.5,1.8 c1.6,1.5,3.4,2.8,5.4,3.9c0.1,0.1,0.3,0.1,0.4,0.2l0.2,0.1c0.5,0.3,1.2,0.3,1.7-0.1l2.5-1.7c0.8,0.3,1.7,0.5,2.6,0.7l0.9,2.5 c0.3,0.6,0.8,1.1,1.4,1.1c0.6,0.1,1.3,0.1,1.9,0.1c2,0,4.1-0.3,6-0.7c0.6-0.2,1.1-0.6,1.3-1.2l0.8-2.9c0.5-0.2,1-0.5,1.4-0.6 l2.4,1.4c0.5,0.4,1.3,0.3,1.8-0.1c2.3-1.5,4.3-3.4,6-5.7c0.4-0.5,0.5-1.2,0.2-1.7l-1.2-2.7c0.1-0.2,0.3-0.5,0.4-0.6 c0.1-0.1,0.1-0.3,0.2-0.4l2.9-0.5c0.6-0.1,1.1-0.5,1.4-1.1c0.9-2.6,1.4-5.4,1.4-8.1C49.8,24,49.4,23.4,48.8,23.2z M45.6,30.7 l-2.8,0.5c-0.5,0.1-1,0.5-1.3,1c-0.2,0.4-0.4,0.7-0.5,1.1c-0.2,0.5-0.5,0.8-0.7,1.2c-0.3,0.5-0.4,1.1-0.1,1.6l1.2,2.5 c-1.1,1.3-2.3,2.5-3.7,3.5l-2.3-1.4c-0.5-0.3-1.2-0.3-1.7,0c-0.8,0.5-1.7,0.8-2.6,1.2c-0.5,0.2-0.9,0.6-1.1,1.2l-0.8,2.8 c-1.5,0.3-3.2,0.5-4.8,0.5l-0.8-2.5c-0.2-0.6-0.7-1-1.4-1.1c-1.3-0.2-2.5-0.5-3.7-1c-0.5-0.2-1.1-0.1-1.5,0.2l-2.3,1.6 c-1.3-0.7-2.3-1.4-3.4-2.3l0.8-2.3c0.2-0.6,0.1-1.3-0.4-1.7c-1.1-1.2-2.1-2.6-2.9-4.1c-0.3-0.5-0.8-0.9-1.4-0.9l-2.7-0.3 c-0.4-1.2-0.7-2.3-0.9-3.5l2.2-1.3c0.5-0.4,0.9-0.9,0.9-1.5c-0.1-2,0.2-3.9,0.7-5.7c0.2-0.6,0-1.3-0.5-1.6l-1.8-1.9 c0.2-0.5,0.5-1,0.7-1.4c0.3-0.5,0.5-1,0.9-1.5l2.5,0.5c0.6,0.1,1.3-0.1,1.6-0.6c1.3-1.4,2.7-2.8,4.3-3.8c0.5-0.4,0.8-0.9,0.8-1.6 L16,5.2c1.1-0.5,2.3-0.9,3.4-1.3L21.1,6c0.4,0.5,0.9,0.6,1.5,0.6c1.7-0.2,3.4-0.2,5,0.1C28.3,6.9,29,6.6,29.3,6l1.4-2 c1.4,0.4,2.6,0.9,3.9,1.5v2.9c0,0.5,0.3,1.1,0.7,1.4c1.1,0.8,2.1,1.6,3,2.6c0.5,0.5,1.1,0.6,1.7,0.5l2.4-0.6 c0.9,1.3,1.7,2.7,2.3,4.1l-1.9,2.2c-0.4,0.5-0.5,1-0.4,1.5c0.3,0.9,0.4,1.9,0.5,2.9c0.1,0.6,0.5,1.1,1,1.4l2.3,1.2 C46.3,27.4,46,29.1,45.6,30.7z" />
    <path
      d="M29.7,16c-1.4-0.8-3.2-1.2-4.8-1.2c-3.8,0-7.3,2.1-9,5.4c-1.3,2.4-1.5,5.1-0.7,7.7c0.8,2.6,2.6,4.8,5,6 c1.4,0.8,3.2,1.2,4.8,1.2c3.8,0,7.3-2.1,9-5.4c1.3-2.4,1.5-5.1,0.7-7.7C33.9,19.4,32.1,17.2,29.7,16z M31,28.2 c-1.2,2.3-3.5,3.6-6,3.6c-1.1,0-2.2-0.3-3.2-0.8c-1.6-0.8-2.8-2.3-3.3-4c-0.5-1.7-0.4-3.6,0.5-5.2c1.2-2.3,3.5-3.6,6-3.6 c1.1,0,2.2,0.3,3.2,0.8c1.6,0.8,2.8,2.3,3.3,4C31.9,24.7,31.8,26.6,31,28.2z" />
  </svg>

  <svg id="icon-add-fireman" viewBox="0 0 50 50">
    <path
      d="M28.6,9.6c0-2.2-1.8-3.9-3.9-3.9c-2.2,0-3.9,1.8-3.9,3.9c0,2.2,1.8,3.9,3.9,3.9C26.8,13.5,28.6,11.8,28.6,9.6z M22.8,9.6 c0-1,0.8-1.8,1.8-1.8c1,0,1.8,0.8,1.8,1.8c0,1-0.8,1.8-1.8,1.8C23.6,11.5,22.8,10.6,22.8,9.6z" />
    <path
      d="M50,26.5c0-3.7-3.2-6.9-8.3-9.1c-1.4-7.1-6.2-12.8-12.5-14.8V1c0-0.6-0.5-1-1-1h-7.3c-0.6,0-1,0.5-1,1v1.6 c-6.4,2-11.2,7.8-12.5,15.2C2.8,20.1,0,23.1,0,26.5c0,3,2.4,6,6.3,8.1c2.2,1.1,2.1,0.2,2.1,0.2V34v-6.5c0.3-0.1,0.6-0.2,1-0.4 l2.1-0.7c3.8-1.1,10.2-2.5,17.8-1.7c-4.2,2.3-7.1,6.7-7.1,11.9c0,7.4,6,13.5,13.5,13.5s13.5-6,13.5-13.5c0-1.8-0.4-3.6-1-5.2 C49.3,29.9,50,28.2,50,26.5z M21.2,4.5C21.6,4.4,22,4,22,3.5V2.1h5.2v1.4c0,0.5,0.3,0.9,0.8,1c5.5,1.4,9.8,6,11.4,12.1 c-3.5-1.2-7.7-1.9-12.3-2.1c-0.1,0-0.2,0-0.3,0c-0.6,0-1.2,0-1.8,0c-0.6,0-1.3,0-1.9,0c-0.1,0-0.2,0-0.3,0c-5,0.2-9.5,1.1-13.1,2.4 C11.2,10.7,15.5,5.9,21.2,4.5z M6.9,25.8c-0.4,0.2-0.6,0.5-0.6,0.9v5.4c-2.7-1.7-4.2-3.7-4.2-5.7c0-2.7,2.6-5.2,6.7-7 c0.4-0.2,0.9-0.4,1.3-0.5c0.3-0.1,0.5-0.2,0.8-0.3c0.2-0.1,0.5-0.2,0.8-0.3c0.5-0.2,1-0.3,1.5-0.4c0.1,0,0.1,0,0.2-0.1 c0.6-0.2,1.2-0.3,1.8-0.4c0,0,0.1,0,0.1,0c0.6-0.1,1.2-0.2,1.8-0.3c0.1,0,0.3,0,0.4-0.1c0.5-0.1,1-0.1,1.5-0.2c0.3,0,0.7-0.1,1-0.1 c0.3,0,0.6-0.1,0.9-0.1c0.6,0,1.1-0.1,1.7-0.1c0.1,0,0.2,0,0.3,0c0.7,0,1.4,0,2.1,0c0.7,0,1.3,0,2,0c0.1,0,0.2,0,0.3,0 c0.6,0,1.2,0.1,1.7,0.1c0.2,0,0.3,0,0.5,0c0.5,0,1,0.1,1.5,0.2c0.2,0,0.5,0.1,0.7,0.1c0.4,0.1,0.9,0.1,1.3,0.2 c0.3,0,0.6,0.1,0.8,0.1c0.3,0.1,0.6,0.1,0.8,0.2c0.4,0.1,0.8,0.2,1.1,0.3c0.2,0,0.4,0.1,0.6,0.1c0.4,0.1,0.8,0.2,1.2,0.3 c0.3,0.1,0.5,0.2,0.8,0.2c0.3,0.1,0.6,0.2,0.9,0.3c0.4,0.1,0.8,0.3,1.2,0.4c4.6,1.9,7.6,4.5,7.6,7.4c0,0.9-0.3,1.9-1,2.8 c-2.4-3.7-6.6-6.2-11.3-6.2c-0.7,0-1.3,0.1-1.9,0.2C18.9,20.2,7.4,25.6,6.9,25.8z M35.6,48c-6.3,0-11.5-5.1-11.5-11.5 s5.1-11.5,11.5-11.5c6.3,0,11.5,5.1,11.5,11.5S41.9,48,35.6,48z" />
    <path
      d="M41.3,35.3h-4.4v-4.4c0-0.7-0.6-1.3-1.3-1.3c-0.7,0-1.3,0.6-1.3,1.3v4.4h-4.4c-0.7,0-1.3,0.6-1.3,1.3 c0,0.7,0.6,1.3,1.3,1.3h4.4v4.4c0,0.7,0.6,1.3,1.3,1.3c0.7,0,1.3-0.6,1.3-1.3v-4.4h4.4c0.7,0,1.3-0.6,1.3-1.3 C42.6,35.9,42,35.3,41.3,35.3z" />
  </svg>

  <svg id="icon-bin" viewBox="0 0 50 50">
    <path
      d="M8.1,4.2h33.8c0.8,0,1.5,0.7,1.5,1.5v0c0,0.8-0.7,1.5-1.5,1.5H8.1c-0.8,0-1.5-0.7-1.5-1.5v0C6.6,4.9,7.3,4.2,8.1,4.2z" />
    <path
      d="M22.8,0h4.3c0.8,0,1.5,0.7,1.5,1.5v0C28.6,2.3,28,3,27.2,3h-4.3c-0.8,0-1.5-0.7-1.5-1.5v0C21.3,0.7,22,0,22.8,0z" />
    <path
      d="M12.1,46.8c-0.2-0.2-0.3-0.4-0.3-0.6l-1.4-34.8c0-0.7-0.6-1.3-1.3-1.3H8.8c-0.7,0-1.3,0.6-1.3,1.4l1.4,34.8 c0,1,0.5,1.9,1.2,2.6c0.7,0.7,1.6,1.1,2.6,1.1h24.6c1,0,2-0.4,2.6-1.1c0.7-0.7,1.1-1.6,1.2-2.6l1.4-34.8c0-0.7-0.6-1.4-1.3-1.4h-0.3 c-0.7,0-1.3,0.6-1.3,1.3l-1.4,34.8c0,0.2-0.1,0.4-0.3,0.6c-0.2,0.2-0.4,0.2-0.6,0.2h-3.5c-0.7,0-1.3-0.6-1.3-1.4l1.3-34.1 c0-0.7-0.6-1.4-1.3-1.4h-0.3c-0.7,0-1.3,0.6-1.3,1.3l-1.3,34.3c0,0.7-0.6,1.3-1.3,1.3H22c-0.7,0-1.3-0.6-1.3-1.3l-1.3-34.3 c0-0.7-0.6-1.3-1.3-1.3h-0.3c-0.7,0-1.3,0.6-1.3,1.4l1.3,34.1c0,0.7-0.6,1.4-1.3,1.4h-3.6C12.5,47,12.3,46.9,12.1,46.8z" />
  </svg>

  <symbol id="icon-delete" viewBox="0 0 512 512">
    <path
      d="m 435.04446,77.455538 c 8.48528,8.485281 8.48528,22.627412 0,31.112702 L 287.25914,256.35355 434.33736,403.43176 c 9.19238,9.19239 7.77817,23.33453 0,31.1127 -7.77818,7.77818 -22.62742,8.48528 -31.1127,0 L 256.14645,287.46625 109.06824,434.54446 c -9.192392,9.19239 -23.334528,7.77818 -31.112702,0 -7.778175,-7.77817 -8.485282,-22.62741 0,-31.1127 L 225.03375,256.35355 77.955538,109.27534 c -9.192388,-9.19239 -7.778175,-23.334521 0,-31.112695 7.778174,-7.778175 22.627412,-8.485282 31.112702,0 L 256.14645,225.24086 403.22466,78.162645 c 8.48528,-9.899495 23.33452,-9.192389 31.8198,-0.707107 z" />
  </symbol>
  <symbol id="icon-info" viewBox="0 0 10 65">
    <path
      d="M 10.8 21.501 L 10.8 73.501 L 1.8 73.501 L 1.8 21.501 L 10.8 21.501 Z M 1.908 10.808 A 6.059 6.059 0 0 0 6.3 12.601 A 8.264 8.264 0 0 0 7.956 12.443 A 5.661 5.661 0 0 0 10.9 10.901 A 5.588 5.588 0 0 0 12.371 8.269 A 7.987 7.987 0 0 0 12.6 6.301 A 7.26 7.26 0 0 0 12.6 6.22 A 6.06 6.06 0 0 0 10.75 1.851 A 7.395 7.395 0 0 0 10.692 1.793 A 6.059 6.059 0 0 0 6.3 0.001 A 8.264 8.264 0 0 0 4.644 0.158 A 5.661 5.661 0 0 0 1.7 1.701 A 5.588 5.588 0 0 0 0.229 4.333 A 7.987 7.987 0 0 0 0 6.301 A 7.26 7.26 0 0 0 0.001 6.382 A 6.06 6.06 0 0 0 1.85 10.751 A 7.395 7.395 0 0 0 1.908 10.808 Z" />
  </symbol>
  <svg id="icon-date-picker" viewBox="0 0 50 50">
    <path
      d="M46.1,4.8h-6c-0.7,0-1.3,0.6-1.3,1.3c0,0.7,0.6,1.3,1.3,1.3h6c0.7,0,1.3,0.6,1.3,1.3v8.1H3V8.7c0-0.7,0.6-1.3,1.3-1.3h6.8 c0.7,0,1.3-0.6,1.3-1.3c0-0.7-0.6-1.3-1.3-1.3H4.3c-2.1,0-3.9,1.7-3.9,3.9v35.1c0,2.1,1.7,3.9,3.9,3.9h41.8c2.1,0,3.9-1.7,3.9-3.9 V8.7C50,6.5,48.2,4.8,46.1,4.8z M47.4,43.8c0,0.7-0.6,1.3-1.3,1.3H4.3c-0.7,0-1.3-0.6-1.3-1.3V19.3h44.4L47.4,43.8L47.4,43.8z M19.9,7.4h10.4c0.7,0,1.3-0.6,1.3-1.3c0-0.7-0.6-1.3-1.3-1.3H19.9c-0.7,0-1.3,0.6-1.3,1.3C18.6,6.8,19.2,7.4,19.9,7.4z M15.2,14.4 c1.5,0,2.7-1.2,2.7-2.7c0-1-0.6-1.9-1.4-2.3V1.6c0-0.7-0.6-1.3-1.3-1.3c-0.7,0-1.3,0.6-1.3,1.3v7.7c-0.8,0.5-1.4,1.3-1.4,2.3 C12.5,13.2,13.8,14.4,15.2,14.4z M35.1,14.4c1.5,0,2.7-1.2,2.7-2.7c0-1-0.6-1.9-1.4-2.3V1.6c0-0.7-0.6-1.3-1.3-1.3 c-0.7,0-1.3,0.6-1.3,1.3v7.7c-0.8,0.5-1.4,1.3-1.4,2.3C32.4,13.2,33.6,14.4,35.1,14.4z" />
    <path
      d="M42.5,42.5h-5.4c-1.1,0-2-0.9-2-2v-5.4c0-1.1,0.9-2,2-2h5.4c1.1,0,2,0.9,2,2v5.4C44.5,41.6,43.6,42.5,42.5,42.5z" />
  </svg>

  <svg id="icon-loupe" viewBox="0 0 50 50">
    <path d="M47.4,45.6L34.3,32.5C37.2,29.1,39,24.8,39,20C39,9.5,30.5,1,20,1S1,9.5,1,20s8.5,19,19,19c4.3,0,8.2-1.4,11.4-3.8
        l13.2,13.2C45,48.8,45.5,49,46,49s1-0.2,1.4-0.6C48.2,47.6,48.2,46.4,47.4,45.6z M5,20c0-8.3,6.7-15,15-15c8.3,0,15,6.7,15,15
        c0,8.3-6.7,15-15,15C11.7,35,5,28.3,5,20z" />
    <path d="M21.8,8.2c-5.4-0.3-10.5,2.6-12.9,7.5C8.4,16.7,8,17.9,7.7,19c-0.2,1.1,0.5,2.1,1.6,2.4c0.1,0,0.3,0,0.4,0
        c0.9,0,1.8-0.7,2-1.6c0.2-0.8,0.4-1.6,0.8-2.4c1.7-3.4,5.3-5.5,9.1-5.3c1.1,0.1,2-0.8,2.1-1.9S22.9,8.2,21.8,8.2z" />
  </svg>

  <svg id="icon-double-arrow" viewBox="0 0 50 50">
    <path d="M22.2,6.7c-1.1-1.1-2.9-1.1-4,0c0,0,0,0,0,0L1.8,23c-1.1,1.1-1.1,2.9,0,4c0,0,0,0,0,0l16.3,16.3c1.1,1.1,2.9,1.1,4,0
      c1.1-1.1,1.1-2.9,0-4l0,0L7.9,25l14.3-14.3C23.3,9.6,23.3,7.8,22.2,6.7C22.2,6.7,22.2,6.7,22.2,6.7z" />
    <path d="M47.2,2.1c-1.4-1.4-3.7-1.4-5.1,0c0,0,0,0,0,0L21.7,22.5c-1.4,1.4-1.4,3.7,0,5.1c0,0,0,0,0,0l20.4,20.4
      c1.4,1.4,3.7,1.4,5.1,0c1.4-1.4,1.4-3.7,0-5.1l0,0L29.3,25L47.2,7.1C48.6,5.7,48.6,3.5,47.2,2.1C47.2,2.1,47.2,2.1,47.2,2.1z" />
  </svg>

  <svg id="icon-download" viewBox="0 0 512 512">
    <g>
      <g>
        <path
          d="M382.56,233.376C379.968,227.648,374.272,224,368,224h-64V16c0-8.832-7.168-16-16-16h-64c-8.832,0-16,7.168-16,16v208h-64    c-6.272,0-11.968,3.68-14.56,9.376c-2.624,5.728-1.6,12.416,2.528,17.152l112,128c3.04,3.488,7.424,5.472,12.032,5.472    c4.608,0,8.992-2.016,12.032-5.472l112-128C384.192,245.824,385.152,239.104,382.56,233.376z" />
      </g>
    </g>
    <g>
      <g>
        <path d="M432,352v96H80v-96H16v128c0,17.696,14.336,32,32,32h416c17.696,0,32-14.304,32-32V352H432z" />
      </g>
    </g>
  </svg>
  <symbol id="icon-rici" viewBox="0 0 512 512">
    <path
      d="M 7 7 C 5.3550302 7 4 8.3550302 4 10 L 4 22 C 4 23.64497 5.3550302 25 7 25 L 25 25 C 26.64497 25 28 23.64497 28 22 L 28 10 C 28 8.3550302 26.64497 7 25 7 L 7 7 z M 7 9 L 25 9 C 25.56503 9 26 9.4349698 26 10 L 26 22 C 26 22.56503 25.56503 23 25 23 L 7 23 C 6.4349698 23 6 22.56503 6 22 L 6 10 C 6 9.4349698 6.4349698 9 7 9 z M 8 11 L 8 17 L 20 17 L 20 11 L 8 11 z M 22 11 L 22 13 L 24 13 L 24 11 L 22 11 z M 22 15 L 22 17 L 24 17 L 24 15 L 22 15 z M 8 19 L 8 21 L 20 21 L 20 19 L 8 19 z M 22 19 L 22 21 L 24 21 L 24 19 L 22 19 z"
      transform="scale(18) translate(-2, -2)"
    />
  </symbol>

  <symbol fill="none" id="icon-lost" viewBox="0 0 24 24">

    <path
      d="M12 7.5V11.5M12 14.5H12.01M19 10.2C19 14.1764 15.5 17.4 12 21C8.5 17.4 5 14.1764 5 10.2C5 6.22355 8.13401 3 12 3C15.866 3 19 6.22355 19 10.2Z"
      stroke="#ffffff"
      stroke-linecap="round" stroke-linejoin="round" stroke-width="2" xmlns="http://www.w3.org/2000/svg" />

  </symbol>

  <symbol id="icon-transmit" viewBox="0 0 24 24">

    <path
      d="M11.86,2L11.34,3.93C15.75,4.78 19.2,8.23 20.05,12.65L22,12.13C20.95,7.03 16.96,3.04 11.86,2M10.82,5.86L10.3,7.81C13.34,8.27 15.72,10.65 16.18,13.68L18.12,13.16C17.46,9.44 14.55,6.5 10.82,5.86M3.72,9.69C3.25,10.73 3,11.86 3,13C3,14.95 3.71,16.82 5,18.28V22H8V20.41C8.95,20.8 9.97,21 11,21C12.14,21 13.27,20.75 14.3,20.28L3.72,9.69M9.79,9.76L9.26,11.72A3,3 0 0,1 12.26,14.72L14.23,14.2C14,11.86 12.13,10 9.79,9.76Z"
      xmlns="http://www.w3.org/2000/svg" />
  </symbol>

  <svg id="icon-upload" viewBox="0 0 512 512">
    <g>
      <g>
        <path
          d="M382.56,233.376C379.968,227.648,374.272,224,368,224h-64V16c0-8.832-7.168-16-16-16h-64c-8.832,0-16,7.168-16,16v208h-64    c-6.272,0-11.968,3.68-14.56,9.376c-2.624,5.728-1.6,12.416,2.528,17.152l112,128c3.04,3.488,7.424,5.472,12.032,5.472    c4.608,0,8.992-2.016,12.032-5.472l112-128C384.192,245.824,385.152,239.104,382.56,233.376z"
          transform="rotate(180, 256, 185)" />
      </g>
    </g>
    <g>
      <g>
        <path d="M432,352v96H80v-96H16v128c0,17.696,14.336,32,32,32h416c17.696,0,32-14.304,32-32V352H432z" />
      </g>
    </g>
  </svg>

  <svg
    id="icon-system"
    viewBox="0 0 60 60"
  >
    <path
      d="m 20.101,15.858 -4.243,4.242 2.359,2.359 c -0.879,1.37 -1.528,2.9 -1.886,4.541 H 13 v 6 h 3.33 c 0.359,1.641 1.006,3.173 1.885,4.542 l -2.357,2.357 4.243,4.243 2.357,-2.357 c 1.37,0.878 2.901,1.526 4.542,1.885 V 47 h 6 v -3.33 c 1.641,-0.359 3.17,-1.008 4.541,-1.886 L 39.9,44.143 44.143,39.9 41.784,37.541 c 0.879,-1.37 1.528,-2.9 1.886,-4.541 H 47 v -6 h -3.33 c -0.359,-1.641 -1.006,-3.173 -1.885,-4.542 l 2.357,-2.357 -4.243,-4.243 -2.357,2.357 C 36.172,17.337 34.641,16.689 33,16.33 V 13 h -6 v 3.33 c -1.641,0.359 -3.17,1.008 -4.541,1.886 z M 30,21 c 4.971,0 9,4.029 9,9 0,4.971 -4.029,9 -9,9 -4.971,0 -9,-4.029 -9,-9 0,-4.971 4.029,-9 9,-9 z"
      id="path36"
    />
    <circle
      cx="30"
      cy="3"
      id="circle38"
      r="3" />
    <circle
      cx="30"
      cy="57"
      id="circle40"
      r="3" />
    <circle
      cx="57"
      cy="29"
      id="circle42" r="3" />
    <circle cx="3" cy="30" r="3" />
    <path
      d="m 18.367,5.897 0.734,2.203 5,-5 -7,-1 0.623,1.869 C 10.136,7.913 4.671,15.256 3.1,23.631 L 5.065,24 C 6.505,16.328 11.468,9.594 18.367,5.897 Z"
      id="path46" />
    <path
      d="m 4.1,35.999 -1,7 1.869,-0.623 C 8.913,49.964 16.256,55.429 24.631,57 L 25,55.035 C 17.328,53.595 10.594,48.632 6.897,41.733 L 9.1,40.999 Z"
      id="path48" />
    <path
      d="M 57.001,35.469 55.036,35.1 c -1.44,7.672 -6.403,14.406 -13.302,18.103 L 41,51 l -5,5 7,1 -0.623,-1.869 C 49.965,51.187 55.43,43.845 57.001,35.469 Z"
      id="path50" />
    <path
      d="m 53.204,17.367 -2.203,0.734 5,5 1,-7 -1.869,0.623 C 51.188,9.136 43.846,3.671 35.47,2.1 l -0.369,1.965 c 7.672,1.44 14.406,6.403 18.103,13.302 z"
      id="path52" />
  </svg>

  <svg id="icon-lock" style="enable-background:new 0 0 50 50;" version="1.1" viewBox="0 0 50 50" x="0px"
       xml:space="preserve" xmlns="http://www.w3.org/2000/svg" y="0px">
    <g>
      <path d="M40.3,21.1h-1.4v-8c0-7-5.7-12.7-12.7-12.7l-1.1,0c-0.1,0-0.2,0-0.3,0.1c-7,0.2-12.4,5.8-12.4,12.7v8h-1.4
      c-2.2,0-3.9,1.8-3.9,3.9v20.3c0,2.2,1.8,3.9,3.9,3.9h29.5c2.2,0,3.9-1.8,3.9-3.9V25C44.3,22.9,42.5,21.1,40.3,21.1z M26,3.5h-0.8
      c-5,0-9.1,4.1-9.1,9.1v8.5h-1.4v-8c0-5.7,4.7-10.4,10.4-10.4h1.1c5.7,0,10.4,4.7,10.4,10.4v8h-1.4v-8.5C35.1,7.6,31,3.5,26,3.5z
      M18.4,12.6c0-3.7,3-6.7,6.8-6.7H26c3.7,0,6.8,3,6.8,6.7v8.5H18.4V12.6z M9.3,25c0-0.9,0.7-1.6,1.6-1.6h29.5c0.9,0,1.6,0.7,1.6,1.6
      v20.3c0,0.9-0.7,1.6-1.6,1.6H10.9c-0.9,0-1.6-0.7-1.6-1.6V25z" />
      <path d="M23,39.7c0,1.5,1.2,2.6,2.6,2.6s2.6-1.2,2.6-2.6v-4.3c1-0.8,1.6-1.9,1.6-3.1c0-1.1-0.4-2.2-1.2-3c-0.8-0.8-1.8-1.2-3-1.2
      c-2.3,0-4.2,1.9-4.2,4.2c0,1.2,0.6,2.3,1.6,3.1V39.7z M26.5,33.9c-0.4,0.2-0.6,0.6-0.6,1v4.8c0,0.2-0.1,0.3-0.3,0.3
      c-0.2,0-0.3-0.1-0.3-0.3v-4.8c0-0.4-0.2-0.8-0.6-1c-0.6-0.3-0.9-0.9-0.9-1.6c0-0.5,0.2-1,0.5-1.3c0.3-0.3,0.8-0.5,1.3-0.5
      c1,0,1.8,0.8,1.8,1.8C27.4,32.9,27.1,33.5,26.5,33.9z" />
    </g>
  </svg>

  <svg id="icon-view" version="1.1" viewBox="15 0 65 81.25" xmlns="http://www.w3.org/2000/svg">
    <g>
      <path
        d="M67.7,31.6c-5.9-1.8-12.4-2.9-19.2-2.9c-7.7,0-15,1.3-21.5,3.6c-0.7,2.1-1.1,4.4-1.1,6.7c0,11.9,9.7,21.6,21.6,21.6    S69.1,51,69.1,39.1C69.1,36.4,68.6,33.9,67.7,31.6z M56.5,47.1c-3.3,0-6-2.7-6-6s2.7-6,6-6c3.3,0,6,2.7,6,6S59.9,47.1,56.5,47.1z" />
      <path
        d="M47.5,68.3C29.1,68.3,12.1,61,2,48.8l-1.1-1.3L2,46.2C12.1,34,29.1,26.7,47.5,26.7S82.9,34,93,46.2l1.1,1.3L93,48.8    C82.9,61,65.9,68.3,47.5,68.3z M6.1,47.5C15.6,58,30.9,64.3,47.5,64.3S79.4,58,88.9,47.5C79.4,37,64.1,30.7,47.5,30.7    S15.6,37,6.1,47.5z" />
    </g>
  </svg>

  <svg id="icon-download-pdf" style="enable-background:new 0 0 28 32;" viewBox="5 0 20 26" x="0px" y="0px">
    <g>
      <g>
        <g>
          <path
            d="M6.75,19.402H5.415v1.991H6.75c0.536,0,0.96-0.472,0.96-0.968C7.709,19.866,7.286,19.402,6.75,19.402z" />
          <path d="M12.123,19.442h-1.327v3.582h1.327C14.282,23.024,14.377,19.442,12.123,19.442z" />
          <path
            d="M23,15H1c-0.55,0-1,0.45-1,1v10c0,0.55,0.45,1,1,1h22c0.551,0,1-0.45,1-1V16C24,15.45,23.551,15,23,15z M6.75,22.393     H5.415v1.056C5.415,23.801,5.191,24,4.903,24c-0.264,0-0.543-0.199-0.543-0.552v-4.501c0-0.264,0.208-0.553,0.543-0.553H6.75     c1.04,0,1.976,0.696,1.976,2.031C8.725,21.689,7.79,22.393,6.75,22.393z M12.195,24h-1.903c-0.264,0-0.552-0.145-0.552-0.496     v-4.541c0-0.288,0.288-0.496,0.552-0.496h1.831C15.777,18.467,15.697,24,12.195,24z M19.32,19.506h-2.328v1.271h2.039     c0.289,0,0.576,0.288,0.576,0.568c0,0.264-0.287,0.479-0.576,0.479h-2.039v1.679c0,0.28-0.199,0.496-0.479,0.496     c-0.352,0-0.568-0.216-0.568-0.496v-4.541c0-0.288,0.217-0.496,0.568-0.496h2.807c0.352,0,0.559,0.208,0.559,0.496     C19.879,19.219,19.672,19.506,19.32,19.506z" />
        </g>
        <path
          d="M27,7.586L20.414,1C19.836,0.42,18.82,0,18,0H7C5.346,0,4,1.346,4,3v10c0,0.553,0.447,1,1,1s1-0.447,1-1V3    c0-0.551,0.448-1,1-1h9c1.105,0,2,0.896,2,2v3.75C18,8.988,19.014,10,20.25,10H24c1.105,0,2,0.896,2,2v17c0,0.551-0.447,1-1,1H7    c-0.552,0-1-0.449-1-1c0-0.553-0.447-1-1-1s-1,0.447-1,1c0,1.654,1.346,3,3,3h18c1.654,0,3-1.346,3-3V10    C28,9.181,27.58,8.165,27,7.586z" />
      </g>
    </g>
  </svg>

  <svg id="icon-print" viewBox="15 0 70 85" x="0px" y="0px">
    <g data-name="Group">
      <path
        d="M27.3,89.8H72.7V73.2h20V24.4h-21V10.2H28.3V24.4H7.3V73.2h20Zm8-8V58.9H64.7V81.8Zm1-63.7H63.7v6.2H36.3Zm-21,47V32.4H84.7V65.2h-12V58.9H79v-8H21v8h6.3v6.3Z"
        data-name="Compound Path" />
      <rect data-name="Path" height="8" width="9" x="72.4" y="34.7" />
      <rect data-name="Path" height="8" width="17.3" x="41.3" y="61.3" />
      <rect data-name="Path" height="8" width="23.3" x="38.3" y="71.3" />
    </g>
  </svg>

  <svg id="icon-split" viewBox="8 0 50 66" x="0px" y="0px">
    <g enable-background="new    ">
      <g>
        <g>
          <path
            d="M57,51c0-1.657-1.343-3-3-3c-0.885,0-1.673,0.391-2.223,1l-0.008-0.007L48,53.182V29l0,0V3c0-1.657-1.343-3-3-3     s-3,1.343-3,3v7.818l0,0V26l0,0v13l0,0v14.182l-3.77-4.188L38.223,49c-0.55-0.609-1.338-1-2.223-1c-1.657,0-3,1.343-3,3     c0,0.771,0.3,1.468,0.777,2l-0.008,0.007l9,10L42.777,63c0.55,0.609,1.338,1,2.223,1s1.672-0.391,2.223-1l0.008,0.007l9-10     L56.223,53C56.701,52.468,57,51.771,57,51z M30.23,10.993l-9-10L21.222,1C20.673,0.391,19.885,0,19,0s-1.673,0.391-2.222,1.001     L16.77,0.993l-9,10L7.778,11C7.3,11.532,7,12.228,7,13c0,1.657,1.343,3,3,3c0.885,0,1.673-0.391,2.222-1l0.008,0.007L16,10.818     V25l0,0v13l0,0v15.182l0,0V61c0,1.657,1.343,3,3,3s3-1.343,3-3V29l0,0V10.818l3.77,4.189L25.778,15c0.549,0.61,1.337,1,2.222,1     c1.657,0,3-1.343,3-3c0-0.772-0.299-1.468-0.778-2L30.23,10.993z" />
        </g>
      </g>
    </g>
  </svg>

  <svg enable-background="new 0 0 512 512" id="icon-merge" viewBox="50 0 384 480" x="0px" y="0px">
    <g>
      <path
        d="M484.6,230.6H27.4C13.4,230.6,2,242,2,256s11.4,25.4,25.4,25.4h457.2c14,0,25.4-11.4,25.4-25.4S498.6,230.6,484.6,230.6z" />
      <path
        d="M250.4,202.9c3.1,3.1,8.1,3.1,11.2,0l68.3-60.3c5-5,1.5-13.6-5.6-13.6h-42.9V27.4C281.4,13.4,270,2,256,2   c-14,0-25.4,11.4-25.4,25.4V129h-42.9c-7.1,0-10.6,8.6-5.6,13.6L250.4,202.9z" />
      <path
        d="M261.6,309.1c-3.1-3.1-8.1-3.1-11.2,0l-68.3,60.3c-5,5-1.5,13.6,5.6,13.6h42.9v101.6c0,14,11.4,25.4,25.4,25.4   c14,0,25.4-11.4,25.4-25.4V383h42.9c7.1,0,10.6-8.6,5.6-13.6L261.6,309.1z" />
    </g>
  </svg>

  <svg id="icon-warning" viewBox="0 0 100 125" x="0px" y="0px">
    <path
      d="M93.61,74.95,76.25,44.87,58.88,14.8a10.26,10.26,0,0,0-17.76,0L23.75,44.87,6.39,74.95a10.26,10.26,0,0,0,8.88,15.38H84.73A10.26,10.26,0,0,0,93.61,74.95ZM89.28,82.7a5.16,5.16,0,0,1-4.55,2.63H15.27a5.26,5.26,0,0,1-4.55-7.88L28.08,47.37,45.45,17.3a5.26,5.26,0,0,1,9.1,0L71.92,47.37,89.28,77.45A5.16,5.16,0,0,1,89.28,82.7Z" />
    <rect height="6.52" width="6.52" x="46.74" y="69.48" />
    <polygon points="44.34 42.93 46.78 66.06 53.22 66.06 55.66 42.93 55.66 38.07 44.34 38.07 44.34 42.93" />
  </svg>

  <svg
    id="ADJUDANT_INCSASAP"
    version="1.1"
    viewBox="0 0 141.72999 141.73"
    xmlns="http://www.w3.org/2000/svg">
    <defs
      id="defs434">
      <style
        id="style432">.cls-3 {
        fill: #dadada;
      }

      .cls-4 {
        fill: #fbd12c;
      }</style>
    </defs>
    <polygon
      class="cls-2"
      id="polygon458"
      points="141.73,323.86 141.73,182.13 0,182.13 0,323.86 "
      style="fill:#1d1d1b"
      transform="translate(0,-182.13)" />
    <polygon
      class="cls-3"
      id="polygon466"
      points="113.39,306.85 113.39,289.84 28.35,289.84 28.35,306.85 "
      style="fill:#dadada"
      transform="translate(0,-182.13)" />
    <polygon
      class="cls-3"
      id="polygon474"
      points="113.39,281.34 113.39,264.33 28.35,264.33 28.35,281.34 "
      style="fill:#dadada"
      transform="translate(0,-182.13)" />
    <polygon
      class="cls-4"
      id="polygon480"
      points="56.64,220.08 66.39,218.58 70.87,209.76 75.34,218.58 85.09,220.08 78.1,227.04 79.66,236.7 70.87,232.22 62.07,236.7 63.63,227.04 "
      style="fill:#fbd12c"
      transform="translate(0,-182.13)" />
  </svg>

  <svg
    id="ADJUDANT_GIS"
    version="1.1"
    viewBox="0 0 141.72999 141.73"
    xmlns="http://www.w3.org/2000/svg">
    <defs
      id="defs434">
      <style
        id="style432">.cls-1 {
        font-size: 12px;
        font-family: ClearSans;
      }

      .cls-1, .cls-2 {
        fill: #235f29;
      }

      .cls-3 {
        fill: #dadada;
      }

      .cls-4 {
        fill: #fbd12c;
      }</style>
    </defs>
    <polygon
      class="cls-2"
      id="polygon458"
      points="141.73,323.86 141.73,182.13 0,182.13 0,323.86 "
      style="fill:#235f29"
      transform="translate(0,-182.13)" />
    <polygon
      class="cls-3"
      id="polygon466"
      points="113.39,306.85 113.39,289.84 28.35,289.84 28.35,306.85 "
      style="fill:#dadada"
      transform="translate(0,-182.13)" />
    <polygon
      class="cls-3"
      id="polygon474"
      points="113.39,281.34 113.39,264.33 28.35,264.33 28.35,281.34 "
      style="fill:#dadada"
      transform="translate(0,-182.13)" />
    <polygon
      class="cls-4"
      id="polygon480"
      points="56.64,220.08 66.39,218.58 70.87,209.76 75.34,218.58 85.09,220.08 78.1,227.04 79.66,236.7 70.87,232.22 62.07,236.7 63.63,227.04 "
      style="fill:#fbd12c"
      transform="translate(0,-182.13)" />
  </svg>

  <svg
    id="ADJUDANTCH_GIS"
    version="1.1"
    viewBox="0 0 141.72999 141.73"
    xmlns="http://www.w3.org/2000/svg"
  >
    <defs
      id="defs434">
      <style
        id="style432">.cls-1 {
        font-size: 12px;
        font-family: ClearSans;
      }

      .cls-1, .cls-2 {
        fill: #235f29;
      }

      .cls-3 {
        fill: #dadada;
      }

      .cls-4 {
        fill: #fbd12c;
      }</style>
    </defs>
    <polygon
      class="cls-2"
      id="polygon462"
      points="141.73,505.98 141.73,364.25 0,364.25 0,505.98 "
      style="fill:#235f29;"
      transform="translate(0,-364.25)" />
    <polygon
      class="cls-3"
      id="polygon470"
      points="113.39,488.98 113.39,471.97 28.35,471.97 28.35,488.98 "
      style="fill:#dadada"
      transform="translate(0,-364.25)" />
    <polygon
      class="cls-3"
      id="polygon476"
      points="113.39,463.46 113.39,446.46 28.35,446.46 28.35,463.46 "
      style="fill:#dadada"
      transform="translate(0,-364.25)" />
    <polygon
      class="cls-4"
      id="polygon482"
      points="28.35,402.21 38.1,400.7 42.58,391.88 47.05,400.7 56.8,402.21 49.81,409.16 51.37,418.82 42.58,414.34 33.78,418.82 35.34,409.16 "
      style="fill:#fbd12c"
      transform="translate(0,-364.25)" />
    <polygon
      class="cls-4"
      id="polygon484"
      points="84.93,402.21 94.68,400.7 99.16,391.88 103.63,400.7 113.39,402.21 106.39,409.16 107.95,418.82 99.16,414.34 90.36,418.82 91.92,409.16 "
      style="fill:#fbd12c"
      transform="translate(0,-364.25)" />
  </svg>


  <svg
    id="ADJUDANTASP_INCSASAP"
    version="1.1"
    viewBox="0 0 141.72999 141.73"
    xmlns="http://www.w3.org/2000/svg">
    <defs
      id="defs434">
      <style
        id="style432">.cls-1 {
        font-size: 12px;
        font-family: ClearSans;
      }

      .cls-1, .cls-2 {
        fill: #1d1d1b;
      }

      .cls-3 {
        fill: #dadada;
      }

      .cls-4 {
        fill: #fbd12c;
      }</style>
    </defs>
    <polygon
      class="cls-2"
      id="polygon460"
      points="141.73,141.73 141.73,0 0,0 0,141.73 "
      style="fill:#1d1d1b" />
    <polygon
      class="cls-3"
      id="polygon468"
      points="113.39,124.72 113.39,107.72 28.35,107.72 28.35,124.72 "
      style="fill:#dadada" />
  </svg>

  <svg
    id="ADJUDANTCH_INCSASAP"
    version="1.1"
    viewBox="0 0 141.72999 141.73"
    xmlns="http://www.w3.org/2000/svg"
  >
    <defs
      id="defs434">
      <style
        id="style432">.cls-1 {
        font-size: 12px;
        font-family: ClearSans;
      }

      .cls-1, .cls-2 {
        fill: #1d1d1b;
      }

      .cls-3 {
        fill: #dadada;
      }

      .cls-4 {
        fill: #fbd12c;
      }</style>
    </defs>
    <polygon
      class="cls-2"
      id="polygon462"
      points="141.73,505.98 141.73,364.25 0,364.25 0,505.98 "
      style="fill:#1d1d1b"
      transform="translate(0,-364.25)" />
    <polygon
      class="cls-3"
      id="polygon470"
      points="113.39,488.98 113.39,471.97 28.35,471.97 28.35,488.98 "
      style="fill:#dadada"
      transform="translate(0,-364.25)" />
    <polygon
      class="cls-3"
      id="polygon476"
      points="113.39,463.46 113.39,446.46 28.35,446.46 28.35,463.46 "
      style="fill:#dadada"
      transform="translate(0,-364.25)" />
    <polygon
      class="cls-4"
      id="polygon482"
      points="28.35,402.21 38.1,400.7 42.58,391.88 47.05,400.7 56.8,402.21 49.81,409.16 51.37,418.82 42.58,414.34 33.78,418.82 35.34,409.16 "
      style="fill:#fbd12c"
      transform="translate(0,-364.25)" />
    <polygon
      class="cls-4"
      id="polygon484"
      points="84.93,402.21 94.68,400.7 99.16,391.88 103.63,400.7 113.39,402.21 106.39,409.16 107.95,418.82 99.16,414.34 90.36,418.82 91.92,409.16 "
      style="fill:#fbd12c"
      transform="translate(0,-364.25)" />
  </svg>

  <svg
    id="ADJUDANTMAJ_INCSASAP"
    version="1.1"
    viewBox="0 0 141.72999 141.73"
    xmlns="http://www.w3.org/2000/svg"
  >
    <defs
      id="defs434">
      <style
        id="style432">.cls-1 {
        font-size: 12px;
        font-family: ClearSans;
      }

      .cls-1, .cls-2 {
        fill: #1d1d1b;
      }

      .cls-3 {
        fill: #dadada;
      }

      .cls-4 {
        fill: #fbd12c;
      }</style>
    </defs>
    <polygon
      class="cls-2"
      id="polygon464"
      points="141.73,688.11 141.73,546.38 0,546.38 0,688.11 "
      style="fill:#1d1d1b"
      transform="translate(0,-546.38)" />
    <polygon
      class="cls-3"
      id="polygon472"
      points="113.39,671.1 113.39,654.09 28.35,654.09 28.35,671.1 "
      style="fill:#dadada"
      transform="translate(0,-546.38)" />
    <polygon
      class="cls-3"
      id="polygon478"
      points="113.39,645.59 113.39,628.58 28.35,628.58 28.35,645.59 "
      style="fill:#dadada"
      transform="translate(0,-546.38)" />
    <polygon
      class="cls-4"
      id="polygon486"
      points="84.93,597.8 94.68,596.3 99.16,587.48 103.63,596.3 113.39,597.8 106.39,604.76 107.95,614.42 99.16,609.94 90.36,614.42 91.92,604.76 "
      style="fill:#fbd12c"
      transform="translate(0,-546.38)" />
    <polygon
      class="cls-4"
      id="polygon488"
      points="28.35,597.8 38.1,596.3 42.58,587.48 47.05,596.3 56.8,597.8 49.81,604.76 51.37,614.42 42.58,609.94 33.78,614.42 35.34,604.76 "
      style="fill:#fbd12c"
      transform="translate(0,-546.38)" />
    <polygon
      class="cls-4"
      id="polygon490"
      points="56.47,570.86 66.22,569.36 70.7,560.54 75.18,569.36 84.93,570.86 77.94,577.82 79.5,587.48 70.7,583 61.91,587.48 63.46,577.82 "
      style="fill:#fbd12c"
      transform="translate(0,-546.38)" />
  </svg>

  <svg
    id="ADJUDANTMAJ_GIS"
    version="1.1"
    viewBox="0 0 141.72999 141.73"
    xmlns="http://www.w3.org/2000/svg"
  >
    <defs
      id="defs434">
      <style
        id="style432">.cls-1 {
        font-size: 12px;
        font-family: ClearSans;
      }

      .cls-1, .cls-2 {
        fill: #235f29;
      }

      .cls-3 {
        fill: #dadada;
      }

      .cls-4 {
        fill: #fbd12c;
      }</style>
    </defs>
    <polygon
      class="cls-2"
      id="polygon464"
      points="141.73,688.11 141.73,546.38 0,546.38 0,688.11 "
      style="fill:#235f29"
      transform="translate(0,-546.38)" />
    <polygon
      class="cls-3"
      id="polygon472"
      points="113.39,671.1 113.39,654.09 28.35,654.09 28.35,671.1 "
      style="fill:#dadada"
      transform="translate(0,-546.38)" />
    <polygon
      class="cls-3"
      id="polygon478"
      points="113.39,645.59 113.39,628.58 28.35,628.58 28.35,645.59 "
      style="fill:#dadada"
      transform="translate(0,-546.38)" />
    <polygon
      class="cls-4"
      id="polygon486"
      points="84.93,597.8 94.68,596.3 99.16,587.48 103.63,596.3 113.39,597.8 106.39,604.76 107.95,614.42 99.16,609.94 90.36,614.42 91.92,604.76 "
      style="fill:#fbd12c"
      transform="translate(0,-546.38)" />
    <polygon
      class="cls-4"
      id="polygon488"
      points="28.35,597.8 38.1,596.3 42.58,587.48 47.05,596.3 56.8,597.8 49.81,604.76 51.37,614.42 42.58,609.94 33.78,614.42 35.34,604.76 "
      style="fill:#fbd12c"
      transform="translate(0,-546.38)" />
    <polygon
      class="cls-4"
      id="polygon490"
      points="56.47,570.86 66.22,569.36 70.7,560.54 75.18,569.36 84.93,570.86 77.94,577.82 79.5,587.48 70.7,583 61.91,587.48 63.46,577.82 "
      style="fill:#fbd12c"
      transform="translate(0,-546.38)" />
  </svg>

  <svg
    id="EXPERT_GIS"
    version="1.1"
    viewBox="0 0 142 142"
    xmlns="http://www.w3.org/2000/svg"
  >

    <defs
      id="defs29">
      <style
        id="style27">.cls-1 {
        font-size: 12px;
      }

      .cls-1, .cls-2, .cls-3 {
        fill: #1d1d1b;
      }

      .cls-1, .cls-2 {
        font-family: ClearSans;
      }

      .cls-2 {
        font-size: 15px;
      }

      .cls-4 {
        fill: #a02c36;
      }

      .cls-5 {
        fill: none;
        stroke: #dadada;
        stroke-width: 4.48px;
      }

      .cls-6 {
        fill: #dadada;
      }</style>
    </defs>
    <polygon
      class="cls-3"
      id="polygon37"
      points="0,218.14 141.73,218.14 141.73,76.41 0,76.41 "
      style="fill:#1d1d1b;"
      transform="translate(0,-76.41)" />
    <path
      class="cls-5"
      d="m 70.87,122.48 a 51.71,51.71 0 1 0 -51.71,-51.7 51.7,51.7 0 0 0 51.71,51.7 z"
      id="path41"
      style="fill:none;stroke:#dadada;stroke-width:4.48px;"
    />
    <polygon
      class="cls-6"
      id="polygon43"
      points="112.72,174.34 70.87,97.55 29.07,174.34 "
      transform="translate(0,-76.41)"
    />
  </svg>


  <svg
    id="EXPERT_DMS"
    version="1.1"
    viewBox="0 0 142 142"
    xmlns="http://www.w3.org/2000/svg"
  >

    <defs
      id="defs29">
      <style
        id="style27">.cls-1 {
        font-size: 12px;
      }

      .cls-1, .cls-2, .cls-3 {
        fill: #1d1d1b;
      }

      .cls-1, .cls-2 {
        font-family: ClearSans;
      }

      .cls-2 {
        font-size: 15px;
      }

      .cls-4 {
        fill: #a02c36;
      }

      .cls-5 {
        fill: none;
        stroke: #dadada;
        stroke-width: 4.48px;
      }

      .cls-6 {
        fill: #dadada;
      }</style>
    </defs>
    <polygon
      class="cls-3"
      id="polygon37"
      points="0,218.14 141.73,218.14 141.73,76.41 0,76.41 "
      style="fill:#a02c36;"
      transform="translate(0,-76.41)" />
    <path
      class="cls-5"
      d="m 70.87,122.48 a 51.71,51.71 0 1 0 -51.71,-51.7 51.7,51.7 0 0 0 51.71,51.7 z"
      id="path41"
      style="fill:none;stroke:#dadada;stroke-width:4.48px;"
    />
    <polygon
      class="cls-6"
      id="polygon43"
      points="112.72,174.34 70.87,97.55 29.07,174.34 "
      transform="translate(0,-76.41)"
    />
  </svg>

  <svg
    id="BRIGADIER_INCSASAP"
    version="1.1"
    viewBox="0 0 141.73 141.73"
    xmlns="http://www.w3.org/2000/svg">
    <defs
      id="defs87">
      <style
        id="style85">.cls-1 {
        font-size: 12px;
        font-family: ClearSans;
      }

      .cls-1, .cls-2 {
        fill: #1d1d1b;
      }

      .cls-3 {
        fill: #e30613;
      }

      .cls-4 {
        fill: #dadada;
      }</style>
    </defs>
    <polygon
      class="cls-2"
      id="polygon91"
      points="0,323.86 141.73,323.86 141.73,182.13 0,182.13 "
      style="fill:#1d1d1b"
      transform="translate(0,-182.13)" />
    <polygon
      class="cls-3"
      id="polygon95"
      points="28.35,306.85 113.39,306.85 113.39,289.84 28.35,289.84 "
      style="fill:#e30613"
      transform="translate(0,-182.13)" />
    <polygon
      class="cls-4"
      id="polygon103"
      points="75.34,231.34 85.09,232.84 78.1,239.79 79.66,249.46 70.87,244.97 62.07,249.46 63.63,239.79 56.64,232.84 66.39,231.34 70.87,222.52 "
      style="fill:#dadada"
      transform="translate(0,-182.13)" />
  </svg>

  <svg
    id="BRIGADIER_GIS"
    version="1.1"
    viewBox="0 0 141.73 141.73"
    xmlns="http://www.w3.org/2000/svg">
    <defs
      id="defs87">
      <style
        id="style85">.cls-1 {
        font-size: 12px;
        font-family: ClearSans;
      }

      .cls-1, .cls-2 {
        fill: #235f29;
      }

      .cls-3 {
        fill: #e30613;
      }

      .cls-4 {
        fill: #dadada;
      }</style>
    </defs>
    <polygon
      class="cls-2"
      id="polygon91"
      points="0,323.86 141.73,323.86 141.73,182.13 0,182.13 "
      style="fill:#235f29"
      transform="translate(0,-182.13)" />
    <polygon
      class="cls-3"
      id="polygon95"
      points="28.35,306.85 113.39,306.85 113.39,289.84 28.35,289.84 "
      style="fill:#e30613"
      transform="translate(0,-182.13)" />
    <polygon
      class="cls-4"
      id="polygon103"
      points="75.34,231.34 85.09,232.84 78.1,239.79 79.66,249.46 70.87,244.97 62.07,249.46 63.63,239.79 56.64,232.84 66.39,231.34 70.87,222.52 "
      style="fill:#dadada"
      transform="translate(0,-182.13)" />
  </svg>

  <svg
    id="BRIGADIERASP_GIS"
    version="1.1"
    viewBox="0 0 150 150"
    xmlns="http://www.w3.org/2000/svg"
  >

    <defs
      id="defs38">
      <style
        id="style36">.cls-1 {
        font-size: 12px;
        font-family: ClearSans;
      }

      .cls-1, .cls-2 {
        fill: #235f29;
      }

      .cls-3 {
        fill: #e30613;
      }

      .cls-4 {
        fill: #dadada;
      }</style>
    </defs>
    <polygon
      class="cls-2"
      id="polygon43"
      points="0 0 0 141.73 141.73 141.73 141.73 0 0 0 0 0"
      style="fill:#235f29;" />
    <polygon
      class="cls-3"
      id="polygon47"
      points="28.35 107.72 28.35 124.72 113.39 124.72 113.39 107.72 28.35 107.72 28.35 107.72"
      style="fill:#e30613" />
  </svg>

  <svg
    id="BRIGADIERASP_INCSASAP"
    version="1.1"
    viewBox="0 0 150 150"
    xmlns="http://www.w3.org/2000/svg"
  >

    <defs
      id="defs37">
      <style
        id="style35">.cls-1 {
        font-size: 12px;
        font-family: ClearSans;
      }

      .cls-1, .cls-2 {
        fill: #1d1d1b;
      }

      .cls-3 {
        fill: #e30613;
      }

      .cls-4 {
        fill: #dadada;
      }</style>
    </defs>
    <polygon
      class="cls-2"
      id="polygon43"
      points="0 0 0 141.73 141.73 141.73 141.73 0 0 0 0 0"
      style="fill:#1d1d1b" />
    <polygon
      class="cls-3"
      id="polygon47"
      points="28.35 107.72 28.35 124.72 113.39 124.72 113.39 107.72 28.35 107.72 28.35 107.72"
      style="fill:#e30613" />
  </svg>

  <svg
    data-name="Ebene 1"
    id="CAPITAINE_INCSASAP"
    version="1.1"
    viewBox="0 0 141.73 141.73"
    xmlns="http://www.w3.org/2000/svg">
    <defs
      id="defs253">
      <style
        id="style251">.cls-1 {
        font-size: 12px;
        font-family: ClearSans;
      }

      .cls-1, .cls-2 {
        fill: #1d1d1b;
      }

      .cls-3 {
        fill: #fbd12c;
      }</style>
    </defs>
    <polygon
      class="cls-2"
      id="polygon257"
      points="141.73,141.73 141.73,0 0,0 0,141.73 "
      style="fill:#1d1d1b" />
    <polygon
      class="cls-3"
      id="polygon263"
      points="113.39,124.72 113.39,107.72 28.35,107.72 28.35,124.72 "
      style="fill:#fbd12c" />
    <polygon
      class="cls-3"
      id="polygon269"
      points="113.39,99.21 113.39,82.2 28.35,82.2 28.35,99.21 "
      style="fill:#fbd12c" />
    <polygon
      class="cls-3"
      id="polygon275"
      points="56.64,37.95 66.39,36.45 70.87,27.63 75.34,36.45 85.09,37.95 78.1,44.91 79.66,54.57 70.87,50.09 62.07,54.57 63.63,44.91 "
      style="fill:#fbd12c" />
  </svg>

  <svg
    id="CAPITAINE_GIS"
    version="1.1"
    viewBox="0 0 141.73 141.73"
    xmlns="http://www.w3.org/2000/svg">
    <defs
      id="defs253">
      <style
        id="style251">.cls-1 {
        font-size: 12px;
        font-family: ClearSans;
      }

      .cls-1, .cls-2 {
        fill: #235f29;
      }

      .cls-3 {
        fill: #fbd12c;
      }</style>
    </defs>
    <polygon
      class="cls-2"
      id="polygon257"
      points="141.73,141.73 141.73,0 0,0 0,141.73 "
      style="fill:#235f29" />
    <polygon
      class="cls-3"
      id="polygon263"
      points="113.39,124.72 113.39,107.72 28.35,107.72 28.35,124.72 "
      style="fill:#fbd12c" />
    <polygon
      class="cls-3"
      id="polygon269"
      points="113.39,99.21 113.39,82.2 28.35,82.2 28.35,99.21 "
      style="fill:#fbd12c" />
    <polygon
      class="cls-3"
      id="polygon275"
      points="56.64,37.95 66.39,36.45 70.87,27.63 75.34,36.45 85.09,37.95 78.1,44.91 79.66,54.57 70.87,50.09 62.07,54.57 63.63,44.91 "
      style="fill:#fbd12c" />
  </svg>

  <svg
    id="CAPORAL_INCSASAP"
    version="1.1"
    viewBox="0 0 141.73 141.73"
    xmlns="http://www.w3.org/2000/svg"
  >
    <defs
      id="defs161">
      <style
        id="style159">.cls-1 {
        font-size: 12px;
        font-family: ClearSans;
      }

      .cls-1, .cls-2 {
        fill: #1d1d1b;
      }

      .cls-3 {
        fill: #e30613;
      }

      .cls-4 {
        fill: #dadada;
      }</style>
    </defs>
    <polygon
      class="cls-2"
      id="polygon181"
      points="141.73,141.73 141.73,0 0,0 0,141.73 "
      style="fill:#1d1d1b" />
    <polygon
      class="cls-3"
      id="polygon185"
      points="113.39,124.73 113.39,107.72 28.35,107.72 28.35,124.73 "
      style="fill:#e30613" />
    <polygon
      class="cls-4"
      id="polygon193"
      points="84.93,50.71 94.68,49.21 99.16,40.39 103.63,49.21 113.39,50.71 106.39,57.67 107.95,67.33 99.16,62.85 90.36,67.33 91.92,57.67 "
      style="fill:#dadada" />
    <polygon
      class="cls-4"
      id="polygon195"
      points="28.35,50.71 38.1,49.21 42.58,40.39 47.05,49.21 56.8,50.71 49.81,57.67 51.37,67.33 42.58,62.85 33.78,67.33 35.34,57.67 "
      style="fill:#dadada" />
  </svg>

  <svg
    id="CAPORAL_GIS"
    version="1.1"
    viewBox="0 0 141.73 141.73"
    xmlns="http://www.w3.org/2000/svg"
  >
    <defs
      id="defs161">
      <style
        id="style159">.cls-1 {
        font-size: 12px;
        font-family: ClearSans;
      }

      .cls-1, .cls-2 {
        fill: #235f29;
      }

      .cls-3 {
        fill: #e30613;
      }

      .cls-4 {
        fill: #dadada;
      }</style>
    </defs>
    <polygon
      class="cls-2"
      id="polygon181"
      points="141.73,141.73 141.73,0 0,0 0,141.73 "
      style="fill:#235f29" />
    <polygon
      class="cls-3"
      id="polygon185"
      points="113.39,124.73 113.39,107.72 28.35,107.72 28.35,124.73 "
      style="fill:#e30613" />
    <polygon
      class="cls-4"
      id="polygon193"
      points="84.93,50.71 94.68,49.21 99.16,40.39 103.63,49.21 113.39,50.71 106.39,57.67 107.95,67.33 99.16,62.85 90.36,67.33 91.92,57.67 "
      style="fill:#dadada" />
    <polygon
      class="cls-4"
      id="polygon195"
      points="28.35,50.71 38.1,49.21 42.58,40.39 47.05,49.21 56.8,50.71 49.81,57.67 51.37,67.33 42.58,62.85 33.78,67.33 35.34,57.67 "
      style="fill:#dadada" />
  </svg>

  <svg
    id="CAPORALCH_INCSASAP"
    version="1.1"
    viewBox="0 0 141.73 141.73"
    xmlns="http://www.w3.org/2000/svg"
  >
    <defs
      id="defs161">
      <style
        id="style159">.cls-1 {
        font-size: 12px;
        font-family: ClearSans;
      }

      .cls-1, .cls-2 {
        fill: #1d1d1b;
      }

      .cls-3 {
        fill: #e30613;
      }

      .cls-4 {
        fill: #dadada;
      }</style>
    </defs>
    <polygon
      class="cls-2"
      id="polygon183"
      points="141.73,323.86 141.73,182.13 0,182.13 0,323.86 "
      style="fill:#1d1d1b"
      transform="translate(0,-182.13)" />
    <polygon
      class="cls-3"
      id="polygon187"
      points="113.39,306.85 113.39,289.84 28.35,289.84 28.35,306.85 "
      style="fill:#e30613"
      transform="translate(0,-182.13)" />
    <polygon
      class="cls-4"
      id="polygon197"
      points="84.93,246.31 94.68,244.8 99.16,235.98 103.63,244.8 113.39,246.31 106.39,253.26 107.95,262.92 99.16,258.44 90.36,262.92 91.92,253.26 "
      style="fill:#dadada"
      transform="translate(0,-182.13)" />
    <polygon
      class="cls-4"
      id="polygon199"
      points="28.35,246.31 38.1,244.8 42.58,235.98 47.05,244.8 56.8,246.31 49.81,253.26 51.37,262.92 42.58,258.44 33.78,262.92 35.34,253.26 "
      style="fill:#dadada"
      transform="translate(0,-182.13)" />
    <polygon
      class="cls-4"
      id="polygon201"
      points="56.47,219.37 66.22,217.87 70.7,209.04 75.18,217.87 84.93,219.37 77.94,226.32 79.5,235.98 70.7,231.5 61.91,235.98 63.46,226.32 "
      style="fill:#dadada"
      transform="translate(0,-182.13)" />
  </svg>

  <svg
    id="CAPORALCH_GIS"
    version="1.1"
    viewBox="0 0 141.73 141.73"
    xmlns="http://www.w3.org/2000/svg"
  >
    <defs
      id="defs161">
      <style
        id="style159">.cls-1 {
        font-size: 12px;
        font-family: ClearSans;
      }

      .cls-1, .cls-2 {
        fill: #235f29;
      }

      .cls-3 {
        fill: #e30613;
      }

      .cls-4 {
        fill: #dadada;
      }</style>
    </defs>
    <polygon
      class="cls-2"
      id="polygon183"
      points="141.73,323.86 141.73,182.13 0,182.13 0,323.86 "
      style="fill:#235f29"
      transform="translate(0,-182.13)" />
    <polygon
      class="cls-3"
      id="polygon187"
      points="113.39,306.85 113.39,289.84 28.35,289.84 28.35,306.85 "
      style="fill:#e30613"
      transform="translate(0,-182.13)" />
    <polygon
      class="cls-4"
      id="polygon197"
      points="84.93,246.31 94.68,244.8 99.16,235.98 103.63,244.8 113.39,246.31 106.39,253.26 107.95,262.92 99.16,258.44 90.36,262.92 91.92,253.26 "
      style="fill:#dadada"
      transform="translate(0,-182.13)" />
    <polygon
      class="cls-4"
      id="polygon199"
      points="28.35,246.31 38.1,244.8 42.58,235.98 47.05,244.8 56.8,246.31 49.81,253.26 51.37,262.92 42.58,258.44 33.78,262.92 35.34,253.26 "
      style="fill:#dadada"
      transform="translate(0,-182.13)" />
    <polygon
      class="cls-4"
      id="polygon201"
      points="56.47,219.37 66.22,217.87 70.7,209.04 75.18,217.87 84.93,219.37 77.94,226.32 79.5,235.98 70.7,231.5 61.91,235.98 63.46,226.32 "
      style="fill:#dadada"
      transform="translate(0,-182.13)" />
  </svg>

  <svg
    id="CAPORALCH 1CL_INCSASAP"
    version="1.1"
    viewBox="0 0 141.73 141.73"
    xmlns="http://www.w3.org/2000/svg"
  >
    <defs
      id="defs161">
      <style
        id="style159">.cls-1 {
        font-size: 12px;
        font-family: ClearSans;
      }

      .cls-1, .cls-2 {
        fill: #1d1d1b;
      }

      .cls-3 {
        fill: #e30613;
      }

      .cls-4 {
        fill: #dadada;
      }</style>
    </defs>
    <polygon
      class="cls-2"
      id="polygon165"
      points="141.73,505.98 141.73,364.25 0,364.25 0,505.98 "
      style="fill:#1d1d1b"
      transform="translate(0,-364.25)" />
    <polygon
      class="cls-3"
      id="polygon167"
      points="113.39,488.98 113.39,471.97 28.35,471.97 28.35,488.98 "
      style="fill:#e30613"
      transform="translate(0,-364.25)" />
    <polygon
      class="cls-4"
      id="polygon173"
      points="84.93,428.43 94.68,426.93 99.16,418.11 103.63,426.93 113.39,428.43 106.39,435.39 107.95,445.05 99.16,440.57 90.36,445.05 91.92,435.39 "
      style="fill:#dadada"
      transform="translate(0,-364.25)" />
    <polygon
      class="cls-4"
      id="polygon175"
      points="28.35,428.43 38.1,426.93 42.58,418.11 47.05,426.93 56.8,428.43 49.81,435.39 51.37,445.05 42.58,440.57 33.78,445.05 35.34,435.39 "
      style="fill:#dadada"
      transform="translate(0,-364.25)" />
    <polygon
      class="cls-4"
      id="polygon177"
      points="56.47,401.49 66.22,399.99 70.7,391.17 75.18,399.99 84.93,401.49 77.94,408.45 79.5,418.11 70.7,413.63 61.91,418.11 63.46,408.45 "
      style="fill:#dadada"
      transform="translate(0,-364.25)" />
    <polygon
      class="cls-4"
      id="polygon179"
      points="113.39,482.63 113.39,478.31 28.35,478.31 28.35,482.63 "
      style="fill:#dadada"
      transform="translate(0,-364.25)" />
  </svg>

  <svg
    id="CAPORALCH 1CL_GIS"
    version="1.1"
    viewBox="0 0 141.73 141.73"
    xmlns="http://www.w3.org/2000/svg"
  >
    <defs
      id="defs161">
      <style
        id="style159">.cls-1 {
        font-size: 12px;
        font-family: ClearSans;
      }

      .cls-1, .cls-2 {
        fill: #235f29;
      }

      .cls-3 {
        fill: #e30613;
      }

      .cls-4 {
        fill: #dadada;
      }</style>
    </defs>
    <polygon
      class="cls-2"
      id="polygon165"
      points="141.73,505.98 141.73,364.25 0,364.25 0,505.98 "
      style="fill:#235f29"
      transform="translate(0,-364.25)" />
    <polygon
      class="cls-3"
      id="polygon167"
      points="113.39,488.98 113.39,471.97 28.35,471.97 28.35,488.98 "
      style="fill:#e30613"
      transform="translate(0,-364.25)" />
    <polygon
      class="cls-4"
      id="polygon173"
      points="84.93,428.43 94.68,426.93 99.16,418.11 103.63,426.93 113.39,428.43 106.39,435.39 107.95,445.05 99.16,440.57 90.36,445.05 91.92,435.39 "
      style="fill:#dadada"
      transform="translate(0,-364.25)" />
    <polygon
      class="cls-4"
      id="polygon175"
      points="28.35,428.43 38.1,426.93 42.58,418.11 47.05,426.93 56.8,428.43 49.81,435.39 51.37,445.05 42.58,440.57 33.78,445.05 35.34,435.39 "
      style="fill:#dadada"
      transform="translate(0,-364.25)" />
    <polygon
      class="cls-4"
      id="polygon177"
      points="56.47,401.49 66.22,399.99 70.7,391.17 75.18,399.99 84.93,401.49 77.94,408.45 79.5,418.11 70.7,413.63 61.91,418.11 63.46,408.45 "
      style="fill:#dadada"
      transform="translate(0,-364.25)" />
    <polygon
      class="cls-4"
      id="polygon179"
      points="113.39,482.63 113.39,478.31 28.35,478.31 28.35,482.63 "
      style="fill:#dadada"
      transform="translate(0,-364.25)" />
  </svg>

  <svg
    id="COLONEL_INCSASAP"
    version="1.1"
    viewBox="0 0 141.72999 141.72999"
    xmlns="http://www.w3.org/2000/svg">
    <defs
      id="defs594">
      <style
        id="style592">.cls-1 {
        font-size: 12px;
        font-family: ClearSans;
      }

      .cls-1, .cls-2 {
        fill: #1d1d1b;
      }

      .cls-3 {
        fill: #fbd12c;
      }

      .cls-4 {
        fill: #f9d12c;
      }</style>
    </defs>
    <polygon
      class="cls-2"
      id="polygon606"
      points="141.73,141.73 141.73,0 0,0 0,141.73 "
      style="fill:#1d1d1b" />
    <polygon
      class="cls-3"
      id="polygon610"
      points="113.39,124.72 113.39,107.72 28.35,107.72 28.35,124.72 "
      style="fill:#fbd12c" />
    <polygon
      class="cls-3"
      id="polygon614"
      points="113.39,99.21 113.39,82.2 28.35,82.2 28.35,99.21 "
      style="fill:#fbd12c" />
    <polygon
      class="cls-3"
      id="polygon618"
      points="56.64,39.37 66.39,37.87 70.87,29.05 75.34,37.87 85.09,39.37 78.1,46.33 79.66,55.99 70.87,51.51 62.07,55.99 63.63,46.33 "
      style="fill:#fbd12c" />
    <path
      class="cls-4"
      d="m 48.52,76.58 c 0,0 -17.9,-3.62 -17.9,-16.67 0,-6.26 4.66,-0.94 4.66,-0.94 0,0 -8.19,-11.6 -6.77,-20.17 1.53,-9.21 5.46,0 5.46,0 0,0 -3.95,-21.44 2.18,-24.25 3.57,-1.63 2,8 2,8 0,0 2.39,-12.19 8.87,-14.27 3.27,-1 -3.16,17.65 -3.16,17.65 0,0 3,-1.94 3.28,2.73 0.28,4.67 -8.08,12.66 -8.08,12.66 0,0 5.32,-5 5.46,2.56 0.14,7.56 -3.17,11.34 -3.17,11.34 0,0 3.88,-5.89 5,0.63 1.12,6.52 -0.13,9.38 0.93,12.78 1.16,3.68 1.64,3.82 3.89,5.33 2.25,1.51 -2.62,2.57 -2.62,2.57 z"
      id="path624"
      style="fill:#f9d12c" />
    <path
      class="cls-4"
      d="m 93.21,76.58 c 0,0 17.9,-3.62 17.9,-16.67 0,-6.26 -4.66,-0.94 -4.66,-0.94 0,0 8.19,-11.6 6.77,-20.17 -1.53,-9.21 -5.46,0 -5.46,0 0,0 3.95,-21.44 -2.18,-24.25 -3.57,-1.63 -2,8 -2,8 0,0 -2.39,-12.19 -8.87,-14.27 -3.27,-1 3.16,17.65 3.16,17.65 0,0 -3,-1.94 -3.27,2.73 -0.27,4.67 8.07,12.66 8.07,12.66 0,0 -5.31,-5 -5.46,2.56 -0.15,7.56 3.18,11.34 3.18,11.34 0,0 -3.89,-5.89 -5,0.63 -1.11,6.52 0.13,9.38 -0.93,12.78 -1.16,3.68 -1.64,3.82 -3.89,5.33 -2.25,1.51 2.62,2.57 2.62,2.57 z"
      id="path628"
      style="fill:#f9d12c" />
  </svg>

  <svg
    id="DIRGENER_INCSASAP"
    version="1.1"
    viewBox="0 0 141.72999 141.74"
    xmlns="http://www.w3.org/2000/svg">
    <defs
      id="defs594">
      <style
        id="style592">.cls-1 {
        font-size: 12px;
        font-family: ClearSans;
      }

      .cls-1, .cls-2 {
        fill: #1d1d1b;
      }

      .cls-3 {
        fill: #fbd12c;
      }

      .cls-4 {
        fill: #f9d12c;
      }</style>
    </defs>
    <polygon
      class="cls-2"
      id="polygon608"
      points="141.73,336.76 141.73,195.02 0,195.02 0,336.76 "
      style="fill:#1d1d1b"
      transform="translate(0,-195.02)" />
    <polygon
      class="cls-3"
      id="polygon612"
      points="113.39,319.75 113.39,302.74 28.35,302.74 28.35,319.75 "
      style="fill:#fbd12c"
      transform="translate(0,-195.02)" />
    <polygon
      class="cls-3"
      id="polygon616"
      points="113.39,294.24 113.39,277.23 28.35,277.23 28.35,294.24 "
      style="fill:#fbd12c"
      transform="translate(0,-195.02)" />
    <polygon
      class="cls-3"
      id="polygon620"
      points="56.64,251.3 66.39,249.8 70.87,240.98 75.34,249.8 85.09,251.3 78.1,258.26 79.66,267.92 70.87,263.44 62.07,267.92 63.63,258.26 "
      style="fill:#fbd12c"
      transform="translate(0,-195.02)" />
    <polygon
      class="cls-3"
      id="polygon622"
      points="56.64,213.64 66.39,212.13 70.87,203.31 75.34,212.13 85.09,213.64 78.1,220.59 79.66,230.25 70.87,225.77 62.07,230.25 63.63,220.59 "
      style="fill:#fbd12c"
      transform="translate(0,-195.02)" />
    <path
      class="cls-4"
      d="m 48.52,76.54 c 0,0 -17.9,-3.61 -17.9,-16.67 0,-6.26 4.66,-0.93 4.66,-0.93 0,0 -8.19,-11.61 -6.77,-20.17 1.53,-9.21 5.46,0 5.46,0 0,0 -3.95,-21.45 2.18,-24.25 3.57,-1.64 2,8 2,8 0,0 2.39,-12.19 8.87,-14.26 3.27,-1.05 -3.16,17.64 -3.16,17.64 0,0 3,-1.93 3.28,2.73 0.28,4.66 -8.08,12.66 -8.08,12.66 0,0 5.32,-5 5.46,2.57 0.14,7.57 -3.17,11.33 -3.17,11.33 0,0 3.88,-5.89 5,0.64 1.12,6.53 -0.13,9.37 0.93,12.77 1.16,3.69 1.64,3.82 3.89,5.33 2.25,1.51 -2.62,2.57 -2.62,2.57 z"
      id="path626"
      style="fill:#f9d12c" />
    <path
      class="cls-4"
      d="m 93.21,76.58 c 0,0 17.9,-3.61 17.9,-16.67 0,-6.26 -4.66,-0.93 -4.66,-0.93 0,0 8.19,-11.61 6.77,-20.17 -1.53,-9.21 -5.46,0 -5.46,0 0,0 3.95,-21.44 -2.18,-24.25 -3.57,-1.63 -2,8 -2,8 0,0 -2.39,-12.19 -8.87,-14.26 -3.27,-1.05 3.16,17.64 3.16,17.64 0,0 -3,-1.93 -3.27,2.73 -0.27,4.66 8.07,12.66 8.07,12.66 0,0 -5.31,-5 -5.46,2.57 -0.15,7.57 3.18,11.33 3.18,11.33 0,0 -3.89,-5.89 -5,0.64 -1.11,6.53 0.13,9.37 -0.93,12.77 -1.16,3.69 -1.64,3.82 -3.89,5.34 -2.25,1.52 2.62,2.56 2.62,2.56 z"
      id="path630"
      style="fill:#f9d12c" />
  </svg>

  <svg
    id="LIEUTENANT_INCSASAP"
    version="1.1"
    viewBox="0 0 141.73 141.73"
    xmlns="http://www.w3.org/2000/svg"
  >
    <defs
      id="defs120">
      <style
        id="style118">.cls-1 {
        font-size: 12px;
        font-family: ClearSans;
      }

      .cls-1, .cls-2 {
        fill: #1d1d1b;
      }

      .cls-3 {
        fill: #fbd12c;
      }</style>
    </defs>
    <polygon
      class="cls-2"
      id="polygon126"
      points="141.73,323.86 141.73,182.13 0,182.13 0,323.86 "
      style="fill:#1d1d1b"
      transform="translate(0,-182.13)" />
    <polygon
      class="cls-3"
      id="polygon132"
      points="113.39,306.85 113.39,289.85 28.35,289.85 28.35,306.85 "
      style="fill:#fbd12c"
      transform="translate(0,-182.13)" />
    <polygon
      class="cls-3"
      id="polygon138"
      points="84.93,232.84 94.68,231.34 99.16,222.52 103.63,231.34 113.39,232.84 106.39,239.79 107.95,249.46 99.16,244.97 90.36,249.46 91.92,239.79 "
      style="fill:#fbd12c"
      transform="translate(0,-182.13)" />
    <polygon
      class="cls-3"
      id="polygon140"
      points="28.35,232.84 38.1,231.34 42.58,222.52 47.05,231.34 56.8,232.84 49.81,239.79 51.37,249.46 42.58,244.97 33.78,249.46 35.34,239.79 "
      style="fill:#fbd12c"
      transform="translate(0,-182.13)" />
  </svg>

  <svg
    id="LIEUTENANT_GIS"
    version="1.1"
    viewBox="0 0 141.73 141.73"
    xmlns="http://www.w3.org/2000/svg"
  >
    <defs
      id="defs120">
      <style
        id="style118">.cls-1 {
        font-size: 12px;
        font-family: ClearSans;
      }

      .cls-1, .cls-2 {
        fill: #235f29;
      }

      .cls-3 {
        fill: #fbd12c;
      }</style>
    </defs>
    <polygon
      class="cls-2"
      id="polygon126"
      points="141.73,323.86 141.73,182.13 0,182.13 0,323.86 "
      style="fill:#235f29"
      transform="translate(0,-182.13)" />
    <polygon
      class="cls-3"
      id="polygon132"
      points="113.39,306.85 113.39,289.85 28.35,289.85 28.35,306.85 "
      style="fill:#fbd12c"
      transform="translate(0,-182.13)" />
    <polygon
      class="cls-3"
      id="polygon138"
      points="84.93,232.84 94.68,231.34 99.16,222.52 103.63,231.34 113.39,232.84 106.39,239.79 107.95,249.46 99.16,244.97 90.36,249.46 91.92,239.79 "
      style="fill:#fbd12c"
      transform="translate(0,-182.13)" />
    <polygon
      class="cls-3"
      id="polygon140"
      points="28.35,232.84 38.1,231.34 42.58,222.52 47.05,231.34 56.8,232.84 49.81,239.79 51.37,249.46 42.58,244.97 33.78,249.46 35.34,239.79 "
      style="fill:#fbd12c"
      transform="translate(0,-182.13)" />
  </svg>

  <svg
    id="LIEUTENANT1CL_INCSASAP"
    version="1.1"
    viewBox="0 0 141.73 141.73"
    xmlns="http://www.w3.org/2000/svg">
    <defs
      id="defs120">
      <style
        id="style118">.cls-1 {
        font-size: 12px;
        font-family: ClearSans;
      }

      .cls-1, .cls-2 {
        fill: #1d1d1b;
      }

      .cls-3 {
        fill: #fbd12c;
      }</style>
    </defs>
    <polygon
      class="cls-2"
      id="polygon128"
      points="141.73,505.98 141.73,364.25 0,364.25 0,505.98 "
      style="fill:#1d1d1b"
      transform="translate(0,-364.25)" />
    <polygon
      class="cls-3"
      id="polygon134"
      points="113.39,488.98 113.39,471.97 28.35,471.97 28.35,488.98 "
      style="fill:#fbd12c"
      transform="translate(0,-364.25)" />
    <polygon
      class="cls-3"
      id="polygon142"
      points="84.93,428.43 94.68,426.93 99.16,418.11 103.63,426.93 113.39,428.43 106.39,435.39 107.95,445.05 99.16,440.57 90.36,445.05 91.92,435.39 "
      style="fill:#fbd12c"
      transform="translate(0,-364.25)" />
    <polygon
      class="cls-3"
      id="polygon144"
      points="28.35,428.43 38.1,426.93 42.58,418.11 47.05,426.93 56.8,428.43 49.81,435.39 51.37,445.05 42.58,440.57 33.78,445.05 35.34,435.39 "
      style="fill:#fbd12c"
      transform="translate(0,-364.25)" />
    <polygon
      class="cls-3"
      id="polygon146"
      points="56.47,401.49 66.22,399.99 70.7,391.17 75.18,399.99 84.93,401.49 77.94,408.45 79.5,418.11 70.7,413.63 61.91,418.11 63.46,408.45 "
      style="fill:#fbd12c"
      transform="translate(0,-364.25)" />
  </svg>

  <svg
    id="LIEUTENANT1CL_DMS"
    version="1.1"
    viewBox="0 0 141.74001 141.72999"
    xmlns="http://www.w3.org/2000/svg">
    <defs
      id="defs784">
      <style
        id="style782">.cls-1 {
        font-size: 12px;
      }

      .cls-1, .cls-2 {
        fill: #1d1d1b;
        font-family: ClearSans;
      }

      .cls-2 {
        font-size: 15px;
      }

      .cls-3 {
        fill: #235f29;
      }

      .cls-4 {
        fill: #fbd12c;
      }

      .cls-5 {
        fill: #a02c36;
      }</style>
    </defs>
    <polygon
      class="cls-5"
      id="polygon822"
      points="156.04,524.96 156.04,383.23 14.3,383.23 14.3,524.96 "
      style="fill:#a02c36"
      transform="translate(-14.3,-383.23)" />
    <polygon
      class="cls-4"
      id="polygon824"
      points="127.69,507.95 127.69,490.94 42.65,490.94 42.65,507.95 "
      style="fill:#fbd12c"
      transform="translate(-14.3,-383.23)" />
    <polygon
      class="cls-4"
      id="polygon826"
      points="99.23,447.41 108.98,445.9 113.46,437.08 117.94,445.9 127.69,447.41 120.7,454.36 122.26,464.02 113.46,459.54 104.67,464.02 106.22,454.36 "
      style="fill:#fbd12c"
      transform="translate(-14.3,-383.23)" />
    <polygon
      class="cls-4"
      id="polygon828"
      points="42.65,447.41 52.4,445.9 56.88,437.08 61.35,445.9 71.11,447.41 64.11,454.36 65.67,464.02 56.88,459.54 48.08,464.02 49.64,454.36 "
      style="fill:#fbd12c"
      transform="translate(-14.3,-383.23)" />
    <polygon
      class="cls-4"
      id="polygon830"
      points="70.78,420.47 80.53,418.96 85,410.14 89.48,418.96 99.23,420.47 92.24,427.42 93.8,437.08 85,432.6 76.21,437.08 77.77,427.42 "
      style="fill:#fbd12c"
      transform="translate(-14.3,-383.23)" />
  </svg>

  <svg
    id="PSYCHOL-1CL_DMS"
    version="1.1"
    viewBox="0 0 141.74001 141.72999"
    xmlns="http://www.w3.org/2000/svg">
    <defs
      id="defs784">
      <style
        id="style782">.cls-1 {
        font-size: 12px;
      }

      .cls-1, .cls-2 {
        fill: #1d1d1b;
        font-family: ClearSans;
      }

      .cls-2 {
        font-size: 15px;
      }

      .cls-3 {
        fill: #235f29;
      }

      .cls-4 {
        fill: #fbd12c;
      }

      .cls-5 {
        fill: #a02c36;
      }</style>
    </defs>
    <polygon
      class="cls-5"
      id="polygon822"
      points="156.04,524.96 156.04,383.23 14.3,383.23 14.3,524.96 "
      style="fill:#a02c36"
      transform="translate(-14.3,-383.23)" />
    <polygon
      class="cls-4"
      id="polygon824"
      points="127.69,507.95 127.69,490.94 42.65,490.94 42.65,507.95 "
      style="fill:#fbd12c"
      transform="translate(-14.3,-383.23)" />
    <polygon
      class="cls-4"
      id="polygon826"
      points="99.23,447.41 108.98,445.9 113.46,437.08 117.94,445.9 127.69,447.41 120.7,454.36 122.26,464.02 113.46,459.54 104.67,464.02 106.22,454.36 "
      style="fill:#fbd12c"
      transform="translate(-14.3,-383.23)" />
    <polygon
      class="cls-4"
      id="polygon828"
      points="42.65,447.41 52.4,445.9 56.88,437.08 61.35,445.9 71.11,447.41 64.11,454.36 65.67,464.02 56.88,459.54 48.08,464.02 49.64,454.36 "
      style="fill:#fbd12c"
      transform="translate(-14.3,-383.23)" />
    <polygon
      class="cls-4"
      id="polygon830"
      points="70.78,420.47 80.53,418.96 85,410.14 89.48,418.96 99.23,420.47 92.24,427.42 93.8,437.08 85,432.6 76.21,437.08 77.77,427.42 "
      style="fill:#fbd12c"
      transform="translate(-14.3,-383.23)" />
  </svg>

  <svg
    id="MEDECINL-1CL_DMS"
    version="1.1"
    viewBox="0 0 141.74001 141.72999"
    xmlns="http://www.w3.org/2000/svg">
    <defs
      id="defs784">
      <style
        id="style782">.cls-1 {
        font-size: 12px;
      }

      .cls-1, .cls-2 {
        fill: #1d1d1b;
        font-family: ClearSans;
      }

      .cls-2 {
        font-size: 15px;
      }

      .cls-3 {
        fill: #235f29;
      }

      .cls-4 {
        fill: #fbd12c;
      }

      .cls-5 {
        fill: #a02c36;
      }</style>
    </defs>
    <polygon
      class="cls-5"
      id="polygon822"
      points="156.04,524.96 156.04,383.23 14.3,383.23 14.3,524.96 "
      style="fill:#a02c36"
      transform="translate(-14.3,-383.23)" />
    <polygon
      class="cls-4"
      id="polygon824"
      points="127.69,507.95 127.69,490.94 42.65,490.94 42.65,507.95 "
      style="fill:#fbd12c"
      transform="translate(-14.3,-383.23)" />
    <polygon
      class="cls-4"
      id="polygon826"
      points="99.23,447.41 108.98,445.9 113.46,437.08 117.94,445.9 127.69,447.41 120.7,454.36 122.26,464.02 113.46,459.54 104.67,464.02 106.22,454.36 "
      style="fill:#fbd12c"
      transform="translate(-14.3,-383.23)" />
    <polygon
      class="cls-4"
      id="polygon828"
      points="42.65,447.41 52.4,445.9 56.88,437.08 61.35,445.9 71.11,447.41 64.11,454.36 65.67,464.02 56.88,459.54 48.08,464.02 49.64,454.36 "
      style="fill:#fbd12c"
      transform="translate(-14.3,-383.23)" />
    <polygon
      class="cls-4"
      id="polygon830"
      points="70.78,420.47 80.53,418.96 85,410.14 89.48,418.96 99.23,420.47 92.24,427.42 93.8,437.08 85,432.6 76.21,437.08 77.77,427.42 "
      style="fill:#fbd12c"
      transform="translate(-14.3,-383.23)" />
  </svg>

  <svg
    id="VETERINL-1CL_DMS"
    version="1.1"
    viewBox="0 0 141.74001 141.72999"
    xmlns="http://www.w3.org/2000/svg">
    <defs
      id="defs784">
      <style
        id="style782">.cls-1 {
        font-size: 12px;
      }

      .cls-1, .cls-2 {
        fill: #1d1d1b;
        font-family: ClearSans;
      }

      .cls-2 {
        font-size: 15px;
      }

      .cls-3 {
        fill: #235f29;
      }

      .cls-4 {
        fill: #fbd12c;
      }

      .cls-5 {
        fill: #a02c36;
      }</style>
    </defs>
    <polygon
      class="cls-5"
      id="polygon822"
      points="156.04,524.96 156.04,383.23 14.3,383.23 14.3,524.96 "
      style="fill:#a02c36"
      transform="translate(-14.3,-383.23)" />
    <polygon
      class="cls-4"
      id="polygon824"
      points="127.69,507.95 127.69,490.94 42.65,490.94 42.65,507.95 "
      style="fill:#fbd12c"
      transform="translate(-14.3,-383.23)" />
    <polygon
      class="cls-4"
      id="polygon826"
      points="99.23,447.41 108.98,445.9 113.46,437.08 117.94,445.9 127.69,447.41 120.7,454.36 122.26,464.02 113.46,459.54 104.67,464.02 106.22,454.36 "
      style="fill:#fbd12c"
      transform="translate(-14.3,-383.23)" />
    <polygon
      class="cls-4"
      id="polygon828"
      points="42.65,447.41 52.4,445.9 56.88,437.08 61.35,445.9 71.11,447.41 64.11,454.36 65.67,464.02 56.88,459.54 48.08,464.02 49.64,454.36 "
      style="fill:#fbd12c"
      transform="translate(-14.3,-383.23)" />
    <polygon
      class="cls-4"
      id="polygon830"
      points="70.78,420.47 80.53,418.96 85,410.14 89.48,418.96 99.23,420.47 92.24,427.42 93.8,437.08 85,432.6 76.21,437.08 77.77,427.42 "
      style="fill:#fbd12c"
      transform="translate(-14.3,-383.23)" />
  </svg>

  <svg
    id="LIEUTENANT1CL_GIS"
    version="1.1"
    viewBox="0 0 141.74001 141.72999"
    xmlns="http://www.w3.org/2000/svg">
    <defs
      id="defs784">
      <style
        id="style782">.cls-1 {
        font-size: 12px;
      }

      .cls-1, .cls-2 {
        fill: #1d1d1b;
        font-family: ClearSans;
      }

      .cls-2 {
        font-size: 15px;
      }

      .cls-3 {
        fill: #235f29;
      }

      .cls-4 {
        fill: #fbd12c;
      }

      .cls-5 {
        fill: #a02c36;
      }</style>
    </defs>
    <polygon
      class="cls-3"
      id="polygon794"
      points="156.04,218.05 156.04,76.32 14.3,76.32 14.3,218.05 "
      style="fill:#235f29"
      transform="translate(-14.3,-76.32)" />
    <polygon
      class="cls-4"
      id="polygon796"
      points="127.69,201.04 127.69,184.03 42.65,184.03 42.65,201.04 "
      style="fill:#fbd12c"
      transform="translate(-14.3,-76.32)" />
    <polygon
      class="cls-4"
      id="polygon798"
      points="99.23,140.5 108.98,139 113.46,130.18 117.94,139 127.69,140.5 120.7,147.45 122.26,157.12 113.46,152.63 104.67,157.12 106.22,147.45 "
      style="fill:#fbd12c"
      transform="translate(-14.3,-76.32)" />
    <polygon
      class="cls-4"
      id="polygon800"
      points="42.65,140.5 52.4,139 56.88,130.18 61.35,139 71.11,140.5 64.11,147.45 65.67,157.12 56.88,152.63 48.08,157.12 49.64,147.45 "
      style="fill:#fbd12c"
      transform="translate(-14.3,-76.32)" />
    <polygon
      class="cls-4"
      id="polygon802"
      points="70.78,113.56 80.53,112.06 85,103.24 89.48,112.06 99.23,113.56 92.24,120.51 93.8,130.18 85,125.69 76.21,130.18 77.77,120.51 "
      style="fill:#fbd12c"
      transform="translate(-14.3,-76.32)" />
  </svg>

  <svg
    id="LIEUTENANTASP_INCSASAP"
    version="1.1"
    viewBox="0 0 141.73 141.73"
    xmlns="http://www.w3.org/2000/svg">
    <defs
      id="defs120">
      <style
        id="style118">.cls-1 {
        font-size: 12px;
        font-family: ClearSans;
      }

      .cls-1, .cls-2 {
        fill: #1d1d1b;
      }

      .cls-3 {
        fill: #fbd12c;
      }</style>
    </defs>
    <polygon
      class="cls-2"
      id="polygon124"
      points="141.73,141.73 141.73,0 0,0 0,141.73 "
      style="fill:#1d1d1b" />
    <polygon
      class="cls-3"
      id="polygon130"
      points="113.39,124.72 113.39,107.72 28.35,107.72 28.35,124.72 "
      style="fill:#fbd12c" />
    <polygon
      class="cls-3"
      id="polygon136"
      points="56.64,50.71 66.39,49.21 70.87,40.39 75.34,49.21 85.09,50.71 78.1,57.67 79.66,67.33 70.87,62.85 62.07,67.33 63.63,57.67 "
      style="fill:#fbd12c" />
  </svg>

  <svg
    id="INFIRMIERL-ASP_DMS"
    version="1.1"
    viewBox="0 0 141.73 141.73"
    xmlns="http://www.w3.org/2000/svg">
    <defs
      id="defs120">
      <style
        id="style118">.cls-1 {
        font-size: 12px;
        font-family: ClearSans;
      }

      .cls-1, .cls-2 {
        fill: #1d1d1b;
      }

      .cls-3 {
        fill: #fbd12c;
      }</style>
    </defs>
    <polygon
      class="cls-2"
      id="polygon124"
      points="141.73,141.73 141.73,0 0,0 0,141.73 "
      style="fill:#a02c36" />
    <polygon
      class="cls-3"
      id="polygon130"
      points="113.39,124.72 113.39,107.72 28.35,107.72 28.35,124.72 "
      style="fill:#fbd12c" />
    <polygon
      class="cls-3"
      id="polygon136"
      points="56.64,50.71 66.39,49.21 70.87,40.39 75.34,49.21 85.09,50.71 78.1,57.67 79.66,67.33 70.87,62.85 62.07,67.33 63.63,57.67 "
      style="fill:#fbd12c" />
  </svg>

  <svg
    id="LIEUTENANTCOL_INCSASAP"
    version="1.1"
    viewBox="0 0 141.73 141.73"
    xmlns="http://www.w3.org/2000/svg">
    <defs
      id="defs253">
      <style
        id="style251">.cls-1 {
        font-size: 12px;
        font-family: ClearSans;
      }

      .cls-1, .cls-2 {
        fill: #1d1d1b;
      }

      .cls-3 {
        fill: #fbd12c;
      }</style>
    </defs>
    <polygon
      class="cls-2"
      id="polygon261"
      points="141.73,505.98 141.73,364.25 0,364.25 0,505.98 "
      style="fill:#1d1d1b"
      transform="translate(0,-364.25)" />
    <polygon
      class="cls-3"
      id="polygon267"
      points="113.39,488.98 113.39,471.97 28.35,471.97 28.35,488.98 "
      style="fill:#fbd12c"
      transform="translate(0,-364.25)" />
    <polygon
      class="cls-3"
      id="polygon273"
      points="113.39,463.46 113.39,446.46 28.35,446.46 28.35,463.46 "
      style="fill:#fbd12c"
      transform="translate(0,-364.25)" />
    <polygon
      class="cls-3"
      id="polygon281"
      points="84.93,415.68 94.68,414.17 99.16,405.35 103.63,414.17 113.39,415.68 106.39,422.63 107.95,432.29 99.16,427.81 90.36,432.29 91.92,422.63 "
      style="fill:#fbd12c"
      transform="translate(0,-364.25)" />
    <polygon
      class="cls-3"
      id="polygon283"
      points="28.35,415.68 38.1,414.17 42.58,405.35 47.05,414.17 56.8,415.68 49.81,422.63 51.37,432.29 42.58,427.81 33.78,432.29 35.34,422.63 "
      style="fill:#fbd12c"
      transform="translate(0,-364.25)" />
    <polygon
      class="cls-3"
      id="polygon285"
      points="56.47,388.74 66.22,387.24 70.7,378.42 75.18,387.24 84.93,388.74 77.94,395.69 79.5,405.35 70.7,400.87 61.91,405.35 63.46,395.69 "
      style="fill:#fbd12c"
      transform="translate(0,-364.25)" />
  </svg>

  <svg
    id="LIEUTENANTCOL_DMS"
    version="1.1"
    viewBox="0 0 141.74001 141.72999"
    xmlns="http://www.w3.org/2000/svg">
    <defs
      id="defs784">
      <style
        id="style782">.cls-1 {
        font-size: 12px;
      }

      .cls-1, .cls-2 {
        fill: #1d1d1b;
        font-family: ClearSans;
      }

      .cls-2 {
        font-size: 15px;
      }

      .cls-3 {
        fill: #235f29;
      }

      .cls-4 {
        fill: #fbd12c;
      }

      .cls-5 {
        fill: #a02c36;
      }</style>
    </defs>
    <polygon
      class="cls-5"
      id="polygon832"
      points="397.17,524.96 397.17,383.23 255.43,383.23 255.43,524.96 "
      style="fill:#a02c36"
      transform="translate(-255.43,-383.23)" />
    <polygon
      class="cls-4"
      id="polygon834"
      points="368.82,507.95 368.82,490.94 283.78,490.94 283.78,507.95 "
      style="fill:#fbd12c"
      transform="translate(-255.43,-383.23)" />
    <polygon
      class="cls-4"
      id="polygon836"
      points="368.82,482.44 368.82,465.43 283.78,465.43 283.78,482.44 "
      style="fill:#fbd12c"
      transform="translate(-255.43,-383.23)" />
    <polygon
      class="cls-4"
      id="polygon838"
      points="340.36,434.65 350.11,433.15 354.59,424.33 359.07,433.15 368.82,434.65 361.83,441.61 363.39,451.27 354.59,446.79 345.8,451.27 347.35,441.61 "
      style="fill:#fbd12c"
      transform="translate(-255.43,-383.23)" />
    <polygon
      class="cls-4"
      id="polygon840"
      points="283.78,434.65 293.53,433.15 298.01,424.33 302.48,433.15 312.24,434.65 305.24,441.61 306.8,451.27 298.01,446.79 289.21,451.27 290.77,441.61 "
      style="fill:#fbd12c"
      transform="translate(-255.43,-383.23)" />
    <polygon
      class="cls-4"
      id="polygon842"
      points="311.9,407.71 321.66,406.21 326.13,397.39 330.61,406.21 340.36,407.71 333.37,414.67 334.93,424.33 326.13,419.85 317.34,424.33 318.9,414.67 "
      style="fill:#fbd12c"
      transform="translate(-255.43,-383.23)" />
  </svg>

  <svg
    id="LIEUTENANTCOL_GIS"
    version="1.1"
    viewBox="0 0 141.74001 141.72999"
    xmlns="http://www.w3.org/2000/svg">
    <defs
      id="defs784">
      <style
        id="style782">.cls-1 {
        font-size: 12px;
      }

      .cls-1, .cls-2 {
        fill: #1d1d1b;
        font-family: ClearSans;
      }

      .cls-2 {
        font-size: 15px;
      }

      .cls-3 {
        fill: #235f29;
      }

      .cls-4 {
        fill: #fbd12c;
      }

      .cls-5 {
        fill: #a02c36;
      }</style>
    </defs>
    <polygon
      class="cls-3"
      id="polygon794"
      points="156.04,218.05 156.04,76.32 14.3,76.32 14.3,218.05 "
      style="fill:#235f29"
      transform="translate(-14.3,-76.32)" />
    <polygon
      class="cls-4"
      id="polygon796"
      points="127.69,201.04 127.69,184.03 42.65,184.03 42.65,201.04 "
      style="fill:#fbd12c"
      transform="translate(-14.3,-76.32)" />
    <polygon
      class="cls-4"
      id="polygon798"
      points="99.23,140.5 108.98,139 113.46,130.18 117.94,139 127.69,140.5 120.7,147.45 122.26,157.12 113.46,152.63 104.67,157.12 106.22,147.45 "
      style="fill:#fbd12c"
      transform="translate(-14.3,-76.32)" />
    <polygon
      class="cls-4"
      id="polygon800"
      points="42.65,140.5 52.4,139 56.88,130.18 61.35,139 71.11,140.5 64.11,147.45 65.67,157.12 56.88,152.63 48.08,157.12 49.64,147.45 "
      style="fill:#fbd12c"
      transform="translate(-14.3,-76.32)" />
    <polygon
      class="cls-4"
      id="polygon802"
      points="70.78,113.56 80.53,112.06 85,103.24 89.48,112.06 99.23,113.56 92.24,120.51 93.8,130.18 85,125.69 76.21,130.18 77.77,120.51 "
      style="fill:#fbd12c"
      transform="translate(-14.3,-76.32)" />
  </svg>

  <svg
    id="VETERINL-COL_DMS"
    version="1.1"
    viewBox="0 0 141.74001 141.72999"
    xmlns="http://www.w3.org/2000/svg">
    <defs
      id="defs784">
      <style
        id="style782">.cls-1 {
        font-size: 12px;
      }

      .cls-1, .cls-2 {
        fill: #1d1d1b;
        font-family: ClearSans;
      }

      .cls-2 {
        font-size: 15px;
      }

      .cls-3 {
        fill: #235f29;
      }

      .cls-4 {
        fill: #fbd12c;
      }

      .cls-5 {
        fill: #a02c36;
      }</style>
    </defs>
    <polygon
      class="cls-5"
      id="polygon832"
      points="397.17,524.96 397.17,383.23 255.43,383.23 255.43,524.96 "
      style="fill:#a02c36"
      transform="translate(-255.43,-383.23)" />
    <polygon
      class="cls-4"
      id="polygon834"
      points="368.82,507.95 368.82,490.94 283.78,490.94 283.78,507.95 "
      style="fill:#fbd12c"
      transform="translate(-255.43,-383.23)" />
    <polygon
      class="cls-4"
      id="polygon836"
      points="368.82,482.44 368.82,465.43 283.78,465.43 283.78,482.44 "
      style="fill:#fbd12c"
      transform="translate(-255.43,-383.23)" />
    <polygon
      class="cls-4"
      id="polygon838"
      points="340.36,434.65 350.11,433.15 354.59,424.33 359.07,433.15 368.82,434.65 361.83,441.61 363.39,451.27 354.59,446.79 345.8,451.27 347.35,441.61 "
      style="fill:#fbd12c"
      transform="translate(-255.43,-383.23)" />
    <polygon
      class="cls-4"
      id="polygon840"
      points="283.78,434.65 293.53,433.15 298.01,424.33 302.48,433.15 312.24,434.65 305.24,441.61 306.8,451.27 298.01,446.79 289.21,451.27 290.77,441.61 "
      style="fill:#fbd12c"
      transform="translate(-255.43,-383.23)" />
    <polygon
      class="cls-4"
      id="polygon842"
      points="311.9,407.71 321.66,406.21 326.13,397.39 330.61,406.21 340.36,407.71 333.37,414.67 334.93,424.33 326.13,419.85 317.34,424.33 318.9,414.67 "
      style="fill:#fbd12c"
      transform="translate(-255.43,-383.23)" />
  </svg>

  <svg
    id="PSYCHOL-COL_DMS"
    version="1.1"
    viewBox="0 0 141.74001 141.72999"
    xmlns="http://www.w3.org/2000/svg">
    <defs
      id="defs784">
      <style
        id="style782">.cls-1 {
        font-size: 12px;
      }

      .cls-1, .cls-2 {
        fill: #1d1d1b;
        font-family: ClearSans;
      }

      .cls-2 {
        font-size: 15px;
      }

      .cls-3 {
        fill: #235f29;
      }

      .cls-4 {
        fill: #fbd12c;
      }

      .cls-5 {
        fill: #a02c36;
      }</style>
    </defs>
    <polygon
      class="cls-5"
      id="polygon832"
      points="397.17,524.96 397.17,383.23 255.43,383.23 255.43,524.96 "
      style="fill:#a02c36"
      transform="translate(-255.43,-383.23)" />
    <polygon
      class="cls-4"
      id="polygon834"
      points="368.82,507.95 368.82,490.94 283.78,490.94 283.78,507.95 "
      style="fill:#fbd12c"
      transform="translate(-255.43,-383.23)" />
    <polygon
      class="cls-4"
      id="polygon836"
      points="368.82,482.44 368.82,465.43 283.78,465.43 283.78,482.44 "
      style="fill:#fbd12c"
      transform="translate(-255.43,-383.23)" />
    <polygon
      class="cls-4"
      id="polygon838"
      points="340.36,434.65 350.11,433.15 354.59,424.33 359.07,433.15 368.82,434.65 361.83,441.61 363.39,451.27 354.59,446.79 345.8,451.27 347.35,441.61 "
      style="fill:#fbd12c"
      transform="translate(-255.43,-383.23)" />
    <polygon
      class="cls-4"
      id="polygon840"
      points="283.78,434.65 293.53,433.15 298.01,424.33 302.48,433.15 312.24,434.65 305.24,441.61 306.8,451.27 298.01,446.79 289.21,451.27 290.77,441.61 "
      style="fill:#fbd12c"
      transform="translate(-255.43,-383.23)" />
    <polygon
      class="cls-4"
      id="polygon842"
      points="311.9,407.71 321.66,406.21 326.13,397.39 330.61,406.21 340.36,407.71 333.37,414.67 334.93,424.33 326.13,419.85 317.34,424.33 318.9,414.67 "
      style="fill:#fbd12c"
      transform="translate(-255.43,-383.23)" />
  </svg>

  <svg
    id="INFIRMIERL-COL_DMS"
    version="1.1"
    viewBox="0 0 141.74001 141.72999"
    xmlns="http://www.w3.org/2000/svg">
    <defs
      id="defs784">
      <style
        id="style782">.cls-1 {
        font-size: 12px;
      }

      .cls-1, .cls-2 {
        fill: #1d1d1b;
        font-family: ClearSans;
      }

      .cls-2 {
        font-size: 15px;
      }

      .cls-3 {
        fill: #235f29;
      }

      .cls-4 {
        fill: #fbd12c;
      }

      .cls-5 {
        fill: #a02c36;
      }</style>
    </defs>
    <polygon
      class="cls-5"
      id="polygon832"
      points="397.17,524.96 397.17,383.23 255.43,383.23 255.43,524.96 "
      style="fill:#a02c36"
      transform="translate(-255.43,-383.23)" />
    <polygon
      class="cls-4"
      id="polygon834"
      points="368.82,507.95 368.82,490.94 283.78,490.94 283.78,507.95 "
      style="fill:#fbd12c"
      transform="translate(-255.43,-383.23)" />
    <polygon
      class="cls-4"
      id="polygon836"
      points="368.82,482.44 368.82,465.43 283.78,465.43 283.78,482.44 "
      style="fill:#fbd12c"
      transform="translate(-255.43,-383.23)" />
    <polygon
      class="cls-4"
      id="polygon838"
      points="340.36,434.65 350.11,433.15 354.59,424.33 359.07,433.15 368.82,434.65 361.83,441.61 363.39,451.27 354.59,446.79 345.8,451.27 347.35,441.61 "
      style="fill:#fbd12c"
      transform="translate(-255.43,-383.23)" />
    <polygon
      class="cls-4"
      id="polygon840"
      points="283.78,434.65 293.53,433.15 298.01,424.33 302.48,433.15 312.24,434.65 305.24,441.61 306.8,451.27 298.01,446.79 289.21,451.27 290.77,441.61 "
      style="fill:#fbd12c"
      transform="translate(-255.43,-383.23)" />
    <polygon
      class="cls-4"
      id="polygon842"
      points="311.9,407.71 321.66,406.21 326.13,397.39 330.61,406.21 340.36,407.71 333.37,414.67 334.93,424.33 326.13,419.85 317.34,424.33 318.9,414.67 "
      style="fill:#fbd12c"
      transform="translate(-255.43,-383.23)" />
  </svg>

  <svg
    id="PHARMAL-COL_DMS"
    version="1.1"
    viewBox="0 0 141.74001 141.72999"
    xmlns="http://www.w3.org/2000/svg">
    <defs
      id="defs784">
      <style
        id="style782">.cls-1 {
        font-size: 12px;
      }

      .cls-1, .cls-2 {
        fill: #1d1d1b;
        font-family: ClearSans;
      }

      .cls-2 {
        font-size: 15px;
      }

      .cls-3 {
        fill: #235f29;
      }

      .cls-4 {
        fill: #fbd12c;
      }

      .cls-5 {
        fill: #a02c36;
      }</style>
    </defs>
    <polygon
      class="cls-5"
      id="polygon832"
      points="397.17,524.96 397.17,383.23 255.43,383.23 255.43,524.96 "
      style="fill:#a02c36"
      transform="translate(-255.43,-383.23)" />
    <polygon
      class="cls-4"
      id="polygon834"
      points="368.82,507.95 368.82,490.94 283.78,490.94 283.78,507.95 "
      style="fill:#fbd12c"
      transform="translate(-255.43,-383.23)" />
    <polygon
      class="cls-4"
      id="polygon836"
      points="368.82,482.44 368.82,465.43 283.78,465.43 283.78,482.44 "
      style="fill:#fbd12c"
      transform="translate(-255.43,-383.23)" />
    <polygon
      class="cls-4"
      id="polygon838"
      points="340.36,434.65 350.11,433.15 354.59,424.33 359.07,433.15 368.82,434.65 361.83,441.61 363.39,451.27 354.59,446.79 345.8,451.27 347.35,441.61 "
      style="fill:#fbd12c"
      transform="translate(-255.43,-383.23)" />
    <polygon
      class="cls-4"
      id="polygon840"
      points="283.78,434.65 293.53,433.15 298.01,424.33 302.48,433.15 312.24,434.65 305.24,441.61 306.8,451.27 298.01,446.79 289.21,451.27 290.77,441.61 "
      style="fill:#fbd12c"
      transform="translate(-255.43,-383.23)" />
    <polygon
      class="cls-4"
      id="polygon842"
      points="311.9,407.71 321.66,406.21 326.13,397.39 330.61,406.21 340.36,407.71 333.37,414.67 334.93,424.33 326.13,419.85 317.34,424.33 318.9,414.67 "
      style="fill:#fbd12c"
      transform="translate(-255.43,-383.23)" />
  </svg>

  <svg
    id="MAJOR_INCSASAP"
    version="1.1"
    viewBox="0 0 141.73 141.73"
    xmlns="http://www.w3.org/2000/svg">
    <defs
      id="defs253">
      <style
        id="style251">.cls-1 {
        font-size: 12px;
        font-family: ClearSans;
      }

      .cls-1, .cls-2 {
        fill: #1d1d1b;
      }

      .cls-3 {
        fill: #fbd12c;
      }</style>
    </defs>
    <polygon
      class="cls-2"
      id="polygon259"
      points="141.73,323.86 141.73,182.13 0,182.13 0,323.86 "
      style="fill:#1d1d1b"
      transform="translate(0,-182.13)" />
    <polygon
      class="cls-3"
      id="polygon265"
      points="113.39,306.85 113.39,289.84 28.35,289.84 28.35,306.85 "
      style="fill:#fbd12c"
      transform="translate(0,-182.13)" />
    <polygon
      class="cls-3"
      id="polygon271"
      points="113.39,281.34 113.39,264.33 28.35,264.33 28.35,281.34 "
      style="fill:#fbd12c"
      transform="translate(0,-182.13)" />
    <polygon
      class="cls-3"
      id="polygon277"
      points="28.35,220.08 38.1,218.58 42.58,209.76 47.05,218.58 56.8,220.08 49.81,227.04 51.37,236.7 42.58,232.22 33.78,236.7 35.34,227.04 "
      style="fill:#fbd12c"
      transform="translate(0,-182.13)" />
    <polygon
      class="cls-3"
      id="polygon279"
      points="84.93,220.08 94.68,218.58 99.16,209.76 103.63,218.58 113.39,220.08 106.39,227.04 107.95,236.7 99.16,232.22 90.36,236.7 91.92,227.04 "
      style="fill:#fbd12c"
      transform="translate(0,-182.13)" />
  </svg>


  <svg
    id="PSYCHOMAJ_DMS"
    version="1.1"
    viewBox="0 0 141.73 141.73"
    xmlns="http://www.w3.org/2000/svg">
    <defs
      id="defs253">
      <style
        id="style251">.cls-1 {
        font-size: 12px;
        font-family: ClearSans;
      }

      .cls-1, .cls-2 {
        fill: #1d1d1b;
      }

      .cls-3 {
        fill: #fbd12c;
      }</style>
    </defs>
    <polygon
      class="cls-2"
      id="polygon259"
      points="141.73,323.86 141.73,182.13 0,182.13 0,323.86 "
      style="fill:#a02c36"
      transform="translate(0,-182.13)" />
    <polygon
      class="cls-3"
      id="polygon265"
      points="113.39,306.85 113.39,289.84 28.35,289.84 28.35,306.85 "
      style="fill:#fbd12c"
      transform="translate(0,-182.13)" />
    <polygon
      class="cls-3"
      id="polygon271"
      points="113.39,281.34 113.39,264.33 28.35,264.33 28.35,281.34 "
      style="fill:#fbd12c"
      transform="translate(0,-182.13)" />
    <polygon
      class="cls-3"
      id="polygon277"
      points="28.35,220.08 38.1,218.58 42.58,209.76 47.05,218.58 56.8,220.08 49.81,227.04 51.37,236.7 42.58,232.22 33.78,236.7 35.34,227.04 "
      style="fill:#fbd12c"
      transform="translate(0,-182.13)" />
    <polygon
      class="cls-3"
      id="polygon279"
      points="84.93,220.08 94.68,218.58 99.16,209.76 103.63,218.58 113.39,220.08 106.39,227.04 107.95,236.7 99.16,232.22 90.36,236.7 91.92,227.04 "
      style="fill:#fbd12c"
      transform="translate(0,-182.13)" />
  </svg>

  <svg
    id="VETERINMAJ_DMS"
    version="1.1"
    viewBox="0 0 141.73 141.73"
    xmlns="http://www.w3.org/2000/svg">
    <defs
      id="defs253">
      <style
        id="style251">.cls-1 {
        font-size: 12px;
        font-family: ClearSans;
      }

      .cls-1, .cls-2 {
        fill: #1d1d1b;
      }

      .cls-3 {
        fill: #fbd12c;
      }</style>
    </defs>
    <polygon
      class="cls-2"
      id="polygon259"
      points="141.73,323.86 141.73,182.13 0,182.13 0,323.86 "
      style="fill:#a02c36"
      transform="translate(0,-182.13)" />
    <polygon
      class="cls-3"
      id="polygon265"
      points="113.39,306.85 113.39,289.84 28.35,289.84 28.35,306.85 "
      style="fill:#fbd12c"
      transform="translate(0,-182.13)" />
    <polygon
      class="cls-3"
      id="polygon271"
      points="113.39,281.34 113.39,264.33 28.35,264.33 28.35,281.34 "
      style="fill:#fbd12c"
      transform="translate(0,-182.13)" />
    <polygon
      class="cls-3"
      id="polygon277"
      points="28.35,220.08 38.1,218.58 42.58,209.76 47.05,218.58 56.8,220.08 49.81,227.04 51.37,236.7 42.58,232.22 33.78,236.7 35.34,227.04 "
      style="fill:#fbd12c"
      transform="translate(0,-182.13)" />
    <polygon
      class="cls-3"
      id="polygon279"
      points="84.93,220.08 94.68,218.58 99.16,209.76 103.63,218.58 113.39,220.08 106.39,227.04 107.95,236.7 99.16,232.22 90.36,236.7 91.92,227.04 "
      style="fill:#fbd12c"
      transform="translate(0,-182.13)" />
  </svg>

  <svg
    id="PHARMAMAJ_DMS"
    version="1.1"
    viewBox="0 0 141.73 141.73"
    xmlns="http://www.w3.org/2000/svg">
    <defs
      id="defs253">
      <style
        id="style251">.cls-1 {
        font-size: 12px;
        font-family: ClearSans;
      }

      .cls-1, .cls-2 {
        fill: #1d1d1b;
      }

      .cls-3 {
        fill: #fbd12c;
      }</style>
    </defs>
    <polygon
      class="cls-2"
      id="polygon259"
      points="141.73,323.86 141.73,182.13 0,182.13 0,323.86 "
      style="fill:#a02c36"
      transform="translate(0,-182.13)" />
    <polygon
      class="cls-3"
      id="polygon265"
      points="113.39,306.85 113.39,289.84 28.35,289.84 28.35,306.85 "
      style="fill:#fbd12c"
      transform="translate(0,-182.13)" />
    <polygon
      class="cls-3"
      id="polygon271"
      points="113.39,281.34 113.39,264.33 28.35,264.33 28.35,281.34 "
      style="fill:#fbd12c"
      transform="translate(0,-182.13)" />
    <polygon
      class="cls-3"
      id="polygon277"
      points="28.35,220.08 38.1,218.58 42.58,209.76 47.05,218.58 56.8,220.08 49.81,227.04 51.37,236.7 42.58,232.22 33.78,236.7 35.34,227.04 "
      style="fill:#fbd12c"
      transform="translate(0,-182.13)" />
    <polygon
      class="cls-3"
      id="polygon279"
      points="84.93,220.08 94.68,218.58 99.16,209.76 103.63,218.58 113.39,220.08 106.39,227.04 107.95,236.7 99.16,232.22 90.36,236.7 91.92,227.04 "
      style="fill:#fbd12c"
      transform="translate(0,-182.13)" />
  </svg>

  <svg
    id="INFIRMIERMAJ_DMS"
    version="1.1"
    viewBox="0 0 141.73 141.73"
    xmlns="http://www.w3.org/2000/svg">
    <defs
      id="defs253">
      <style
        id="style251">.cls-1 {
        font-size: 12px;
        font-family: ClearSans;
      }

      .cls-1, .cls-2 {
        fill: #1d1d1b;
      }

      .cls-3 {
        fill: #fbd12c;
      }</style>
    </defs>
    <polygon
      class="cls-2"
      id="polygon259"
      points="141.73,323.86 141.73,182.13 0,182.13 0,323.86 "
      style="fill:#a02c36"
      transform="translate(0,-182.13)" />
    <polygon
      class="cls-3"
      id="polygon265"
      points="113.39,306.85 113.39,289.84 28.35,289.84 28.35,306.85 "
      style="fill:#fbd12c"
      transform="translate(0,-182.13)" />
    <polygon
      class="cls-3"
      id="polygon271"
      points="113.39,281.34 113.39,264.33 28.35,264.33 28.35,281.34 "
      style="fill:#fbd12c"
      transform="translate(0,-182.13)" />
    <polygon
      class="cls-3"
      id="polygon277"
      points="28.35,220.08 38.1,218.58 42.58,209.76 47.05,218.58 56.8,220.08 49.81,227.04 51.37,236.7 42.58,232.22 33.78,236.7 35.34,227.04 "
      style="fill:#fbd12c"
      transform="translate(0,-182.13)" />
    <polygon
      class="cls-3"
      id="polygon279"
      points="84.93,220.08 94.68,218.58 99.16,209.76 103.63,218.58 113.39,220.08 106.39,227.04 107.95,236.7 99.16,232.22 90.36,236.7 91.92,227.04 "
      style="fill:#fbd12c"
      transform="translate(0,-182.13)" />
  </svg>

  <svg
    id="SERGENT_INCSASAP"
    version="1.1"
    viewBox="0 0 141.73 141.73"
    xmlns="http://www.w3.org/2000/svg">
    <defs
      id="defs263">
      <style
        id="style261">.cls-1 {
        font-size: 12px;
        font-family: ClearSans;
      }

      .cls-1, .cls-2 {
        fill: #1d1d1b;
      }

      .cls-3 {
        fill: #e30613;
      }

      .cls-4 {
        fill: #dadada;
      }</style>
    </defs>
    <polygon
      class="cls-2"
      id="polygon283"
      points="141.73,141.73 141.73,0 0,0 0,141.73 "
      style="fill:#1d1d1b" />
    <polygon
      class="cls-3"
      id="polygon287"
      points="113.39,124.72 113.39,107.72 28.35,107.72 28.35,124.72 "
      style="fill:#e30613" />
    <polygon
      class="cls-3"
      id="polygon291"
      points="113.39,99.21 113.39,82.2 28.35,82.2 28.35,99.21 "
      style="fill:#e30613" />
    <polygon
      class="cls-4"
      id="polygon303"
      points="56.64,37.95 66.39,36.45 70.87,27.63 75.34,36.45 85.09,37.95 78.1,44.91 79.66,54.57 70.87,50.09 62.07,54.57 63.63,44.91 "
      style="fill:#dadada" />
  </svg>

  <svg
    id="SERGENT_GIS"
    version="1.1"
    viewBox="0 0 141.73 141.73"
    xmlns="http://www.w3.org/2000/svg">
    <defs
      id="defs263">
      <style
        id="style261">.cls-1 {
        font-size: 12px;
        font-family: ClearSans;
      }

      .cls-1, .cls-2 {
        fill: #235f29;
      }

      .cls-3 {
        fill: #e30613;
      }

      .cls-4 {
        fill: #dadada;
      }</style>
    </defs>
    <polygon
      class="cls-2"
      id="polygon283"
      points="141.73,141.73 141.73,0 0,0 0,141.73 "
      style="fill:#235f29" />
    <polygon
      class="cls-3"
      id="polygon287"
      points="113.39,124.72 113.39,107.72 28.35,107.72 28.35,124.72 "
      style="fill:#e30613" />
    <polygon
      class="cls-3"
      id="polygon291"
      points="113.39,99.21 113.39,82.2 28.35,82.2 28.35,99.21 "
      style="fill:#e30613" />
    <polygon
      class="cls-4"
      id="polygon303"
      points="56.64,37.95 66.39,36.45 70.87,27.63 75.34,36.45 85.09,37.95 78.1,44.91 79.66,54.57 70.87,50.09 62.07,54.57 63.63,44.91 "
      style="fill:#dadada" />
  </svg>

  <svg
    id="SERGENTCH_INCSASAP"
    version="1.1"
    viewBox="0 0 141.73 141.73"
    xmlns="http://www.w3.org/2000/svg">
    <defs
      id="defs263">
      <style
        id="style261">.cls-1 {
        font-size: 12px;
        font-family: ClearSans;
      }

      .cls-1, .cls-2 {
        fill: #1d1d1b;
      }

      .cls-3 {
        fill: #e30613;
      }

      .cls-4 {
        fill: #dadada;
      }</style>
    </defs>
    <polygon
      class="cls-2"
      id="polygon285"
      points="141.73,323.86 141.73,182.13 0,182.13 0,323.86 "
      style="fill:#1d1d1b"
      transform="translate(0,-182.13)" />
    <polygon
      class="cls-3"
      id="polygon289"
      points="113.39,306.85 113.39,289.84 28.35,289.84 28.35,306.85 "
      style="fill:#e30613"
      transform="translate(0,-182.13)" />
    <polygon
      class="cls-3"
      id="polygon293"
      points="113.39,281.34 113.39,264.33 28.35,264.33 28.35,281.34 "
      style="fill:#e30613"
      transform="translate(0,-182.13)" />
    <polygon
      class="cls-4"
      id="polygon305"
      points="28.35,220.08 38.1,218.58 42.58,209.76 47.05,218.58 56.8,220.08 49.81,227.04 51.37,236.7 42.58,232.22 33.78,236.7 35.34,227.04 "
      style="fill:#dadada"
      transform="translate(0,-182.13)" />
    <polygon
      class="cls-4"
      id="polygon307"
      points="84.93,220.08 94.68,218.58 99.16,209.76 103.63,218.58 113.39,220.08 106.39,227.04 107.95,236.7 99.16,232.22 90.36,236.7 91.92,227.04 "
      style="fill:#dadada"
      transform="translate(0,-182.13)" />
  </svg>

  <svg
    id="SERGENTCH_GIS"
    version="1.1"
    viewBox="0 0 141.73 141.73"
    xmlns="http://www.w3.org/2000/svg">
    <defs
      id="defs263">
      <style
        id="style261">.cls-1 {
        font-size: 12px;
        font-family: ClearSans;
      }

      .cls-1, .cls-2 {
        fill: #235f29;
      }

      .cls-3 {
        fill: #e30613;
      }

      .cls-4 {
        fill: #dadada;
      }</style>
    </defs>
    <polygon
      class="cls-2"
      id="polygon285"
      points="141.73,323.86 141.73,182.13 0,182.13 0,323.86 "
      style="fill:#235f29"
      transform="translate(0,-182.13)" />
    <polygon
      class="cls-3"
      id="polygon289"
      points="113.39,306.85 113.39,289.84 28.35,289.84 28.35,306.85 "
      style="fill:#e30613"
      transform="translate(0,-182.13)" />
    <polygon
      class="cls-3"
      id="polygon293"
      points="113.39,281.34 113.39,264.33 28.35,264.33 28.35,281.34 "
      style="fill:#e30613"
      transform="translate(0,-182.13)" />
    <polygon
      class="cls-4"
      id="polygon305"
      points="28.35,220.08 38.1,218.58 42.58,209.76 47.05,218.58 56.8,220.08 49.81,227.04 51.37,236.7 42.58,232.22 33.78,236.7 35.34,227.04 "
      style="fill:#dadada"
      transform="translate(0,-182.13)" />
    <polygon
      class="cls-4"
      id="polygon307"
      points="84.93,220.08 94.68,218.58 99.16,209.76 103.63,218.58 113.39,220.08 106.39,227.04 107.95,236.7 99.16,232.22 90.36,236.7 91.92,227.04 "
      style="fill:#dadada"
      transform="translate(0,-182.13)" />
  </svg>

  <svg
    id="SERGENTMAJ_INCSASAP"
    version="1.1"
    viewBox="0 0 141.73 141.73"
    xmlns="http://www.w3.org/2000/svg">
    <defs
      id="defs263">
      <style
        id="style261">.cls-1 {
        font-size: 12px;
        font-family: ClearSans;
      }

      .cls-1, .cls-2 {
        fill: #1d1d1b;
      }

      .cls-3 {
        fill: #e30613;
      }

      .cls-4 {
        fill: #dadada;
      }</style>
    </defs>
    <polygon
      class="cls-2"
      id="polygon267"
      points="141.73,505.98 141.73,364.25 0,364.25 0,505.98 "
      style="fill:#1d1d1b"
      transform="translate(0,-364.25)" />
    <polygon
      class="cls-3"
      id="polygon269"
      points="113.39,488.98 113.39,471.97 28.35,471.97 28.35,488.98 "
      style="fill:#e30613"
      transform="translate(0,-364.25)" />
    <polygon
      class="cls-3"
      id="polygon271"
      points="113.39,463.46 113.39,446.46 28.35,446.46 28.35,463.46 "
      style="fill:#e30613"
      transform="translate(0,-364.25)" />
    <polygon
      class="cls-4"
      id="polygon277"
      points="84.93,415.68 94.68,414.17 99.16,405.35 103.63,414.17 113.39,415.68 106.39,422.63 107.95,432.29 99.16,427.81 90.36,432.29 91.92,422.63 "
      style="fill:#dadada"
      transform="translate(0,-364.25)" />
    <polygon
      class="cls-4"
      id="polygon279"
      points="28.35,415.68 38.1,414.17 42.58,405.35 47.05,414.17 56.8,415.68 49.81,422.63 51.37,432.29 42.58,427.81 33.78,432.29 35.34,422.63 "
      style="fill:#dadada"
      transform="translate(0,-364.25)" />
    <polygon
      class="cls-4"
      id="polygon281"
      points="56.47,388.74 66.22,387.24 70.7,378.42 75.18,387.24 84.93,388.74 77.94,395.69 79.5,405.35 70.7,400.87 61.91,405.35 63.46,395.69 "
      style="fill:#dadada"
      transform="translate(0,-364.25)" />
  </svg>

  <svg
    id="SERGENTMAJ_GIS"
    version="1.1"
    viewBox="0 0 141.73 141.73"
    xmlns="http://www.w3.org/2000/svg">
    <defs
      id="defs263">
      <style
        id="style261">.cls-1 {
        font-size: 12px;
        font-family: ClearSans;
      }

      .cls-1, .cls-2 {
        fill: #235f29;
      }

      .cls-3 {
        fill: #e30613;
      }

      .cls-4 {
        fill: #dadada;
      }</style>
    </defs>
    <polygon
      class="cls-2"
      id="polygon267"
      points="141.73,505.98 141.73,364.25 0,364.25 0,505.98 "
      style="fill:#235f29"
      transform="translate(0,-364.25)" />
    <polygon
      class="cls-3"
      id="polygon269"
      points="113.39,488.98 113.39,471.97 28.35,471.97 28.35,488.98 "
      style="fill:#e30613"
      transform="translate(0,-364.25)" />
    <polygon
      class="cls-3"
      id="polygon271"
      points="113.39,463.46 113.39,446.46 28.35,446.46 28.35,463.46 "
      style="fill:#e30613"
      transform="translate(0,-364.25)" />
    <polygon
      class="cls-4"
      id="polygon277"
      points="84.93,415.68 94.68,414.17 99.16,405.35 103.63,414.17 113.39,415.68 106.39,422.63 107.95,432.29 99.16,427.81 90.36,432.29 91.92,422.63 "
      style="fill:#dadada"
      transform="translate(0,-364.25)" />
    <polygon
      class="cls-4"
      id="polygon279"
      points="28.35,415.68 38.1,414.17 42.58,405.35 47.05,414.17 56.8,415.68 49.81,422.63 51.37,432.29 42.58,427.81 33.78,432.29 35.34,422.63 "
      style="fill:#dadada"
      transform="translate(0,-364.25)" />
    <polygon
      class="cls-4"
      id="polygon281"
      points="56.47,388.74 66.22,387.24 70.7,378.42 75.18,387.24 84.93,388.74 77.94,395.69 79.5,405.35 70.7,400.87 61.91,405.35 63.46,395.69 "
      style="fill:#dadada"
      transform="translate(0,-364.25)" />
  </svg>

  <svg
    id="INFIRMIERL-1CL_DMS"
    version="1.1"
    viewBox="0 0 141.74001 141.72999"
    xmlns="http://www.w3.org/2000/svg">
    <defs
      id="defs784">
      <style
        id="style782">.cls-1 {
        font-size: 12px;
      }

      .cls-1, .cls-2 {
        fill: #1d1d1b;
        font-family: ClearSans;
      }

      .cls-2 {
        font-size: 15px;
      }

      .cls-3 {
        fill: #235f29;
      }

      .cls-4 {
        fill: #fbd12c;
      }

      .cls-5 {
        fill: #a02c36;
      }</style>
    </defs>
    <polygon
      class="cls-5"
      id="polygon822"
      points="156.04,524.96 156.04,383.23 14.3,383.23 14.3,524.96 "
      style="fill:#a02c36"
      transform="translate(-14.3,-383.23)" />
    <polygon
      class="cls-4"
      id="polygon824"
      points="127.69,507.95 127.69,490.94 42.65,490.94 42.65,507.95 "
      style="fill:#fbd12c"
      transform="translate(-14.3,-383.23)" />
    <polygon
      class="cls-4"
      id="polygon826"
      points="99.23,447.41 108.98,445.9 113.46,437.08 117.94,445.9 127.69,447.41 120.7,454.36 122.26,464.02 113.46,459.54 104.67,464.02 106.22,454.36 "
      style="fill:#fbd12c"
      transform="translate(-14.3,-383.23)" />
    <polygon
      class="cls-4"
      id="polygon828"
      points="42.65,447.41 52.4,445.9 56.88,437.08 61.35,445.9 71.11,447.41 64.11,454.36 65.67,464.02 56.88,459.54 48.08,464.02 49.64,454.36 "
      style="fill:#fbd12c"
      transform="translate(-14.3,-383.23)" />
    <polygon
      class="cls-4"
      id="polygon830"
      points="70.78,420.47 80.53,418.96 85,410.14 89.48,418.96 99.23,420.47 92.24,427.42 93.8,437.08 85,432.6 76.21,437.08 77.77,427.42 "
      style="fill:#fbd12c"
      transform="translate(-14.3,-383.23)" />
  </svg>

  <svg
    id="MEDECINCPT_DMS"
    version="1.1"
    viewBox="0 0 141.73 141.73"
    xmlns="http://www.w3.org/2000/svg">
    <defs
      id="defs253">
      <style
        id="style251">.cls-1 {
        font-size: 12px;
        font-family: ClearSans;
      }

      .cls-1, .cls-2 {
        fill: #a02c36;
      }

      .cls-3 {
        fill: #fbd12c;
      }</style>
    </defs>
    <polygon
      class="cls-2"
      id="polygon257"
      points="141.73,141.73 141.73,0 0,0 0,141.73 "
      style="fill:#a02c36" />
    <polygon
      class="cls-3"
      id="polygon263"
      points="113.39,124.72 113.39,107.72 28.35,107.72 28.35,124.72 "
      style="fill:#fbd12c" />
    <polygon
      class="cls-3"
      id="polygon269"
      points="113.39,99.21 113.39,82.2 28.35,82.2 28.35,99.21 "
      style="fill:#fbd12c" />
    <polygon
      class="cls-3"
      id="polygon275"
      points="56.64,37.95 66.39,36.45 70.87,27.63 75.34,36.45 85.09,37.95 78.1,44.91 79.66,54.57 70.87,50.09 62.07,54.57 63.63,44.91 "
      style="fill:#fbd12c" />
  </svg>

  <svg
    id="MEDECINMAJ_DMS"
    version="1.1"
    viewBox="0 0 141.73 141.73"
    xmlns="http://www.w3.org/2000/svg">
    <defs
      id="defs253">
      <style
        id="style251">.cls-1 {
        font-size: 12px;
        font-family: ClearSans;
      }

      .cls-1, .cls-2 {
        fill: #a02c36;
      }

      .cls-3 {
        fill: #fbd12c;
      }</style>
    </defs>
    <polygon
      class="cls-2"
      id="polygon259"
      points="141.73,323.86 141.73,182.13 0,182.13 0,323.86 "
      style="fill:#a02c36"
      transform="translate(0,-182.13)" />
    <polygon
      class="cls-3"
      id="polygon265"
      points="113.39,306.85 113.39,289.84 28.35,289.84 28.35,306.85 "
      style="fill:#fbd12c"
      transform="translate(0,-182.13)" />
    <polygon
      class="cls-3"
      id="polygon271"
      points="113.39,281.34 113.39,264.33 28.35,264.33 28.35,281.34 "
      style="fill:#fbd12c"
      transform="translate(0,-182.13)" />
    <polygon
      class="cls-3"
      id="polygon277"
      points="28.35,220.08 38.1,218.58 42.58,209.76 47.05,218.58 56.8,220.08 49.81,227.04 51.37,236.7 42.58,232.22 33.78,236.7 35.34,227.04 "
      style="fill:#fbd12c"
      transform="translate(0,-182.13)" />
    <polygon
      class="cls-3"
      id="polygon279"
      points="84.93,220.08 94.68,218.58 99.16,209.76 103.63,218.58 113.39,220.08 106.39,227.04 107.95,236.7 99.16,232.22 90.36,236.7 91.92,227.04 "
      style="fill:#fbd12c"
      transform="translate(0,-182.13)" />
  </svg>

  <svg
    id="INFIRMIERLIEUT_DMS"
    version="1.1"
    viewBox="0 0 141.73 141.73"
    xmlns="http://www.w3.org/2000/svg"
  >
    <defs
      id="defs120">
      <style
        id="style118">.cls-1 {
        font-size: 12px;
        font-family: ClearSans;
      }

      .cls-1, .cls-2 {
        fill: #a02c36;
      }

      .cls-3 {
        fill: #fbd12c;
      }</style>
    </defs>
    <polygon
      class="cls-2"
      id="polygon126"
      points="141.73,323.86 141.73,182.13 0,182.13 0,323.86 "
      style="fill:#a02c36"
      transform="translate(0,-182.13)" />
    <polygon
      class="cls-3"
      id="polygon132"
      points="113.39,306.85 113.39,289.85 28.35,289.85 28.35,306.85 "
      style="fill:#fbd12c"
      transform="translate(0,-182.13)" />
    <polygon
      class="cls-3"
      id="polygon138"
      points="84.93,232.84 94.68,231.34 99.16,222.52 103.63,231.34 113.39,232.84 106.39,239.79 107.95,249.46 99.16,244.97 90.36,249.46 91.92,239.79 "
      style="fill:#fbd12c"
      transform="translate(0,-182.13)" />
    <polygon
      class="cls-3"
      id="polygon140"
      points="28.35,232.84 38.1,231.34 42.58,222.52 47.05,231.34 56.8,232.84 49.81,239.79 51.37,249.46 42.58,244.97 33.78,249.46 35.34,239.79 "
      style="fill:#fbd12c"
      transform="translate(0,-182.13)" />
  </svg>

  <svg
    id="INFIRMIERADJ-MAJ_DMS"
    version="1.1"
    viewBox="0 0 141.72999 141.73"
    xmlns="http://www.w3.org/2000/svg"
  >
    <defs
      id="defs434">
      <style
        id="style432">.cls-1 {
        font-size: 12px;
        font-family: ClearSans;
      }

      .cls-1, .cls-2 {
        fill: #a02c36;
      }

      .cls-3 {
        fill: #dadada;
      }

      .cls-4 {
        fill: #fbd12c;
      }</style>
    </defs>
    <polygon
      class="cls-2"
      id="polygon464"
      points="141.73,688.11 141.73,546.38 0,546.38 0,688.11 "
      style="fill:#a02c36"
      transform="translate(0,-546.38)" />
    <polygon
      class="cls-3"
      id="polygon472"
      points="113.39,671.1 113.39,654.09 28.35,654.09 28.35,671.1 "
      style="fill:#dadada"
      transform="translate(0,-546.38)" />
    <polygon
      class="cls-3"
      id="polygon478"
      points="113.39,645.59 113.39,628.58 28.35,628.58 28.35,645.59 "
      style="fill:#dadada"
      transform="translate(0,-546.38)" />
    <polygon
      class="cls-4"
      id="polygon486"
      points="84.93,597.8 94.68,596.3 99.16,587.48 103.63,596.3 113.39,597.8 106.39,604.76 107.95,614.42 99.16,609.94 90.36,614.42 91.92,604.76 "
      style="fill:#fbd12c"
      transform="translate(0,-546.38)" />
    <polygon
      class="cls-4"
      id="polygon488"
      points="28.35,597.8 38.1,596.3 42.58,587.48 47.05,596.3 56.8,597.8 49.81,604.76 51.37,614.42 42.58,609.94 33.78,614.42 35.34,604.76 "
      style="fill:#fbd12c"
      transform="translate(0,-546.38)" />
    <polygon
      class="cls-4"
      id="polygon490"
      points="56.47,570.86 66.22,569.36 70.7,560.54 75.18,569.36 84.93,570.86 77.94,577.82 79.5,587.48 70.7,583 61.91,587.48 63.46,577.82 "
      style="fill:#fbd12c"
      transform="translate(0,-546.38)" />
  </svg>

  <svg
    id="MEDECINL-COL_DMS"
    version="1.1"
    viewBox="0 0 141.73 141.73"
    xmlns="http://www.w3.org/2000/svg">
    <defs
      id="defs253">
      <style
        id="style251">.cls-1 {
        font-size: 12px;
        font-family: ClearSans;
      }

      .cls-1, .cls-2 {
        fill: #a02c36;
      }

      .cls-3 {
        fill: #fbd12c;
      }</style>
    </defs>
    <polygon
      class="cls-2"
      id="polygon261"
      points="141.73,505.98 141.73,364.25 0,364.25 0,505.98 "
      style="fill:#a02c36"
      transform="translate(0,-364.25)" />
    <polygon
      class="cls-3"
      id="polygon267"
      points="113.39,488.98 113.39,471.97 28.35,471.97 28.35,488.98 "
      style="fill:#fbd12c"
      transform="translate(0,-364.25)" />
    <polygon
      class="cls-3"
      id="polygon273"
      points="113.39,463.46 113.39,446.46 28.35,446.46 28.35,463.46 "
      style="fill:#fbd12c"
      transform="translate(0,-364.25)" />
    <polygon
      class="cls-3"
      id="polygon281"
      points="84.93,415.68 94.68,414.17 99.16,405.35 103.63,414.17 113.39,415.68 106.39,422.63 107.95,432.29 99.16,427.81 90.36,432.29 91.92,422.63 "
      style="fill:#fbd12c"
      transform="translate(0,-364.25)" />
    <polygon
      class="cls-3"
      id="polygon283"
      points="28.35,415.68 38.1,414.17 42.58,405.35 47.05,414.17 56.8,415.68 49.81,422.63 51.37,432.29 42.58,427.81 33.78,432.29 35.34,422.63 "
      style="fill:#fbd12c"
      transform="translate(0,-364.25)" />
    <polygon
      class="cls-3"
      id="polygon285"
      points="56.47,388.74 66.22,387.24 70.7,378.42 75.18,387.24 84.93,388.74 77.94,395.69 79.5,405.35 70.7,400.87 61.91,405.35 63.46,395.69 "
      style="fill:#fbd12c"
      transform="translate(0,-364.25)" />
  </svg>

  <svg
    id="INFIRMIERCAPIT_DMS"
    version="1.1"
    viewBox="0 0 141.73 141.73"
    xmlns="http://www.w3.org/2000/svg">
    <defs
      id="defs253">
      <style
        id="style251">.cls-1 {
        font-size: 12px;
        font-family: ClearSans;
      }

      .cls-1, .cls-2 {
        fill: #a02c36;
      }

      .cls-3 {
        fill: #fbd12c;
      }</style>
    </defs>
    <polygon
      class="cls-2"
      id="polygon257"
      points="141.73,141.73 141.73,0 0,0 0,141.73 "
      style="fill:#a02c36" />
    <polygon
      class="cls-3"
      id="polygon263"
      points="113.39,124.72 113.39,107.72 28.35,107.72 28.35,124.72 "
      style="fill:#fbd12c" />
    <polygon
      class="cls-3"
      id="polygon269"
      points="113.39,99.21 113.39,82.2 28.35,82.2 28.35,99.21 "
      style="fill:#fbd12c" />
    <polygon
      class="cls-3"
      id="polygon275"
      points="56.64,37.95 66.39,36.45 70.87,27.63 75.34,36.45 85.09,37.95 78.1,44.91 79.66,54.57 70.87,50.09 62.07,54.57 63.63,44.91 "
      style="fill:#fbd12c" />
  </svg>

  <svg
    id="PSYCHOCPT_DMS"
    version="1.1"
    viewBox="0 0 141.73 141.73"
    xmlns="http://www.w3.org/2000/svg">
    <defs
      id="defs253">
      <style
        id="style251">.cls-1 {
        font-size: 12px;
        font-family: ClearSans;
      }

      .cls-1, .cls-2 {
        fill: #a02c36;
      }

      .cls-3 {
        fill: #fbd12c;
      }</style>
    </defs>
    <polygon
      class="cls-2"
      id="polygon257"
      points="141.73,141.73 141.73,0 0,0 0,141.73 "
      style="fill:#a02c36" />
    <polygon
      class="cls-3"
      id="polygon263"
      points="113.39,124.72 113.39,107.72 28.35,107.72 28.35,124.72 "
      style="fill:#fbd12c" />
    <polygon
      class="cls-3"
      id="polygon269"
      points="113.39,99.21 113.39,82.2 28.35,82.2 28.35,99.21 "
      style="fill:#fbd12c" />
    <polygon
      class="cls-3"
      id="polygon275"
      points="56.64,37.95 66.39,36.45 70.87,27.63 75.34,36.45 85.09,37.95 78.1,44.91 79.66,54.57 70.87,50.09 62.07,54.57 63.63,44.91 "
      style="fill:#fbd12c" />
  </svg>

  <svg
    id="VETERINCPT_DMS"
    version="1.1"
    viewBox="0 0 141.73 141.73"
    xmlns="http://www.w3.org/2000/svg">
    <defs
      id="defs253">
      <style
        id="style251">.cls-1 {
        font-size: 12px;
        font-family: ClearSans;
      }

      .cls-1, .cls-2 {
        fill: #a02c36;
      }

      .cls-3 {
        fill: #fbd12c;
      }</style>
    </defs>
    <polygon
      class="cls-2"
      id="polygon257"
      points="141.73,141.73 141.73,0 0,0 0,141.73 "
      style="fill:#a02c36" />
    <polygon
      class="cls-3"
      id="polygon263"
      points="113.39,124.72 113.39,107.72 28.35,107.72 28.35,124.72 "
      style="fill:#fbd12c" />
    <polygon
      class="cls-3"
      id="polygon269"
      points="113.39,99.21 113.39,82.2 28.35,82.2 28.35,99.21 "
      style="fill:#fbd12c" />
    <polygon
      class="cls-3"
      id="polygon275"
      points="56.64,37.95 66.39,36.45 70.87,27.63 75.34,36.45 85.09,37.95 78.1,44.91 79.66,54.57 70.87,50.09 62.07,54.57 63.63,44.91 "
      style="fill:#fbd12c" />
  </svg>

  <svg
    id="PHARMACPT_DMS"
    version="1.1"
    viewBox="0 0 141.73 141.73"
    xmlns="http://www.w3.org/2000/svg">
    <defs
      id="defs253">
      <style
        id="style251">.cls-1 {
        font-size: 12px;
        font-family: ClearSans;
      }

      .cls-1, .cls-2 {
        fill: #a02c36;
      }

      .cls-3 {
        fill: #fbd12c;
      }</style>
    </defs>
    <polygon
      class="cls-2"
      id="polygon257"
      points="141.73,141.73 141.73,0 0,0 0,141.73 "
      style="fill:#a02c36" />
    <polygon
      class="cls-3"
      id="polygon263"
      points="113.39,124.72 113.39,107.72 28.35,107.72 28.35,124.72 "
      style="fill:#fbd12c" />
    <polygon
      class="cls-3"
      id="polygon269"
      points="113.39,99.21 113.39,82.2 28.35,82.2 28.35,99.21 "
      style="fill:#fbd12c" />
    <polygon
      class="cls-3"
      id="polygon275"
      points="56.64,37.95 66.39,36.45 70.87,27.63 75.34,36.45 85.09,37.95 78.1,44.91 79.66,54.57 70.87,50.09 62.07,54.57 63.63,44.91 "
      style="fill:#fbd12c" />
  </svg>

  <svg
    id="INFIRMIERADJ_DMS"
    version="1.1"
    viewBox="0 0 141.72999 141.73"
    xmlns="http://www.w3.org/2000/svg">
    <defs
      id="defs434">
      <style
        id="style432">.cls-3 {
        fill: #dadada;
      }

      .cls-4 {
        fill: #fbd12c;
      }</style>
    </defs>
    <polygon
      class="cls-2"
      id="polygon458"
      points="141.73,323.86 141.73,182.13 0,182.13 0,323.86 "
      style="fill:#a02c36"
      transform="translate(0,-182.13)" />
    <polygon
      class="cls-3"
      id="polygon466"
      points="113.39,306.85 113.39,289.84 28.35,289.84 28.35,306.85 "
      style="fill:#dadada"
      transform="translate(0,-182.13)" />
    <polygon
      class="cls-3"
      id="polygon474"
      points="113.39,281.34 113.39,264.33 28.35,264.33 28.35,281.34 "
      style="fill:#dadada"
      transform="translate(0,-182.13)" />
    <polygon
      class="cls-4"
      id="polygon480"
      points="56.64,220.08 66.39,218.58 70.87,209.76 75.34,218.58 85.09,220.08 78.1,227.04 79.66,236.7 70.87,232.22 62.07,236.7 63.63,227.04 "
      style="fill:#fbd12c"
      transform="translate(0,-182.13)" />
  </svg>

  <svg
    id="INFIRMIERADJ-CH_DMS"
    version="1.1"
    viewBox="0 0 141.72999 141.73"
    xmlns="http://www.w3.org/2000/svg"
  >
    <defs
      id="defs434">
      <style
        id="style432">.cls-1 {
        font-size: 12px;
        font-family: ClearSans;
      }

      .cls-1, .cls-2 {
        fill: #a02c36;
      }

      .cls-3 {
        fill: #dadada;
      }

      .cls-4 {
        fill: #fbd12c;
      }</style>
    </defs>
    <polygon
      class="cls-2"
      id="polygon462"
      points="141.73,505.98 141.73,364.25 0,364.25 0,505.98 "
      style="fill:#a02c36"
      transform="translate(0,-364.25)" />
    <polygon
      class="cls-3"
      id="polygon470"
      points="113.39,488.98 113.39,471.97 28.35,471.97 28.35,488.98 "
      style="fill:#dadada"
      transform="translate(0,-364.25)" />
    <polygon
      class="cls-3"
      id="polygon476"
      points="113.39,463.46 113.39,446.46 28.35,446.46 28.35,463.46 "
      style="fill:#dadada"
      transform="translate(0,-364.25)" />
    <polygon
      class="cls-4"
      id="polygon482"
      points="28.35,402.21 38.1,400.7 42.58,391.88 47.05,400.7 56.8,402.21 49.81,409.16 51.37,418.82 42.58,414.34 33.78,418.82 35.34,409.16 "
      style="fill:#fbd12c"
      transform="translate(0,-364.25)" />
    <polygon
      class="cls-4"
      id="polygon484"
      points="84.93,402.21 94.68,400.7 99.16,391.88 103.63,400.7 113.39,402.21 106.39,409.16 107.95,418.82 99.16,414.34 90.36,418.82 91.92,409.16 "
      style="fill:#fbd12c"
      transform="translate(0,-364.25)" />
  </svg>

  <svg
    id="MEDECINCOL_DMS"
    version="1.1"
    viewBox="0 0 141.72999 141.72999"
    xmlns="http://www.w3.org/2000/svg">
    <defs
      id="defs594">
      <style
        id="style592">.cls-1 {
        font-size: 12px;
        font-family: ClearSans;
      }

      .cls-1, .cls-2 {
        fill: #a02c36;
      }

      .cls-3 {
        fill: #fbd12c;
      }

      .cls-4 {
        fill: #f9d12c;
      }</style>
    </defs>
    <polygon
      class="cls-2"
      id="polygon606"
      points="141.73,141.73 141.73,0 0,0 0,141.73 "
      style="fill:#a02c36" />
    <polygon
      class="cls-3"
      id="polygon610"
      points="113.39,124.72 113.39,107.72 28.35,107.72 28.35,124.72 "
      style="fill:#fbd12c" />
    <polygon
      class="cls-3"
      id="polygon614"
      points="113.39,99.21 113.39,82.2 28.35,82.2 28.35,99.21 "
      style="fill:#fbd12c" />
    <polygon
      class="cls-3"
      id="polygon618"
      points="56.64,39.37 66.39,37.87 70.87,29.05 75.34,37.87 85.09,39.37 78.1,46.33 79.66,55.99 70.87,51.51 62.07,55.99 63.63,46.33 "
      style="fill:#fbd12c" />
    <path
      class="cls-4"
      d="m 48.52,76.58 c 0,0 -17.9,-3.62 -17.9,-16.67 0,-6.26 4.66,-0.94 4.66,-0.94 0,0 -8.19,-11.6 -6.77,-20.17 1.53,-9.21 5.46,0 5.46,0 0,0 -3.95,-21.44 2.18,-24.25 3.57,-1.63 2,8 2,8 0,0 2.39,-12.19 8.87,-14.27 3.27,-1 -3.16,17.65 -3.16,17.65 0,0 3,-1.94 3.28,2.73 0.28,4.67 -8.08,12.66 -8.08,12.66 0,0 5.32,-5 5.46,2.56 0.14,7.56 -3.17,11.34 -3.17,11.34 0,0 3.88,-5.89 5,0.63 1.12,6.52 -0.13,9.38 0.93,12.78 1.16,3.68 1.64,3.82 3.89,5.33 2.25,1.51 -2.62,2.57 -2.62,2.57 z"
      id="path624"
      style="fill:#f9d12c" />
    <path
      class="cls-4"
      d="m 93.21,76.58 c 0,0 17.9,-3.62 17.9,-16.67 0,-6.26 -4.66,-0.94 -4.66,-0.94 0,0 8.19,-11.6 6.77,-20.17 -1.53,-9.21 -5.46,0 -5.46,0 0,0 3.95,-21.44 -2.18,-24.25 -3.57,-1.63 -2,8 -2,8 0,0 -2.39,-12.19 -8.87,-14.27 -3.27,-1 3.16,17.65 3.16,17.65 0,0 -3,-1.94 -3.27,2.73 -0.27,4.67 8.07,12.66 8.07,12.66 0,0 -5.31,-5 -5.46,2.56 -0.15,7.56 3.18,11.34 3.18,11.34 0,0 -3.89,-5.89 -5,0.63 -1.11,6.52 0.13,9.38 -0.93,12.78 -1.16,3.68 -1.64,3.82 -3.89,5.33 -2.25,1.51 2.62,2.57 2.62,2.57 z"
      id="path628"
      style="fill:#f9d12c" />
  </svg>

  <svg data-name="PSUPP_INCSASAP" id="PSUPP_INCSASAP" viewBox="35 80 150 150" xmlns="http://www.w3.org/2000/svg">
    <defs>
      <style>.cls-1 {
        font-size: 12px;
      }

      .cls-1, .cls-2, .cls-3, .cls-4 {
        fill: #1d1d1b;
      }

      .cls-1, .cls-2, .cls-3 {
        font-family: ClearSans;
      }

      .cls-2, .cls-3 {
        font-size: 15px;
      }

      .cls-3 {
        letter-spacing: -0.02em;
      }

      .cls-5 {
        fill: none;
        stroke: #dadada;
        stroke-linecap: round;
        stroke-linejoin: round;
        stroke-width: 3px;
      }

      .cls-6 {
        fill: #dadada;
      }</style>
    </defs>
    <polygon class="cls-4" points="27.98 76.41 27.98 218.14 169.71 218.14 169.71 76.41 27.98 76.41 27.98 76.41" />
    <path class="cls-5"
          d="M294.27,270.59s.28-7.64,0-7.71a35.76,35.76,0,0,1-11-4.5s-5.6,5.78-6.06,5.51c-9.46-5.68-15.78-16.15-15.78-16.15s5.5-5.51,5.32-5.79a32.3,32.3,0,0,1-4.5-11h-7.61a39.17,39.17,0,0,1-1.86-11.72c0-11.54,1.9-11.73,1.9-11.73s7.64.28,7.71,0a35.76,35.76,0,0,1,4.5-11s-5.79-5.6-5.51-6.06c5.68-9.46,16.15-15.78,16.15-15.78s5.51,5.5,5.78,5.32a32.39,32.39,0,0,1,11-4.5v-7.62a37.8,37.8,0,0,1,23.05,0v7.62a32.3,32.3,0,0,1,11,4.5c.28.18,5.78-5.32,5.78-5.32s10.48,6.32,16.16,15.78c.27.46-5.51,6.06-5.51,6.06a35.76,35.76,0,0,1,4.5,11c.07.28,7.71,0,7.71,0s1.92.19,1.9,11.73a39.55,39.55,0,0,1-1.86,11.72h-7.62A32.4,32.4,0,0,1,345,242c-.19.28,5.32,5.79,5.32,5.79s-6.32,10.47-15.79,16.15c-.45.27-6.05-5.51-6.05-5.51a35.91,35.91,0,0,1-11,4.5c-.28.07,0,7.71,0,7.71s-.18,1.93-11.57,1.93-11.57-1.93-11.57-1.93Zm11.57-23.12a28.2,28.2,0,1,0-28.2-28.2,28.19,28.19,0,0,0,28.2,28.2Z"
          transform="translate(-207 -72)" />
    <polygon class="cls-6" points="98.83 120.51 76.34 161.84 121.35 161.84 98.83 120.51 98.83 120.51" />
  </svg>


  <svg class="bi bi-truck" fill="currentColor" height="1em" id="icon-truck" viewBox="0 0 16 16" width="1em">
    <path
      d="M0 3.5A1.5 1.5 0 0 1 1.5 2h9A1.5 1.5 0 0 1 12 3.5v7h-1v-7a.5.5 0 0 0-.5-.5h-9a.5.5 0 0 0-.5.5v7a.5.5 0 0 0 .5.5v1A1.5 1.5 0 0 1 0 10.5v-7zM4.5 11h6v1h-6v-1z"
      fill-rule="evenodd" />
    <path
      d="M11 5h2.02a1.5 1.5 0 0 1 1.17.563l1.481 1.85a1.5 1.5 0 0 1 .329.938V10.5a1.5 1.5 0 0 1-1.5 1.5h-1v-1h1a.5.5 0 0 0 .5-.5V8.35a.5.5 0 0 0-.11-.312l-1.48-1.85A.5.5 0 0 0 13.02 6H12v4.5h-1V5zm-8 8a1 1 0 1 0 0-2 1 1 0 0 0 0 2zm0 1a2 2 0 1 0 0-4 2 2 0 0 0 0 4z"
      fill-rule="evenodd" />
    <path d="M12 13a1 1 0 1 0 0-2 1 1 0 0 0 0 2zm0 1a2 2 0 1 0 0-4 2 2 0 0 0 0 4z" fill-rule="evenodd" />
  </svg>

  <svg id="icon-turning-device" style="fill-rule: evenodd;" viewBox="0 0 512 512">
    <path
      d="M 453.5,-0.5 C 457.5,-0.5 461.5,-0.5 465.5,-0.5C 476.066,4.66339 479.566,12.9967 476,24.5C 474.207,27.2953 472.04,29.7953 469.5,32C 462.348,35.0767 455.348,38.41 448.5,42C 492.469,67.7615 512.969,106.261 510,157.5C 508.767,167.432 506.434,177.098 503,186.5C 498.727,196.383 491.227,200.217 480.5,198C 470.242,193.943 466.409,186.443 469,175.5C 482.116,135.267 471.782,102.6 438,77.5C 440.48,84.94 442.98,92.44 445.5,100C 442.16,116.005 432.827,121.005 417.5,115C 413.541,112.035 410.708,108.202 409,103.5C 403.275,89.7161 397.275,76.0494 391,62.5C 385.048,48.2592 387.548,35.7592 398.5,25C 416.859,16.3887 435.192,7.88872 453.5,-0.5 Z"
      fill="#000000"
      style="opacity:0.966" />
    <path
      d="M 224.5,60.5 C 232.14,59.8175 239.473,60.9842 246.5,64C 302.946,86.0564 359.279,108.39 415.5,131C 426.108,141.388 427.941,153.221 421,166.5C 384.61,259.612 347.944,352.612 311,445.5C 301.503,455.997 290.337,458.164 277.5,452C 222.246,430.137 166.912,408.471 111.5,387C 98.3173,379.12 94.4839,367.954 100,353.5C 136.333,260.5 172.667,167.5 209,74.5C 212.492,67.8344 217.659,63.1678 224.5,60.5 Z M 235.5,102.5 C 285.212,120.349 334.545,139.349 383.5,159.5C 384.696,160.559 385.363,161.893 385.5,163.5C 358.962,232.613 332.129,301.613 305,370.5C 302.529,375.67 298.696,376.837 293.5,374C 246.596,355.629 199.762,337.129 153,318.5C 152.219,317.271 151.719,315.938 151.5,314.5C 177.33,248.172 203.164,181.838 229,115.5C 230.668,110.83 232.835,106.497 235.5,102.5 Z M 209.5,359.5 C 226.626,359.792 235.126,368.458 235,385.5C 231.457,400.022 222.29,406.189 207.5,404C 196.862,401.026 190.862,394.026 189.5,383C 191.031,370.646 197.697,362.813 209.5,359.5 Z"
      fill="#000000"
      style="opacity:0.97" />
    <path
      d="M 57.5,511.5 C 53.5,511.5 49.5,511.5 45.5,511.5C 34.9237,506.298 31.4237,497.964 35,486.5C 36.7932,483.705 38.9598,481.205 41.5,479C 48.6522,475.923 55.6522,472.59 62.5,469C 18.5306,443.239 -1.96936,404.739 1,353.5C 2.41127,342.522 5.07794,331.855 9,321.5C 12.4047,315.13 17.738,312.13 25,312.5C 33.7156,312.22 39.3823,316.22 42,324.5C 42.6667,328.167 42.6667,331.833 42,335.5C 29.194,375.639 39.5273,408.306 73,433.5C 71.351,427.836 69.351,422.169 67,416.5C 65.1455,399.688 72.6455,392.188 89.5,394C 96.6511,396.769 101.151,401.936 103,409.5C 108.392,422.617 114.058,435.617 120,448.5C 125.952,462.741 123.452,475.241 112.5,486C 94.1414,494.611 75.8081,503.111 57.5,511.5 Z"
      fill="#000000"
      style="opacity:0.966" />
  </svg>

  <svg id="icon-dms" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
    <g>
      <path
        d="m92.445 37.82c-0.46875-0.30078-0.99219-0.44922-1.5156-0.44922-0.82813 0-1.6445 0.36719-2.1914 1.0625l-0.80859 1.0234c-0.59375 0.75-1.4961 1.1875-2.4531 1.1875-2.3359 0-4.5508 1.0391-6.043 2.8359l-2.1875 2.6328 0.003906 0.003907-8.2812 9.1562c-0.14062 0.15625-0.33594 0.24609-0.54688 0.25781-0.23047 0.007812-0.41406-0.066406-0.56641-0.21094l-10.488-9.8633c-3.1992-3.0117-7.3828-4.668-11.773-4.668h-1.6406c-0.078125 0-0.15625 0.015626-0.23438 0.039063-0.011719 0.003906-0.023438 0.011719-0.035156 0.015625-0.042969 0.015625-0.085938 0.027344-0.12109 0.050781l-0.97266 0.5625c-3.5977 2.0859-8.0742 2.0938-11.676 0.019531l-1.0117-0.58203c-0.019532-0.011719-0.039063-0.011719-0.058594-0.019531-0.070312-0.035156-0.14062-0.058594-0.21875-0.070312-0.015625-0.003907-0.035156-0.003907-0.050781-0.007813-0.019531 0-0.039063-0.007813-0.0625-0.007813h-1.2578c-4.0117 0-7.9648 1.3555-11.137 3.8125-4.9141 3.8125-7.5156 9.7852-6.9609 15.977l2.6719 30.18c0.11328 1.2578 1.1523 2.2031 2.4141 2.2031h37.246c0.66016 0 1.3008-0.27344 1.7578-0.75391 0.46094-0.48828 0.69531-1.125 0.66406-1.793l-0.48047-9.6719c-0.23438-4.4844 0.26172-8.6992 1.4766-12.527 0.074219-0.24219 0.14062-0.48438 0.20703-0.73047 0-0.003906 0.003907-0.007813 0.003907-0.011719v-0.007812c0.26172-0.98828 0.44531-2.0078 0.53125-3.043l6.7227 5.1133c1.4375 1.0938 3.2227 1.6953 5.0273 1.6953 2.7461 0 5.3086-1.3516 6.8555-3.6211l10.719-15.699 7.2344-10.121c0.91797-1.2969 0.57422-3.1016-0.76172-3.9688zm-6.9688 4.3906c1.4414 0 2.7852-0.64844 3.6797-1.7812l0.80859-1.0234c0.23438-0.29688 0.58594-0.46875 0.96484-0.46875 0.23437 0 0.46484 0.066406 0.66406 0.19922 0.28516 0.18359 0.47656 0.46875 0.53906 0.80078s-0.011718 0.66797-0.20703 0.94531l-6.6875 9.3555-5.7383-4.3945 1.1289-1.3594c1.207-1.4453 2.9727-2.2734 4.8477-2.2734zm-65.875 25.711c-0.84375-3.1094-1.1914-7.1758-1.0312-12.086 0.015626-0.42969-0.32422-0.79297-0.75391-0.80469h-0.027344c-0.41797 0-0.76562 0.33203-0.78125 0.75781-0.16406 5.0664 0.20313 9.2891 1.0859 12.547 1.0234 3.7812 1.4492 7.5156 1.2656 11.102l-0.60156 11.969h-3.5195c-0.44922 0-0.81641-0.33594-0.85547-0.78125l-2.6719-30.176c-0.50781-5.6602 1.8711-11.121 6.3633-14.602 2.3594-1.8281 5.1953-2.9766 8.1445-3.3438-0.83594 0.67969-1.6797 1.5977-2.2852 2.8281-1.2695 2.5703-1.1641 5.75 0.30859 9.4414 0.011718 0.027343 0.03125 0.050781 0.042968 0.074218-0.98828 0.78125-1.6289 1.9922-1.6289 3.3555 0 2.3711 1.9297 4.2969 4.2969 4.2969s4.2969-1.9258 4.2969-4.2969-1.9297-4.2969-4.2969-4.2969c-0.44922 0-0.88281 0.070312-1.2891 0.19922-1.2695-3.2188-1.3867-5.9375-0.33984-8.0742 1.1133-2.2773 3.2812-3.293 4.082-3.6016l5.957 9.6328c0.16016 0.25781 0.375 0.45312 0.61328 0.58203v38.754h-15.652l0.59766-11.891c0.19141-3.7461-0.25-7.6445-1.3203-11.586zm7.3516-12.453c1.5078 0 2.7344 1.2266 2.7344 2.7344s-1.2266 2.7344-2.7344 2.7344-2.7344-1.2266-2.7344-2.7344 1.2266-2.7344 2.7344-2.7344zm47 11.258c-1.2578 1.8398-3.3359 2.9375-5.5664 2.9375-1.4648 0-2.9141-0.48828-4.0781-1.375l-7.6172-5.793c0.24219-4.4219-0.43359-6.7812-0.47266-6.9102-0.12109-0.41406-0.55859-0.64844-0.96875-0.52734-0.41406 0.12109-0.64844 0.55469-0.52734 0.96875 0.011719 0.039062 1.0977 3.9062-0.13281 11.043-0.058594 0.22266-0.11328 0.44922-0.18359 0.67188-1.2695 4.0078-1.7891 8.4102-1.5469 13.078l0.48047 9.6719c0.011718 0.23828-0.070313 0.46484-0.23438 0.63672-0.16406 0.17188-0.38672 0.26562-0.625 0.26562l-14.941 0.003907v-38.754c0.24219-0.12891 0.45313-0.32422 0.61328-0.58203l5.8945-9.5312c1.4258 0.91406 5.5938 4.2734 3.3398 11.172-0.035157 0.11328-0.042969 0.22656-0.027344 0.33984-0.28906-0.039063-0.58203-0.066407-0.88281-0.066407-3.6641 0-6.6445 2.9805-6.6445 6.6445v3.5156c0 0.078125 0.023438 0.15234 0.046875 0.22266 0.14062 1.1523 1.1172 2.0469 2.3086 2.0469 1.293 0 2.3438-1.0508 2.3438-2.3438s-1.0508-2.3438-2.3438-2.3438c-0.28125 0-0.54688 0.058594-0.79688 0.14844v-1.2461c0-2.8047 2.2812-5.082 5.082-5.082 2.8047 0 5.082 2.2812 5.082 5.082v1.2422c-0.24219-0.089843-0.5-0.14453-0.77344-0.14453-1.293 0-2.3438 1.0508-2.3438 2.3438s1.0508 2.3438 2.3438 2.3438c1.2617 0 2.2812-0.99609 2.332-2.2422 0-0.007812 0.003907-0.019531 0.003907-0.027343v-0.027344c0.003906-0.015625 0.007812-0.03125 0.007812-0.046875s-0.003906-0.03125-0.003906-0.046875v-3.3984c0-2.8594-1.8164-5.293-4.3555-6.2305 0.042969-0.0625 0.082031-0.12891 0.10547-0.20312 2-6.1133-0.51562-9.8945-2.5312-11.801 3.7188 0.17578 7.2305 1.6445 9.957 4.207l10.484 9.8633c0.46094 0.43359 1.0664 0.66016 1.6992 0.63281 0.63281-0.027344 1.2188-0.30078 1.6445-0.76953l8.3672-9.2539 5.8438 4.4492z" />
      <path
        d="m21.547 36.031h5.5625c2.5156 2.3594 5.8906 3.8125 9.6094 3.8125s7.0938-1.4531 9.6094-3.8125h5.1211c1.1055 0 1.8477-1.1133 1.4492-2.1445-1.6758-4.332-1.3711-7.9336-1.3711-7.9336 0.5-8.6211-6.3555-15.883-14.988-15.883-8.6328 0-15.488 7.2656-14.988 15.883 0 0 0.15625 3.5625-1.457 7.9648-0.37891 1.0234 0.36328 2.1133 1.4531 2.1133zm15.172 2.25c-2.6523 0-5.1094-0.83594-7.1367-2.25-2.5977-1.8125-4.4766-4.582-5.1211-7.7969 10.012-0.49219 13.816-10.223 13.816-10.223 3.7852 4.8789 9.4141 5.6484 10.762 5.7617 0.10938 0.65625 0.17969 1.3242 0.17969 2.0078 0 4.2383-2.125 7.9883-5.3633 10.25-2.0273 1.4141-4.4844 2.25-7.1367 2.25z" />
      <path
        d="m63.281 31.25h4.6875v4.6875c0 0.86328 0.69922 1.5625 1.5625 1.5625h4.6875c0.86328 0 1.5625-0.69922 1.5625-1.5625v-4.6875h4.6875c0.86328 0 1.5625-0.69922 1.5625-1.5625v-4.6875c0-0.86328-0.69922-1.5625-1.5625-1.5625h-4.6875v-4.6875c0-0.86328-0.69922-1.5625-1.5625-1.5625h-4.6875c-0.86328 0-1.5625 0.69922-1.5625 1.5625v4.6875h-4.6875c-0.86328 0-1.5625 0.69922-1.5625 1.5625v4.6875c0 0.86328 0.69922 1.5625 1.5625 1.5625z" />
    </g>
  </svg>

  <svg id="icon-gis" viewBox="0 0 100 100" x="0px" xmlns="http://www.w3.org/2000/svg" y="0px">
    <g>
      <g>
        <g>
          <g>
            <path
              d="M70.1,80.6c-0.4,0.8-1.8,0.8-2.2,0c-0.5-0.9-0.9-1.9-1.4-2.8c-0.2-0.4-1-1.8-1.2-2.3c-0.6,0-2.1-0.3-2.6-0.4      c-1-0.2-2.1-0.3-3.1-0.5c-1-0.1-1.2-1.5-0.5-2.1c0.7-0.7,1.5-1.5,2.2-2.2c0.2-0.2,1.2-1.3,1.8-1.7c0-0.1,0-0.3-0.1-0.4      c-0.2-0.6-0.2-1.3-0.3-1.9c-0.2-1-0.4-2.1-0.5-3.1c-0.2-1.1,0.8-1.9,1.8-1.4c1.6,0.9,3.3,1.7,4.9,2.6c1.6-0.9,3.3-1.7,4.9-2.6      c1-0.5,2,0.3,1.8,1.4c-0.2,1-0.3,2.1-0.5,3.1c-0.1,0.6-0.2,1.3-0.3,1.9c0,0.1-0.1,0.3-0.1,0.4c0.5,0.4,1.5,1.5,1.8,1.7      c0.8,0.7,1.5,1.5,2.2,2.2c0.6,0.6,0.4,1.9-0.6,2.1c-1,0.2-2.1,0.3-3.1,0.5c-0.4,0.1-2,0.4-2.6,0.4c-0.2,0.5-0.9,1.9-1.2,2.3      C71,78.7,70.6,79.6,70.1,80.6z" />
            <path
              d="M91.6,63c0,3.2-1.3,5.9-3.4,8c0,0,0,0.1-0.1,0.1C87.7,81.6,79.2,90,68.6,90c-5.8,0-11.1-2.6-14.6-6.6      c-9.6,0.8-20.5-0.9-25.3-5.7c-0.5-0.5-0.9-1-1.3-1.5C20.9,75.9,15,74.2,11.8,71c-2.2-2.2-3.4-4.9-3.4-8c0-6.6,2.2-13.9,5.9-19.5      c1.8-2.8,3.9-5,6.2-6.7c0.4-0.4,0.5-0.9,0.6-1.3c0.1-0.7-0.1-1.6-0.6-2.2c-2.2-2.4-3.7-5.7-3.7-9.4C16.8,16.2,23,10,30.7,10      c3.5,0,6.7,1.3,9.1,3.5c2.7-2.2,6-3.4,9.6-3.4c3.9,0,7.4,1.5,10.1,3.8c2.4-2.4,5.8-3.9,9.6-3.9c7.7,0,13.9,6.2,13.9,13.9      c0,3.5-1.3,6.8-3.7,9.4c-0.5,0.7-0.6,1.5-0.6,2.2c0.1,0.5,0.2,1,0.6,1.3c2.3,1.7,4.4,3.9,6.2,6.7C89.4,49.1,91.6,56.4,91.6,63z       M87.1,64.3c0.1-0.5,0.1-0.8,0.1-1.3c0-15.6-10.5-25.2-17.9-25.2c-2.3,0-5,1-7.6,2.7c2.2,1.7,4.3,4,6,6.7      c0.9,1.2,1.6,2.4,2.2,3.9C78,51.6,84.7,57,87.1,64.3z M83.3,70.5c0-0.5,0-0.9-0.1-1.3c-0.5-6.2-5-11.5-11-12.9      c-1.1-0.2-2.3-0.5-3.5-0.5c-0.5,0-1,0-1.6,0.1c-0.2,0-0.5,0-0.7,0.1C59.5,57.1,54,63.2,54,70.5c0,2.7,0.9,5.4,2.2,7.5      c1.1,1.7,2.4,3.3,4.1,4.4c2.4,1.7,5.2,2.7,8.4,2.7c6.8,0,12.4-4.6,14.1-10.8C83.2,73.1,83.3,71.9,83.3,70.5z M77,29.3      c1.1-1.6,1.7-3.4,1.7-5.5c0-5.2-4.3-9.5-9.5-9.5c-2.7,0-5,1.1-6.7,2.8c1.5,2.4,2.4,5.2,2.4,8.2c0,2.2-0.5,4.1-1.2,6.1      c1.6,1.2,3.5,1.8,5.6,1.8c2.7,0,5.1-1.1,6.8-2.9C76.4,30.1,76.6,29.7,77,29.3z M63.1,51.8c0.5-0.1,1.1-0.2,1.6-0.4      c-2-3.2-4.3-5.7-6.8-7.4c-1.3-1-2.6-1.7-3.9-2.2c-1.6-0.6-3-1-4.5-1c-1.2,0-2.6,0.2-3.9,0.7c-1.3,0.5-2.7,1.1-4,2.1      c-6.2,4.2-11.9,13-11.9,25.2c0,1.1,0.2,2.1,0.6,2.9c0.4,1,1,1.7,1.7,2.4c0.9,0.7,1.9,1.5,3.3,1.9c4,1.7,9.9,2.4,15.6,2.3      c-1.1-2.4-1.7-5.1-1.7-7.9C49.1,61.6,55.1,54.2,63.1,51.8z M59.8,25.1c0-5.7-4.6-10.4-10.4-10.4s-10.4,4.6-10.4,10.4      c0,5.7,4.6,10.4,10.4,10.4S59.8,30.8,59.8,25.1z M34.2,25.4c0-3.3,1-6.2,2.7-8.7c-1.7-1.5-3.8-2.3-6.2-2.3      c-5.2,0-9.5,4.3-9.5,9.5c0,2.1,0.6,3.9,1.8,5.5c0.4,0.4,0.6,0.7,0.9,1.1c1.7,1.8,4.1,2.9,6.8,2.9c1.8,0,3.5-0.5,4.9-1.5      C34.7,29.9,34.2,27.8,34.2,25.4z M31.4,47.1c1.8-2.9,4-5.2,6.5-7.1c-2.3-1.6-4.9-2.4-7.1-2.4c-7.4,0-17.9,9.6-17.9,25.2      c0,1.9,0.6,3.5,2.1,4.9c1.9,1.8,5.8,3,10.2,3.5C25,70.4,25,69.6,25,68.6C24.8,61.4,27.3,53.3,31.4,47.1z" />
          </g>
        </g>
      </g>
    </g>
  </svg>

  <svg id="icon-commandment" viewBox="0 0 32 32" x="0px" xmlns="http://www.w3.org/2000/svg" y="0px">
    <path
      d="M18.67,20H13.33A7.34,7.34,0,0,0,6,27.33V29a1,1,0,0,0,1,1H25a1,1,0,0,0,1-1V27.33A7.34,7.34,0,0,0,18.67,20ZM24,28H8v-.67A5.34,5.34,0,0,1,13.33,22h5.33A5.34,5.34,0,0,1,24,27.33Z" />
    <path
      d="M27,11H25.29l1.65-4.67a1,1,0,0,0-.57-1.26l-10-4a1,1,0,0,0-.74,0l-10,4a1,1,0,0,0-.54,1.35L7.19,11H5a1,1,0,0,0,0,2H9.08a7,7,0,0,0,13.84,0H27a1,1,0,0,0,0-2ZM7.35,6.54,16,3.08l8.74,3.49L23.17,11H9.39ZM16,17a5,5,0,0,1-4.9-4h9.8A5,5,0,0,1,16,17Z" />
    <path
      d="M13.91,9.31a1,1,0,0,0,1,.69h2.27a1,1,0,0,0,1-.69l.7-2.16A1,1,0,0,0,18.43,6L16.59,4.69a1,1,0,0,0-1.18,0L13.57,6a1,1,0,0,0-.36,1.12ZM16,6.74l.66.48L16.41,8h-.82l-.25-.78Z" />
  </svg>

  <svg id="icon-samu" viewBox="0 0 100 100" x="0px" xmlns="http://www.w3.org/2000/svg" y="0px">
    <g>
      <path
        d="M81.48,62.143c-3.648,0-6.617,2.969-6.617,6.616c0,3.648,2.969,6.617,6.617,6.617s6.616-2.969,6.616-6.617   C88.097,65.111,85.129,62.143,81.48,62.143z" />
      <path
        d="M23.385,62.346c-3.449,0-6.255,2.807-6.255,6.255s2.806,6.254,6.255,6.254s6.256-2.806,6.256-6.254   S26.834,62.346,23.385,62.346z" />
      <path
        d="M94.451,53.976c-0.085-0.023-9.018-2.538-11.511-3.368c-0.67-0.223-2.108-2.166-3.379-3.882   c-2.851-3.844-6.398-8.631-10.409-8.631c-1.13,0-4.283-0.008-8.587-0.022h-0.002c-0.06,0-0.118,0.024-0.16,0.066   c-0.043,0.042-0.067,0.101-0.067,0.161v34.655c0,0.126,0.103,0.229,0.228,0.229H73.08c0.269,0,0.521-0.12,0.689-0.329   c0.169-0.207,0.233-0.478,0.177-0.742c-0.043-0.217-1.023-5.292,1.45-8.35c1.292-1.6,3.357-2.412,6.138-2.412   c2.998,0,4.591,0.816,5.86,2.431c2.136,2.71,1.741,6.857,1.731,6.898c-0.058,0.23-0.017,0.48,0.112,0.685   c0.132,0.205,0.341,0.347,0.575,0.395c0.112,0.021,0.496,0.085,1.011,0.085c1.285,0,4.274-0.423,4.274-4.336V54.829   C95.099,54.434,94.833,54.083,94.451,53.976z M63.095,50.648l0.068-9.798c0.521-0.014,1.458-0.033,2.873-0.033   c0.256,0,0.521-0.005,0.801-0.014c0.297-0.007,0.577-0.013,0.868-0.013c2.191,0,4.514,0.443,6.658,3.66   c1.701,2.551,3.278,4.864,4.194,6.197H63.095z" />
      <path
        d="M58.212,29.389c-4.931-0.017-11.456-0.025-19.392-0.025c-9.735,0-19.435,0.012-25.224,0.021L8.625,29.39l-0.32-0.001   c-0.112-0.042-0.333-0.069-0.527-0.042c-0.61-0.103-1.188,0.441-1.186,1.07v30.33c0,0.488,0.323,0.913,0.803,1.059   c0.011,0.003,0.021,0.005,0.033,0.005h4.471c0.692,0,1.257,0.563,1.257,1.257c0,0.691-0.564,1.254-1.257,1.254H5.016   c-0.064,0-0.114,0.052-0.114,0.115v6.172c0,0.929,0.754,1.685,1.684,1.685h8.454c0.932-0.002,1.688-0.757,1.688-1.685   c0-0.126-0.015-0.258-0.043-0.382c-0.063-0.431-0.425-4.104,1.521-6.444c1.079-1.293,2.659-2.195,5.004-2.195   c2.166,0,3.908,0.673,5.178,2.001c2.88,3.011,2.727,8.416,2.719,8.644c-0.023,0.446,0.144,0.895,0.46,1.229   c0.321,0.333,0.767,0.523,1.222,0.523h25.42c0.565,0,1.027-0.461,1.027-1.027V30.417C59.236,29.851,58.776,29.391,58.212,29.389z    M12.98,34.288h-2.588c-0.756,0-1.37-0.615-1.37-1.371c0-0.757,0.613-1.37,1.37-1.37h2.588c0.756,0,1.37,0.613,1.37,1.37   C14.35,33.673,13.736,34.288,12.98,34.288z M32.574,46.548c0.046,0.032,0.078,0.08,0.087,0.135   c0.011,0.055-0.002,0.112-0.034,0.157l-2.133,3.08c-0.041,0.058-0.105,0.091-0.172,0.091c-0.041,0-0.083-0.014-0.121-0.039   l-2.651-1.837v3.227c0,0.116-0.094,0.211-0.21,0.211h-3.747c-0.115,0-0.21-0.095-0.21-0.211v-3.227l-2.653,1.837   c-0.038,0.025-0.079,0.039-0.12,0.039c-0.066,0-0.131-0.034-0.172-0.091l-2.134-3.08c-0.031-0.045-0.043-0.103-0.034-0.157   c0.01-0.055,0.041-0.103,0.087-0.135l3.448-2.388l-3.446-2.388c-0.046-0.031-0.079-0.081-0.087-0.133   c-0.01-0.056,0.002-0.113,0.034-0.159l2.132-3.08c0.063-0.094,0.195-0.119,0.292-0.053l2.653,1.836v-3.226   c0-0.118,0.095-0.211,0.21-0.211h3.747c0.116,0,0.21,0.093,0.21,0.211v3.226l2.651-1.836c0.097-0.066,0.227-0.041,0.293,0.053   l2.131,3.08c0.032,0.046,0.045,0.103,0.036,0.159c-0.01,0.053-0.041,0.103-0.087,0.133l-3.448,2.388L32.574,46.548z M54.844,34.288   h-2.587c-0.757,0-1.371-0.615-1.371-1.371c0-0.757,0.614-1.37,1.371-1.37h2.587c0.756,0,1.37,0.613,1.37,1.37   C56.214,33.673,55.6,34.288,54.844,34.288z" />
      <path
        d="M60.336,33.312v3.332c0,0.253,0.203,0.458,0.456,0.458h5.623c0.253,0,0.457-0.205,0.457-0.458v-3.332   c0-0.252-0.204-0.457-0.457-0.457h-5.623C60.539,32.855,60.336,33.059,60.336,33.312z" />
      <path
        d="M69.457,36.494c0,0.25,0.203,0.452,0.453,0.452h2.491c0.25,0,0.453-0.202,0.453-0.452s-0.203-0.453-0.453-0.453H69.91   C69.66,36.041,69.457,36.244,69.457,36.494z" />
      <path
        d="M61.718,27.567v-2.491c0-0.25-0.203-0.453-0.453-0.453c-0.251,0-0.454,0.203-0.454,0.453v2.491   c0,0.25,0.203,0.453,0.454,0.453C61.515,28.021,61.718,27.817,61.718,27.567z" />
      <path
        d="M71.072,30.348l-2.253,1.058c-0.228,0.106-0.325,0.376-0.218,0.603c0.077,0.164,0.239,0.26,0.41,0.26   c0.064,0,0.13-0.014,0.191-0.043l2.253-1.058c0.228-0.106,0.324-0.375,0.218-0.602C71.567,30.34,71.298,30.242,71.072,30.348z" />
      <path
        d="M66.163,28.875l1.422-2.043c0.142-0.205,0.092-0.488-0.114-0.63c-0.205-0.143-0.487-0.092-0.63,0.113l-1.422,2.043   c-0.143,0.205-0.092,0.487,0.114,0.63c0.078,0.056,0.169,0.082,0.258,0.082C65.934,29.07,66.075,29.002,66.163,28.875z" />
    </g>
  </svg>

</svg>
