import {
  ChangeDetectorRef,
  Component,
  Input,
  OnChanges,
  On<PERSON><PERSON>roy,
  <PERSON>Chang<PERSON>,
} from '@angular/core';
import {
  SchedulerManagementMessage,
  SchedulerManagementMessageType,
} from '../scheduler-management-message.model';
import { TranslateService } from '@ngx-translate/core';
import { BreakpointObserver, BreakpointState } from '@angular/cdk/layout';
import { Subscription } from 'rxjs';

@Component({
  selector: 'cgdis-portal-scheduler-management-detail',
  templateUrl: './scheduler-management-detail.component.html',
  styleUrls: ['./_scheduler-management-detail.scss'],
})
export class SchedulerManagementDetailComponent
  implements OnChanges, OnDestroy
{
  /**
   * The scheduler detail
   */
  @Input() message: SchedulerManagementMessage;

  public isAllowance: boolean = false;

  public isMobile: boolean = false;

  private subscriptions: Subscription[] = [];

  constructor(
    private translateService: TranslateService,
    private breakpointObserver: BreakpointObserver,
    private cd: ChangeDetectorRef,
  ) {
    this.subscriptions.push(
      this.breakpointObserver
        .observe(['(max-width: 992px)'])
        .subscribe((result: BreakpointState) => {
          this.isMobile = result.matches;
          this.cd.markForCheck();
        }),
    );
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (this.message) {
      this.isAllowance =
        this.message.type === SchedulerManagementMessageType.ALLOWANCE;
    } else {
      this.isAllowance = false;
    }
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((subscription) => {
      subscription.unsubscribe();
    });
  }

  /**
   * Remove character from name
   * @param name
   */
  public getName(name: string): string {
    return name.replace(/\//g, '');
  }

  /**
   * Convert cron to a human readable text
   * @param cron
   */
  public getReadableCron(cron: string): string {
    let cronstrue = require('cronstrue/i18n');
    return cronstrue.toString(cron, {
      locale: this.translateService.currentLang,
    });
  }
}
