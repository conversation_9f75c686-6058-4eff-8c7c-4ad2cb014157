import { BaseModel } from './base-model.model';
import { OperationalContactInformation } from '@app/model/operational-contact-information.model';

export class Prestation<PERSON>erson extends BaseModel {
  firstName: string;
  lastName: string;
  isProfessional: boolean;
  isVolunteer: boolean;
  isTrainee: boolean;
  tic: number;
  civilStatus: string;
  cgdisRegistrationNumber: string;
  operationalContactInformation: OperationalContactInformation;

  constructor(args: PrestationPerson) {
    super(args);
    this.firstName = args.firstName;
    this.lastName = args.lastName;
    this.isProfessional = args.isProfessional;
    this.isVolunteer = args.isVolunteer;
    this.isTrainee = args.isTrainee;
    this.tic = args.tic;
    this.civilStatus = args.civilStatus;
    this.cgdisRegistrationNumber = args.cgdisRegistrationNumber;
    this.operationalContactInformation = args.operationalContactInformation;
  }
}
