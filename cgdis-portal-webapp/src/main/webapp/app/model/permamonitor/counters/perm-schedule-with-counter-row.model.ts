import { PermScheduleWithCounterRowSlotModel } from '@app/model/permamonitor/counters/perm-schedule-with-counter-row-slot.model';
import { ServicePlan } from '@app/model/service-plan.model';

export class PermScheduleWithCounterRowModel {
  servicePlan: ServicePlan;
  slots: PermScheduleWithCounterRowSlotModel[];

  constructor(args: Partial<PermScheduleWithCounterRowModel>) {
    this.servicePlan = args.servicePlan;
    this.slots = args.slots;
  }
}
