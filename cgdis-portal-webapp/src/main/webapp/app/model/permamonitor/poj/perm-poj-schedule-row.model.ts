import { DateModel } from '@eportal/core';
import { PermPojScheduleRowDetailModel } from '@app/model/permamonitor/poj/perm-poj-schedule-row-detail.model';

export class PermPojScheduleRowModel {
  date: DateModel;
  hours: Map<number, PermPojScheduleRowDetailModel>;
  total: number;
  totalReady: number;
  minReady: number;
  avgReady: number;
  pojOptimal: number;
  pojCritical: number;
  pojUnacceptable: number;

  constructor(args: Partial<PermPojScheduleRowModel>) {
    this.date = args.date;
    this.hours = args.hours;
    this.total = args.total;
    this.totalReady = args.totalReady;
    this.minReady = args.minReady;
    this.avgReady = args.avgReady;
    this.pojOptimal = args.pojOptimal;
    this.pojCritical = args.pojCritical;
    this.pojUnacceptable = args.pojUnacceptable;
  }
}
