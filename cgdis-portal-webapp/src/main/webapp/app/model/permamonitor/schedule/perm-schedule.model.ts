import { PermScheduleRowModel } from '@app/model/permamonitor/schedule/perm-schedule-row.model';
import { DatetimeModel } from '@eportal/core';

export class PermScheduleModel {
  startDateTime: DatetimeModel; // Corresponds to LocalDateTime in Java
  rows: PermScheduleRowModel[]; // Corresponds to List<PermScheduleRow>

  constructor(args: Partial<PermScheduleModel>) {
    this.startDateTime = args.startDateTime;
    this.rows = args.rows;
  }
}
