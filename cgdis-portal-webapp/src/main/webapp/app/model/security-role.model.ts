import { BaseModel } from './base-model.model';

export class SecurityRole extends BaseModel {
  role: string;
  permission: string;
  parentPermission = false;
  parentPermissionDeletion = false;
  entityInheritance = false;
  domains: string[];

  constructor(args: SecurityRole) {
    super(args);
    this.role = args.role;
    this.permission = args.permission;
    this.parentPermission = args.parentPermission;
    this.parentPermissionDeletion = args.parentPermissionDeletion;
    this.entityInheritance = args.entityInheritance;
    this.domains = args.domains;
  }
}
