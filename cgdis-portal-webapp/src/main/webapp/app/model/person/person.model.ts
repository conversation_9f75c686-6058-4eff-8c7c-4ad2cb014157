import { DateModel, DatetimeModel } from '@eportal/core';
import { BaseModel } from '@app/model/base-model.model';

export class Person extends BaseModel {
  firstName?: string;
  lastName?: string;
  title?: string;
  isProfessional?: boolean;
  isVolunteer?: boolean;
  isExternal?: boolean;
  isRetired?: boolean;
  isYoungFirefighter?: boolean;
  isVeteran?: boolean;
  isProfessionalOperational?: boolean;
  isProfessionalAdmTech?: boolean;
  isIntern?: boolean;
  isCandidate?: boolean;
  isOperationalFirefighter?: boolean;
  isSupportFirefighter?: boolean;
  isSamu?: boolean;
  tic?: string;
  foreignRegistrationNumber?: string;
  iamNumber?: string;
  birthdate?: DateModel;
  birthPlace?: string;
  gender?: string;
  hiringDate?: DateModel;
  vacationDate?: DateModel;
  cgdisRegistrationNumber?: string;
  hrRegistrationNumber?: string;
  deleteDateTime?: DatetimeModel;

  constructor(args: Person) {
    super(args);
    this.firstName = args.firstName;
    this.lastName = args.lastName;
    this.title = args.title;
    this.isProfessional = args.isProfessional;
    this.isVolunteer = args.isVolunteer;
    this.isExternal = args.isExternal;
    this.isRetired = args.isRetired;
    this.isYoungFirefighter = args.isYoungFirefighter;
    this.isVeteran = args.isVeteran;
    this.isProfessionalOperational = args.isProfessionalOperational;
    this.isProfessionalAdmTech = args.isProfessionalAdmTech;
    this.isIntern = args.isIntern;
    this.isCandidate = args.isCandidate;
    this.isOperationalFirefighter = args.isOperationalFirefighter;
    this.isSupportFirefighter = args.isSupportFirefighter;
    this.isSamu = args.isSamu;
    this.tic = args.tic;
    this.foreignRegistrationNumber = args.foreignRegistrationNumber;
    this.iamNumber = args.iamNumber;
    this.birthdate = args.birthdate;
    this.birthPlace = args.birthPlace;
    this.gender = args.gender;
    this.hiringDate = args.hiringDate;
    this.vacationDate = args.vacationDate;
    this.cgdisRegistrationNumber = args.cgdisRegistrationNumber;
    this.hrRegistrationNumber = args.hrRegistrationNumber;
    this.deleteDateTime = args.deleteDateTime;
  }
}
