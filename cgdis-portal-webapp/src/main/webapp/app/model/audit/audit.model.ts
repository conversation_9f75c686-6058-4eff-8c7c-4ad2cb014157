import { BaseModel } from '@app/model/base-model.model';
import { PersonLight } from '@app/model/person/person-light.model';
import { DateModel, DatetimeModel } from '@eportal/core';
import { ServicePlan, ServicePlanNoJoin } from '@app/model/service-plan.model';
import { Entity } from '@app/model/entity.model';
import { PermConfigDayType } from '@app/common/types/PermConfigDayType';

export class Audit extends BaseModel {
  personTecid: PersonLight;

  type: string;

  actionType: string;

  actionDatetime: DatetimeModel;

  servicePlans: ServicePlanNoJoin[];

  constructor(args: Audit) {
    super(args);
    this.servicePlans = args.servicePlans;
    this.personTecid = args.personTecid;
    this.type = args.type;
    this.actionType = args.actionType;
    this.actionDatetime = args.actionDatetime;
  }
}

// --- RICI Pager Audit Model ---
export class AuditRiciPagerModel extends Audit {
  pagerTecid: number;
  pagerId: string | null;
  serialNumber: string | null;
  manufacturer: string | null;
  model: string | null;
  deliveryDate: DateModel | null;
  status: string | null; // Consider using RiciPagerStatus enum if available frontend-side
  associatedSimCardTecid: number | null;
  associatedSimCardIccid: string | null;
  assignmentType: string | null; // Consider using RiciPagerAssignmentType enum if available
  assignedPersonTecid: number | null;
  assignedPersonName: string | null;
  assignedEntityTecid: number | null;
  assignedEntityName: string | null;

  constructor(args: AuditRiciPagerModel) {
    super(args); // Call base class constructor
    this.pagerTecid = args.pagerTecid;
    this.pagerId = args.pagerId;
    this.serialNumber = args.serialNumber;
    this.manufacturer = args.manufacturer;
    this.model = args.model;
    this.deliveryDate = args.deliveryDate;
    this.status = args.status;
    this.associatedSimCardTecid = args.associatedSimCardTecid;
    this.associatedSimCardIccid = args.associatedSimCardIccid;
    this.assignmentType = args.assignmentType;
    this.assignedPersonTecid = args.assignedPersonTecid;
    this.assignedPersonName = args.assignedPersonName;
    this.assignedEntityTecid = args.assignedEntityTecid;
    this.assignedEntityName = args.assignedEntityName;
  }
}

export class AuditPrestation extends Audit {
  prestationPersonTecid: PersonLight;

  prestationDate: DateModel;

  startDatetime: DatetimeModel;

  endDatetime: DatetimeModel;

  positionName: string;

  servicePlanPortalLabel: string;

  byPassFunction: boolean;

  constructor(
    args: Audit,
    prestationPersonTecid: PersonLight,
    prestationDate: DateModel,
    startDatetime: DatetimeModel,
    endDatetime: DatetimeModel,
    positionName: string,
    servicePlanPortalLabel: string,
    byPassFunction: boolean,
  ) {
    super(args);
    this.prestationPersonTecid = prestationPersonTecid;
    this.prestationDate = prestationDate;
    this.startDatetime = startDatetime;
    this.endDatetime = endDatetime;
    this.positionName = positionName;
    this.servicePlanPortalLabel = servicePlanPortalLabel;
    this.byPassFunction = byPassFunction;
  }
}

export class AuditServicePlan extends Audit {
  servicePlanTecid: number;

  servicePlanName: string;

  servicePlanEntity: string;

  constructor(
    args: Audit,
    servicePlanTecid: number,
    servicePlanName: string,
    servicePlanEntity: string,
    servicePlanType: string,
  ) {
    super(args);
    this.servicePlanTecid = servicePlanTecid;
    this.servicePlanName = servicePlanName;
    this.servicePlanEntity = servicePlanEntity;
  }
}

export class AuditModel extends Audit {
  servicePlanModelTecid: number;

  servicePlanModelName: string;

  servicePlanModelEntity: string;

  servicePlanModelInterventionType: string;

  constructor(
    args: Audit,
    servicePlanModelTecid: number,
    servicePlanModelName: string,
    servicePlanModelEntity: string,
    servicePlanModelInterventionType: string,
  ) {
    super(args);
    this.servicePlanModelTecid = servicePlanModelTecid;
    this.servicePlanModelName = servicePlanModelName;
    this.servicePlanModelEntity = servicePlanModelEntity;
    this.servicePlanModelInterventionType = servicePlanModelInterventionType;
  }
}

export class AuditVersionModel extends Audit {
  modelVersionTecid: number;

  versionLabel: string;

  modelName: string;

  startDate: DateModel;

  endDate?: DateModel;

  constructor(args: AuditVersionModel) {
    super(args);
    this.modelVersionTecid = args.modelVersionTecid;
    this.versionLabel = args.versionLabel;
    this.modelName = args.modelName;
    this.startDate = args.startDate;
    this.endDate = args.endDate;
  }
}

export class AuditVersionServicePlan extends Audit {
  servicePlanVersionTecid: number;

  versionLabel: string;

  servicePlanName: string;

  startDate: DateModel;

  endDate?: DateModel;

  servicePlanType: string;

  constructor(args: AuditVersionServicePlan) {
    super(args);
    this.servicePlanVersionTecid = args.servicePlanVersionTecid;
    this.versionLabel = args.versionLabel;
    this.servicePlanName = args.servicePlanName;
    this.startDate = args.startDate;
    this.endDate = args.endDate;
    this.servicePlanType = args.servicePlanType;
  }
}

export class AuditCopyPrestation extends Audit {
  servicePlanName: string;

  fromDatetime: DatetimeModel;

  targetDatetime: DatetimeModel;

  constructor(
    args: Audit,
    servicePlanName: string,
    fromDatetime: DatetimeModel,
    targetDatetime: DatetimeModel,
  ) {
    super(args);
    this.servicePlanName = servicePlanName;
    this.fromDatetime = fromDatetime;
    this.targetDatetime = targetDatetime;
  }
}

export class AuditTimeSlot extends Audit {
  startDatetime: DatetimeModel;

  endDatetime: DatetimeModel;

  targetDatetime: DatetimeModel;

  constructor(
    args: Audit,
    startDatetime: DatetimeModel,
    endDatetime: DatetimeModel,
    targetDatetime: DatetimeModel,
  ) {
    super(args);
    this.startDatetime = startDatetime;
    this.endDatetime = endDatetime;
    this.targetDatetime = targetDatetime;
  }
}

export class AuditPermDeploymentPlanModel extends Audit {
  deploymentPlanTecid: number;
  name: string;
  startDate: DateModel;
  description?: string;

  constructor(args: AuditPermDeploymentPlanModel) {
    super(args);
    this.deploymentPlanTecid = args.deploymentPlanTecid;
    this.name = args.name;
    this.startDate = args.startDate;
    this.description = args.description;
  }
}

export class AuditPermServicePlanCategoryModel extends Audit {
  deploymentPlanTecid: number;
  deploymentPlanName: string;
  servicePlanTecid: number;
  servicePlanName: string;
  bookmarked?: boolean;
  categoryName?: string;
  categoryTecid?: number;
  subcategoryName?: string;
  subcategoryTecid?: number;

  constructor(args: AuditPermServicePlanCategoryModel) {
    super(args);
    this.deploymentPlanTecid = args.deploymentPlanTecid;
    this.deploymentPlanName = args.deploymentPlanName;
    this.servicePlanTecid = args.servicePlanTecid;
    this.servicePlanName = args.servicePlanName;
    this.bookmarked = args.bookmarked;
    this.categoryName = args.categoryName;
    this.categoryTecid = args.categoryTecid;
    this.subcategoryName = args.subcategoryName;
  }
}

export class AuditPermConfigDpceModel extends Audit {
  deploymentPlanTecid: number;
  deploymentPlanName?: string;
  permConfigDpceTecid: number;
  entityTecid: number;
  entity?: Entity;
  categoryName?: string;
  categoryTecid: number;
  dayValue: PermConfigDayType;
  startHour: number;
  optValue: number;
  criticalValue: number;
  unacceptableValue: number;

  constructor(args: AuditPermConfigDpceModel) {
    super(args);
    this.deploymentPlanTecid = args.deploymentPlanTecid;
    this.deploymentPlanName = args.deploymentPlanName;
    this.permConfigDpceTecid = args.permConfigDpceTecid;
    this.entityTecid = args.entityTecid;
    this.entity = args.entity;
    this.categoryName = args.categoryName;
    this.categoryTecid = args.categoryTecid;
    this.dayValue = args.dayValue;
    this.startHour = args.startHour;
    this.optValue = args.optValue;
    this.criticalValue = args.criticalValue;
    this.unacceptableValue = args.unacceptableValue;
  }
}

export class AuditPermConfigDpceCopyModel extends Audit {
  deploymentPlanTecid: number;
  deploymentPlanName?: string;
  categoryName: string;
  categoryTecid: number;
  entityTecid: number;
  entity?: Entity;
  toEntityTecid: number;
  toEntity?: Entity;
  startHour: number;
  endHour: number;
  toStartHour: number;
  toEndHour: number;

  constructor(args: AuditPermConfigDpceCopyModel) {
    super(args);
    this.deploymentPlanTecid = args.deploymentPlanTecid;
    this.deploymentPlanName = args.deploymentPlanName;
    this.categoryName = args.categoryName;
    this.categoryTecid = args.categoryTecid;
    this.entityTecid = args.entityTecid;
    this.entity = args.entity;
    this.toEntityTecid = args.toEntityTecid;
    this.toEntity = args.toEntity;
    this.startHour = args.startHour;
    this.endHour = args.endHour;
    this.toStartHour = args.toStartHour;
    this.toEndHour = args.toEndHour;
  }
}

export class AuditRiciSimCardModel extends Audit {
  simCardTecid: number;
  associatedPagerTecid?: number; // Made optional as associatedPagerId is primary
  associatedPagerId?: string; // New field for Pager's string ID
  iccid: string;
  msisdn: string;
  pin: string;
  status: string;

  constructor(args: AuditRiciSimCardModel) {
    super(args);
    this.simCardTecid = args.simCardTecid;
    this.associatedPagerTecid = args.associatedPagerTecid;
    this.associatedPagerId = args.associatedPagerId;
    this.iccid = args.iccid;
    this.msisdn = args.msisdn;
    this.pin = args.pin;
    this.status = args.status;
  }
}
