import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Input,
  On<PERSON><PERSON>roy,
  OnInit,
} from '@angular/core';
import { AuditManagementListRiciSimCardService } from '@app/audit-management/rici/simcard/audit-management-list-rici-sim-card.service';
import { FieldOption } from '@app/common/modules/form-module/model/field-option.model';
import { FormControl, UntypedFormControl } from '@angular/forms';
import { Subscription } from 'rxjs';
import { BreakpointObserver, BreakpointState } from '@angular/cdk/layout';
import { AuditRiciSimCardModel } from '@app/model/audit/audit.model';
import { NgIf } from '@angular/common';
import { SharedModule } from '@app/common/shared/shared.module';
import * as _ from 'lodash';
import { DatatableModule } from '@app/common/modules/datatable/datatable.module';
import { TranslateModule } from '@ngx-translate/core';
import { AuditManagementRiciSimCardDetailComponent } from '@app/audit-management/rici/detail/simcard/audit-management-rici-sim-card-detail.component';
import {
  EpDatatableModule,
  FilterConfig,
  SearchOperator,
} from '@eportal/components';
import { RiciRicRangeType } from '@app/model/rici/rici.model';
import { FieldGroupOption } from '@app/common/modules/form-module/model/field-group-option.model';
import { RiciSimCardStatus } from '@rici/common/enums/sim-card-status.enum';

@Component({
  selector: 'cgdis-portal-audit-management-list-rici-sim-card',
  templateUrl: './audit-management-list-rici-sim-card.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: true,
  providers: [AuditManagementListRiciSimCardService],
  imports: [
    NgIf,
    SharedModule,
    DatatableModule,
    TranslateModule,
    EpDatatableModule,
    AuditManagementRiciSimCardDetailComponent,
  ],
})
export class AuditManagementListRiciSimCardComponent
  implements OnInit, OnDestroy
{
  @Input() actionTypes: FieldOption<string>[];
  @Input() dateFormControl: UntypedFormControl;
  showFilter = false;
  isMobile: boolean = false;

  numberOfFilters: number;
  ricSimStatuses: FieldOption<string>[];

  iccidFormControl = new FormControl<string>(undefined);
  iccidFilterConfig = new FilterConfig({
    operator: SearchOperator.like,
    inUrl: true,
  });

  msisdnFormControl = new FormControl<string>(undefined);
  msisdnFilterConfig = new FilterConfig({
    operator: SearchOperator.like,
  });

  statusFormControl = new FormControl<string>(undefined);
  statusFilterConfig = new FilterConfig({
    operator: SearchOperator.eq,
  });

  pagerIdFormControl = new FormControl<string>(undefined); // Renamed from pagerFormControl
  pagerIdFilterConfig = new FilterConfig({
    // Renamed from pagerFilterConfig
    operator: SearchOperator.like, // Changed to 'like' for partial search on Pager ID string
  });

  private subscriptions: Subscription[] = [];

  constructor(
    public auditService: AuditManagementListRiciSimCardService,
    private breakpointObserver: BreakpointObserver,
    private cd: ChangeDetectorRef,
  ) {
    this.subscriptions.push(
      this.breakpointObserver
        .observe(['(max-width: 992px)'])
        .subscribe((result: BreakpointState) => {
          this.isMobile = result.matches;
          this.cd.markForCheck();
        }),
    );
  }

  cast(row: any): AuditRiciSimCardModel {
    return row as AuditRiciSimCardModel;
  }

  ngOnInit() {
    this.numberOfFilters = 0;
    this.loadStatuses();

    this.subscriptions.push(
      this.auditService.canExecuteFirstSearch().subscribe(() => {
        this.updateFilterNumber();

        // Get the ICCID from URL search params since we can't access route directly
        const urlParams = new URLSearchParams(window.location.search);
        const hashParams = new URLSearchParams(
          window.location.hash.split('?')[1] || '',
        );

        // Try to get iccid from both regular query params and hash fragment params
        const iccid = urlParams.get('iccid') || hashParams.get('iccid');

        if (iccid) {
          // Set the form control value
          this.iccidFormControl.setValue(iccid);

          // Add the filter using the form control
          this.auditService.addFilterWithFormControl(
            'riciSimCardIccid',
            this.iccidFormControl,
            this.iccidFilterConfig,
          );
          // Note: The filter for pagerId will be added via HTML template if user types in it.
          // Or can be added here if there's an initial value to filter by.

          // Trigger search to apply the filter
          this.auditService.search();
        }
      }),
    );
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((subscription) => {
      subscription.unsubscribe();
    });
  }

  updateFilterNumber() {
    this.numberOfFilters = this.auditService.getNumberOfFilters();
  }

  private loadStatuses(): void {
    // Assuming RiciSimCardStatus enum is available or fetched
    const allTypes = Object.values(RiciSimCardStatus);
    this.ricSimStatuses = _.map(allTypes, (oneType) => {
      return new FieldGroupOption({
        // Assuming translation keys follow a pattern like 'rici.ranges.types.TYPE_VALUE'
        I18NLabel: 'rici.sim.statuses.' + oneType.toLowerCase(),
        value: oneType,
      });
    });
  }
}
