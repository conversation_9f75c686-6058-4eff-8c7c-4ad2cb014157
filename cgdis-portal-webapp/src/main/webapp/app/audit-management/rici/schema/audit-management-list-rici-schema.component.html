<div class="accordion__panel">
    <cgdis-portal-button-link *ngIf="isMobile" (click)="showFilter = !showFilter; updateFilterNumber()">
        <span *ngIf="!showFilter" before-icon>{{ 'service_plan.filter-link' | translate }} ({{ numberOfFilters }})</span>
        <span *ngIf="showFilter" before-icon>{{ 'service_plan.filter-link-toclose' | translate }} ({{ numberOfFilters }})</span>
    </cgdis-portal-button-link>

    <ng-container *ngIf="isMobile">
        <div class="row search-filter" [hidden]="!showFilter">
            <!-- Common Filters -->
            <div class="col-md-2">
                <label class="form-label" [translate]="'audit.actionDateTime'"></label>
                <cgdis-portal-datatable-datepicker-filter (onValueChanged)="updateFilterNumber()" class="informations-datepicker-filter" [customFormControl]="dateFormControl" [initialValue]="dateFormControl.value" [filterName]="'actionDate'" [datatableService]="auditSchemaService"></cgdis-portal-datatable-datepicker-filter>
            </div>
            <div class="col-md-2">
                <label class="form-label" [translate]="'audit.actionType.title'"></label>
                <cgdis-portal-datatable-select-filter (onValueChanged)="updateFilterNumber()" [allowClear]="true" [possibleValues]="actionTypes" [filterName]="'actionType'" [datatableService]="auditSchemaService"></cgdis-portal-datatable-select-filter>
            </div>
            <!-- RICI Schema Specific Filters -->
            <div class="col-md-2">
                <label class="form-label" [translate]="'audit.rici.schema.schemaName'"></label>
                <cgdis-portal-datatable-text-with-null-filter (onValueChanged)="updateFilterNumber()" [customFormControl]="schemaNameFormControl" [filterName]="'riciSchemaName'" [filterConfig]="schemaNameFilterConfig" [datatableService]="auditSchemaService"></cgdis-portal-datatable-text-with-null-filter>
            </div>
            <div class="col-md-2">
                <label class="form-label" [translate]="'audit.rici.schema.schemaAlias'"></label>
                <cgdis-portal-datatable-text-with-null-filter (onValueChanged)="updateFilterNumber()" [customFormControl]="schemaAliasFormControl" [filterName]="'riciSchemaAlias'" [filterConfig]="schemaAliasFilterConfig" [datatableService]="auditSchemaService"></cgdis-portal-datatable-text-with-null-filter>
            </div>
            <div class="col-md-2">
                <label class="form-label" [translate]="'audit.rici.schema.functionCodeA'"></label>
                <cgdis-portal-datatable-text-with-null-filter (onValueChanged)="updateFilterNumber()" [customFormControl]="functionCodeANameFormControl" [filterName]="'riciSchemaFunctionCodeAName'" [filterConfig]="functionCodeANameFilterConfig" [datatableService]="auditSchemaService"></cgdis-portal-datatable-text-with-null-filter>
            </div>
            <div class="col-md-2">
                <label class="form-label" [translate]="'audit.rici.schema.functionCodeB'"></label>
                <cgdis-portal-datatable-text-with-null-filter (onValueChanged)="updateFilterNumber()" [customFormControl]="functionCodeBNameFormControl" [filterName]="'riciSchemaFunctionCodeBName'" [filterConfig]="functionCodeBNameFilterConfig" [datatableService]="auditSchemaService"></cgdis-portal-datatable-text-with-null-filter>
            </div>
            <div class="col-md-2">
                <label class="form-label" [translate]="'audit.rici.schema.functionCodeC'"></label>
                <cgdis-portal-datatable-text-with-null-filter (onValueChanged)="updateFilterNumber()" [customFormControl]="functionCodeCNameFormControl" [filterName]="'riciSchemaFunctionCodeCName'" [filterConfig]="functionCodeCNameFilterConfig" [datatableService]="auditSchemaService"></cgdis-portal-datatable-text-with-null-filter>
            </div>
            <div class="col-md-2">
                <label class="form-label" [translate]="'audit.rici.schema.functionCodeD'"></label>
                <cgdis-portal-datatable-text-with-null-filter (onValueChanged)="updateFilterNumber()" [customFormControl]="functionCodeDNameFormControl" [filterName]="'riciSchemaFunctionCodeDName'" [filterConfig]="functionCodeDNameFilterConfig" [datatableService]="auditSchemaService"></cgdis-portal-datatable-text-with-null-filter>
            </div>
        </div>
    </ng-container>

    <cgdis-portal-cgdisdatatable [datatableService]="auditSchemaService" [id]="'audit-rici-schema-list'" [sorts]="[{dir:'desc',prop:'tecid'}]" [showDetails]="isMobile" [class]="'entity__table'">
        <!-- Common Columns -->
        <ep-datatable-column [columnName]="'actionDatetime'" [flexGrow]="2">
            <ng-template epDatatableHeader><span [translate]="'audit.actionDateTime'"></span></ng-template>
            <ng-template epDatatableCell let-context>{{ cast(context.row).actionDatetime | dateTimeFormat }}</ng-template>
            <ng-template epDatatableFilter *ngIf="!isMobile">
                <cgdis-portal-datatable-datepicker-filter [customFormControl]="dateFormControl" [initialValue]="dateFormControl.value" [filterName]="'actionDate'" [datatableService]="auditSchemaService"></cgdis-portal-datatable-datepicker-filter>
            </ng-template>
        </ep-datatable-column>
        <ep-datatable-column [columnName]="'person'" [flexGrow]="2">
            <ng-template epDatatableHeader><span [translate]="'audit.person'"></span></ng-template>
            <!-- Access nested person details via personTecid -->
            <ng-template epDatatableCell let-context>{{ cast(context.row).personTecid?.firstName }} {{ cast(context.row).personTecid?.lastName }}</ng-template>
            <ng-template epDatatableFilter *ngIf="!isMobile">
                <cgdis-portal-datatable-text-with-null-filter [filterName]="'personTecid.cgdisRegistrationNumber'" [datatableService]="auditSchemaService"></cgdis-portal-datatable-text-with-null-filter>
            </ng-template>
        </ep-datatable-column>
        <ep-datatable-column [columnName]="'actionType'" [flexGrow]="1">
            <ng-template epDatatableHeader><span [translate]="'audit.actionType.title'"></span></ng-template>
            <ng-template epDatatableCell let-context>{{ 'audit.actionType.' + cast(context.row).actionType | translate }}</ng-template>
            <ng-template epDatatableFilter *ngIf="!isMobile">
                <cgdis-portal-datatable-select-filter [allowClear]="true" [possibleValues]="actionTypes" [filterName]="'actionType'" [datatableService]="auditSchemaService"></cgdis-portal-datatable-select-filter>
            </ng-template>
        </ep-datatable-column>
        <ep-datatable-column [columnName]="'cgdisRegistrationNumber'" [flexGrow]="1">
            <ng-template epDatatableHeader><span [translate]="'audit.cgdisRegistrationNumber'"></span></ng-template>
            <!-- Access nested person details via personTecid -->
            <ng-template epDatatableCell let-context>{{ cast(context.row).personTecid?.cgdisRegistrationNumber }}</ng-template>
            <!-- Filter is handled by the 'person' column filter -->
        </ep-datatable-column>

        <!-- RICI Schema Specific Columns -->
        <ep-datatable-column [columnName]="'schemaName'" [flexGrow]="2">
            <ng-template epDatatableHeader><span [translate]="'audit.rici.schema.schemaName'"></span></ng-template>
            <ng-template epDatatableCell let-context>{{ cast(context.row).schemaName }}</ng-template>
            <ng-template epDatatableFilter *ngIf="!isMobile">
                <cgdis-portal-datatable-text-with-null-filter [customFormControl]="schemaNameFormControl" [filterName]="'riciSchemaName'" [filterConfig]="schemaNameFilterConfig" [datatableService]="auditSchemaService"></cgdis-portal-datatable-text-with-null-filter>
            </ng-template>
        </ep-datatable-column>
        <ep-datatable-column [columnName]="'schemaAlias'" [flexGrow]="2">
            <ng-template epDatatableHeader><span [translate]="'audit.rici.schema.schemaAlias'"></span></ng-template>
            <ng-template epDatatableCell let-context>{{ cast(context.row).schemaAlias }}</ng-template>
            <ng-template epDatatableFilter *ngIf="!isMobile">
                <cgdis-portal-datatable-text-with-null-filter [customFormControl]="schemaAliasFormControl" [filterName]="'riciSchemaAlias'" [filterConfig]="schemaAliasFilterConfig" [datatableService]="auditSchemaService"></cgdis-portal-datatable-text-with-null-filter>
            </ng-template>
        </ep-datatable-column>
        <ep-datatable-column [columnName]="'functionCodeA'" [flexGrow]="2">
            <ng-template epDatatableHeader><span [translate]="'audit.rici.schema.functionCodeA'"></span></ng-template>
            <ng-template epDatatableCell let-context>{{ cast(context.row).functionCodeAName }}</ng-template>
            <ng-template epDatatableFilter *ngIf="!isMobile">
                <cgdis-portal-datatable-text-with-null-filter [customFormControl]="functionCodeANameFormControl" [filterName]="'riciSchemaFunctionCodeAName'" [filterConfig]="functionCodeANameFilterConfig" [datatableService]="auditSchemaService"></cgdis-portal-datatable-text-with-null-filter>
            </ng-template>
        </ep-datatable-column>
        <ep-datatable-column [columnName]="'functionCodeB'" [flexGrow]="2">
            <ng-template epDatatableHeader><span [translate]="'audit.rici.schema.functionCodeB'"></span></ng-template>
            <ng-template epDatatableCell let-context>{{ cast(context.row).functionCodeBName }}</ng-template>
            <ng-template epDatatableFilter *ngIf="!isMobile">
                <cgdis-portal-datatable-text-with-null-filter [customFormControl]="functionCodeBNameFormControl" [filterName]="'riciSchemaFunctionCodeBName'" [filterConfig]="functionCodeBNameFilterConfig" [datatableService]="auditSchemaService"></cgdis-portal-datatable-text-with-null-filter>
            </ng-template>
        </ep-datatable-column>
        <ep-datatable-column [columnName]="'functionCodeC'" [flexGrow]="2">
            <ng-template epDatatableHeader><span [translate]="'audit.rici.schema.functionCodeC'"></span></ng-template>
            <ng-template epDatatableCell let-context>{{ cast(context.row).functionCodeCName }}</ng-template>
            <ng-template epDatatableFilter *ngIf="!isMobile">
                <cgdis-portal-datatable-text-with-null-filter [customFormControl]="functionCodeCNameFormControl" [filterName]="'riciSchemaFunctionCodeCName'" [filterConfig]="functionCodeCNameFilterConfig" [datatableService]="auditSchemaService"></cgdis-portal-datatable-text-with-null-filter>
            </ng-template>
        </ep-datatable-column>
        <ep-datatable-column [columnName]="'functionCodeD'" [flexGrow]="2">
            <ng-template epDatatableHeader><span [translate]="'audit.rici.schema.functionCodeD'"></span></ng-template>
            <ng-template epDatatableCell let-context>{{ cast(context.row).functionCodeDName }}</ng-template>
            <ng-template epDatatableFilter *ngIf="!isMobile">
                <cgdis-portal-datatable-text-with-null-filter [customFormControl]="functionCodeDNameFormControl" [filterName]="'riciSchemaFunctionCodeDName'" [filterConfig]="functionCodeDNameFilterConfig" [datatableService]="auditSchemaService"></cgdis-portal-datatable-text-with-null-filter>
            </ng-template>
        </ep-datatable-column>

        <!-- Mobile Detail Template -->
        <ng-template #template let-row="row">
            <div *ngIf="row.tecid">
                <cgdis-portal-audit-management-rici-schema-detail [audit]="row"></cgdis-portal-audit-management-rici-schema-detail>
            </div>
        </ng-template>
    </cgdis-portal-cgdisdatatable>
</div>
