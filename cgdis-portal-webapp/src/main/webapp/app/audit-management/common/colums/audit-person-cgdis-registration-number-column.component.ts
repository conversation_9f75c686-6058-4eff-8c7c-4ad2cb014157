import {
  ChangeDetectionStrategy,
  Component,
  forwardRef,
  Input,
} from '@angular/core';
import { CgdisDatatableColumnComponent } from '@app/common/modules/datatable/cgdisdatatable-column.component';
import { Audit } from '@app/model/audit/audit.model';
import { CgdisDatatableService } from '@app/common/modules/datatable/cgdisdatatable-service';

@Component({
  selector: 'cgdis-portal-audit-person-cgdis-registration-number-column',
  templateUrl: './audit-person-cgdis-registration-number-column.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,

  providers: [
    {
      provide: CgdisDatatableColumnComponent,
      useExisting: forwardRef(
        () => AuditPersonCgdisRegistrationNumberColumnComponent,
      ),
    },
  ],
})
export class AuditPersonCgdisRegistrationNumberColumnComponent extends CgdisDatatableColumnComponent<Audit> {
  @Input() auditService: CgdisDatatableService<Audit>;
}
