package lu.fujitsu.ts.cgdis.portal.core.domain.esb.notification.prestation;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lu.fujitsu.ts.cgdis.portal.core.domain.esb.notification.GenericNotifierRequest;
import lu.fujitsu.ts.cgdis.portal.core.domain.esb.notification.NotificationType;
import lu.fujitsu.ts.cgdis.portal.core.domain.person.IPersonTecid;

import java.time.LocalDateTime;

/**
 * The type Prestation creation notifier request.
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PrestationUpdateNotifierRequest extends GenericNotifierRequest implements IPersonTecid {

    /**
     * The Tecid.
     */
    private Long tecid;

    /**
     * The Person tecid.
     */
    private Long personTecid;
    /**
     * The Start date.
     */
    private LocalDateTime startDate;
    /**
     * The End date.
     */
    private LocalDateTime endDate;
    /**
     * The Service plan name.
     */
    private String servicePlanName;
    /**
     * The Position name.
     */
    private String positionName;

    /**
     * The Entity name.
     */
    private String entityName;

    /**
     * barrcked or not.
     */
    private Boolean barracked;

    /**
     * professional or not.
     */
    private Boolean professional;

    public PrestationUpdateNotifierRequest() {
        super(NotificationType.PRESTATION_UPDATE);
    }

    /**
     * Instantiates a new Prestation creation notifier request.
     *
     * @param tecid the tecid
     */
    public PrestationUpdateNotifierRequest(Long tecid, Long personTecid, LocalDateTime startDate, LocalDateTime endDate, String servicePlanName, String positionName, String entityName, Boolean barracked, Boolean professional) {
        super(NotificationType.PRESTATION_UPDATE, LocalDateTime.now());
        this.tecid = tecid;
        this.personTecid = personTecid;
        this.startDate = startDate;
        this.endDate = endDate;
        this.servicePlanName = servicePlanName;
        this.positionName = positionName;
        this.entityName = entityName;
        this.barracked = barracked;
        this.professional = professional;
    }
}
