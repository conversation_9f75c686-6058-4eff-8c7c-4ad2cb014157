package lu.fujitsu.ts.cgdis.portal.core.domain;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * The type Service plan position.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class ServicePlanPositionWithFunctions extends ServicePlanPosition {

    /**
     * The Functions.
     */
    private List<OperationalFunction> functions;




}
