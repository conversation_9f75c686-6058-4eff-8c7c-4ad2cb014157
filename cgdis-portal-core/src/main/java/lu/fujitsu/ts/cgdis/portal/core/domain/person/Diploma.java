package lu.fujitsu.ts.cgdis.portal.core.domain.person;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lu.fujitsu.ts.cgdis.portal.core.domain.BaseModel;

/**
 * The type Diploma.
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class Diploma extends BaseModel {

    /**
     * The Domain.
     */
    private String ident;
    /**
     * The Type.
     */
    private String type;
    /**
     * The Name.
     */
    private String name;

}
