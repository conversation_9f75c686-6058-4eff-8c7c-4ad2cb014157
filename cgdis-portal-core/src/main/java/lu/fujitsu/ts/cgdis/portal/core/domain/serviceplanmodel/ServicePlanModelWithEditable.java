package lu.fujitsu.ts.cgdis.portal.core.domain.serviceplanmodel;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * The type Service plan model with editable and deletable infos.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class ServicePlanModelWithEditable extends ServicePlanModel {

    /**
     * Only some datas can bed edited
     * (name....... )
     */
    private boolean minimumEditable;
    /**
     * All datas can be edited
     */
    private boolean fullEditable;

    private boolean deletable;

    private boolean closurable;

    private boolean closed;
}
