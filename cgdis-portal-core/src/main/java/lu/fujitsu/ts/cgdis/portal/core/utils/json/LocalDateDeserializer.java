package lu.fujitsu.ts.cgdis.portal.core.utils.json;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.deser.std.StdDeserializer;
import lu.fujitsu.ts.cgdis.portal.core.domain.DateModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.time.LocalDate;

@Component
public class LocalDateDeserializer extends StdDeserializer<LocalDate> {


  @Autowired
  public LocalDateDeserializer() {
    super(LocalDate.class);

  }


  @Override
  public LocalDate deserialize(JsonParser parser, DeserializationContext ctxt) throws IOException {

    JsonNode node = parser.readValueAsTree();
    DateModel dateModel = null;
    if (node != null) {
      int year = node.get("year").asInt();
      int month = node.get("month").asInt();
      int day = node.get("day").asInt();
      dateModel = new DateModel(year, month, day);
    }

    LocalDate date = null;
    if (dateModel != null) {
      date = LocalDate.of(
        dateModel.getYear(),
        dateModel.getMonth(), // Month in localdate is 1 based
        dateModel.getDay());
    }
    return date;
  }


}
