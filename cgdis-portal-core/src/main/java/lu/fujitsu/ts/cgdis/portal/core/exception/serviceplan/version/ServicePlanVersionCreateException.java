package lu.fujitsu.ts.cgdis.portal.core.exception.serviceplan.version;

import lu.fujitsu.ts.eportal.server.core.exceptions.EPortalServerBusinessException;

/**
 * The type Service plan version create exception.
 */
public class ServicePlanVersionCreateException extends EPortalServerBusinessException {

    /**
     * The Service plan tecid.
     */
    private Long servicePlanTecid;


    /**
     * Instantiates a new Service plan version create exception.
     *
     * @param servicePlanTecid the service plan tecid
     */
    public ServicePlanVersionCreateException(Long servicePlanTecid) {
        this(servicePlanTecid, null);
    }

    /**
     * Instantiates a new Service plan version create exception.
     *
     * @param servicePlanTecid the service plan tecid
     * @param cause            the cause
     */
    public ServicePlanVersionCreateException(Long servicePlanTecid, String additionalMessage, Throwable cause) {
        super(additionalMessage,cause);
        this.servicePlanTecid = servicePlanTecid;
    }

    /**
     * Instantiates a new Service plan version create exception.
     *
     * @param servicePlanTecid the service plan tecid
     * @param cause            the cause
     */
    public ServicePlanVersionCreateException(Long servicePlanTecid, Throwable cause) {
        this(servicePlanTecid, null, cause);
    }

    /**
     * Instantiates a new Service plan version create exception.
     *
     * @param message          the message
     * @param servicePlanTecid the service plan tecid
     */
    public ServicePlanVersionCreateException(String message, Long servicePlanTecid) {
        this(servicePlanTecid, message, null);
    }

    @Override
    public String getMessage() {
        String message = super.getMessage();
        return String.format("Error to create new version for service plan  %s : %s", servicePlanTecid, message);
    }
}
