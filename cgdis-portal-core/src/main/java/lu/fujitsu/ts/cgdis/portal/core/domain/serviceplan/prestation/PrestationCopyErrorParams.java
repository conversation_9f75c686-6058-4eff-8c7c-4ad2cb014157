package lu.fujitsu.ts.cgdis.portal.core.domain.serviceplan.prestation;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lu.fujitsu.ts.cgdis.portal.core.domain.person.PersonLight;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class PrestationCopyErrorParams {

    /**
     * The person
     */
    private PersonLight person;

    /**
     * The date where the prestation has not been copied
     */
    private LocalDate date;

    /**
     * Start time slot
     */
    private LocalDateTime startTime;

    /**
     * End time slot
     */
    private LocalDateTime endTime;
}
