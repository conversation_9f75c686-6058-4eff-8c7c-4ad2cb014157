package lu.fujitsu.ts.cgdis.portal.core.domain.warning;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lu.fujitsu.ts.cgdis.portal.core.domain.BaseModel;

import java.time.LocalDateTime;

/**
 * The type Warning message.
 */
@Data
@EqualsAndHashCode(callSuper = true,exclude = "creationDate")
@NoArgsConstructor
public class WarningMessage extends BaseModel {


    /**
     * The Warning type.
     */
    private String warningType;


    /**
     * The Creation date.
     */
    private LocalDateTime creationDate;

}
