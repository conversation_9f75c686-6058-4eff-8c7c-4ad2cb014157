package lu.fujitsu.ts.cgdis.portal.core.pojo.permamonitor.config;

import java.time.LocalDate;
import java.util.*;

public class PermConfigDpceCriticityDboInfoList {

    private Map<LocalDate, Map<Short, PermConfigDpceCriticityDboInfo>> criticityByHDateAndHourMap;

    public PermConfigDpceCriticityDboInfoList() {
        criticityByHDateAndHourMap = new HashMap<>();
    }


    public List<LocalDate> getAllDates() {
        return new ArrayList<>(criticityByHDateAndHourMap.keySet());
    }

    public Map<Short, PermConfigDpceCriticityDboInfo> getCriticityByDate(LocalDate hDate) {
        return criticityByHDateAndHourMap.getOrDefault(hDate, new HashMap<>());
    }

    public Optional<PermConfigDpceCriticityDboInfo> getCriticityByDateAndHour(LocalDate hDate, Short hour) {
        return Optional.ofNullable(criticityByHDateAndHourMap.getOrDefault(hDate, new HashMap<>()).get(hour));
    }


    public void add(LocalDate date, PermConfigDpceCriticityDboInfo info) {
        criticityByHDateAndHourMap.computeIfAbsent(date, theDate -> new HashMap<>())
                .put(info.getStartHour(), info);
    }
}
