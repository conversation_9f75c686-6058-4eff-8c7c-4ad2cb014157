package lu.fujitsu.ts.cgdis.portal.core.service.assignment;

import lu.fujitsu.ts.cgdis.portal.core.domain.CGDISSecurityConstraint;
import lu.fujitsu.ts.cgdis.portal.core.domain.assignement.Assignment;
import lu.fujitsu.ts.cgdis.portal.core.domain.assignement.AssignmentStatus;
import lu.fujitsu.ts.cgdis.portal.core.domain.enums.AssignmentType;
import lu.fujitsu.ts.cgdis.portal.core.security.ICGDISSecurityConstraint;
import lu.fujitsu.ts.eportal.server.core.business.services.ISimpleROBusinessService;
import lu.fujitsu.ts.eportal.server.core.domain.SearchCriterion;
import lu.fujitsu.ts.eportal.server.core.exceptions.NotFoundException;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;

import java.time.LocalDate;
import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * The interface Assignment business service
 */
public interface IAssignmentBusinessService extends ISimpleROBusinessService<Assignment, Long> {

    /**
     * Get the current assignment for the given person and entity
     *
     * @param securityConstraint the security constraint
     * @param entityId           the entity id
     * @param personId           the person id
     * @return list of assignments
     * @throws NotFoundException the not found exception
     */
    List<Assignment> findAllCurrentAssignmentsForEntityAndPerson(CGDISSecurityConstraint securityConstraint, Long entityId, Long personId) throws NotFoundException;

    /**
     * Get the current assignment for the given person and entity
     *
     * @param securityConstraint the security constraint
     * @param personId           the person id
     * @return list of assignments
     * @throws NotFoundException the not found exception
     */
    List<Assignment> findAllAssignmentsForPerson(CGDISSecurityConstraint securityConstraint, Long personId) throws NotFoundException;

    /**
     * Get the current & future assignment for the given person
     *
     * @param securityConstraint the security constraint
     * @param personId           the person id
     * @return list of assignments
     * @throws NotFoundException the not found exception
     */
    List<Assignment> findAllCurrentAndFutureAssignmentsForPerson(CGDISSecurityConstraint securityConstraint, Long personId) throws NotFoundException;

    /**
     * Search person assignment
     *
     * @param criteria    the search criteria
     * @param pageRequest the page request
     * @return the assignments
     */
    Page<? extends Assignment> searchWithPersonId(List<SearchCriterion> criteria, PageRequest pageRequest);

    /**
     * Return true if the person is assigned to entity for given date
     *
     * @param personId the person id
     * @param entityId the entity id
     * @param date     the date
     * @return assigned ?
     */
    boolean isAssigned(Long personId, Long entityId, LocalDate date);

    /**
     * Get the primary assignment of the given person
     *
     * @param personId the person id
     * @return the primary assignment
     */
    Long getPrimaryEntityTecid(Long personId);

    /**
     * Is only external boolean.
     *
     * @param personId the person id
     * @return if the personId has only external current assignment
     */
    boolean isOnlyExternal(Long personId);


    /**
     * Exist assignment for one person on entity between dates.
     * <br/>
     *
     * @param personTecid     the person tecid
     * @param entityTecid     the entity tecid
     * @param startDateTime   the start date time
     * @param endDateTime     the end date time (optional) if null, use the same value as startDateTime
     * @param assignmentTypes the assignment types
     * @return the boolean
     */
    boolean existAssignment(Long personTecid, Long entityTecid, LocalDate startDateTime, LocalDate endDateTime, Collection<AssignmentType> assignmentTypes);

    /**
     * Exist technical asssignments for entity
      * @param entityTecid
     * @return
     */
    boolean existTechnicalAssignmentsForEntity(Long entityTecid ,boolean subentities);

    /**
     * Exist assignment for all entities boolean.
     *
     * @param personTecid     the person tecid
     * @param entityTecid     the entity tecid
     * @param startDateTime   the start date time
     * @param endDateTime     the end date time
     * @param assignmentTypes the assignment types
     * @return the boolean
     */
    boolean existAssignmentForAllEntities(Long personTecid, Collection<Long> entityTecid, LocalDate startDateTime, LocalDate endDateTime, Collection<AssignmentType> assignmentTypes);

    /**
     * Get the current & future assignment for the given person
     *
     * @param securityConstraint the security constraint
     * @param personIds          the person id
     * @param allChildren        th
     * @param entityId           the entity id
     * @return list of assignments
     * @throws NotFoundException the not found exception
     */
    List<Assignment> findAllCurrentAndFutureAssignmentsForPersonsAndEnities(CGDISSecurityConstraint securityConstraint, List<Long> personIds, List<String> allChildren, Long entityId) throws NotFoundException;


    /**
     * Find all assignments for persons and dates list.
     *
     * @param securityConstraint the security constraint
     * @param personId           the person id
     * @param startDate          the start date
     * @param endDate            the end date
     * @return list
     */
    List<Assignment> findAllAssignmentsForPersonsAndDates(ICGDISSecurityConstraint securityConstraint, Long personId, LocalDate startDate, LocalDate endDate);

    /**
     * Find all assignments for a person that partially fall between the given dates.
     *
     * @param securityConstraint the security constraint
     * @param personTecid        the person tecid
     * @param startDate          the start date
     * @param endDate            the end date
     * @return the list of assignments
     */
    List<Assignment> findAllAssignmentForPersonPartiallyBetweenDates(ICGDISSecurityConstraint securityConstraint, Long personTecid, LocalDate startDate, LocalDate endDate);

    /**
     * Find all assignments for dates and entity list.
     *
     * @param securityConstraint the security constraint
     * @param entityTecid        the entity tecid
     * @param startDate          the start date
     * @param endDate            the end date
     * @return list
     */
    List<Assignment> findAllAssignmentsForDatesAndEntity(CGDISSecurityConstraint securityConstraint, Long entityTecid, LocalDate startDate, LocalDate endDate);

    /**
     * Gets all assignement status for entity.
     *
     * @param securityConstraint the security constraint
     * @param entityId           the entity id
     * @param subEntities        the sub entities
     * @return all assignement status for entity
     */
    AssignmentStatus getAllAssignementStatusForEntity(CGDISSecurityConstraint securityConstraint, Long entityId, boolean subEntities, boolean technical);

    /**
     * Check if person has at least one current assignment for any entities
     *
     * @param personTecid the person tecid
     * @param entities    the entities
     * @return boolean boolean
     */
    boolean existCurrentOrFutureAssignmentForPersonAndAnyEntities(Long personTecid, Set<String> entities);
}
