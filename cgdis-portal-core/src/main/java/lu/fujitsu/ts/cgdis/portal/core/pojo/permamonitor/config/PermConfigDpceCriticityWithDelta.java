package lu.fujitsu.ts.cgdis.portal.core.pojo.permamonitor.config;

import lombok.*;
import lombok.experimental.SuperBuilder;
import lu.fujitsu.ts.cgdis.portal.core.domain.permamonitor.config.PermConfigDpceCriticity;

/**
 * Data Transfer Object (DTO) representing a DPCE criticity configuration with delta values.
 * This class extends {@link PermConfigDpceCriticity} and adds delta threshold values
 * for different criticality levels.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@EqualsAndHashCode(callSuper = true, onlyExplicitlyIncluded = true)
@ToString(callSuper = true, onlyExplicitlyIncluded = true)
public class PermConfigDpceCriticityWithDelta extends PermConfigDpceCriticity {

    private static final long serialVersionUID = 8017582974661693523L;

    /**
     * Delta value for included in opt level.
     */
    @ToString.Include
    @Builder.Default
    Short includedOptDeltaValue = 0;

    /**
     * Delta value included in critical value.
     */
    @ToString.Include
    @Builder.Default
    Short includedCriticalDeltaValue = 0;

    /**
     * Delta value included in unacceptable value.
     */
    @ToString.Include
    @Builder.Default
    Short includedUnacceptableDeltaValue = 0;
}
