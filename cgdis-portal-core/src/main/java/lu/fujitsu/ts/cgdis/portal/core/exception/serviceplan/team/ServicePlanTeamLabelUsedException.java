package lu.fujitsu.ts.cgdis.portal.core.exception.serviceplan.team;

import lu.fujitsu.ts.cgdis.portal.core.exception.CGDISValidationException;

/**
 * The type Service plan delete exception.
 */
public class ServicePlanTeamLabelUsedException extends CGDISValidationException {

    /**
     * Instantiates a new Service plan team label used exception.
     */
    public ServicePlanTeamLabelUsedException() {
    }

    /**
     * Instantiates a new Service plan team label used exception.
     *
     * @param message the message
     */
    public ServicePlanTeamLabelUsedException(String message) {
        super(message);
    }

    /**
     * Instantiates a new Service plan team label used exception.
     *
     * @param message the message
     * @param cause   the cause
     */
    public ServicePlanTeamLabelUsedException(String message, Throwable cause) {
        super(message, cause);
    }

    /**
     * Instantiates a new Service plan team label used exception.
     *
     * @param cause the cause
     */
    public ServicePlanTeamLabelUsedException(Throwable cause) {
        super(cause);
    }

    /**
     * Instantiates a new Service plan team label used exception.
     *
     * @param message            the message
     * @param cause              the cause
     * @param enableSuppression  the enable suppression
     * @param writableStackTrace the writable stack trace
     */
    public ServicePlanTeamLabelUsedException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }
}
