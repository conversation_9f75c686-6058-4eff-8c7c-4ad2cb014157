package lu.fujitsu.ts.cgdis.portal.core.exception.serviceplan.merge;

/**
 * The type Service plan time slots merge already started exception.
 */
public class ServicePlanTimeSlotsMergeAlreadyStartedException extends ServicePlanTimeSlotsMergeException {

    /**
     * The constant MESSAGE.
     */
    private static final String MESSAGE = "The slot is already started";

    /**
     * Instantiates a new Service plan time slots merge already started exception.
     *
     * @param fromSlotTecid the from slot tecid
     * @param toSlotTecid   the to slot tecid
     */
    public ServicePlanTimeSlotsMergeAlreadyStartedException(Long fromSlotTecid, Long toSlotTecid) {
        this(MESSAGE, fromSlotTecid, toSlotTecid);
    }

    /**
     * Instantiates a new Service plan time slots merge already started exception.
     *
     * @param message       the message
     * @param fromSlotTecid the from slot tecid
     * @param toSlotTecid   the to slot tecid
     */
    public ServicePlanTimeSlotsMergeAlreadyStartedException(String message, Long fromSlotTecid, Long toSlotTecid) {
        super(message, fromSlotTecid, toSlotTecid);
    }

    /**
     * Instantiates a new Service plan time slots merge already started exception.
     *
     * @param message       the message
     * @param cause         the cause
     * @param fromSlotTecid the from slot tecid
     * @param toSlotTecid   the to slot tecid
     */
    public ServicePlanTimeSlotsMergeAlreadyStartedException(String message, Throwable cause, Long fromSlotTecid, Long toSlotTecid) {
        super(message, cause, fromSlotTecid, toSlotTecid);
    }

    /**
     * Instantiates a new Service plan time slots merge already started exception.
     *
     * @param cause         the cause
     * @param fromSlotTecid the from slot tecid
     * @param toSlotTecid   the to slot tecid
     */
    public ServicePlanTimeSlotsMergeAlreadyStartedException(Throwable cause, Long fromSlotTecid, Long toSlotTecid) {
        this(MESSAGE, cause, fromSlotTecid, toSlotTecid);
    }
}

