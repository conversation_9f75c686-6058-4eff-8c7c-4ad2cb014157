package lu.fujitsu.ts.cgdis.portal.core.domain.serviceplan.backupgroup;

import lombok.*;
import lu.fujitsu.ts.cgdis.portal.core.domain.BaseModel;
import lu.fujitsu.ts.cgdis.portal.core.domain.enums.ServicePlanCompletionStatus;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * DTO for {@link lu.fujitsu.ts.cgdis.portal.connector.portaldb.dbos.ServicePlanBackupGroupActivationHistoricDbo}
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class ServicePlanBackupGroupActivationHistoric extends BaseModel implements Serializable {

    private static final long serialVersionUID = -9212290053866469548L;

    private Long servicePlanTecid;

    private Boolean active;

    private String backupGroupForeignKey;

    private ServicePlanCompletionStatus completionStatus;

    private LocalDateTime startDatetime;
}
