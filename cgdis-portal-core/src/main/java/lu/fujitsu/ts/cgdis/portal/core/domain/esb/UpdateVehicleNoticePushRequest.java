package lu.fujitsu.ts.cgdis.portal.core.domain.esb;

import com.sun.org.apache.xpath.internal.operations.Bool;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lu.fujitsu.ts.cgdis.portal.core.domain.Vehicle;
import lu.fujitsu.ts.cgdis.portal.core.pojo.serviceplan.IGetServicePlanId;

import java.time.LocalDateTime;

/**
 * The type Update service plan push request.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class UpdateVehicleNoticePushRequest implements IGetServicePlanId {

    /**
     * The Update datetime.
     */
    private LocalDateTime updateDatetime;

    /**
     * The service plan Tecid.
     */
    private Long servicePlanId;


    /**
     * The type of action at origin of update
     */
    private VehicleNoticeType noticeType;

    private String vehicleToClean;
}
