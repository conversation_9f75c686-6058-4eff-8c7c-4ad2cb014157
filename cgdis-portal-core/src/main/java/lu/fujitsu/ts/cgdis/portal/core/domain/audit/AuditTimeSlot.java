package lu.fujitsu.ts.cgdis.portal.core.domain.audit;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import java.time.LocalDateTime;

/**
 * The type Audit timeslot.
 */
@EqualsAndHashCode(callSuper = true)
public class AuditTimeSlot extends Audit {




    /**
     * The Start datetime.
     */
    @Getter
    @Setter
    private LocalDateTime startDatetime;

    /**
     * The End datetime.
     */
    @Getter
    @Setter
    private LocalDateTime endDatetime;

    /**
     * The End datetime.
     */
    @Getter
    @Setter
    private LocalDateTime targetDatetime;


    public AuditTimeSlot() {
        super(AuditType.SLOT);

    }

}
