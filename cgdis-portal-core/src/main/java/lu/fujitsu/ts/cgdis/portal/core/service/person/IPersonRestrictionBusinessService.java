package lu.fujitsu.ts.cgdis.portal.core.service.person;

import lu.fujitsu.ts.cgdis.portal.core.domain.person.PersonRestriction;
import lu.fujitsu.ts.cgdis.portal.core.domain.person.Restriction;
import lu.fujitsu.ts.eportal.server.core.business.services.ISimpleROBusinessService;
import lu.fujitsu.ts.eportal.server.core.domain.SearchCriterion;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;

import java.util.List;

/**
 * The interface Person restriction business service
 */
public interface IPersonRestrictionBusinessService extends ISimpleROBusinessService<PersonRestriction, Long> {

    /**
     * Get all restrictions for the given person
     * @return person's restrictions
     */
    Page<? extends PersonRestriction> getRestrictions(List<SearchCriterion> criteria, PageRequest pageRequest);

    /**
     * Get all restriction types
     * @return the restriction types
     */
    List<String> getAllTypes();

    /**
     * Get all restriction descriptions
     * @return restriction descriptions
     */
    List<String> getAllDescriptions();

    /**
     * Get all restriction details
     * @return restriction descriptions
     */
    List<Restriction> getAllRestrictions();

}
