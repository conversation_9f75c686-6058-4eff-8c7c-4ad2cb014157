package lu.fujitsu.ts.cgdis.portal.core.domain.rici;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;
import lombok.experimental.SuperBuilder;
import lu.fujitsu.ts.cgdis.portal.core.domain.BaseModel;
import lu.fujitsu.ts.cgdis.portal.core.domain.CGDISEntity;


/**
 * The type Rici ric range.
 */
@Data
@NoArgsConstructor
@SuperBuilder
@EqualsAndHashCode(callSuper = true)
@FieldNameConstants
public class RiciRicRange extends BaseModel {
    /**
     * The Name.
     */
    private String name;

    /**
     * The Range start.
     */
    private Long rangeStart;

    /**
     * The Range end.
     */
    private Long rangeEnd;

    /**
     * The Type.
     */
    private RiciRicRangeType type;

    /**
     * The active allocation.
     */
    private CGDISEntity activeAllocationEntity;


    /**
     * The active allocation tecid.
     */
    private Long activeAllocationEntityTecid;

    /**
     * The occupancy rate of the RIC range.
     * Calculated as (attributed RICs / attributable RICs) * 100.
     * RICs divisible by 8 are excluded from both attributed and attributable counts.
     */
    private Double occupancyRate;
}
