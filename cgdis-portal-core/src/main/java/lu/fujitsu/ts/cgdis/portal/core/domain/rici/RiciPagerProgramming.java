package lu.fujitsu.ts.cgdis.portal.core.domain.rici;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lu.fujitsu.ts.cgdis.portal.core.domain.BaseModel;
// Assuming the view domain object is in the same package
import lu.fujitsu.ts.cgdis.portal.core.domain.rici.RiciPagerProgrammingStatusView;

import java.io.Serializable;
import java.time.LocalDateTime;


@Data
@NoArgsConstructor
@SuperBuilder
// Exclude the view from equals/hashCode as it's derived data
@EqualsAndHashCode(callSuper = true, exclude = {"programmingStatusView"})
// Optionally exclude from toString as well, depending on needs
@ToString(callSuper = true, exclude = {"programmingStatusView"})
@FieldNameConstants
public class RiciPagerProgramming extends BaseModel implements Serializable {
    private String programmingStatus;
    private LocalDateTime lastProgrammingSentDate;
    private LocalDateTime programmingUpdateDate;
    private LocalDateTime lastSuccessfulUpdateDate;
    private LocalDateTime statusChangeDate;

    /**
     * Represents the calculated programming status from the associated view.
     * This field is typically populated during read operations when joining with the view.
     */
    private RiciPagerProgrammingStatusView programmingStatusView;
}
