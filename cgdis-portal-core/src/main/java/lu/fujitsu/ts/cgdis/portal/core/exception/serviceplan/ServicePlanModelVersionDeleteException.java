package lu.fujitsu.ts.cgdis.portal.core.exception.serviceplan;

import lu.fujitsu.ts.eportal.server.core.exceptions.CannotDeleteItemException;

/**
 * The type Service plan model version delete exception.
 */
public class ServicePlanModelVersionDeleteException extends CannotDeleteItemException {

    /**
     * Instantiates a new Service plan model version delete exception.
     */
    public ServicePlanModelVersionDeleteException() {
    }

    /**
     * Instantiates a new Service plan model version delete exception.
     *
     * @param message the message
     */
    public ServicePlanModelVersionDeleteException(String message) {
        super(message);
    }

    /**
     * Instantiates a new Service plan model version delete exception.
     *
     * @param message the message
     * @param cause   the cause
     */
    public ServicePlanModelVersionDeleteException(String message, Throwable cause) {
        super(message, cause);
    }

}
