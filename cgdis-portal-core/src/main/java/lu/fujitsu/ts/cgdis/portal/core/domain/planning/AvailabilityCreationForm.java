package lu.fujitsu.ts.cgdis.portal.core.domain.planning;

import lombok.Data;
import lu.fujitsu.ts.cgdis.portal.core.domain.volunteer.Availability;
import lu.fujitsu.ts.cgdis.portal.core.domain.volunteer.AvailabilityType;
import lu.fujitsu.ts.eportal.server.core.domain.IModelCreate;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class AvailabilityCreationForm implements IModelCreate<Availability> {

  private AvailabilityType type;
  private LocalDateTime startDatetime;
  private LocalDateTime endDatetime;
  private Long personId;
  private List<EntityAvailability> entities;
  private List<Long> interventionTypes;

  @Override
  public Availability create() {
    throw new UnsupportedOperationException();
  }

  @Data
  public static class EntityAvailability {
    private long tecid;
    private Boolean acceptBarracked;

    public EntityAvailability() {

    }

    public EntityAvailability(long tecid, Boolean acceptBarracked){
      this.tecid=tecid;
      this.acceptBarracked=acceptBarracked;
    }
  }


}
