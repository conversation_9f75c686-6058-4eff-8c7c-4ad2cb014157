package lu.fujitsu.ts.cgdis.portal.core.service.export;

import lu.fujitsu.ts.cgdis.portal.core.domain.export.ExportProfile;
import lu.fujitsu.ts.cgdis.portal.core.security.ICGDISSecurityConstraint;
import lu.fujitsu.ts.eportal.server.core.business.services.IModifiableSecuredBusinessService;

import java.util.List;

/**
 * The interface Export profile business service
 */
public interface IExportProfileBusinessService extends IModifiableSecuredBusinessService<ICGDISSecurityConstraint, ExportProfile, Long> {

    /**
     * Get all export to execute
     * @return list of export profile
     */
    List<ExportProfile> getAllExportToExecute();

    /**
     * Force the execution of the given export profile
     * @param securityConstraint the security constraint
     * @param exportProfileId the export profile id
     * @return the updated export profile
     */
    ExportProfile forceExecution(ICGDISSecurityConstraint securityConstraint, Long exportProfileId);

}
