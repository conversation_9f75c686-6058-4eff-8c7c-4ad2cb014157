package lu.fujitsu.ts.cgdis.portal.core.service.permamonitor;

import lu.fujitsu.ts.cgdis.portal.core.domain.permamonitor.PermServicePlanCategory;
import lu.fujitsu.ts.cgdis.portal.core.domain.permamonitor.PermServicePlanCategoryWithSubcategory;
import lu.fujitsu.ts.cgdis.portal.core.exception.NoApplicableServicePlanModelVersionFoundException;
import lu.fujitsu.ts.cgdis.portal.core.pojo.permamonitor.filter.PermEntityScheduleFilter;
import lu.fujitsu.ts.cgdis.portal.core.pojo.permamonitor.filter.PermScheduleFilter;
import lu.fujitsu.ts.cgdis.portal.core.pojo.permamonitor.schedule.detail.PermScheduleRowDetail;
import lu.fujitsu.ts.cgdis.portal.core.pojo.permamonitor.schedule.entity.PermEntitySchedule;
import lu.fujitsu.ts.cgdis.portal.core.pojo.permamonitor.schedule.poj.PermPojSchedule;
import lu.fujitsu.ts.cgdis.portal.core.pojo.permamonitor.schedule.row.PermSchedule;
import lu.fujitsu.ts.cgdis.portal.core.pojo.permamonitor.schedule.row.counters.PermScheduleWithCountersAndCriticty;
import lu.fujitsu.ts.cgdis.portal.core.security.ICGDISSecurityConstraint;
import lu.fujitsu.ts.eportal.server.core.business.services.ISecuredROBusinessService;
import lu.fujitsu.ts.eportal.server.core.domain.SearchCriterion;
import lu.fujitsu.ts.eportal.server.core.exceptions.NotFoundException;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;

import java.time.LocalDate;
import java.util.List;

/**
 * Interface for the PermServicePlanCategory Read-Only Business Service.
 * Extends the ISecuredROBusinessService interface with specific types.
 */
public interface IPermServicePlanCategoryROBusinessService extends ISecuredROBusinessService<ICGDISSecurityConstraint, PermServicePlanCategory, Long> {

    /**
     * Retrieves the schedule based on the provided security constraint and filter.
     *
     * @param securityConstraint the security constraint to apply
     * @param filter             the filter criteria for the schedule
     * @return the filtered schedule
     */
    PermSchedule getSchedule(ICGDISSecurityConstraint securityConstraint, PermScheduleFilter filter);

    PermPojSchedule getPojSchedule(ICGDISSecurityConstraint securityConstraint, PermScheduleFilter filter);

    /**
     * Retrieves the schedule with counters based on the provided security constraint and filter.
     *
     * @param securityConstraint the security constraint to apply
     * @param filter             the filter criteria for the schedule
     * @return the filtered schedule with counters
     */
    PermScheduleWithCountersAndCriticty getScheduleWithCounters(ICGDISSecurityConstraint securityConstraint, PermScheduleFilter filter);

    /**
     * Retrieves the schedule row detail based on the provided security constraint, time slot ID, and date.
     *
     * @param securityConstraint the security constraint to apply
     * @param timeSlotTecid      the ID of the time slot
     * @param date               the date for which the schedule row detail is requested
     * @return the schedule row detail
     * @throws NoApplicableServicePlanModelVersionFoundException if no applicable service plan model version is found
     * @throws NotFoundException                                 if the schedule row detail is not found
     */
    PermScheduleRowDetail getScheduleRow(ICGDISSecurityConstraint securityConstraint, Long timeSlotTecid, LocalDate date) throws NoApplicableServicePlanModelVersionFoundException, NotFoundException;


    /**
     * Retrieves the entity schedule based on the provided security constraint and filter.
     *
     * @param securityConstraint the security constraint to apply
     * @param filter             the filter criteria for the entity schedule
     * @return the filtered entity schedule
     */
    PermEntitySchedule getEntitySchedule(ICGDISSecurityConstraint securityConstraint, PermEntityScheduleFilter filter);

    /**
     * Retrieves all PermServicePlanCategory entities for a given DeploymentPlanTecId.
     *
     * @param searchCriteria the filter criteria for the entities PermServicePlanCategory
     * @return a Page of PermServicePlanCategory entities
     */
    Page<PermServicePlanCategoryWithSubcategory> getAllServicePlansCategories(ICGDISSecurityConstraint securityConstraint, List<SearchCriterion> searchCriteria, PageRequest pageRequest);
}
