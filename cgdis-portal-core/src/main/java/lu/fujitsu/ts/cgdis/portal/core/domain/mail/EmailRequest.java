package lu.fujitsu.ts.cgdis.portal.core.domain.mail;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class EmailRequest {


    /**
     * The principal
     */
    private Email email;

    /**
     * The attachments
     */
    private List<EmailAttachment> attachments;
}
