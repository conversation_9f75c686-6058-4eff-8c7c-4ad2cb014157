package lu.fujitsu.ts.cgdis.portal.core.domain.assignement;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lu.fujitsu.ts.cgdis.portal.core.domain.person.Person;

import java.math.BigDecimal;
import java.time.Duration;

/**
 * The type Assignment allowance statistics.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AssignmentAllowanceStatistics {

    /**
     * The Cgdis registration number.
     */
    private String cgdisRegistrationNumber;
    /**
     * The Last name.
     */
    private String lastName;
    /**
     * The First name.
     */
    private String firstName;
    /**
     * The Entity name.
     */
    private String entityName;
    /**
     * The Professional.
     */
    private boolean professional;


    /**
     * The Hours totals.
     */
    private Duration hoursTotals;

    /**
     * The Hours fire.
     */
    private Duration hoursFireNoBarracked;

    /**
     * The Hours ambulance.
     */
    private Duration hoursAmbulanceNoBarracked;

    /**
     * The Hours commandment.
     */
    private Duration hoursCommandment;

    /**
     * The Hours commandment no barracked.
     */
    private Duration hoursCommandmentNoBarracked;

    /**
     * The Hours commandment.
     */
    private Duration hoursSamu;

    /**
     * The Hours samu no barracked.
     */
    private Duration hoursSamuNoBarracked;

    /**
     * The Hours commandment.
     */
    private Duration hoursGis;

    /**
     * The Hours GIS No barracked.
     */
    private Duration hoursGisNoBarracked;

    /**
     * The Hours commandment.
     */
    private Duration hoursDms;

    /**
     * The Hours Dms no barracked.
     */
    private Duration hoursDmsNoBarracked;

    /**
     * The Hours others.
     */
    private Duration hoursOthersNoBarracked;


    /**
     * The Hours fire.
     */
    private Duration hoursFireBarracked;

    /**
     * The Hours ambulance.
     */
    private Duration hoursAmbulanceBarracked;

    /**
     * The Hours others.
     */
    private Duration hoursOthersBarracked;

    private BigDecimal allowance;
    private boolean hasAllowance;

    private BigDecimal allowanceBarracked;
    private boolean hasAllowanceBarracked;

    private BigDecimal allowanceNoBarracked;
    private boolean hasAllowanceNoBarracked;

    /**
     * Add person.
     *
     * @param person the person
     */
    public void addPerson(Person person) {
        cgdisRegistrationNumber=person.getCgdisRegistrationNumber();
        lastName=person.getLastName();
        firstName=person.getFirstName();
    }
}
