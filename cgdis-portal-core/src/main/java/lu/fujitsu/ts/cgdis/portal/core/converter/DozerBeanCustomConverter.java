package lu.fujitsu.ts.cgdis.portal.core.converter;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.dozer.CustomConverter;


/**
 * this Dozer custom converter will be registered in dozer configuration with the id {@link #getId()}
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DozerBeanCustomConverter implements IDozerBeanCustomConverter {
    /**
     * The Id.
     */
    private String id;
    /**
     * The Converter.
     */
    private CustomConverter converter;

}
