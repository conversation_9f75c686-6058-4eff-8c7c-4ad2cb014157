package lu.fujitsu.ts.cgdis.portal.core.domain.rici;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;
import lombok.experimental.SuperBuilder;
import lu.fujitsu.ts.cgdis.portal.core.domain.BaseModel;
import lu.fujitsu.ts.cgdis.portal.core.domain.CGDISEntity;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@SuperBuilder
@EqualsAndHashCode(callSuper = true)
@FieldNameConstants
public class RiciRangeEntity extends BaseModel {

    /**
     * The allocation entity.
     */
    private CGDISEntity entity;

    /**
     * The allocation entity tecid.
     */
    private Long entityTecid;

    /**
     * The RIC range tecid.
     */
    private Long ricRangeTecid;

    /**
     * The start datetime when allocation was set.
     */
    private LocalDateTime startDatetime;

    /**
     * The end datetime when allocation cleared / new one was set.
     */
    private LocalDateTime endDatetime;
}
