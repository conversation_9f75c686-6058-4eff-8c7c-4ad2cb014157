package lu.fujitsu.ts.cgdis.portal.core.service.person;

import lu.fujitsu.ts.cgdis.portal.core.domain.person.DrivingLicense;
import lu.fujitsu.ts.cgdis.portal.core.domain.person.PersonDiploma;
import lu.fujitsu.ts.eportal.server.core.business.services.IModifiableBusinessService;
import lu.fujitsu.ts.eportal.server.core.domain.SearchCriterion;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;

import java.util.List;

/**
 * The interface Person diploma business service.
 */
public interface IPersonDiplomaBusinessService extends IModifiableBusinessService<PersonDiploma, Long> {

    /**
     * Get all driving license regarding the given search criteria
     * @param criteria the search criteria
     * @param pageRequest the page request
     * @return all found driving licenses
     */
    Page<? extends PersonDiploma> searchWithPersonId(List<SearchCriterion> criteria, PageRequest pageRequest);

}
