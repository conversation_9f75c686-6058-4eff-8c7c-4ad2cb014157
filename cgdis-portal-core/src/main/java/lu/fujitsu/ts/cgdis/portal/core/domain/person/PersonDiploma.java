package lu.fujitsu.ts.cgdis.portal.core.domain.person;

import lombok.*;
import lu.fujitsu.ts.cgdis.portal.core.domain.BaseModel;

import java.time.LocalDateTime;

/**
 * The type Person operational occupation.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(exclude = {
        "person"
})
@EqualsAndHashCode(exclude = {
        "person"
})
public class PersonDiploma extends BaseModel {

    /**
     * the comment
     */
    private String comment;
    /**
     * The Start date.
     */
    private LocalDateTime validFrom;
    /**
     * The End date.
     */
    private LocalDateTime validUntil;


    /**
     * The External id.
     */
    private Long externalId;

    /**
     * The Diploma.
     */
    private Diploma diploma;

    /**
     * The Person.
     */
    private Person person;

}
