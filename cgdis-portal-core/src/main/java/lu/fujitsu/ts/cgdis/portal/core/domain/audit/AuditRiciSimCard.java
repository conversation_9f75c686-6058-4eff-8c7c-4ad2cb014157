package lu.fujitsu.ts.cgdis.portal.core.domain.audit;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * Represents an audit entry for a RICI SIM card.
 * Extends the base Audit class.
 */
@Setter
@Getter
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AuditRiciSimCard extends Audit {

    /**
     * The technical ID of the SIM card.
     */
    private Long simCardTecid;

    /**
     * The technical ID of the associated pager.
     */
    private Long associatedPagerTecid;

    /**
     * The ICCID of the SIM card.
     */
    private String iccid;

    /**
     * The MSISDN of the SIM card.
     */
    private String msisdn;

    /**
     * The PIN of the SIM card.
     */
    private String pin;

    /**
     * The status of the SIM card.
     */
    private String status;

    /**
     * The string identifier (pagerId) of the associated pager.
     */
    private String associatedPagerId;


    /**
     * Constructs a new AuditRiciSimCard with the specified audit type.
     */
    public AuditRiciSimCard() {
        super(AuditType.RICI_SIM_CARD);
    }
}
