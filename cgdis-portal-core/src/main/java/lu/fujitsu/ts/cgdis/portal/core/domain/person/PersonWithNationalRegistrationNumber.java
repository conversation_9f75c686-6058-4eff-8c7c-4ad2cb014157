package lu.fujitsu.ts.cgdis.portal.core.domain.person;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * The type Person crud.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PersonWithNationalRegistrationNumber extends Person {

    /**
     * The National registration number
     */
    private byte[] nationalRegistrationNumber;

}
