package lu.fujitsu.ts.cgdis.portal.core.domain.esb.notification.assignment;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lu.fujitsu.ts.cgdis.portal.core.domain.assignement.Assignment;
import lu.fujitsu.ts.cgdis.portal.core.domain.esb.notification.GenericNotifierRequest;
import lu.fujitsu.ts.cgdis.portal.core.domain.esb.notification.NotificationType;

import java.time.LocalDateTime;
import java.util.Set;

/**
 * The type New assignment notifier request.
 */
@Getter
@Setter
@ToString
@EqualsAndHashCode(callSuper = true)
public class AssignmentClosureMemberNotifierRequest extends GenericNotifierRequest  {

    /**
     * The Assignment.
     */
    private Assignment assignment;



    /**
     * Instantiates a new New assignment notifier request.
     */
    public AssignmentClosureMemberNotifierRequest() {
        this(null);
    }

    /**
     * Instantiates a new New assignment notifier request.
     *
     * @param assignment the assignment
     */
    public AssignmentClosureMemberNotifierRequest(Assignment assignment) {
        super(NotificationType.ASSIGNMENT_CLOSURE_MEMBER, LocalDateTime.now());
        this.assignment = assignment;

    }
}
