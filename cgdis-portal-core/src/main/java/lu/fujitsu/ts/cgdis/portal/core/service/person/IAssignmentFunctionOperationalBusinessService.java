package lu.fujitsu.ts.cgdis.portal.core.service.person;

import lu.fujitsu.ts.cgdis.portal.core.domain.FunctionOperationalWithAssignment;
import lu.fujitsu.ts.cgdis.portal.core.domain.assignement.AssignmentFunctionOperational;
import lu.fujitsu.ts.cgdis.portal.core.domain.assignement.AssignmentWithFunctions;
import lu.fujitsu.ts.cgdis.portal.core.security.ICGDISSecurityConstraint;
import lu.fujitsu.ts.eportal.server.core.business.services.ISimpleROBusinessService;
import lu.fujitsu.ts.eportal.server.core.domain.SearchCriterion;
import lu.fujitsu.ts.eportal.server.core.exceptions.NotFoundException;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;

import java.util.List;

public interface IAssignmentFunctionOperationalBusinessService extends ISimpleROBusinessService<AssignmentFunctionOperational, Long> {

    Page<? extends AssignmentWithFunctions> getMapping(ICGDISSecurityConstraint securityConstraint, Long enitityId, List<Long> functionIds, String search, Boolean subentities, List<SearchCriterion> criteria, PageRequest pageRequest);

    Page<? extends FunctionOperationalWithAssignment> getPersonFunctionOperationalForPersonAndEntity(List<SearchCriterion> criteria, PageRequest pageRequest, Long personId);

    void mapPersonFunctionForEntity(ICGDISSecurityConstraint securityConstraint, Long functionOperationalId, Long assignemntId, Boolean value) throws NotFoundException;
}
