package lu.fujitsu.ts.cgdis.portal.core.domain;

import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class DatetimeModel extends DateModel {
    private Integer hours;
    private Integer minute;
    private Integer second;
    private Integer millisecond;

    @Builder(builderMethodName = "buildDatetime")
    public DatetimeModel(Integer year, Integer month, Integer day, Integer hours, Integer minute, Integer second, Integer millisecond) {
        super(year, month, day);
        this.hours = hours;
        this.minute = minute;
        this.second = second;
        this.millisecond = millisecond;
    }

    public DatetimeModel(Integer year, Integer month, Integer day) {
        super(year, month, day);
    }
}
