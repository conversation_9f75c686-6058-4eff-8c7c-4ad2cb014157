package lu.fujitsu.ts.cgdis.portal.core.pojo.prestation.split;

import lombok.Getter;
import lu.fujitsu.ts.cgdis.portal.core.domain.serviceplan.prestation.Prestation;
import lu.fujitsu.ts.cgdis.portal.core.domain.serviceplan.prestation.PrestationWithPossibleClosure;

import java.util.List;

public class PrestationSplitResultDto {

    @Getter
    private final List<PrestationWithPossibleClosure> prestations;

    @Getter
    private final List<Prestation> prestationsSplitted;


    public PrestationSplitResultDto(List<PrestationWithPossibleClosure> prestations, List<Prestation> prestationsSplitted) {
        this.prestations = prestations;
        this.prestationsSplitted = prestationsSplitted;
    }
}
