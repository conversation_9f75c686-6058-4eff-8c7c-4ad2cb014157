package lu.fujitsu.ts.cgdis.portal.core.converter;

import org.dozer.DozerConverter;

import javax.xml.datatype.DatatypeConfigurationException;
import javax.xml.datatype.DatatypeFactory;
import javax.xml.datatype.XMLGregorianCalendar;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.GregorianCalendar;

/**
 * Convert {@link XMLGregorianCalendar} to/from {@link LocalDateTime}
 * <p>
 * Created by LUXJVERC on 25/01/2017.
 */
public class XmlGregorianCalendarConverter extends DozerConverter<XMLGregorianCalendar, LocalDateTime> {

    public XmlGregorianCalendarConverter() {
        super(XMLGregorianCalendar.class, LocalDateTime.class);
    }

    @Override
    public LocalDateTime convertTo(XMLGregorianCalendar source, LocalDateTime destination) {
        if (source == null) {
            return null;
        }
        return source.toGregorianCalendar().toZonedDateTime().toLocalDateTime();
    }

    @Override
    public XMLGregorianCalendar convertFrom(LocalDateTime source, XMLGregorianCalendar destination) {
        if (source == null) {
            return null;
        }
        GregorianCalendar gregorianCalendar = GregorianCalendar.from(source.atZone(ZoneId.systemDefault()));
        try {
            return DatatypeFactory.newInstance().newXMLGregorianCalendar(gregorianCalendar);
        } catch (DatatypeConfigurationException e) {
            return null;
        }
    }
}
