package lu.fujitsu.ts.cgdis.portal.core.domain.esb.notification.availability;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lu.fujitsu.ts.cgdis.portal.core.domain.esb.notification.GenericNotifierRequest;
import lu.fujitsu.ts.cgdis.portal.core.domain.esb.notification.NotificationType;

import java.time.LocalDateTime;

/**
 * The type Availability creation notifier request.
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AvailabilityCreationNotifierRequest extends GenericNotifierRequest implements IAvailabilityNotifierRequest {

    /**
     * The Tecid.
     */
    private Long tecid;


    /**
     * Instantiates a new Availability creation notifier request.
     */
    public AvailabilityCreationNotifierRequest() {
        super(NotificationType.AVAILABILITY_CREATION);
    }

    /**
     * Instantiates a new Availability creation notifier request.
     *
     * @param tecid the tecid
     */
    public AvailabilityCreationNotifierRequest(Long tecid) {
        super(NotificationType.AVAILABILITY_CREATION, LocalDateTime.now());
        this.tecid = tecid;
    }
}
