package lu.fujitsu.ts.cgdis.portal.core.domain.rici;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;
import lombok.experimental.SuperBuilder;
import lu.fujitsu.ts.cgdis.portal.core.domain.BaseModel;
import org.springframework.data.annotation.Transient;

/**
 * The type Rici sim card.
 */
@Data
@NoArgsConstructor
@SuperBuilder
@EqualsAndHashCode(callSuper = true)
@FieldNameConstants
public class RiciSimCard extends BaseModel {
    /**
     * The ICCID.
     */
    private String iccid;

    /**
     * The MSISDN.
     */
    private String msisdn;

    /**
     * The PIN.
     */
    private String pin;

    /**
     * The Status.
     */
    private String status;

    /**
     * Transient field to hold the ID of the currently associated active pager.
     * Not persisted in the database.
     */
    @Transient
    private String activePagerId;

    /**
     * Transient field to hold the tecid of the currently associated active pager.
     * Not persisted in the database.
     */
    @Transient
    private Long activePagerTecid;

    /**
     * Transient field to hold the ID of the last associated pager (active or inactive).
     * Not persisted in the database.
     */
    @Transient
    private String lastPagerId;
}
