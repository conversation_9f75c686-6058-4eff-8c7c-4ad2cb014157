package lu.fujitsu.ts.cgdis.portal.core.domain.intervention;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lu.fujitsu.ts.cgdis.portal.core.domain.BaseModel;

import java.time.LocalDateTime;
import java.util.List;

/**
 * The type Intervention.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor()
@EqualsAndHashCode(callSuper = true)
public class Intervention extends BaseModel {

    /**
     * The intervention number
     */
    private String interventionNumber;

    /**
     * The intervention keyword
     */
    private String interventionKeyword;

    /**
     * The alarm date time
     */
    private LocalDateTime alarmDatetime;

    /**
     * The start date time
     */
    private LocalDateTime startDatetime;

    /**
     * The end date time
     */
    private LocalDateTime endDatetime;

    /**
     * The list of vehicle
     */
    private List<InterventionVehicle> vehicles;

}
