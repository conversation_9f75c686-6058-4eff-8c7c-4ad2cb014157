package lu.fujitsu.ts.cgdis.portal.core.domain.rici;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class RiciPagerWithPassword extends RiciPager {
    private RiciPagerMaintenance riciPagerMaintenance;

    public static RiciPagerWithPassword from(RiciPager pager, String maintenancePassword) {
        RiciPagerWithPassword pagerWithPassword = new RiciPagerWithPassword();
        BeanUtils.copyProperties(pager, pagerWithPassword);
        
        RiciPagerMaintenance maintenance = new RiciPagerMaintenance();
        maintenance.setMaintenancePassword(maintenancePassword);
        pagerWithPassword.setRiciPagerMaintenance(maintenance);
        
        return pagerWithPassword;
    }

    // Convenience getter for the password from the maintenance object
    public String getMaintenancePassword() {
        return this.riciPagerMaintenance != null ? this.riciPagerMaintenance.getMaintenancePassword() : null;
    }
}
