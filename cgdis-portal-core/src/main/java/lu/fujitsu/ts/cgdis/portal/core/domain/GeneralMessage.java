package lu.fujitsu.ts.cgdis.portal.core.domain;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * The type Service plan model.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class GeneralMessage extends BaseModel {

    private String name;

    private String message;

    private LocalDateTime startDateTime;

    private LocalDateTime endDateTime;

}
