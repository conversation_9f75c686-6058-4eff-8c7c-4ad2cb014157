package lu.fujitsu.ts.cgdis.portal.core.domain;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * The type Vehicle status historic.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class VehicleStatusHistoric extends BaseModel{


    /**
     * The Status.
     */
    private VehicleStatus status;


    /**
     * The Date.
     */
    private LocalDateTime date;

    /**
     * The From user.
     */
    private String fromUser;

    private String userName;

    private Long vehicleTecId;

    /**
     * The Date.
     */
    private LocalDateTime expiredDate;

    /**
     * The From user.
     */
    private String text;

}
