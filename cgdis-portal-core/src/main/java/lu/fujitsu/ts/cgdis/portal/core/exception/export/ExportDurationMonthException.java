package lu.fujitsu.ts.cgdis.portal.core.exception.export;

import lu.fujitsu.ts.cgdis.portal.core.exception.CGDISTechnicalException;

public class ExportDurationMonthException extends CGDISTechnicalException {

    /**
     * The Nb days.
     */
    private long nbMonth;
    /**
     * The Max days.
     */
    private int maxMonth;

    /**
     * Instantiates a new Export service plan duration exception.
     *
     * @param message the message
     * @param nbDays  the nb days
     * @param maxDays the max days
     */
    public ExportDurationMonthException(String message, long nbDays, int maxDays) {
        super(message);
        this.nbMonth = nbDays;
        this.maxMonth = maxDays;
    }

    /**
     * Gets nb days.
     *
     * @return the nb days
     */
    public long getNbDays() {
        return nbMonth;
    }

    /**
     * Gets max days.
     *
     * @return the max days
     */
    public int getMaxDays() {
        return maxMonth;
    }
}
