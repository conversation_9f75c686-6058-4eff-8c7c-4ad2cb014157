package lu.fujitsu.ts.cgdis.portal.core.pojo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lu.fujitsu.ts.cgdis.portal.core.pojo.date.IRangeDateTime;

import java.time.LocalDateTime;

/**
 * The type Test range date time.
 */
@Getter
@AllArgsConstructor
@Builder
public class TestRangeDateTime implements IRangeDateTime {

    private LocalDateTime startDateTime;
    private LocalDateTime endDateTime;



}
