spring:
  profiles:
    include: kafka,kafka-dev, debug, testemail,sendmailint
  kafka:
    bootstrap-servers: KAFKA1:15092
  mail:
    host: mailhost
    port: 5025
  thymeleaf:
    check-template-location: false
  boot:
    admin:
      client:
        password: eC72nnw6euatCQQPSzA
        username: Fujitsu
        url: http://docker-master-vm.fujitsubas.local:11090
        enabled: true
        instance:
          metadata:
            "user.name": FujitsuNotifier
            "user.password": "2WL7d63AgRRzECbsruR"
          prefer-ip: false
          service-base-url: http://docker-master-vm.fujitsubas.local:11091/
          name: ${application.name}
        period: 120000
  security:
    user:
      name: FujitsuNotifier
      password: "2WL7d63AgRRzECbsruR"
  datasource:
    hikari:
      connection-test-query: SELECT 1
      maximum-pool-size: 10
      minimum-idle: 10
    driver-class-name: org.mariadb.jdbc.Driver
    url: ****************************************************************************
    username: cgdis_portal_user
    password: SRnP5t2C7fvkgtSB
#    properties:
#      mail:
#        smtp:
#          port: 25
#          auth: true
#          starttls:
#            enable: false
#            required: false
#        transport:
#          protocol:smtp
#    username: user
#    password: password
cgdis-portal:
  notifier:
  connector:
    ad:
      domain: DOM.AD.FUJITSU
      url: ldaps://drupal.fujitsubas.lu:636/
      mapping:
        ou: PortailCGDIS
        authorities:
          - groupSuffix: ChefdeCentre
            authority: ROLE_CENTER_CHIEF
          - groupSuffix: ChefdeZone
            authority: ROLE_ZONE_CHIEF
          - groupSuffix: Volunteer
            authority: ROLE_MEMBER
          - groupSuffix: Volunteer
            authority: ROLE_MEMBER
          - groupSuffix: Volontaire
            authority: ROLE_MEMBER
          - groupSuffix: Perma_Gest
            authority: ROLE_PERMANENCE_MANAGEMENT
          - groupSuffix: Perma_Gest_Light
            authority: ROLE_PERMANENCE_MANAGEMENT_LIGHT
          - groupSuffix: Perma_Gest_Global
            authority: ROLE_PERMANENCE_MANAGEMENT_GLOBAL
          #          - groupSuffix: PermaSAP_Gest
          #            authority: ROLE_MEMBER
          #          - groupSuffix: PermalNCSA_GEST
          #            authority: ROLE_MEMBER
          - groupSuffix: Personnes_Audit
            authority: ROLE_PERSON_AUDIT
          - groupSuffix: Personnes_Audit_Global
            authority: ROLE_PERSON_AUDIT_GLOBAL
          - groupSuffix: Personnes_Gest
            authority: ROLE_PERSON_MANAGEMENT
          - groupSuffix: Personnes_Gest_Global
            authority: ROLE_PERSON_MANAGEMENT_GLOBAL
          - groupSuffix: Vehicules_Audit
            authority: ROLE_VEHICLE_AUDIT
          - groupSuffix: Vehicules_Gest
            authority: ROLE_VEHICLE_MANAGEMENT
          - groupSuffix: Admin
            authority: ROLE_ADMIN
          - groupSuffix: AdminOperational
            authority: ROLE_ADMIN_OPERATIONAL
          - groupSuffix: Audit
            authority: ROLE_AUDIT
          - groupSuffix: DATAELS
            authority: ROLE_DCO_DATA_ELS
          - groupSuffix: DPVol
            authority: ROLE_DCO_VOLUNTEER
          - groupSuffix: Helpdesk
            authority: ROLE_HELPDESK
          - groupSuffix: Export_Gest
            authority: ROLE_MANAGEMENT_EXPORT
          - groupSuffix: DMLPagerGest
            authority: ROLE_PAGER_MANAGEMENT
          - groupSuffix: DCOCGOCoord
            authority: ROLE_DCOCGO_COORDINATOR
          - groupSuffix: DCOCGOAssistant
            authority: ROLE_DCOCGO_ASSISTANT
          - groupSuffix: _DCODCOAdministratif
            authority: ROLE_DCODCO_ADMINISTRATIF
          - groupSuffix: _INFSSecretariat
            authority: ROLE_INFS_SECRETARIAT
          - groupSuffix: _MoniteurJeunesP
            authority: ROLE_MONITEUR_JEUNES
          - groupSuffix: _CCIE
            authority: ROLE_CHEF_COMPAGNIE
          - groupSuffix: _directeur
            authority: ROLE_DIRECTOR
          - groupSuffix: _DMSCoordSAMU
            authority: ROLE_DMS_COORDINATOR_SAMU
          - groupSuffix: _Astreintes_Audit
            authority: ROLE_ASTREINTE_AUDIT
          - groupSuffix: _DAFCompta
            authority: ROLE_DAF_COMPTABILITE
          - groupSuffix: _FO_Gest
            authority: ROLE_FO_GEST
          - groupSuffix: _FO_Gest_Light
            authority: ROLE_FO_GEST_LIGHT
          - groupSuffix: DMSOSCSU
            authority: ROLE_DMS_OFFICIER_SANTE_CSU
          - groupSuffix: DCOCSUCds
            authority: ROLE_DCOCSU_CHEFDESALLE
          - groupSuffix: DCOCSUCadre
            authority: ROLE_DCOCSU_CADRE
          - groupSuffix: DCOCSUReferent
            authority: ROLE_DCOCSU_REFERENT
          - groupSuffix: DCOCSURegulateur
            authority: ROLE_DCOCSU_REGULATEUR
          - groupSuffix: DCOCSUDispatcher
            authority: ROLE_DCOCSU_DISPATCHER
          - groupSuffix: DCOCSUSuppIT
            authority: ROLE_DCOCSU_SUPPORT_IT
          - groupSuffix: INFS_Gestionnaire
            authority: ROLE_INFS_STAGE_GEST
      technical-user: jcv
      technical-password: "Welcome1*"
      use-space-in-entity-name: true

    db:
      show-sql: true
      format-sql: true
    email:
      default-sender:  <EMAIL>
      default-noreply: "[Portail CGDIS] %s <<EMAIL>>"
      test-subject-pattern: "[ TEST - NOTIFIER - INT ] %s"
      test-body-pattern: "<p>TO=%s<br/>CC=%s<br/>BCC=%s<br/>REPLYTO=%s</p> %s"
      test-recipients:
        - "<EMAIL>"
management:
  endpoints:
    enabled-by-default: true
    web:
      exposure:
        include: '*'
