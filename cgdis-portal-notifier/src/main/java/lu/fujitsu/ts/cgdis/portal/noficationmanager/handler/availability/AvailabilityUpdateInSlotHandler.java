package lu.fujitsu.ts.cgdis.portal.noficationmanager.handler.availability;

import lu.fujitsu.ts.cgdis.portal.core.domain.esb.notification.availability.AvailabilityUpdateNotifierRequest;
import lu.fujitsu.ts.cgdis.portal.core.domain.volunteer.Availability;
import lu.fujitsu.ts.cgdis.portal.noficationmanager.config.properties.NotifierProperties;
import lu.fujitsu.ts.cgdis.portal.noficationmanager.domain.availability.AvailabilityUpdateSendNotification;
import lu.fujitsu.ts.cgdis.portal.services.serviceplan.IServicePlanRepositoryService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Set;
import java.util.stream.Collectors;

/**
 * The type Availability in slot handler.
 */
@Component
public class AvailabilityUpdateInSlotHandler extends AbstractAvailabilityInSlotHandler<AvailabilityUpdateNotifierRequest, AvailabilityUpdateSendNotification>  {

    /**
     * The constant LOGGER.
     */
    private static final Logger LOGGER = LoggerFactory.getLogger(AvailabilityUpdateInSlotHandler.class);

    /**
     * The Service plan repository service.
     */
    @Autowired
    private IServicePlanRepositoryService servicePlanRepositoryService;


    @Autowired
    public AvailabilityUpdateInSlotHandler(NotifierProperties notifierProperties) {
        super(notifierProperties);
    }

    @Override
    protected Set<Long> getEntities(AvailabilityUpdateNotifierRequest payload, Availability availability) {

        Availability previousValue = payload.getPreviousValue();
        Set<Long> newEntities = availability.getEntitiesAvailability().stream().map(ae -> ae.getEntity().getTecid()).collect(Collectors.toSet());
        if ( !availability.getStartDateTime().equals(previousValue.getStartDateTime()) || !availability.getEndDateTime().equals(previousValue.getEndDateTime())){
            LOGGER.info("start or end datetime of availability updated => send notification for all entities");
        } else {
            Set<Long> previousEntitites = previousValue.getEntitiesAvailability().stream().map(ae -> ae.getEntity().getTecid()).collect(Collectors.toSet());
            LOGGER.info("entities of availability updated => send notification for all entities added");
            newEntities = newEntities.stream()
                    .filter(oneEntity -> !previousEntitites.contains(oneEntity) )
                    .collect(Collectors.toSet());
        }


        return newEntities;
    }

    @Override
    protected AvailabilityUpdateSendNotification getAvailabilitySendNotification(Availability availability, String oneIam) {
        return new AvailabilityUpdateSendNotification(availability, oneIam );
    }
}
