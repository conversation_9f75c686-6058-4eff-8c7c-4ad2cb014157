package lu.fujitsu.ts.cgdis.portal.noficationmanager.domain;

import lombok.Data;
import lu.fujitsu.ts.cgdis.portal.core.domain.esb.notification.NotificationType;
import lu.fujitsu.ts.cgdis.portal.core.domain.notification.PrestationQueue;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * The type Prestation queue mail context.
 */
@Data
public class PrestationQueueMailContext {


    /**
     * The Prestations created.
     */
    private List<PrestationQueue> prestationsCreated;

    /**
     * The Prestations updated.
     */
    private List<PrestationQueue> prestationsUpdated;

    /**
     * The Prestations deleted.
     */
    private List<PrestationQueue> prestationsDeleted;


    public PrestationQueueMailContext(List<PrestationQueue> prestationQueues) {
        init(prestationQueues);
    }

    public PrestationQueueMailContext() {
        init(null);
    }

    private void init(List<PrestationQueue> prestationQueues) {
        if (prestationQueues != null) {
            Map<NotificationType, List<PrestationQueue>> grouped = prestationQueues.stream().collect(Collectors.groupingBy(PrestationQueue::getNotificationType));
            prestationsCreated = grouped.getOrDefault(NotificationType.PRESTATION_CREATION, new ArrayList<>());
            prestationsCreated.sort(Comparator.comparing(PrestationQueue::getServicePlanName).thenComparing(PrestationQueue::getStartDatetime));
            prestationsDeleted = grouped.getOrDefault(NotificationType.PRESTATION_DELETION, new ArrayList<>());
            prestationsDeleted.sort(Comparator.comparing(PrestationQueue::getServicePlanName).thenComparing(PrestationQueue::getStartDatetime));
            prestationsUpdated = grouped.getOrDefault(NotificationType.PRESTATION_UPDATE, new ArrayList<>());
            prestationsUpdated.sort(Comparator.comparing(PrestationQueue::getServicePlanName).thenComparing(PrestationQueue::getStartDatetime));
        } else {
            prestationsCreated = new ArrayList<>();
            prestationsDeleted = new ArrayList<>();
            prestationsUpdated = new ArrayList<>();
        }

    }

    public int getTotalPrestations() {
        return prestationsCreated.size() + prestationsUpdated.size() + prestationsDeleted.size();
    }


    public List<PrestationQueue> getAllPrestations() {
        List<PrestationQueue> allPrestations = new ArrayList<>();
        allPrestations.addAll(prestationsCreated);
        allPrestations.addAll(prestationsUpdated);
        allPrestations.addAll(prestationsDeleted);
        return allPrestations;
    }

}
