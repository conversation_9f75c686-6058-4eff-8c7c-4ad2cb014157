package lu.fujitsu.ts.cgdis.portal.noficationmanager.security;

import lu.fujitsu.ts.cgdis.portal.connector.portaldb.repositories.ICGDISEntityRepository;
import lu.fujitsu.ts.cgdis.portal.core.domain.CGDISPortalUserDetails;
import lu.fujitsu.ts.cgdis.portal.core.domain.ICGDISPortalUserDetails;
import lu.fujitsu.ts.cgdis.portal.core.security.Permission;
import org.apache.commons.collections4.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

public class NotificationUserDetails extends CGDISPortalUserDetails implements ICGDISPortalUserDetails {

    private ICGDISEntityRepository entityRepositoryService;

    public NotificationUserDetails(ICGDISEntityRepository entityRepositoryService) {
        this.entityRepositoryService = entityRepositoryService;
        setTecid(-1L);
    }

    @Override
    public boolean isSuperUser() {
        return true;
    }

    @Override
    public Set<String> getAccessibleEntities() {
        List<String> entityNames = this.entityRepositoryService.findDistinctColumns(
                String.class,
                (root, query, criteriaBuilder) -> criteriaBuilder.conjunction(),
                (root, criteriaQuery, criteriaBuilder) -> root.get("name")
        );
        return new HashSet<>(entityNames);
    }

    @Override
    public boolean allEntitiesAccessible() {
        return true;
    }

    @Override
    public boolean hasAuthorityForEntity(String authority, String entityName) {
        return true;
    }

    @Override
    public boolean hasAuthorityForEntity(String authority, Long entityId) {
        return true;
    }

    @Override
    public boolean hasAuthorityForAnyEntity(String authority) {
        return true;
    }

    @Override
    public boolean hasPermissionForAnyEntity(Permission permission) {
        return true;
    }

    @Override
    public boolean hasAnyPermissionForAnyEntity(Collection<Permission> permissions) {
        return true;
    }

    @Override
    public boolean hasAnyAuthoritiesForAnyEntity(Collection<String> permissions) {
        return true;
    }

    @Override
    public Set<String> getAllowedDomains(Collection<String> authorities) {
        return getAccessibleEntities();
    }

    @Override
    public Set<Long> getAccessibleEntitiesIds() {
        List<Long> entityIds = this.entityRepositoryService.findDistinctColumns(
                Long.class,
                (root, query, criteriaBuilder) -> criteriaBuilder.conjunction(),
                (root, criteriaQuery, criteriaBuilder) -> root.get("tecid")
        );
        return new HashSet<>(entityIds);
    }

    @Override
    public Set<Long> getAccessibleEntitiesIdsByEntityName(Collection<String> entityNames) {
        List<Long> entityIds = this.entityRepositoryService.findDistinctColumns(
                Long.class,
                (root, query, criteriaBuilder) -> CollectionUtils.isEmpty(entityNames) ? criteriaBuilder.disjunction() : root.get("name").in(entityNames),
                (root, criteriaQuery, criteriaBuilder) -> root.get("tecid")
        );
        return new HashSet<>(entityIds);
    }



    @Override
    public Collection<String> getAuthoritiesWithoutDomains() {
        return Arrays.stream(Permission.values()).map(Enum::name).collect(Collectors.toList());
    }

    @Override
    public Map<String, Set<String>> getAllowedDomainsByAuthority(Collection<String> authorities) {
        final Set<String> accessibleEntities = getAccessibleEntities();
        return authorities.stream().collect(Collectors.toMap(Function.identity(), authority -> accessibleEntities));
    }

    @Override
    public Set<Long> getAccessibleEntitiesIdsByAuthorities(Collection<String> authorities) {
        return getAccessibleEntitiesIds();
    }
    @Override
    public Map<String, Set<Long>> getAccessibleEntitiesIdsGroupedByAuthorities(Collection<String> authorities) {
        final Set<Long> accessibleEntities = getAccessibleEntitiesIds();
        return authorities.stream().collect(Collectors.toMap(Function.identity(), authority -> accessibleEntities));
    }

}
