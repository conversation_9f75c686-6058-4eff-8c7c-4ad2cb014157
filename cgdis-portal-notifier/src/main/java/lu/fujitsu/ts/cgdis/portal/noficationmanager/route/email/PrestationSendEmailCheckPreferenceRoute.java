package lu.fujitsu.ts.cgdis.portal.noficationmanager.route.email;

import lu.fujitsu.ts.cgdis.portal.connector.portaldb.services.person.PersonPreferenceRepositoryService;
import lu.fujitsu.ts.cgdis.portal.connector.portaldb.services.person.PersonRepositoryService;
import lu.fujitsu.ts.cgdis.portal.core.domain.enums.UserPreferenceKeys;
import lu.fujitsu.ts.cgdis.portal.core.domain.person.IPersonTecid;
import lu.fujitsu.ts.eportal.server.core.exceptions.NotFoundException;

public class PrestationSendEmailCheckPreferenceRoute<S extends IPersonTecid> extends AbstractSendEmailCheckPreferenceRoute<S> {

    /**
     * The Person repository service.
     */
    private PersonRepositoryService personRepositoryService;

    /**
     * Instantiates a new Send email check preference route by tecid.
     *
     * @param personPreferenceRepositoryService the person preference repository service
     */
    public PrestationSendEmailCheckPreferenceRoute(PersonPreferenceRepositoryService personPreferenceRepositoryService,PersonRepositoryService personRepositoryService) {
        super(personPreferenceRepositoryService, UserPreferenceKeys.PRESTATION_NOTIFICATION);
        this.personRepositoryService = personRepositoryService;
    }

    @Override
    protected Long getRecipientTecId(S source) throws NotFoundException {
        return source.getPersonTecid();
    }

}
