webpackJsonp([23],{TO9g:function(e,o,r){"use strict";Object.defineProperty(o,"__esModule",{value:!0});var n=r("WT6e"),t=r("FpUO"),a=r("INQx"),d=r("m3yK"),i=r("FW2E"),l=r("dbDO"),p=r("Xjw4"),m=r("ARuB"),R=r("9Sd6"),O=r("XHgV"),M=r("1T37"),s=r("+j5Y"),c=r("U/+3"),u=r("Mcof"),T=r("7u3n"),I=r("f+AH"),C=r("7DMc"),D=r("YKDW"),v=r("bfOx"),_=r("bkcK"),y=r("Uo70"),E=r("6YvO"),A=r("c/fr"),N=r("jFHr"),S=r("0hem"),L=n.\u0275cmf(t.LoginModule,[],function(e){return n.\u0275mod([n.\u0275mpd(512,n.ComponentFactoryResolver,n.\u0275CodegenComponentFactoryResolver,[[8,[a.TooltipComponentNgFactory,d.ToastContainerNgFactory,i.TokenLoginComponentNgFactory,l.TokenLoginErrorComponentNgFactory]],[3,n.ComponentFactoryResolver],n.NgModuleRef]),n.\u0275mpd(4608,p.NgLocalization,p.NgLocaleLocalization,[n.LOCALE_ID,[2,p.\u0275a]]),n.\u0275mpd(4608,m.\u0275c,m.\u0275c,[[2,"loadingConfig"]]),n.\u0275mpd(6144,R.DIR_DOCUMENT,null,[p.DOCUMENT]),n.\u0275mpd(4608,R.Directionality,R.Directionality,[[2,R.DIR_DOCUMENT]]),n.\u0275mpd(4608,O.Platform,O.Platform,[]),n.\u0275mpd(5120,M.ScrollDispatcher,M.SCROLL_DISPATCHER_PROVIDER_FACTORY,[[3,M.ScrollDispatcher],n.NgZone,O.Platform]),n.\u0275mpd(5120,M.ViewportRuler,M.VIEWPORT_RULER_PROVIDER_FACTORY,[[3,M.ViewportRuler],O.Platform,n.NgZone]),n.\u0275mpd(4608,s.ScrollStrategyOptions,s.ScrollStrategyOptions,[M.ScrollDispatcher,M.ViewportRuler,n.NgZone,p.DOCUMENT]),n.\u0275mpd(5120,s.OverlayContainer,s.\u0275a,[[3,s.OverlayContainer],p.DOCUMENT]),n.\u0275mpd(4608,s.OverlayPositionBuilder,s.OverlayPositionBuilder,[M.ViewportRuler,p.DOCUMENT]),n.\u0275mpd(5120,s.OverlayKeyboardDispatcher,s.\u0275f,[[3,s.OverlayKeyboardDispatcher],p.DOCUMENT]),n.\u0275mpd(4608,s.Overlay,s.Overlay,[s.ScrollStrategyOptions,s.OverlayContainer,n.ComponentFactoryResolver,s.OverlayPositionBuilder,s.OverlayKeyboardDispatcher,n.ApplicationRef,n.Injector,n.NgZone,p.DOCUMENT]),n.\u0275mpd(5120,s.\u0275c,s.\u0275d,[s.Overlay]),n.\u0275mpd(4608,c.InteractivityChecker,c.InteractivityChecker,[O.Platform]),n.\u0275mpd(4608,c.FocusTrapFactory,c.FocusTrapFactory,[c.InteractivityChecker,n.NgZone,p.DOCUMENT]),n.\u0275mpd(136192,c.AriaDescriber,c.ARIA_DESCRIBER_PROVIDER_FACTORY,[[3,c.AriaDescriber],p.DOCUMENT]),n.\u0275mpd(5120,c.LiveAnnouncer,c.LIVE_ANNOUNCER_PROVIDER_FACTORY,[[3,c.LiveAnnouncer],[2,c.LIVE_ANNOUNCER_ELEMENT_TOKEN],p.DOCUMENT]),n.\u0275mpd(5120,c.FocusMonitor,c.FOCUS_MONITOR_PROVIDER_FACTORY,[[3,c.FocusMonitor],n.NgZone,O.Platform]),n.\u0275mpd(4608,u.MediaMatcher,u.MediaMatcher,[O.Platform]),n.\u0275mpd(135680,u.BreakpointObserver,u.BreakpointObserver,[u.MediaMatcher,n.NgZone]),n.\u0275mpd(5120,T.MAT_TOOLTIP_SCROLL_STRATEGY,T.MAT_TOOLTIP_SCROLL_STRATEGY_PROVIDER_FACTORY,[s.Overlay]),n.\u0275mpd(5120,I.ResponsiveConfig,I.responsiveConfiguration,[I.RESPONSIVE_CONFIGURATION]),n.\u0275mpd(4608,I.ResponsiveState,I.ResponsiveState,[I.ResponsiveConfig,n.PLATFORM_ID]),n.\u0275mpd(4608,I.ResponsiveSizeInfoRx,I.ResponsiveSizeInfoRx,[I.ResponsiveState,n.PLATFORM_ID]),n.\u0275mpd(4608,I.UserAgentInfoRx,I.UserAgentInfoRx,[I.ResponsiveState,n.PLATFORM_ID]),n.\u0275mpd(4608,I.BrowserInfoRx,I.BrowserInfoRx,[I.ResponsiveState,n.PLATFORM_ID]),n.\u0275mpd(4608,I.IeInfoRx,I.IeInfoRx,[I.ResponsiveState,n.PLATFORM_ID]),n.\u0275mpd(4608,I.DeviceInfoRx,I.DeviceInfoRx,[I.ResponsiveState,n.PLATFORM_ID]),n.\u0275mpd(4608,I.DeviceStandardInfoRx,I.DeviceStandardInfoRx,[I.ResponsiveState,n.PLATFORM_ID]),n.\u0275mpd(4608,I.OrientationInfoRx,I.OrientationInfoRx,[I.ResponsiveState,n.PLATFORM_ID]),n.\u0275mpd(4608,C.\u0275i,C.\u0275i,[]),n.\u0275mpd(512,p.CommonModule,p.CommonModule,[]),n.\u0275mpd(512,D.TranslateModule,D.TranslateModule,[]),n.\u0275mpd(512,v.RouterModule,v.RouterModule,[[2,v.\u0275a],[2,v.Router]]),n.\u0275mpd(512,m.LoadingModule,m.LoadingModule,[]),n.\u0275mpd(512,R.BidiModule,R.BidiModule,[]),n.\u0275mpd(512,_.PortalModule,_.PortalModule,[]),n.\u0275mpd(512,O.PlatformModule,O.PlatformModule,[]),n.\u0275mpd(512,M.ScrollDispatchModule,M.ScrollDispatchModule,[]),n.\u0275mpd(512,s.OverlayModule,s.OverlayModule,[]),n.\u0275mpd(256,y.MATERIAL_SANITY_CHECKS,!0,[]),n.\u0275mpd(512,y.MatCommonModule,y.MatCommonModule,[[2,y.MATERIAL_SANITY_CHECKS]]),n.\u0275mpd(512,c.A11yModule,c.A11yModule,[]),n.\u0275mpd(512,u.LayoutModule,u.LayoutModule,[]),n.\u0275mpd(512,T.MatTooltipModule,T.MatTooltipModule,[]),n.\u0275mpd(512,I.ResponsiveModule,I.ResponsiveModule,[]),n.\u0275mpd(512,E.SharedModule,E.SharedModule,[]),n.\u0275mpd(512,A.ToastModule,A.ToastModule,[]),n.\u0275mpd(512,C.\u0275ba,C.\u0275ba,[]),n.\u0275mpd(512,C.FormsModule,C.FormsModule,[]),n.\u0275mpd(512,t.LoginModule,t.LoginModule,[]),n.\u0275mpd(256,T.MAT_TOOLTIP_DEFAULT_OPTIONS,{showDelay:0,hideDelay:0,touchendHideDelay:1500},[]),n.\u0275mpd(256,I.RESPONSIVE_CONFIGURATION,{breakPoints:{xs:{max:575},sm:{min:576,max:767},md:{min:768,max:991},lg:{min:992,max:1199},xl:{min:1200}},debounceTime:100},[]),n.\u0275mpd(1024,v.ROUTES,function(){return[[{path:"mobile",component:N.TokenLoginComponent},{path:"mobileerror",component:S.TokenLoginErrorComponent}]]},[])])});o.LoginModuleNgFactory=L}});