<?xml version='1.0' encoding='utf-8'?>
<widget android-packageName="{package_name}" android-versionCode="1" ios-CFBundleIdentifier="{package_name}" version="{project_version}" xmlns="http://www.w3.org/ns/widgets" xmlns:android="http://schemas.android.com/apk/res/android" xmlns:cdv="http://cordova.apache.org/ns/1.0">
    <name>{project_name}</name>
    <description>{project_description}</description>
    <author email="<EMAIL>" href="https://www.fujitsu.com">{project_author}</author>
    <content src="index.html" />
    <plugin name="cordova-plugin-whitelist" spec="1.3.3" />
    <plugin name="cordova-custom-config" spec="5.1.0" />
    <plugin name="cordova-plugin-splashscreen" spec="5.0.3" />
    <plugin name="cordova-plugin-statusbar" spec="2.4.2" />
    <plugin name="cordova-plugin-dialogs" spec="2.0.1" />
    <plugin name="cordova-plugin-inappbrowser" spec="4.0.0" />
    <plugin name="cordova-plugin-device" spec="2.0.2" />
    <plugin name="cordova-plugin-fullscreen" spec="^1.2.0" />
    <plugin name="cordova-plugin-ionic-keyboard" spec="^2.2.0" />
    <plugin name="cordova-plugin-ios-longpress-fix" spec="1.1.0" />
    <plugin name="cordova-plugin-wkwebview-file-xhr" spec="3.0.0" />
    <plugin name="cordova-plugin-qr-barcode-scanner" />
    <plugin name="cordova-plugin-androidx-adapter" spec="1.1.1" />

    <access origin="https://portailcgdis.api-stg.etat.lu/cgdis/api/**" subdomains="true"/>
    <allow-navigation href="https://portailcgdis.api-stg.etat.lu/cgdis/api/**"/>
    <allow-intent href="https://portailcgdis.api-stg.etat.lu/cgdis/api/**"/>
    <access origin="https://login.api.etat.lu/mga-int/**" subdomains="true"/>
    <allow-navigation href="https://login.api.etat.lu/mga-int/**"/>
    <allow-intent href="https://login.api.etat.lu/mga-int/**"/>

    <allow-navigation href="tel:*"/>
    <allow-navigation href="sms:*"/>
    <allow-navigation href="mailto:*"/>
    <allow-navigation href="geo:*"/>
    <allow-navigation href="cgdisportal://*"/>

    <access origin="{news_url}/**" subdomains="true" />
    <allow-navigation href="{news_url}/**" />
    <allow-intent href="{news_url}/**" />



    <preference name="scheme" value="cgdisportal" />
    <preference name="hostname" value="localhost" />


    <platform name="android">
        <allow-intent href="market:*" />
        <preference name="loadUrlTimeoutValue" value="700000" />
        <preference name="android-minSdkVersion" value="16" />
        <preference name="android-targetSdkVersion" value="24" />
        <preference name="AndroidLaunchMode" value="singleTop" />
        <preference name="backgroundColor" value="0xff000000" />
        <custom-preference name="android-manifest/application/@android:supportsRtl" value="false" />
        <custom-preference name="android-manifest/application/activity/@android:allowBackup" value="false" />
        <custom-preference name="android-manifest/application/activity/@android:filterTouchesWhenObscured" value="true" />
        <custom-preference name="android-manifest/application/activity/@android:windowSoftInputMode" value="stateAlwaysHidden" />
        <custom-preference name="android-manifest/application/activity/@android:screenOrientation" value="sensorLandscape" />
        <custom-config-file parent="/*" target="AndroidManifest.xml">
            <supports-screens android:anyDensity="true" android:largeScreens="true" android:normalScreens="true" android:resizeable="true" android:smallScreens="true" android:xlargeScreens="true" />
            <uses-permission android:name="android.permission.INTERNET" />
            <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
            <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
        </custom-config-file>
        <hook src="hooks_after_build/cordovaPostBuild.js" type="after_build" />
        <icon density="ldpi" src="res/icon/android/icon-36-ldpi.png" />
        <icon density="mdpi" src="res/icon/android/icon-48-mdpi.png" />
        <icon density="hdpi" src="res/icon/android/icon-72-hdpi.png" />
        <icon density="xhdpi" src="res/icon/android/icon-96-xhdpi.png" />
        <splash density="land-hdpi" src="res/screen/android/XXXHDPI-1920 x 1280.png" />
        <splash density="land-ldpi" src="res/screen/android/XXXHDPI-1920 x 1280.png" />
        <splash density="land-mdpi" src="res/screen/android/XXXHDPI-1920 x 1280.png" />
        <splash density="land-xhdpi" src="res/screen/android/XXXHDPI-1920 x 1280.png" />
        <splash density="land-xxhdpi" src="res/screen/android/XXXHDPI-1920 x 1280.png" />
        <splash density="land-xxxhdpi" src="res/screen/android/XXXHDPI-1920 x 1280.png" />
        <splash density="port-hdpi" src="res/screen/android/XXXHDPI-1280 X 1920.png" />
        <splash density="port-ldpi" src="res/screen/android/XXXHDPI-1280 X 1920.png" />
        <splash density="port-mdpi" src="res/screen/android/XXXHDPI-1280 X 1920.png" />
        <splash density="port-xhdpi" src="res/screen/android/XXXHDPI-1280 X 1920.png" />
        <splash density="port-xxhdpi" src="res/screen/android/XXXHDPI-1280 X 1920.png" />
        <splash density="port-xxxhdpi" src="res/screen/android/XXXHDPI-1280 X 1920.png" />
    </platform>
    <platform name="ios">
        <preference name="iosScheme" value="cgdisportal" />
        <custom-preference name="ios-XCBuildConfiguration-TARGETED_DEVICE_FAMILY" value="1,2" xcconfigEnforce="true" />
        <preference name="ios-XCBuildConfiguration-SWIFT_VERSION" value="4.2" xcconfigEnforce="true"/>
        <preference name="UseSwiftLanguageVersion" value="4.2"/>
        <preference name="BackupWebStorage" value="none" />
        <preference name="SuppressesLongPressGesture" value="true" />
        <preference name="WKWebViewOnly" value="true" />
        <feature name="CDVWKWebViewEngine">
            <param name="ios-package" value="CDVWKWebViewEngine" />
        </feature>
        <preference name="CordovaWebViewEngine" value="CDVWKWebViewEngine" />
        <allow-intent href="itms:*" />
        <allow-intent href="itms-apps:*" />
        <icon height="180" src="res/icon/ios/icon-60-3x.png" width="180" />
        <icon height="60" src="res/icon/ios/icon-60.png" width="60" />
        <icon height="120" src="res/icon/ios/icon-60-2x.png" width="120" />
        <icon height="76" src="res/icon/ios/icon-76.png" width="76" />
        <icon height="152" src="res/icon/ios/icon-76-2x.png" width="152" />
        <icon height="40" src="res/icon/ios/icon-40.png" width="40" />
        <icon height="80" src="res/icon/ios/icon-40-2x.png" width="80" />
        <icon height="57" src="res/icon/ios/icon-57.png" width="57" />
        <icon height="114" src="res/icon/ios/icon-57-2x.png" width="114" />
        <icon height="72" src="res/icon/ios/icon-72.png" width="72" />
        <icon height="29" src="res/icon/ios/icon-small.png" width="29" />
        <icon height="58" src="res/icon/ios/icon-small-2x.png" width="58" />
        <icon height="50" src="res/icon/ios/icon-50.png" width="50" />
        <icon height="1024" src="res/icon/ios/icon-1024.png" width="1024" />
        <icon height="100" src="res/icon/ios/icon-50-2x.png" width="100" />
        <splash src="res/screen/ios/Default@2x~universal~anyany.png" />
        <splash src="res/screen/ios/Default@2x~universal~comany.png" />
        <splash src="res/screen/ios/Default@2x~universal~comcom.png" />
        <splash src="res/screen/ios/Default@3x~universal~anyany.png" />
        <splash src="res/screen/ios/Default@3x~universal~anycom.png" />
        <splash src="res/screen/ios/Default@3x~universal~comany.png" />
        <custom-config-file mode="replace" parent="UISupportedInterfaceOrientations~ipad" platform="ios" target="*-Info.plist">
            <array>
                <string>UIInterfaceOrientationLandscapeLeft</string>
                <string>UIInterfaceOrientationLandscapeRight</string>
            </array>
        </custom-config-file>
        <edit-config target="NSCameraUsageDescription" file="*-Info.plist" mode="merge">
            <string>To scan barcodes</string>
        </edit-config>
    </platform>
    <preference name="AutoHideSplashScreen" value="true" />
    <preference name="StatusBarBackgroundColor" value="#FF0000" />
    <preference name="StatusBarOverlaysWebView" value="false" />
    <preference name="StatusBarStyle" value="lightcontent" />
    <preference name="DisallowOverscroll" value="true" />
    <preference name="KeyboardResize" value="true" />
    <preference name="Fullscreen" value="false" />
    <preference name="KeyboardResizeMode" value="native" />
    <preference name="ScrollEnabled" value="true" />
    <engine name="ios" spec="~6.1.1" />
    <engine name="android" spec="~6.2.2" />
    <engine name="browser" spec="~5.0.1" />
</widget>
