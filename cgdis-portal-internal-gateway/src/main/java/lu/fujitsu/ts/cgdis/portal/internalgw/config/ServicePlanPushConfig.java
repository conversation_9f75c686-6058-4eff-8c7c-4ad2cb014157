package lu.fujitsu.ts.cgdis.portal.internalgw.config;

import lu.fujitsu.ts.cgdis.portal.core.domain.esb.UpdateServicePlanPushRequest;
import lu.fujitsu.ts.cgdis.portal.esb.commons.kafka.KafkaAdminConfig;
import lu.fujitsu.ts.cgdis.portal.esb.commons.kafka.producers.CGDISKafkaProducerFactoryFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.serializer.JsonSerializer;

@Configuration
@EnableKafka
@Import(KafkaAdminConfig.class)
public class ServicePlanPushConfig {

    @Autowired
    private JsonSerializer kafkaJsonSerializer;


    @Bean("servicePlanPushRequestKafkaTemplate")
    public KafkaTemplate<String, UpdateServicePlanPushRequest> servicePlanPushRequestKafkaTemplate(CGDISKafkaProducerFactoryFactory factory) {
        return new KafkaTemplate<>(factory.create(kafkaJsonSerializer));
    }


}
