package lu.fujitsu.ts.cgdis.portal.internalgw.controllers;

import lu.fujitsu.ts.cgdis.portal.core.domain.SchedulerPropertyList;
import lu.fujitsu.ts.cgdis.portal.internalgw.config.properties.BusinessSchedulerProperties;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

@RestController
@RequestMapping("scheduler_properties")
public class SchedulerController {

    /**
     * The constant LOGGER.
     */
    private static final Logger LOGGER = LoggerFactory.getLogger(SchedulerController.class);

    /**
     * The rest template
     */
    private RestTemplate restTemplate;

    /**
     * The scheduler properties
     */
    private BusinessSchedulerProperties businessSchedulerProperties;

    @Autowired
    public SchedulerController(RestTemplate restTemplate, BusinessSchedulerProperties businessSchedulerProperties) {
        this.restTemplate = restTemplate;
        this.businessSchedulerProperties = businessSchedulerProperties;
    }

    /**
     * Get all scheduler properties
     * @return the scheduler properties
     */
    @GetMapping("all")
    public SchedulerPropertyList getSchedulerProperties() {
        String url = this.businessSchedulerProperties.getPropertiesUrl();
        ResponseEntity<SchedulerPropertyList> response = restTemplate.exchange(url, HttpMethod.GET, null, SchedulerPropertyList.class);
        if (response.getStatusCode() == HttpStatus.OK) {
            return response.getBody();
        } else {
            LOGGER.debug("Error {} while getting scheduler properties", response.getStatusCode());
            return null;
        }
    }

}
