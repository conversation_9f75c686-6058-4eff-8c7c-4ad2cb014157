package lu.fujitsu.ts.cgdis.portal.internalgw.controllers;

import lu.fujitsu.ts.cgdis.portal.esb.commons.kafka.KafkaTopics;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Controller for request person assignments push request
 */
@RestController
@RequestMapping("person_assignments_push_request")
public class PersonAssignmentsRequestController extends AbstractPostNoContentMessagingSystemPublicationController {

    @Autowired
    protected PersonAssignmentsRequestController(@Qualifier("personAssignmentsRequestKafkaTemplate")KafkaTemplate kafkaTemplate) {
        super(kafkaTemplate, KafkaTopics.REFRESH_PERSON_ASSIGNMENTS_PUSH_TOPIC);
    }

}
