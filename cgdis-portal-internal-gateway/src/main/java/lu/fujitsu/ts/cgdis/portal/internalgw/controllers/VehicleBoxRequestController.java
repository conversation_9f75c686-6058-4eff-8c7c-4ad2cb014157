package lu.fujitsu.ts.cgdis.portal.internalgw.controllers;

import lu.fujitsu.ts.cgdis.portal.esb.commons.kafka.KafkaTopics;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Controller for request backup group and optional group
 */
@RestController
@RequestMapping("vehicle_box_push_request")
public class VehicleBoxRequestController extends AbstractPostNoContentMessagingSystemPublicationController {

    @Autowired
    public VehicleBoxRequestController(@Qualifier("vehicleBoxRefreshRequestKafkaTemplate") KafkaTemplate kafkaTemplate) {
        super(kafkaTemplate, KafkaTopics.ELS_VEHICLE_BOX_REFRESH_PUSH_TOPIC);
    }

}
