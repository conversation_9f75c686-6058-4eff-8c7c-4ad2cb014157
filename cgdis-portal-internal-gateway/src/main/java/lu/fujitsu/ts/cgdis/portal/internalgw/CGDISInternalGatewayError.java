package lu.fujitsu.ts.cgdis.portal.internalgw;

import lu.fujitsu.ts.cgdis.portal.core.domain.esb.EsbStatusCode;
import lu.fujitsu.ts.eportal.server.core.domain.OperationStatusMessage;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

/**
 * The enum Cgdis internal gateway error.
 */
public enum CGDISInternalGatewayError {
    /**
     * Success cgdis internal gateway error.
     */
    SUCCESS(EsbStatusCode.SUCCESS,"success",HttpStatus.OK),
    /**
     * The Messaging system publication failure.
     */
    MESSAGING_SYSTEM_PUBLICATION_FAILURE("GW199","Failed to publish  Messaging system",HttpStatus.INTERNAL_SERVER_ERROR),
    /**
     * The Messaging system publication timeout.
     */
    MESSAGING_SYSTEM_PUBLICATION_TIMEOUT("GW101","Timeout publishing  Messaging system",HttpStatus.REQUEST_TIMEOUT),

    /**
     * Person tecid is missing during update person informations
     */
    FUNCTION_OPERATIONAL_UPDATED_MISSING(EsbStatusCode.UPDATE_FUNCTION_OPERATIONAL_MISSING,"function operational is missing to update validate/close",HttpStatus.BAD_REQUEST),

    /**
     * Person tecid is missing during update person informations
     */
    UPDATE_PERSON_INFORMATION_TECID_MISSING(EsbStatusCode.UPDATE_PERSON_INFORMATION_TECID_MISSING,"person tecid is missing to update person informations",HttpStatus.BAD_REQUEST),

    UPDATE_PERSON_INFORMATION_UPDATE_DATE_MISSING(EsbStatusCode.UPDATE_PERSON_INFORMATION_UPDATE_DATE_MISSING,"update date is missing to update person informations",HttpStatus.BAD_REQUEST),

    PDS_SEND_FILLING_FROM_MISSING(EsbStatusCode.PDS_SEND_FILLING_FROM_MISSING,"from date is missing to send pds filling",HttpStatus.BAD_REQUEST),
    PDS_SEND_FILLING_TO_MISSING(EsbStatusCode.PDS_SEND_FILLING_TO_MISSING,"to date is missing to send pds filling",HttpStatus.BAD_REQUEST),
    PDS_SEND_FILLING_SERVICE_PLAN_ID_MISSING(EsbStatusCode.PDS_SEND_FILLING_SERVICE_PLAN_ID_MISSING,"from date is missing to send pds filling",HttpStatus.BAD_REQUEST),

    UPDATE_VEHICLE_DATE_MISSING(EsbStatusCode.UPDATE_VEHICLE_DATE_MISSING,"date missing to update vehicle",HttpStatus.BAD_REQUEST),

    UPDATE_VEHICLE_TYPE_MISSING(EsbStatusCode.UPDATE_VEHICLE_DATE_MISSING,"date missing to update vehicle",HttpStatus.BAD_REQUEST),

    UPDATE_VEHICLE_SERVICE_PLAN_ID_MISSING(EsbStatusCode.UPDATE_VEHICLE_SERVICE_PLAN_ID_MISSING,"service plan id is missing to update vehicle",HttpStatus.BAD_REQUEST);
    /**
     * error code.
     */
    private final String code;
    /**
     * error message.
     */
    private final String message;
    /**
     * related HTTP status.
     */
    private final HttpStatus status;

    /**
     * constructor.
     *
     * @param code    code to return to client side
     * @param message message to return to client side
     * @param status  HTTP Status to return to client side
     */
    CGDISInternalGatewayError(final String code, final String message, final HttpStatus status) {
        this.code = code;
        this.message = message;
        this.status = status;
    }

    /**
     * Method used to build the corresponding message object to be send back to client side.
     *
     * @return the built message object
     */
    public final OperationStatusMessage buildMessage() {
        return new OperationStatusMessage(code,message);
    }

    /**
     * Method used to build the corresponding message object to be send back to client side.
     *
     * @param msg overwrite the default message
     * @return the built message object
     */
    public final OperationStatusMessage buildMessage(final String msg) {
        return new OperationStatusMessage(code, msg);
    }

    /**
     * Method used to build the ResponseEntity object corresponding to this error.
     *
     * @return ResponseEntity object corresponding to this error
     */
    public final ResponseEntity<OperationStatusMessage> buildResponse() {
        return new ResponseEntity<>(buildMessage(), status);
    }

    /**
     * Method used to build the ResponseEntity object corresponding to this error.
     *
     * @param msg overwrite the default message
     * @return ResponseEntity object corresponding to this error
     */
    public final ResponseEntity<OperationStatusMessage> buildResponse(final String msg) {
        return new ResponseEntity<>(buildMessage(msg), status);
    }

    }
