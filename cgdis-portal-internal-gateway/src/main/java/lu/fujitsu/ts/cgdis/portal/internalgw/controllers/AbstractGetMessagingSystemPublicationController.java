package lu.fujitsu.ts.cgdis.portal.internalgw.controllers;

import lu.fujitsu.ts.cgdis.portal.core.exception.esb.EsbMessagingException;
import lu.fujitsu.ts.cgdis.portal.core.exception.esb.EsbMessagingTimeoutException;
import lu.fujitsu.ts.cgdis.portal.internalgw.CGDISInternalGatewayError;
import lu.fujitsu.ts.eportal.server.core.domain.OperationStatusMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.web.bind.annotation.GetMapping;

import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

/**
 * The type Abstract messaging system publication controller.
 *
 */
public abstract class AbstractGetMessagingSystemPublicationController {
    /**
     * The constant LOGGER.
     */
    private static final Logger LOGGER = LoggerFactory.getLogger(AbstractGetMessagingSystemPublicationController.class);

    /**
     * The Publish timeout.
     */
    @Value("${cgdis-portal.kafka.publish-timeout}")
    private Long publishTimeout;

    /**
     * The Kafka template.
     */
    private KafkaTemplate<String, String> kafkaTemplate;

    /**
     * The Topic.
     */
    private String topic;

    /**
     * Instantiates a new Abstract messaging system publication controller.
     *
     * @param kafkaTemplate the kafka template
     * @param topic         the topic
     */
    protected AbstractGetMessagingSystemPublicationController(KafkaTemplate<String, String> kafkaTemplate, String topic) {
        this.kafkaTemplate = kafkaTemplate;
        this.topic = topic;
    }

    /**
     * Push operation status message.
     *
     * @return the operation status message
     */
    @GetMapping
    public OperationStatusMessage push() {
        try {
                kafkaTemplate.send(topic, "").get(publishTimeout, TimeUnit.SECONDS);
                return CGDISInternalGatewayError.SUCCESS.buildMessage();
        } catch (InterruptedException e) {
            LOGGER.error("InterruptedException occured publishing to Kafka topic " + topic, e);
            throw new EsbMessagingException("InterruptedException occured publishing to Kafka topic " + topic, e);
        } catch (ExecutionException e) {
            LOGGER.error("ExecutionException occured publishing to Kafka topic " + topic, e);
            throw new EsbMessagingException("ExecutionException occured publishing to Kafka topic " + topic, e);
        } catch (TimeoutException e) {
            LOGGER.error("TimeoutException occured publishing to Kafka topic " + topic, e);
            throw new EsbMessagingTimeoutException("TimeoutException occured publishing to Kafka topic! " + topic, e);
        }
    }


    /**
     * Gets publish timeout.
     *
     * @return the publish timeout
     */
    public Long getPublishTimeout() {
        return publishTimeout;
    }

    /**
     * Gets topic.
     *
     * @return the topic
     */
    public String getTopic() {
        return topic;
    }

    /**
     * Gets kafka template.
     *
     * @return the kafka template
     */
    public KafkaTemplate<String, String> getKafkaTemplate() {
        return kafkaTemplate;
    }
}
