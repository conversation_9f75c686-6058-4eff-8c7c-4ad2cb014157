package lu.fujitsu.ts.cgdis.portal.internalgw.controllers;

import lu.fujitsu.ts.cgdis.portal.esb.commons.kafka.KafkaTopics;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Controller for request vehicle information
 */
@RestController
@RequestMapping("vehicle_push_request")
public class VehiclePushRequestController extends AbstractPostNoContentMessagingSystemPublicationController {

    @Autowired
    public VehiclePushRequestController(@Qualifier("vehicleRefreshRequestKafkaTemplate") KafkaTemplate kafkaTemplate) {
        super(kafkaTemplate, KafkaTopics.ELS_VEHICLE_REFRESH_PUSH_TOPIC);
    }



}
