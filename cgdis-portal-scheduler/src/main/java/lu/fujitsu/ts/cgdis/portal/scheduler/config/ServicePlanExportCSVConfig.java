package lu.fujitsu.ts.cgdis.portal.scheduler.config;

import lu.fujitsu.ts.cgdis.portal.scheduler.config.properties.SchedulerProperties;
import lu.fujitsu.ts.cgdis.portal.scheduler.job.ServicePlanExportCSVJob;
import lu.fujitsu.ts.eportal.server.core.domain.OperationStatusMessage;
import org.aopalliance.aop.Advice;
import org.quartz.JobDetail;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpMethod;
import org.springframework.integration.channel.DirectChannel;
import org.springframework.integration.dsl.IntegrationFlow;
import org.springframework.integration.dsl.IntegrationFlows;
import org.springframework.integration.handler.LoggingHandler;
import org.springframework.integration.http.dsl.Http;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.MessageHeaders;
import org.springframework.scheduling.quartz.CronTriggerFactoryBean;
import org.springframework.scheduling.quartz.JobDetailFactoryBean;

@Configuration
public class ServicePlanExportCSVConfig {

    @Autowired
    private SchedulerProperties properties;

    @Autowired
    @Qualifier("retryExpressionAdvice")
    private Advice retryAdvice;

    @Bean
    public IntegrationFlow servicePlanExportOutbound() {
        return IntegrationFlows.from(servicePlanExportChannel())
                .enrichHeaders(  headerEnricherSpec -> headerEnricherSpec.header( MessageHeaders.CONTENT_TYPE, "application/json") )
                .handle(Http.outboundGateway(properties.getServicePlanExport().getClientUrl())
                                .httpMethod(HttpMethod.POST)
                                .expectedResponseType(OperationStatusMessage.class)
                        ,
                        c -> c.advice(retryAdvice)
                )
                .log(LoggingHandler.Level.INFO,message->"message sent for export service plan at " + message.getPayload() )
                .get();
    }

    @Bean("servicePlanExportChannel")
    public MessageChannel servicePlanExportChannel() {
        return new DirectChannel();
    }

    @Bean
    public CronTriggerFactoryBean servicePlanExportTrigger(@Qualifier("servicePlanExportDetail") JobDetail jobDetail) {
        CronTriggerFactoryBean factoryBean = new CronTriggerFactoryBean();
        factoryBean.setJobDetail(jobDetail);
        factoryBean.setStartDelay(0L);
        factoryBean.setCronExpression(properties.getServicePlanExport().getCron());
        return factoryBean;
    }

    @Bean
    public JobDetailFactoryBean servicePlanExportDetail() {
        JobDetailFactoryBean factoryBean = new JobDetailFactoryBean();
        factoryBean.setJobClass(ServicePlanExportCSVJob.class);
        factoryBean.setDescription("Invoke Service plan export Job service");
        factoryBean.setDurability(true);
        return factoryBean;
    }


}
