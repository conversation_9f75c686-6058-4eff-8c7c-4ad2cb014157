package lu.fujitsu.ts.cgdis.portal.processes.input.msnav.personalinformations.activities.update.occupation;

import lu.fujitsu.ts.cgdis.portal.esb.commons.domain.msnav.OperationalOccupation;
import lu.fujitsu.ts.cgdis.portal.services.entity.IOperationalOccupationRepositoryService;
import lu.fujitsu.ts.eportal.server.core.domain.SearchCriterion;
import lu.fujitsu.ts.eportal.server.core.utils.EPortalConverter;
import lu.fujitsu.ts.eportal.server.core.utils.IEPortalConverter;
import org.dozer.Mapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Handle {@link OperationalOccupation} received from MSNav
 * Retrieve the {@link lu.fujitsu.ts.cgdis.portal.core.domain.person.OperationalOccupation} used in portal and create missing occupation
 */
@Component
public class OperationalOccupationHandler {


    private static final Logger LOGGER = LoggerFactory.getLogger(OperationalOccupationHandler.class);

    /**
     * The Operational occupation repository service.
     */
    private IOperationalOccupationRepositoryService operationalOccupationRepositoryService;

    /**
     * The Occupation converter.
     */
    private IEPortalConverter<OperationalOccupation, lu.fujitsu.ts.cgdis.portal.core.domain.person.OperationalOccupation> occupationConverter;

    @Autowired
    public OperationalOccupationHandler(Mapper mapper, IOperationalOccupationRepositoryService operationalOccupationRepositoryService) {
        occupationConverter = new EPortalConverter<>(OperationalOccupation.class, lu.fujitsu.ts.cgdis.portal.core.domain.person.OperationalOccupation.class, mapper);
        this.operationalOccupationRepositoryService = operationalOccupationRepositoryService;
    }

    /**
     * Get operational occupations map.
     * New occupation are created!
     * @param allOperationalOccupations the all operational occupations
     * @return the map
     */
    List<lu.fujitsu.ts.cgdis.portal.core.domain.person.OperationalOccupation> getOperationalOccupations(List<OperationalOccupation> allOperationalOccupations) {
        LOGGER.debug("get operational functions");

        Set<lu.fujitsu.ts.cgdis.portal.core.domain.person.OperationalOccupation> existingOperationalOccupations = new HashSet<>();

        // Get existing operational functions
        allOperationalOccupations.forEach(occupation -> existingOperationalOccupations.addAll(operationalOccupationRepositoryService.search(Arrays.asList(
                new SearchCriterion("code", occupation.getCode(), SearchCriterion.Operator.eq),
                new SearchCriterion("type", occupation.getType(), SearchCriterion.Operator.eq)))));

        // Create missing operational functions
        if (existingOperationalOccupations.size() != allOperationalOccupations.size()) {

            List<OperationalOccupation> operationalOccupationToCreate = allOperationalOccupations.stream()
                    .filter(operationalOccupation -> existingOperationalOccupations.stream().noneMatch(existingOperationalOccupation -> existingOperationalOccupation.getCode().equals(operationalOccupation.getCode()) && existingOperationalOccupation.getType().equals(operationalOccupation.getType())))
                    .collect(Collectors.toList());

            // Some operational occupations doesn't exists ==> create
            existingOperationalOccupations.addAll(createMissingOperationalOccupation(operationalOccupationToCreate));
        }

        return new ArrayList(existingOperationalOccupations);
    }

    /**
     * Create missing operational occupation list.
     * @param operationalOccupationsToCreate list of operational occupation to create
     * @return the list
     */
    private List<lu.fujitsu.ts.cgdis.portal.core.domain.person.OperationalOccupation> createMissingOperationalOccupation(List<OperationalOccupation> operationalOccupationsToCreate) {
        LOGGER.debug("Create {} missing operation occupations ", operationalOccupationsToCreate.size());
        return operationalOccupationsToCreate.stream()
                .map(oneOperationalOccupation -> occupationConverter.convert(oneOperationalOccupation))
                .map(oneOperationalOccupation -> operationalOccupationRepositoryService.create(oneOperationalOccupation))
                .collect(Collectors.toList());
    }

}
