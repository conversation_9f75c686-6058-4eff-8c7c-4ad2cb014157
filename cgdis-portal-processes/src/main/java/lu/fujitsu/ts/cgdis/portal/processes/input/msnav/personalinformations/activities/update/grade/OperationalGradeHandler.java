package lu.fujitsu.ts.cgdis.portal.processes.input.msnav.personalinformations.activities.update.grade;

import lu.fujitsu.ts.cgdis.portal.core.domain.person.OperationalGradeType;
import lu.fujitsu.ts.cgdis.portal.esb.commons.domain.msnav.OperationalGrade;
import lu.fujitsu.ts.cgdis.portal.services.entity.IOperationalGradeRepositoryService;
import lu.fujitsu.ts.eportal.server.core.domain.SearchCriterion;
import lu.fujitsu.ts.eportal.server.core.utils.EPortalConverter;
import lu.fujitsu.ts.eportal.server.core.utils.IEPortalConverter;
import org.dozer.Mapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Component
public class OperationalGradeHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(OperationalGradeHandler.class);

    /**
     * The Operational occupation repository service.
     */
    private IOperationalGradeRepositoryService operationalGradeRepositoryService;

    /**
     * The Occupation converter.
     */
    private IEPortalConverter<OperationalGrade, lu.fujitsu.ts.cgdis.portal.core.domain.person.OperationalGrade> gradeConverter;

    @Autowired
    public OperationalGradeHandler(Mapper mapper, IOperationalGradeRepositoryService operationalGradeRepositoryService) {
        gradeConverter = new EPortalConverter<>(OperationalGrade.class, lu.fujitsu.ts.cgdis.portal.core.domain.person.OperationalGrade.class, mapper);
        this.operationalGradeRepositoryService=operationalGradeRepositoryService;
    }

    List<lu.fujitsu.ts.cgdis.portal.core.domain.person.OperationalGrade> getOperationalGrades(List<OperationalGrade> allOperationalOccupations) {
        LOGGER.debug("get operational functions");

        Set<lu.fujitsu.ts.cgdis.portal.core.domain.person.OperationalGrade> existingOperationalGrades = new HashSet<>();

        // Get existing operational functions
        allOperationalOccupations.forEach(occupation -> existingOperationalGrades.addAll(operationalGradeRepositoryService.search(Arrays.asList(
                new SearchCriterion("code", occupation.getCode(), SearchCriterion.Operator.eq),
                new SearchCriterion("gradeType", OperationalGradeType.valueOf(occupation.getGradeType().name()), SearchCriterion.Operator.eq)))));

        // Create missing operational functions
        if (existingOperationalGrades.size() != allOperationalOccupations.size()) {

            List<OperationalGrade> operationalGradeToCreate = allOperationalOccupations.stream()
                    .filter(operationalGrade -> existingOperationalGrades.stream()
                            .noneMatch(existingOperationalOccupation -> existingOperationalOccupation.getCode().equals(operationalGrade.getCode())
                                    && existingOperationalOccupation.getGradeType().name().equals(operationalGrade.getGradeType().name())))
                    .collect(Collectors.toList());

            // Some operational occupations doesn't exists ==> create
            existingOperationalGrades.addAll(createMissingOperationalOccupation(operationalGradeToCreate));
        }

        return new ArrayList(existingOperationalGrades);
    }

    /**
     * Create missing operational occupation list.
     * @param operationalGradesToCreate list of operational occupation to create
     * @return the list
     */
    private List<lu.fujitsu.ts.cgdis.portal.core.domain.person.OperationalGrade> createMissingOperationalOccupation(List<OperationalGrade> operationalGradesToCreate) {
        LOGGER.debug("Create {} missing operation occupations ", operationalGradesToCreate.size());
        return operationalGradesToCreate.stream()
                .map(oneOperationalGrade -> gradeConverter.convert(oneOperationalGrade))
                .distinct()
                .map(oneOperationalGrade -> operationalGradeRepositoryService.create(oneOperationalGrade))
                .collect(Collectors.toList());
    }
}
