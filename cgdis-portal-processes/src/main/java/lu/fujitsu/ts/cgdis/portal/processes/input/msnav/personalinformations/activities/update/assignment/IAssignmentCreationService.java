package lu.fujitsu.ts.cgdis.portal.processes.input.msnav.personalinformations.activities.update.assignment;

import lu.fujitsu.ts.cgdis.portal.core.domain.enums.AssignmentType;
import lu.fujitsu.ts.cgdis.portal.core.domain.enums.PrimaryType;
import lu.fujitsu.ts.cgdis.portal.core.domain.person.PersonCRUD;
import lu.fujitsu.ts.cgdis.portal.esb.commons.domain.msnav.Assignment;

import java.util.Collection;
import java.util.List;

/**
 * The interface Assignment creation service.
 */
public interface IAssignmentCreationService {
    /**
     * Create.
     *
     * @param personCRUD  the person crud
     * @param assignments the assignments
     * @param primaryType    the primaryType
     * @param type        the type
     * @return assignments created
     */
    Collection<lu.fujitsu.ts.cgdis.portal.core.domain.assignement.Assignment> create(PersonCRUD personCRUD, List<Assignment> assignments, PrimaryType primaryType, AssignmentType type);
}
