package lu.fujitsu.ts.cgdis.portal.processes.config;

import de.codecentric.boot.admin.client.config.InstanceProperties;
import de.codecentric.boot.admin.client.config.SpringBootAdminClientEnabledCondition;
import de.codecentric.boot.admin.client.registration.DefaultApplicationFactory;
import de.codecentric.boot.admin.client.registration.metadata.MetadataContributor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.actuate.autoconfigure.endpoint.web.WebEndpointProperties;
import org.springframework.boot.actuate.autoconfigure.web.server.ManagementServerProperties;
import org.springframework.boot.actuate.endpoint.web.PathMappedEndpoints;
import org.springframework.boot.autoconfigure.web.ServerProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Conditional;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

import java.net.InetAddress;

@Configuration
public class SpringBootAdminProcessConfig {

    @Bean
    @Profile("!dev")
    @Conditional(SpringBootAdminClientEnabledCondition.class)
    // This config is used when services are deployed in docker containers  => not in dev
    public DefaultApplicationFactory applicationFactory(InstanceProperties instance,
                                                        ManagementServerProperties management,
                                                        ServerProperties server,
                                                        PathMappedEndpoints pathMappedEndpoints,
                                                        WebEndpointProperties webEndpoint,
                                                        MetadataContributor metadataContributor) {
        return new DefaultApplicationFactory(instance, management, server, pathMappedEndpoints, webEndpoint,
                metadataContributor) {
            @Override
            protected String getServiceBaseUrl() {
                String baseUrl = instance.getServiceBaseUrl();
                if (StringUtils.isEmpty(baseUrl)) {
                    return super.getServiceBaseUrl();
                }

                if (getLocalServerPort() == null) {
                    throw new IllegalStateException("couldn't determine local port. Please set spring.boot.admin.client.instance.service-base-url.");
                }

                StringBuilder builder = new StringBuilder(baseUrl);
                builder.append("/").append(getServiceHost()).append("/");
                return builder.toString();
            }
        };

    }

}
