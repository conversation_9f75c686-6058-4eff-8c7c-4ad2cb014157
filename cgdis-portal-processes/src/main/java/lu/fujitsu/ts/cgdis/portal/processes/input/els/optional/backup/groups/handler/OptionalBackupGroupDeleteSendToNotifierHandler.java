package lu.fujitsu.ts.cgdis.portal.processes.input.els.optional.backup.groups.handler;

import lu.fujitsu.ts.cgdis.portal.core.domain.esb.notification.els.DeleteOptionalBackupGroupNotifierRequest;
import lu.fujitsu.ts.cgdis.portal.core.exception.CGDISTechnicalException;
import lu.fujitsu.ts.cgdis.portal.esb.commons.kafka.KafkaTopics;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.integration.handler.GenericHandler;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

@Component
public class OptionalBackupGroupDeleteSendToNotifierHandler implements GenericHandler<DeleteOptionalBackupGroupNotifierRequest> {

    private static final Logger LOGGER = LoggerFactory.getLogger(OptionalBackupGroupDeleteSendToNotifierHandler.class);

    private final KafkaTemplate<String, DeleteOptionalBackupGroupNotifierRequest> optionalBackupGroupDeleteNotifierKafkaTemplate;

    public OptionalBackupGroupDeleteSendToNotifierHandler(KafkaTemplate<String, DeleteOptionalBackupGroupNotifierRequest> optionalBackupGroupDeleteNotifierKafkaTemplate) {
        this.optionalBackupGroupDeleteNotifierKafkaTemplate = optionalBackupGroupDeleteNotifierKafkaTemplate;
    }

    @Override
    public Object handle(DeleteOptionalBackupGroupNotifierRequest payload, Map<String, Object> headers) {
        LOGGER.debug("Sending DeleteOptionalBackupGroupNotifierRequest for impacted entity id: {}", payload.getEntityId());
        try {
            optionalBackupGroupDeleteNotifierKafkaTemplate.send(KafkaTopics.GENERIC_NOTIFIER_TOPIC, payload).get(10, TimeUnit.SECONDS);
        } catch (InterruptedException | ExecutionException | TimeoutException e) {
            throw new CGDISTechnicalException(e);
        }
        return payload;
    }
}
