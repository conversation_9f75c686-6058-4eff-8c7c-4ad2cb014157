package lu.fujitsu.ts.cgdis.portal.processes.internal.prestations.allowance;

import com.fasterxml.jackson.databind.ObjectMapper;
import lu.fujitsu.ts.cgdis.portal.core.domain.error.ErrorMessageType;
import lu.fujitsu.ts.cgdis.portal.esb.commons.AbstractEsbConfig;
import lu.fujitsu.ts.cgdis.portal.esb.commons.domain.internal.prestations.allowance.GenerateAllPrestationsAllowancesRequest;
import lu.fujitsu.ts.cgdis.portal.esb.commons.error.ErrorMessageStrategy;
import lu.fujitsu.ts.cgdis.portal.esb.commons.error.IObjectIdentifierExtractor;
import lu.fujitsu.ts.cgdis.portal.esb.commons.kafka.KafkaAdminConfig;
import lu.fujitsu.ts.cgdis.portal.esb.commons.kafka.KafkaGroups;
import lu.fujitsu.ts.cgdis.portal.esb.commons.kafka.KafkaTopics;
import lu.fujitsu.ts.cgdis.portal.services.person.IPersonRepositoryService;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.integration.annotation.IntegrationComponentScan;
import org.springframework.integration.dsl.IntegrationFlow;
import org.springframework.integration.dsl.IntegrationFlows;
import org.springframework.integration.handler.GenericHandler;
import org.springframework.integration.handler.LoggingHandler;
import org.springframework.integration.kafka.dsl.Kafka;
import org.springframework.integration.kafka.inbound.KafkaMessageDrivenChannelAdapter.ListenerMode;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.kafka.core.ConsumerFactory;
import org.springframework.kafka.core.DefaultKafkaConsumerFactory;
import org.springframework.kafka.support.serializer.JsonDeserializer;
import org.springframework.messaging.MessageHeaders;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * The type Delete person config.
 */
@Configuration
@IntegrationComponentScan
@EnableKafka
@ComponentScan({
})

public class AllPrestationAllowanceProcessConfig extends AbstractEsbConfig {


    /**
     * The Bootstrap servers.
     */
    @Value("${spring.kafka.bootstrap-servers}")
    private String bootstrapServers;

    private final IPersonRepositoryService personRepositoryService;

    private final ObjectMapper mapper;

    private final GenericHandler<ImmutablePair<Long, LocalDate>> sendOnePrestationsAllowanceHandler;

    public AllPrestationAllowanceProcessConfig(IPersonRepositoryService personRepositoryService, ObjectMapper mapper, GenericHandler<ImmutablePair<Long, LocalDate>> sendOnePrestationsAllowanceHandler) {
        this.personRepositoryService = personRepositoryService;
        this.mapper = mapper;
        this.sendOnePrestationsAllowanceHandler = sendOnePrestationsAllowanceHandler;
    }




    /**
     * Delete person flow integration flow.
     *
     * @return the integration flow
     */
    @Bean
    public IntegrationFlow generateAllPrestationsAllowanceFlow( GenericHandler<?> registerUserHandler) {
        return IntegrationFlows
                .from(
                        Kafka.messageDrivenChannelAdapter(
                                        generateAllPrestationsAllowanceConsumerFactory(), ListenerMode.record, KafkaTopics.ALL_PRESTATIONS_ALLOWANCES
                        ).errorMessageStrategy(new ErrorMessageStrategy(ErrorMessageType.INTERNAL_PROCESS, mapper, generateAllPrestationsAllowanceObjectIdentifierExtractor()))
                                .errorChannel(KafkaAdminConfig.ERROR_MESSAGE_CHANNEL)
                )
                .handle(registerUserHandler)
                .enrich(e -> e.transactional(true))
                .enrichHeaders(headerEnricherSpec -> headerEnricherSpec.<GenerateAllPrestationsAllowancesRequest>headerFunction(PrestationAllowanceProcessCommonConfig.ALLOWANCE_DATE_HEADER, message -> message.getPayload().getDate()))
                .<GenerateAllPrestationsAllowancesRequest>handle((p, h) -> personRepositoryService.findAllPersonIds().stream().map(id -> ImmutablePair.of(id, p.getDate())).collect(Collectors.toList()))
                .split()
                .log(LoggingHandler.Level.INFO, m -> String.format("BEGIN %s ",m.getHeaders().getId()))
                .handle(sendOnePrestationsAllowanceHandler)
                .log(LoggingHandler.Level.INFO, m -> String.format("END %s ",m.getHeaders().getId()))
                .get();
    }


    @Bean
    public IObjectIdentifierExtractor generateAllPrestationsAllowanceObjectIdentifierExtractor() {
        return new IObjectIdentifierExtractor() {
            @Override
            public <T> String getObjectIdentifier(T payload, MessageHeaders headers) {
                return ((GenerateAllPrestationsAllowancesRequest) payload).getDate().toString();

            }


        };
    }


    /**
     * Consumer factory consumer factory.
     *
     * @return the consumer factory
     */
    @Bean
    public ConsumerFactory<String, GenerateAllPrestationsAllowancesRequest> generateAllPrestationsAllowanceConsumerFactory() {
        return new DefaultKafkaConsumerFactory<>(generateAllPrestationsAllowanceConsumerConfigs(), new StringDeserializer(), new JsonDeserializer<>(GenerateAllPrestationsAllowancesRequest.class, mapper));
    }

    /**
     * Consumer configs map.
     *
     * @return the map
     */
    @Bean
    public Map<String, Object> generateAllPrestationsAllowanceConsumerConfigs() {
        Map<String, Object> props = new HashMap<>();
        props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);
        props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, JsonDeserializer.class);
        props.put(ConsumerConfig.GROUP_ID_CONFIG, KafkaGroups.INTERNAL);
        props.put(ConsumerConfig.HEARTBEAT_INTERVAL_MS_CONFIG,KafkaAdminConfig.DEFAULT_HEARTBEAT_INTERVAL_MS_CONFIG);
        props.put(ConsumerConfig.SESSION_TIMEOUT_MS_CONFIG,KafkaAdminConfig.DEFAULT_SESSION_TIMEOUT_MS_CONFIG);
        props.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG,KafkaAdminConfig.DEFAULT_MAX_POLL_RECORDS_CONFIG);
        return props;
    }




}

