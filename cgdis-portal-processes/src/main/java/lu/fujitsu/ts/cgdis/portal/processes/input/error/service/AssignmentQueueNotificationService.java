package lu.fujitsu.ts.cgdis.portal.processes.input.error.service;

import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lu.fujitsu.ts.cgdis.portal.core.domain.error.ErrorMessage;
import lu.fujitsu.ts.cgdis.portal.core.exception.esb.EsbMessagingException;
import lu.fujitsu.ts.cgdis.portal.core.exception.esb.EsbMessagingTimeoutException;
import lu.fujitsu.ts.cgdis.portal.esb.commons.kafka.KafkaTopics;
import lu.fujitsu.ts.eportal.server.core.exceptions.EPortalServerTechnicalException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

@Service
public class AssignmentQueueNotificationService implements IErrorMessageResendService {

    /**
     * The constant LOGGER.
     */
    private static final Logger LOGGER = LoggerFactory.getLogger(AssignmentQueueNotificationService.class);


    /**
     * Json mapper
     */
    @Autowired
    private ObjectMapper jsonMapper;


    /**
     * The Kafka template.
     */
    @Autowired
    @Qualifier("assignmentQueueKafkaTemplate")
    private KafkaTemplate<String, String> kafkaTemplate;

    /**
     * The Publish timeout.
     */
    @Value("${cgdis-portal.kafka.publish-timeout}")
    private Long publishTimeout;

    private String topic;


    @Autowired
    public AssignmentQueueNotificationService() {
        this.topic = KafkaTopics.ASSIGNMENT_QUEUE_NOTIFICATION_TOPIC;
    }


    @Override
    public void resend(ErrorMessage errorMessage) throws EPortalServerTechnicalException {
        try {
            String request = jsonMapper.readValue(errorMessage.getMessage(), String.class);
            kafkaTemplate.send(topic, request).get(publishTimeout, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            LOGGER.error("InterruptedException occured publishing to Kafka topic " + topic, e);
            throw new EsbMessagingException("InterruptedException occured publishing to Kafka topic " + topic, e);
        } catch (ExecutionException e) {
            LOGGER.error("ExecutionException occured publishing to Kafka topic " + topic, e);
            throw new EsbMessagingException("ExecutionException occured publishing to Kafka topic " + topic, e);
        } catch (TimeoutException e) {
            LOGGER.error("TimeoutException occured publishing to Kafka topic " + topic, e);
            throw new EsbMessagingTimeoutException("TimeoutException occured publishing to Kafka topic! " + topic, e);
        } catch (JsonParseException e) {
            LOGGER.error("Error parsing object for topic {} error {}" , topic, e);
            throw new EPortalServerTechnicalException("Error parsing object for topic" + topic, e);
        } catch (JsonMappingException e) {
            LOGGER.error("Error mapping object to json for topic {} error {}" ,topic, e);
            throw new EPortalServerTechnicalException("Error mapping object to json for topic " +topic, e);
        }catch (IOException e){
            LOGGER.error("Error read value object for topic {}, error {}", topic, e);
            throw new EPortalServerTechnicalException("Error read value object for topic " +topic, e);
        }

    }



}
