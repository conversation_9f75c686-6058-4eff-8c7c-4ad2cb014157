package lu.fujitsu.ts.cgdis.portal.processes.input.error.service;

import lu.fujitsu.ts.cgdis.portal.core.domain.error.ErrorMessage;
import lu.fujitsu.ts.cgdis.portal.core.domain.esb.PersonUpdatedRequest;
import lu.fujitsu.ts.cgdis.portal.core.exception.esb.EsbMessagingException;
import lu.fujitsu.ts.cgdis.portal.core.exception.esb.EsbMessagingTimeoutException;
import lu.fujitsu.ts.cgdis.portal.esb.commons.kafka.KafkaTopics;
import lu.fujitsu.ts.eportal.server.core.exceptions.EPortalServerTechnicalException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;

import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;


@Service
public  class ProcessUpdatePersonELSService implements IErrorMessageResendService {

    /**
     * The constant LOGGER.
     */
    private static final Logger LOGGER = LoggerFactory.getLogger(ProcessUpdatePersonELSService.class);



    /**
     * The Kafka template.
     */
    @Autowired
    @Qualifier("personUpdatedRequestKafkaTemplate")
    private KafkaTemplate<String, PersonUpdatedRequest> kafkaTemplate;

    /**
     * The Publish timeout.
     */
    @Value("${cgdis-portal.kafka.publish-timeout}")
    private Long publishTimeout;

    private String topic;



    @Autowired
    public ProcessUpdatePersonELSService() {
        this.topic = KafkaTopics.PERSONAL_INFORMATION_UPDATED_PUSH;
    }


    @Override
    public void resend(ErrorMessage errorMessage) throws EPortalServerTechnicalException {
        try {
            String tecidStr = errorMessage.getObjectIdentifier();
            Long tecId = Long.parseLong(tecidStr);
            PersonUpdatedRequest personUpdatedRequest = new PersonUpdatedRequest();
            personUpdatedRequest.setPersonTecid(tecId);
            kafkaTemplate.send(topic, personUpdatedRequest).get(publishTimeout, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            LOGGER.error("InterruptedException occured publishing to Kafka topic " + topic, e);
            throw new EsbMessagingException("InterruptedException occured publishing to Kafka topic " + topic, e);
        } catch (ExecutionException e) {
            LOGGER.error("ExecutionException occured publishing to Kafka topic " + topic, e);
            throw new EsbMessagingException("ExecutionException occured publishing to Kafka topic " + topic, e);
        } catch (TimeoutException e) {
            LOGGER.error("TimeoutException occured publishing to Kafka topic " + topic, e);
            throw new EsbMessagingTimeoutException("TimeoutException occured publishing to Kafka topic! " + topic, e);
        }

    }




}
