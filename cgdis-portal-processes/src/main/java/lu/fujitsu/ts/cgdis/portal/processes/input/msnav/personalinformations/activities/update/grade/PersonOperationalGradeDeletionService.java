package lu.fujitsu.ts.cgdis.portal.processes.input.msnav.personalinformations.activities.update.grade;

import lu.fujitsu.ts.cgdis.portal.core.domain.person.PersonOperationalGrade;
import lu.fujitsu.ts.cgdis.portal.esb.commons.domain.msnav.OperationalGrade;
import lu.fujitsu.ts.cgdis.portal.services.person.IPersonOperationalGradeRepositoryService;
import lu.fujitsu.ts.eportal.server.core.utils.EPortalConverter;
import lu.fujitsu.ts.eportal.server.core.utils.IEPortalConverter;
import org.dozer.Mapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class PersonOperationalGradeDeletionService {

    private static final Logger LOGGER = LoggerFactory.getLogger(PersonOperationalGradeDeletionService.class);
    /**
     * The Occupation 2 person occupation converter.
     */
    private IEPortalConverter<OperationalGrade, PersonOperationalGrade> grade2personGradeConverter;

    @Autowired
    private IPersonOperationalGradeRepositoryService personOperationalGradeRepositoryService;


    @Autowired
    public PersonOperationalGradeDeletionService(Mapper mapper){
        grade2personGradeConverter= new EPortalConverter<>(OperationalGrade.class, PersonOperationalGrade.class, mapper);
    }

    @Transactional
    public void delete(PersonOperationalGrade personOperationalGrade){
        LOGGER.debug("delete person operational grade with id {}", personOperationalGrade.getTecid());
        this.personOperationalGradeRepositoryService.delete(personOperationalGrade);
    }
}
