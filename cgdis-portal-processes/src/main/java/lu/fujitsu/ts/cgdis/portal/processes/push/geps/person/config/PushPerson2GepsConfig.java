package lu.fujitsu.ts.cgdis.portal.processes.push.geps.person.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import lu.fujitsu.ts.cgdis.portal.connector.portaldb.config.CGDISPortalDbConnectorConfig;
import lu.fujitsu.ts.cgdis.portal.connector.portaldb.config.DatabaseConfiguration;
import lu.fujitsu.ts.cgdis.portal.core.domain.error.ErrorMessageType;
import lu.fujitsu.ts.cgdis.portal.core.domain.esb.MessageSource;
import lu.fujitsu.ts.cgdis.portal.core.domain.esb.PersonUpdatedRequest;
import lu.fujitsu.ts.cgdis.portal.esb.commons.AbstractEsbConfig;
import lu.fujitsu.ts.cgdis.portal.esb.commons.delay.MessageTimestampDelay;
import lu.fujitsu.ts.cgdis.portal.esb.commons.domain.geps.UpdatePersonRequest;
import lu.fujitsu.ts.cgdis.portal.esb.commons.error.ErrorMessageStrategy;
import lu.fujitsu.ts.cgdis.portal.esb.commons.error.IObjectIdentifierExtractor;
import lu.fujitsu.ts.cgdis.portal.esb.commons.kafka.KafkaAdminConfig;
import lu.fujitsu.ts.cgdis.portal.esb.commons.kafka.KafkaGroups;
import lu.fujitsu.ts.cgdis.portal.esb.commons.kafka.KafkaTopics;
import lu.fujitsu.ts.cgdis.portal.esb.commons.kafka.properties.KafkaTopicsProperties;
import lu.fujitsu.ts.cgdis.portal.processes.push.geps.person.activities.PersonUpdatedToUpdatePersonRequestTransformer;
import lu.fujitsu.ts.cgdis.portal.processes.push.geps.person.activities.Send2GepsGatewayService;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.integration.annotation.IntegrationComponentScan;
import org.springframework.integration.dsl.IntegrationFlow;
import org.springframework.integration.dsl.IntegrationFlows;
import org.springframework.integration.dsl.channel.MessageChannels;
import org.springframework.integration.handler.GenericHandler;
import org.springframework.integration.handler.LoggingHandler;
import org.springframework.integration.kafka.dsl.Kafka;
import org.springframework.integration.kafka.inbound.KafkaMessageDrivenChannelAdapter.ListenerMode;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.config.KafkaListenerContainerFactory;
import org.springframework.kafka.core.*;
import org.springframework.kafka.listener.ConcurrentMessageListenerContainer;
import org.springframework.kafka.support.serializer.JsonDeserializer;
import org.springframework.kafka.support.serializer.JsonSerializer;
import org.springframework.messaging.MessageHeaders;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;

import java.util.HashMap;
import java.util.Map;

@Configuration
@IntegrationComponentScan
@EnableKafka
@Import({CGDISPortalDbConnectorConfig.class, DatabaseConfiguration.class, KafkaAdminConfig.class})
@ComponentScan({
        "lu.fujitsu.ts.cgdis.portal.processes.push.geps.person.activities"
})
public class PushPerson2GepsConfig extends AbstractEsbConfig {

    public static final long DEFAULT_DELAY = 5000L;

    @Value("${spring.kafka.bootstrap-servers}")
    private String bootstrapServers;

    @Autowired
    private ObjectMapper mapper;

    @Autowired
    private KafkaTopicsProperties topicsProperties;

    @Autowired
    private PersonUpdatedToUpdatePersonRequestTransformer personUpdatedToRequestTransformer;

    @Autowired
    private Send2GepsGatewayService send2GepsGatewayService;


    PushPerson2GepsConfig(ThreadPoolTaskScheduler threadPoolTaskScheduler) {
        threadPoolTaskScheduler.setWaitForTasksToCompleteOnShutdown(true);
    }
    /**
     * Custom serializer json serializer.
     *
     * @return the json serializer
     */

    @Bean
    public JsonSerializer customSerializer() {
        return new JsonSerializer(mapper);
    }

    @Bean
    public IntegrationFlow updatePersonToGepsPushFlow( GenericHandler<?> registerUserHandler) {
        return IntegrationFlows
                .from(
                        Kafka.messageDrivenChannelAdapter(
                                personUpdatedConsumerFactory(), ListenerMode.record, KafkaTopics.PERSONAL_INFORMATION_UPDATED_PUSH
                        )
                                .errorMessageStrategy(updatePersonToGepsPushErrorMessageStrategy())
                                .errorChannel(KafkaAdminConfig.ERROR_MESSAGE_CHANNEL)
                )
                .handle(registerUserHandler)
                .log(LoggingHandler.Level.INFO, message -> String.format("Start push person to geps for message %s", message.getHeaders().getId()))
                .enrich(e -> e.transactional(true))
                .<PersonUpdatedRequest>filter(p -> !MessageSource.GEPS.equals(p.getSource()))
                .gateway( updatePersonToGepsPushFlowWithDelay())
                .log(LoggingHandler.Level.INFO, message -> String.format("End push person to geps for message %s", message.getHeaders().getId()))
                .get();
    }

    @Bean
    public MessageTimestampDelay<Object> messageTimestampDelayForPersonGeps() {
        return new MessageTimestampDelay<>(DEFAULT_DELAY);
    }

    @Bean
    public IntegrationFlow updatePersonToGepsPushFlowWithDelay() {
        return IntegrationFlows
                .from(
                        MessageChannels.direct("updatePersonToGepsPushFlowWithDelayChannel")
                )
                .delay("push-person-geps-delay-group", delayerEndpointSpec ->
                        delayerEndpointSpec
                                .defaultDelay(0)
                                .delayFunction(messageTimestampDelayForPersonGeps()  )
                )
                .transform(personUpdatedToRequestTransformer, e -> e.transactional(true))
                .handle(send2GepsGatewayService, "send")
                .get();
    }


    @Bean
    public ErrorMessageStrategy updatePersonToGepsPushErrorMessageStrategy() {
        return new ErrorMessageStrategy(ErrorMessageType.PROCESS_UPDATE_PERSON_GEPS, mapper, updatePerson2GepsObjectIdentifierExtractor());
    }

    @Bean
    public IntegrationFlow updatePersonToGepsErrorChannelFlow() {
        return createDefaultErrorChannelFlow(updatePersonToGepsPushErrorMessageStrategy());
    }

    @Bean
    public IObjectIdentifierExtractor updatePerson2GepsObjectIdentifierExtractor() {
        return new IObjectIdentifierExtractor() {
            @Override
            public <T> String getObjectIdentifier(T payload, MessageHeaders headers) {
                if (payload instanceof PersonUpdatedRequest) {
                    return ((PersonUpdatedRequest) payload).getPersonTecid().toString();
                }
                return null;
            }
        };
    }

    @Bean
    public KafkaListenerContainerFactory<ConcurrentMessageListenerContainer<String, PersonUpdatedRequest>> kafkaListenerContainerFactory() {
        ConcurrentKafkaListenerContainerFactory<String, PersonUpdatedRequest> factory = new ConcurrentKafkaListenerContainerFactory<>();
        factory.setConsumerFactory(personUpdatedConsumerFactory());
        factory.setConcurrency(1);
        factory.getContainerProperties().setPollTimeout(3000);
        return factory;
    }

    @Bean
    public ConsumerFactory<String, PersonUpdatedRequest> personUpdatedConsumerFactory() {
        return new DefaultKafkaConsumerFactory<>(personUpdatedConfigs(), new StringDeserializer(), new JsonDeserializer<>(PersonUpdatedRequest.class, mapper));
    }

    @Bean
    public Map<String, Object> personUpdatedConfigs() {
        Map<String, Object> props = new HashMap<>();
        props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);
        props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, JsonDeserializer.class);
        props.put(ConsumerConfig.GROUP_ID_CONFIG, KafkaGroups.GEPS);
        props.put(ConsumerConfig.HEARTBEAT_INTERVAL_MS_CONFIG,KafkaAdminConfig.DEFAULT_HEARTBEAT_INTERVAL_MS_CONFIG);
        props.put(ConsumerConfig.SESSION_TIMEOUT_MS_CONFIG,KafkaAdminConfig.DEFAULT_SESSION_TIMEOUT_MS_CONFIG);
        props.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG,KafkaAdminConfig.DEFAULT_MAX_POLL_RECORDS_CONFIG);
        return props;
    }


    @Bean
    public ProducerFactory<String, UpdatePersonRequest> gepsPushPersonProducerFactory() {
        Map<String, Object> props = new HashMap<>();
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);
        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, JsonSerializer.class);
        return new DefaultKafkaProducerFactory<>(props, new StringSerializer(), customSerializer());
    }

    @Bean
    public KafkaTemplate<String, UpdatePersonRequest> gepsPushPersonKafkaTemplate() {
        return new KafkaTemplate<>(gepsPushPersonProducerFactory());
    }



}

