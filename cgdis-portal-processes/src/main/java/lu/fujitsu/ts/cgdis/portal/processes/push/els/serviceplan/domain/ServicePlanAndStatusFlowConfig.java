package lu.fujitsu.ts.cgdis.portal.processes.push.els.serviceplan.domain;

import lombok.Data;
import lu.fujitsu.ts.cgdis.portal.core.domain.enums.ServicePlanCompletionStatus;

/**
 * The type Service p lan and status flow config.
 */
@Data
public class ServicePlanAndStatusFlowConfig {

    /**
     * The Status value.
     */
    private String statusValue;

    /**
     * The Els status.
     */
    private Boolean elsStatus;

    /**
     * The Portal status.
     */
    private Boolean portalStatus;

    /**
     * The Slot status.
     */
    private ServicePlanCompletionStatus slotStatus;

    /**
     * The Send pds.
     */
    private boolean sendPds;
    /**
     * The Send status.
     */
    private boolean sendStatus;
    /**
     * The Send notification.
     */
    private boolean sendNotification;




}
