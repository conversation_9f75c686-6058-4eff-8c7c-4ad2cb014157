package lu.fujitsu.ts.cgdis.portal.processes.push.els.serviceplan.activities.update;

import lu.fujitsu.ts.cgdis.portal.business.services.utils.backupgroup.BackupGroupStatusUtils;
import lu.fujitsu.ts.cgdis.portal.core.domain.VehicleStatus;
import lu.fujitsu.ts.cgdis.portal.core.domain.VehicleStatusHistoric;
import lu.fujitsu.ts.cgdis.portal.core.domain.enums.ServicePlanCompletionStatus;
import lu.fujitsu.ts.cgdis.portal.core.domain.esb.StringList;
import lu.fujitsu.ts.cgdis.portal.core.domain.esb.UpdateServicePlanPushRequest;
import lu.fujitsu.ts.cgdis.portal.core.domain.serviceplan.ServicePlan;
import lu.fujitsu.ts.cgdis.portal.core.domain.serviceplan.ServicePlanElsStatus;
import lu.fujitsu.ts.cgdis.portal.core.domain.serviceplan.ServicePlanTimeSlotCompletionStatus;
import lu.fujitsu.ts.cgdis.portal.core.domain.serviceplan.ServicePlanVersion;
import lu.fujitsu.ts.cgdis.portal.core.domain.serviceplan.prestation.Prestation;
import lu.fujitsu.ts.cgdis.portal.core.domain.serviceplan.timeslot.ServicePlanTimeSlot;
import lu.fujitsu.ts.cgdis.portal.core.domain.serviceplan.timeslot.ServicePlanTimeSlotList;
import lu.fujitsu.ts.cgdis.portal.core.exception.esb.EsbMessagingException;
import lu.fujitsu.ts.cgdis.portal.core.exception.serviceplan.VehicleStatusNotExistException;
import lu.fujitsu.ts.cgdis.portal.core.security.utils.CGDISPortalSecurityHolder;
import lu.fujitsu.ts.cgdis.portal.core.utils.ServicePlanTimeSlotUtils;
import lu.fujitsu.ts.cgdis.portal.processes.push.els.serviceplan.converter.OptionalBackupGroupForeignKeyToServicePlanTecidConverter;
import lu.fujitsu.ts.cgdis.portal.services.entity.vehicle.IVehicleStatusRepositoryService;
import lu.fujitsu.ts.cgdis.portal.services.serviceplan.*;
import lu.fujitsu.ts.eportal.server.core.exceptions.NotFoundException;
import lu.fujitsu.ts.eportal.server.core.utils.EPortalConverter;
import lu.fujitsu.ts.eportal.server.core.utils.IEPortalConverter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.dozer.Mapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.integration.transformer.GenericTransformer;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;

@Component
public class UpdateServicePlanPushRequestWithParametersTransformer implements GenericTransformer<UpdateServicePlanPushRequest, UpdateServicePlanPushRequestWithParameters> {

    private static final Logger LOGGER = LoggerFactory.getLogger(UpdateServicePlanPushRequestWithParametersTransformer.class);

    public static final String DEFAULT_VEHICLE_STATUS = "2";


    private IEPortalConverter<UpdateServicePlanPushRequest, UpdateServicePlanPushRequestWithParameters> converter;

    @Autowired
    private IServicePlanRepositoryService servicePlanRepositoryService;

    @Autowired
    private IOptionalBackupGroupRepositoryService optionalBackupGroupRepositoryService;

    /**
     * The Service plan version repository service.
     */
    @Autowired
    private IServicePlanVersionRepositoryService servicePlanVersionRepositoryService;

    /**
     * The Time slot repository service.
     */
    @Autowired
    private IServicePlanVersionTimeSlotRepositoryService timeSlotRepositoryService;

    @Autowired
    private IServicePlanTimeSlotCompletionStatusRepositoryService servicePlanTimeSlotCompletionStatusRepositoryService;

    @Autowired
    private IVehicleStatusRepositoryService vehicleStatusRepositoryService;

    @Autowired
    private IPrestationRepositoryService prestationRepositoryService;

    @Autowired
    private OptionalBackupGroupForeignKeyToServicePlanTecidConverter optionalBackupGroupForeignKeyToServicePlanTecidConverter;


    public UpdateServicePlanPushRequestWithParametersTransformer(Mapper mapper) {
        this.converter = new EPortalConverter<>(UpdateServicePlanPushRequest.class, UpdateServicePlanPushRequestWithParameters.class, mapper);
    }

    @Override
    public UpdateServicePlanPushRequestWithParameters transform(UpdateServicePlanPushRequest request) {
        try {
            return executeTransform(request);
        } catch (NotFoundException e) {
            throw new EsbMessagingException(e);
        }
    }

    private ImmutablePair<ServicePlanVersion, Optional<ServicePlanTimeSlot>> getServicePlanVersionAndSlot(Long servicePlanId, LocalDateTime atDate) throws NotFoundException {
        LocalDate executionDate = atDate.toLocalDate();
        ServicePlanVersion servicePlanVersion = null;
        try {
            servicePlanVersion = this.servicePlanVersionRepositoryService.getVersionAtDate(CGDISPortalSecurityHolder.getConnectedUserDetails(), servicePlanId, executionDate);
        } catch (NotFoundException e) {
            LOGGER.info("No version found for service {} at {}", servicePlanId, atDate);
            throw e;
        }
        LOGGER.info("Version {} found for service plan with id {} and date {}", servicePlanVersion.getTecid(), servicePlanId, executionDate);
        // get current time slot

        Optional<ServicePlanTimeSlot> timeSlotOptional = this.timeSlotRepositoryService.getTimeSlotByVersionAt(CGDISPortalSecurityHolder.getConnectedUserDetails(), servicePlanVersion.getTecid(), atDate);
        return ImmutablePair.of(servicePlanVersion, timeSlotOptional);

    }

    private UpdateServicePlanPushRequestWithParameters executeTransform(UpdateServicePlanPushRequest request) throws NotFoundException {
        Long servicePlanId = request.getServicePlanId();
        // get service plan
        ServicePlan servicePlan;
        ServicePlanVersion servicePlanVersion = null;
        ServicePlanTimeSlot timeSlot = null;

        List<Prestation> prestations;
        List<Prestation> previousPrestations;
        StringList finalVehicles = new StringList();

        servicePlan = this.servicePlanRepositoryService.get(CGDISPortalSecurityHolder.getConnectedUserDetails(), servicePlanId);

        LOGGER.info("Service plan with id {} found", servicePlanId);
        LocalDateTime executionDatetime = request.getUpdateDatetime();
        LocalDate executionDate = executionDatetime.toLocalDate();
        ImmutablePair<ServicePlanVersion, Optional<ServicePlanTimeSlot>> servicePlanVersionAndSlot = getServicePlanVersionAndSlot(servicePlanId, executionDatetime);
        LocalDate slotDate;
        servicePlanVersion = servicePlanVersionAndSlot.getLeft();
        if (servicePlanVersionAndSlot.getRight().isPresent()) {
            timeSlot = servicePlanVersionAndSlot.getRight().get();
            LOGGER.info("Timeslot  {} found for service plan version  with id {} and date {}", timeSlot.getTecid(), servicePlanVersion.getTecid(), executionDate);
            slotDate = ServicePlanTimeSlotUtils.getSlotDate(timeSlot, executionDatetime);
        } else {
            LOGGER.info("No Timeslot found for service plan version  with id {} and date {} ==> must search previous day", servicePlanVersion.getTecid(), executionDate);
            slotDate = executionDate.minusDays(1);
        }

        if (
                slotDate.isBefore(executionDate)
                        && servicePlanVersion.getStartDate().isEqual(executionDate)

        ) {
            // Slot is not started for this version => so the current slot is started one day before
            // ==> get version and last slot one day before (at time 23:59:59
            // Possible in case of split
            LocalDateTime theDate = slotDate.atTime(LocalTime.of(23, 59, 59));
            servicePlanVersionAndSlot = getServicePlanVersionAndSlot(servicePlanId, theDate);
            servicePlanVersion = servicePlanVersionAndSlot.getLeft();
            if (!servicePlanVersionAndSlot.getRight().isPresent()) {
                throw new NotFoundException(String.format("No slots found service plan %s for version %s at date %s", servicePlanId, servicePlanVersion.getTecid(), theDate));
            }
            timeSlot = servicePlanVersionAndSlot.getRight().get();
            LOGGER.info("Last Timeslot  {} found for previous service plan version  with id {} and date {}", timeSlot.getTecid(), servicePlanVersion.getTecid(), slotDate);

        }

        LocalDateTime prestationEndDateTime = ServicePlanTimeSlotUtils.getEndDateTime(timeSlot, slotDate);
        LocalDateTime prestationStartDateTime = slotDate.atTime(timeSlot.getStartTime());

        prestations = this.prestationRepositoryService.findAllByServicePlanTimeSlotAndDate(CGDISPortalSecurityHolder.getConnectedUserDetails(), Collections.singletonList(timeSlot.getTecid()), prestationStartDateTime, prestationEndDateTime);

        // Now search previous prestations
        ServicePlanUpdatePushParameterImpl parameter = computePreviousPrestations(servicePlanId, servicePlanVersion, timeSlot, slotDate);


        parameter.setServicePlan(servicePlan);
        parameter.setTimeSlot(timeSlot);

        parameter.setServicePlanVersion(servicePlanVersion);
        parameter.setExecutionDate(executionDatetime);
        parameter.setVehicleStatusHistoric(getCurrentVehicleStatus(servicePlanId));
        parameter.setCompletionStatus(getServicePlanCompletionStatus(timeSlot, executionDatetime));
        parameter.setPrestations(prestations);
        parameter.setBackupGroup(servicePlan.getBackupGroup());

        Optional<ServicePlanElsStatus> elsStatusForServicePlan = getElsStatusForServicePlan(servicePlanId, parameter.getCompletionStatus());
        parameter.setNextStatus(getNextStatus(elsStatusForServicePlan));

        UpdateServicePlanPushRequestWithParameters withParameters = converter.convert(request);
        withParameters.setParameters(parameter);

        List<Long> servicePlanIds = new ArrayList<>(Collections.singletonList(servicePlan.getTecid()));
        Set<String> currentLabels = new HashSet<>(Collections.singletonList(servicePlan.getLabel()));
        Set<String> currentForeignKeys = getOptionalBackupGroupForServicePlan(currentLabels);
        // Sometimes the backup group is not in the database => the previous search return no value
        // But the current service plan can be a backup group for another service plan and not be in the database
        // Compute the foreign key for this service plan
        optionalBackupGroupForeignKeyToServicePlanTecidConverter.convertToForeignKey(servicePlanId).ifPresent(currentForeignKeys::add);
        LOGGER.debug("Search for optional/backup group for service plan {}, with labels {} or foreign keys {}", servicePlanId, currentLabels, currentForeignKeys);
        boolean keepSearch = true;
        while (keepSearch && CollectionUtils.isNotEmpty(currentForeignKeys)) {
            keepSearch = false;
            List<ServicePlan> servicePlansBackup = getServiceWithBackupGroup(currentForeignKeys);
            List<ServicePlan> servicePlanOptional = getServiceWithOptionalGroup(currentForeignKeys);
            currentForeignKeys.clear();
            keepSearch = manageVehicleForServicePlan(servicePlansBackup, currentForeignKeys, currentLabels, servicePlanIds, finalVehicles, executionDate, executionDatetime, keepSearch, false);
            keepSearch = manageVehicleForServicePlan(servicePlanOptional, currentForeignKeys, currentLabels, servicePlanIds, finalVehicles, executionDate, executionDatetime, keepSearch, true);
        }

        withParameters.setAllVehicles(finalVehicles);

        return withParameters;
    }

    private ServicePlanUpdatePushParameterImpl computePreviousPrestations(Long servicePlanId, ServicePlanVersion servicePlanVersion, ServicePlanTimeSlot timeSlot, LocalDate slotDate) throws NotFoundException {
        List<Prestation> previousPrestations;
        ServicePlanTimeSlotList allTimeSlotForVersion = timeSlotRepositoryService.getAllTimeSlotForVersion(CGDISPortalSecurityHolder.getConnectedUserDetails(), servicePlanVersion.getTecid());
        Optional<ServicePlanTimeSlot> previous = Optional.empty();
        LocalDate previousSlotDate = slotDate;
        List<ServicePlanTimeSlot> slotsIterator = allTimeSlotForVersion.getSlotsAtDate(previousSlotDate);
        ServicePlanTimeSlot previousTemp = null;
        for (int i = 0; !previous.isPresent() && i < slotsIterator.size(); i++) {
            ServicePlanTimeSlot next = slotsIterator.get(i);
            if (next.getTecid().equals(timeSlot.getTecid())) {
                previous = Optional.ofNullable(previousTemp);

            }
            previousTemp = next;
        }

        if (!previous.isPresent()) {
            if (servicePlanVersion.getStartDate().equals(previousSlotDate)) {
                // the previous slot is the last slot on the previous version (day before)
                previousSlotDate = previousSlotDate.minusDays(1);
                LocalDateTime theDate = previousSlotDate.atTime(LocalTime.of(23, 59, 59));
                try {
                    ImmutablePair<ServicePlanVersion, Optional<ServicePlanTimeSlot>> servicePlanVersionAndSlot = getServicePlanVersionAndSlot(servicePlanId, theDate);
                    if (servicePlanVersionAndSlot.getRight().isPresent()) {
                        previous = servicePlanVersionAndSlot.getRight();
                    }
                } catch (NotFoundException e) {
                    LOGGER.info("No previous version of slot found for service plan {} at {} to retrieve previous prestations", servicePlanId, theDate);
                }
            } else {
                // The previoous slot is last slot of this version but on the day before
                previousSlotDate = previousSlotDate.minusDays(1);
                List<ServicePlanTimeSlot> slotsAtDate = allTimeSlotForVersion.getSlotsAtDate(previousSlotDate);
                previous = Optional.of(slotsAtDate.get(slotsAtDate.size() - 1));
            }
        }


        ServicePlanUpdatePushParameterImpl parameter = new ServicePlanUpdatePushParameterImpl();
        if (previous.isPresent()) {
            LOGGER.debug("Found previous slot {} for slot {} at {}", previous.get().getTecid(), timeSlot.getTecid(), previousSlotDate);
            ServicePlanTimeSlot previousSlot = previous.get();
            LocalDateTime previousStartDateTime = previousSlotDate.atTime(previousSlot.getStartTime());
            LocalDateTime previousEndDateTime = ServicePlanTimeSlotUtils.getEndDateTime(previousSlot, previousSlotDate);
            previousPrestations = this.prestationRepositoryService.findAllByServicePlanTimeSlotAndDate(
                    CGDISPortalSecurityHolder.getConnectedUserDetails(),
                    Collections.singletonList(previousSlot.getTecid()),
                    previousStartDateTime,
                    previousEndDateTime);
            parameter.setPreviousSlot(previousSlot);
            parameter.setPreviousPrestations(previousPrestations);
        }
        return parameter;
    }


    /**
     * Gets current vehicle status.
     *
     * @param servicePlanId the service plan id
     * @return the current vehicle status
     */
    private VehicleStatusHistoric getCurrentVehicleStatus(Long servicePlanId) {
        try {
            return servicePlanRepositoryService.getVehicleStatusForServicePlan(CGDISPortalSecurityHolder.getConnectedUserDetails(), servicePlanId);
        } catch (NotFoundException e) {
            LOGGER.info("No vehicle status found for service plan {}", servicePlanId);
            return null;
        }

    }

    /**
     * Gets current vehicle status.
     *
     * @param labels the labels
     * @return the current vehicle status
     */
    private Set<String> getOptionalBackupGroupForServicePlan(Set<String> labels) {
        return optionalBackupGroupRepositoryService.findForeignKeysByLables(labels);


    }

    /**
     * Gets current vehicle status.
     *
     * @param foreignKeys the foregnKeys
     * @return the current vehicle status
     */
    private List<ServicePlan> getServiceWithBackupGroup(Set<String> foreignKeys) {
        return servicePlanRepositoryService.findActivePlanByBackupForeignKeys(foreignKeys);
    }

    /**
     * Gets current vehicle status.
     *
     * @param foreignKeys the foregnKeys
     * @return the current vehicle status
     */
    private List<ServicePlan> getServiceWithOptionalGroup(Set<String> foreignKeys) {
        return servicePlanRepositoryService.findActivePlanByOptionalForeignKeys(foreignKeys);
    }

    private ServicePlanCompletionStatus getServicePlanCompletionStatus(ServicePlanTimeSlot timeSlot, LocalDateTime executionDateTime) {
        LocalDate slotDate = executionDateTime.toLocalDate();
        if (timeSlot.isOnTwoDays() && timeSlot.isEndTimeAfter(executionDateTime.toLocalTime())) {
            // In this case, the completion status must be retrieved the day before the execution date
            slotDate = slotDate.minusDays(1);
        }
        LOGGER.debug("Search completion status for slot {} and date {} for execution date {}", timeSlot.getTecid(), slotDate, executionDateTime);
        Optional<ServicePlanTimeSlotCompletionStatus> completionStatus = servicePlanTimeSlotCompletionStatusRepositoryService.getForTimeSlotAndDate(timeSlot.getTecid(), slotDate);
        if (completionStatus.isPresent()) {
            return completionStatus.get().getStatus();
        }

        LOGGER.info("No completion status found for timeslot {} and date {}(execution date = {}) => use default value {}", timeSlot, slotDate, executionDateTime, ServicePlanCompletionStatus.EMPTY);
        return ServicePlanCompletionStatus.EMPTY;


    }

    private Optional<ServicePlanElsStatus> getElsStatusForServicePlan(Long servicePlanId, ServicePlanCompletionStatus completionStatus) {
        try {
            return Optional.of(servicePlanRepositoryService.getElsStatusByServicePlanAndCompletionStatus(CGDISPortalSecurityHolder.getConnectedUserDetails(), servicePlanId, completionStatus));
        } catch (NotFoundException e) {
            LOGGER.debug("No els status found for service plan {} and completion status {}", servicePlanId, completionStatus);
            return Optional.empty();
        }

    }

    private VehicleStatus getNextStatus(Optional<ServicePlanElsStatus> servicePlanElsStatus) {

        try {
            if (servicePlanElsStatus.isPresent()) {
                return vehicleStatusRepositoryService.getStatusByValueForPortal(servicePlanElsStatus.get().getStatusValue());
            }
            LOGGER.info("use default vehicle status null");
            return null;

        } catch (NotFoundException e) {
            throw new VehicleStatusNotExistException(e);
        }

    }

    private boolean manageVehicleForServicePlan(List<ServicePlan> servicePlans, Set<String> currentForeignKeys, Set<String> currentLabels, List<Long> servicePlanIds, StringList finalVehicles, LocalDate executionDate, LocalDateTime executionDatetime, boolean keepSearch, boolean optional) throws NotFoundException {
        boolean localKeepSearch = false;
        if (CollectionUtils.isNotEmpty(servicePlans)) {
            currentLabels.clear();
            for (ServicePlan plan : servicePlans) {
                if (!servicePlanIds.contains(plan.getTecid()) && plan.getVehicle() != null) {
                    ServicePlanVersion currentVersion = this.servicePlanVersionRepositoryService.getVersionAtDate(CGDISPortalSecurityHolder.getConnectedUserDetails(), plan.getTecid(), executionDate);
                    LOGGER.info("Version {} found for service plan with id {} and date {}", currentVersion.getTecid(), plan.getTecid(), executionDate);
                    // get current time slot
                    ServicePlanTimeSlot currentTimeSlot;
                    Optional<ServicePlanTimeSlot> currentTimeSlotOptional = this.timeSlotRepositoryService.getTimeSlotByVersionAt(CGDISPortalSecurityHolder.getConnectedUserDetails(), currentVersion.getTecid(), executionDatetime);
                    if (currentTimeSlotOptional.isPresent()) {
                        currentTimeSlot = currentTimeSlotOptional.get();
                    } else {
                        // Maybe the slot is on two Days
                        LOGGER.info("No Timeslot found to manageVehicleForServicePlan for service plan version  with id {} and date {} ==> maybe search previous day", currentVersion.getTecid(), executionDate);
                        if (currentVersion.getStartDate().isEqual(executionDate)) {
                            LOGGER.info("Execution date {} is the start date of  service plan version  with id {}  ==> must search previous day", executionDatetime, currentVersion.getTecid());
                            LocalDateTime previousDate = executionDate.minusDays(1).atTime(LocalTime.of(23, 59, 59));
                            ImmutablePair<ServicePlanVersion, Optional<ServicePlanTimeSlot>> servicePlanVersionAndSlot = getServicePlanVersionAndSlot(plan.getTecid(), previousDate);
                            if (!servicePlanVersionAndSlot.getRight().isPresent()) {
                                throw new NotFoundException(String.format("No slots found service plan %s at date %s", plan.getTecid(), previousDate));
                            }
                            currentTimeSlot = servicePlanVersionAndSlot.getRight().get();
                        } else {
                            throw new NotFoundException("No slot found for version " + currentVersion.getTecid() + " at " + executionDatetime);
                        }
                    }
                    if (optional || BackupGroupStatusUtils.mustActivateBackupGroupForStatus(plan, getServicePlanCompletionStatus(currentTimeSlot, executionDatetime))) {
                        servicePlanIds.add(plan.getTecid());
                        currentLabels.add(plan.getLabel());
                        finalVehicles.add(plan.getVehicle().getId());
                        localKeepSearch = true;
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(currentLabels)) {
                currentForeignKeys.addAll(getOptionalBackupGroupForServicePlan(currentLabels));
            }
        }
        return keepSearch || localKeepSearch;
    }

}
