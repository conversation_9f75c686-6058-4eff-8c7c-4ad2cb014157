
package lu.fujitsu.ts.cgdis.portal.processes.config;

import lu.fujitsu.ts.cgdis.portal.processes.input.msnav.personalinformations.config.DeletePersonConfig;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.context.annotation.Profile;

@Profile("input-delete-person-2-db")
@Configuration
@Import({DeletePersonConfig.class, ProcessBusinessConfig.class})
public class InputDeletePersonConfig {

}

