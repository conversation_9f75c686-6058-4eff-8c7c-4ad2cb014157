package lu.fujitsu.ts.cgdis.portal.processes.input.geps.xlsx.service;

import lu.fujitsu.ts.cgdis.portal.core.domain.person.Aptitude;
import lu.fujitsu.ts.cgdis.portal.core.domain.person.AptitudeStatut;
import lu.fujitsu.ts.cgdis.portal.core.exception.esb.EsbMessagingException;
import lu.fujitsu.ts.cgdis.portal.core.logging.LoggingCategory;
import lu.fujitsu.ts.cgdis.portal.core.logging.LoggingDomain;
import lu.fujitsu.ts.cgdis.portal.esb.commons.domain.geps.input.AptitudeXlsxItem;
import lu.fujitsu.ts.cgdis.portal.esb.commons.domain.geps.input.PersonAptitudesXlsxRequest;
import lu.fujitsu.ts.cgdis.portal.processes.input.geps.xlsx.properties.GepsXlsxProperties;
import lu.fujitsu.ts.cgdis.portal.services.person.IAptitudeRepositoryService;
import lu.fujitsu.ts.cgdis.portal.services.person.IAptitudeStatutRepositoryService;
import lu.fujitsu.ts.eportal.server.logging.milestones.LogMilestone;
import org.apache.poi.hssf.util.CellReference;
import org.apache.poi.openxml4j.exceptions.OpenXML4JException;
import org.apache.poi.ss.usermodel.*;
import org.dozer.Mapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * The type Insert aptitudes service.
 */
@Service
@LogMilestone(category = LoggingCategory.ESB, domain = LoggingDomain.APTITUDE)
public class ReadXlsxService {

    /**
     * The constant LOGGER.
     */
    private static final Logger LOGGER = LoggerFactory.getLogger(ReadXlsxService.class);

    private static final String END_DATE_COLUMN = "AY";

    private static final String CGDIS_REGISTRATION_NUMBER_COLUMN = "E";

    private static final String APTITUDE_A_COLUMN = "V";

    private static final String APTITUDE_B_COLUMN = "W";

    private static final String APTITUDE_ARI_COLUMN = "X";

    private static final String APTITUDE_S_COLUMN = "Y";

    private static final String APTITUDE_P_COLUMN = "Z";

    @Autowired
    private GepsXlsxProperties properties;

    /**
     * The Person aptitude repository service.
     */
    @Autowired
    private IAptitudeRepositoryService aptitudeRepositoryService;

    /**
     * The Person aptitude repository service.
     */
    @Autowired
    private IAptitudeStatutRepositoryService aptitudeStatutRepositoryService;

    /**
     * Instantiates a new Update aptitudes service.
     *
     * @param mapper the mapper
     */
    @Autowired
    public ReadXlsxService(Mapper mapper) {
    }

    public List<PersonAptitudesXlsxRequest> readXlsx() throws IOException, OpenXML4JException {

        List<PersonAptitudesXlsxRequest> requestList = new ArrayList<>();
        List<Aptitude> aptitudes = aptitudeRepositoryService.getAll();
        List<AptitudeStatut> aptitudeStatus = aptitudeStatutRepositoryService.getAll();

        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");
        LOGGER.debug("read file...");



        try (InputStream inputStream = new FileInputStream(this.properties.getFileName())) {
            try(Workbook workbook = WorkbookFactory.create(inputStream)) {
                Sheet sheet = workbook.getSheetAt(0);
                LOGGER.debug("sheet created");
                int rowStart = 3;
                int rowEnd = 4427;
                for (int rowNum = rowStart; rowNum < rowEnd; rowNum++) {
                    Row r = sheet.getRow(rowNum);
                    if (r == null) {
                        // This whole row is empty
                        // Handle it as needed
                        continue;
                    }
                    PersonAptitudesXlsxRequest person = new PersonAptitudesXlsxRequest();
                    person.setAptitudes(new ArrayList<>());
                    LocalDate endDate = null;

                    FormulaEvaluator evaluator = workbook.getCreationHelper().createFormulaEvaluator();

                    int cgdisRegistrationNumberIndex = CellReference.convertColStringToIndex(CGDIS_REGISTRATION_NUMBER_COLUMN);
                    Cell cgdisRegistrationNumber = r.getCell(cgdisRegistrationNumberIndex);
                    if (cgdisRegistrationNumber != null) {
                        person.setCgdisRegistrationNumber(evaluator.evaluate(cgdisRegistrationNumber).getStringValue());
                    } else {
                        continue;
                    }

                    int endDateIndex = CellReference.convertColStringToIndex(END_DATE_COLUMN);
                    Cell endDateCell = r.getCell(endDateIndex);
                    if (endDateCell != null && endDateCell.getCellTypeEnum() == CellType.FORMULA) {
                        LOGGER.debug("Formula is {}", endDateCell.getCellFormula());
                        switch(endDateCell.getCachedFormulaResultType()) {
                            case Cell.CELL_TYPE_NUMERIC:
                                LOGGER.debug("Last evaluated as: {}", endDateCell.getNumericCellValue());
                                Date date =  endDateCell.getDateCellValue();
                                endDate = date != null ? date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate() : null;
                                break;
                            case Cell.CELL_TYPE_STRING:
                                LOGGER.debug("Last evaluated as {}", endDateCell.getRichStringCellValue());
                                String dateString = endDateCell.getStringCellValue();
                                endDate = dateString != null && !"".equals(dateString) && !"Existeiert net".equals(dateString) && !"doublon".equals(dateString) ? LocalDate.parse(dateString, dateFormatter) :  null;
                                break;
                            default:
                                break;
                        }

                    }

                    int aptitudeAIndex = CellReference.convertColStringToIndex(APTITUDE_A_COLUMN);
                    Cell aptitudeACell = r.getCell(aptitudeAIndex);
                    if (aptitudeACell != null) {
                        String status = aptitudeACell.getStringCellValue();
                        Optional<AptitudeStatut> statut = aptitudeStatus.stream().filter(aptitudeStatut -> aptitudeStatut.getName().equals(status)).findFirst();
                        Optional<Aptitude> aptitude = aptitudes.stream().filter(aptitude1 -> aptitude1.getExternalId().equals("A")).findFirst();
                        if (statut.isPresent() && aptitude.isPresent()) {
                            person.getAptitudes().add(createAptitudeItem(endDate, statut.get(), aptitude.get()));
                        }
                    }

                    int aptitudeBIndex = CellReference.convertColStringToIndex(APTITUDE_B_COLUMN);
                    Cell aptitudeBCell = r.getCell(aptitudeBIndex);
                    if (aptitudeBCell != null) {
                        String status = aptitudeBCell.getStringCellValue();
                        Optional<AptitudeStatut> statut = aptitudeStatus.stream().filter(aptitudeStatut -> aptitudeStatut.getName().equals(status)).findFirst();
                        Optional<Aptitude> aptitude = aptitudes.stream().filter(aptitude1 -> aptitude1.getExternalId().equals("B")).findFirst();
                        if (statut.isPresent() && aptitude.isPresent()) {
                            person.getAptitudes().add(createAptitudeItem(endDate, statut.get(), aptitude.get()));
                        }
                    }

                    int aptitudeARIIndex = CellReference.convertColStringToIndex(APTITUDE_ARI_COLUMN);
                    Cell aptitudeARICell = r.getCell(aptitudeARIIndex);
                    if (aptitudeARICell != null) {
                        String status = aptitudeARICell.getStringCellValue();
                        Optional<AptitudeStatut> statut = aptitudeStatus.stream().filter(aptitudeStatut -> aptitudeStatut.getName().equals(status)).findFirst();
                        Optional<Aptitude> aptitude = aptitudes.stream().filter(aptitude1 -> aptitude1.getExternalId().equals("APT1")).findFirst();
                        if (statut.isPresent() && aptitude.isPresent()) {
                            person.getAptitudes().add(createAptitudeItem(endDate, statut.get(), aptitude.get()));
                        }
                    }

                    int aptitudeSIndex = CellReference.convertColStringToIndex(APTITUDE_S_COLUMN);
                    Cell aptitudeSCell = r.getCell(aptitudeSIndex);
                    if (aptitudeSCell != null) {
                        String status = aptitudeSCell.getStringCellValue();
                        Optional<AptitudeStatut> statut = aptitudeStatus.stream().filter(aptitudeStatut -> aptitudeStatut.getName().equals(status)).findFirst();
                        Optional<Aptitude> aptitude = aptitudes.stream().filter(aptitude1 -> aptitude1.getExternalId().equals("S")).findFirst();
                        if (statut.isPresent() && aptitude.isPresent()) {
                            person.getAptitudes().add(createAptitudeItem(endDate, statut.get(), aptitude.get()));
                        }
                    }

                    int aptitudePIndex = CellReference.convertColStringToIndex(APTITUDE_P_COLUMN);
                    Cell aptitudePCell = r.getCell(aptitudePIndex);
                    if (aptitudePCell != null) {
                        String status = aptitudePCell.getStringCellValue();
                        Optional<AptitudeStatut> statut = aptitudeStatus.stream().filter(aptitudeStatut -> aptitudeStatut.getName().equals(status)).findFirst();
                        Optional<Aptitude> aptitude = aptitudes.stream().filter(aptitude1 -> aptitude1.getExternalId().equals("P")).findFirst();
                        if (statut.isPresent() && aptitude.isPresent()) {
                            person.getAptitudes().add(createAptitudeItem(endDate, statut.get(), aptitude.get()));
                        }
                    }
                    LOGGER.debug("add {}", person);
                    requestList.add(person);
                }
            } catch (IOException e) {
                throw new EsbMessagingException("Erreur when reading xlsx", e);
            }
        } catch (IOException e) {
            throw new EsbMessagingException("Erreur when reading xlsx", e);
        }

        LOGGER.debug("file readed");



        return requestList;
    }

    private AptitudeXlsxItem createAptitudeItem(LocalDate endDate, AptitudeStatut statut, Aptitude aptitude) {
        AptitudeXlsxItem item = new AptitudeXlsxItem();
        item.setEndDate(endDate);
        item.setStatus(statut);
        item.setAptitude(aptitude);
        return item;
    }
}
