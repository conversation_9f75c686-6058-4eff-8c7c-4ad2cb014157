package lu.fujitsu.ts.cgdis.portal.processes.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import lu.fujitsu.ts.cgdis.portal.connector.portaldb.config.CGDISPortalDbConnectorConfig;
import lu.fujitsu.ts.cgdis.portal.connector.portaldb.config.DatabaseConfiguration;
import lu.fujitsu.ts.cgdis.portal.esb.commons.kafka.KafkaAdminConfig;
import lu.fujitsu.ts.cgdis.portal.esb.commons.kafka.properties.KafkaTopicsProperties;
import lu.fujitsu.ts.eportal.server.logging.milestones.EPortalMilestoneLoggingAOPConfig;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.*;
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder;

@Profile("process-function-operational")
@Configuration
@Import({
        CGDISPortalDbConnectorConfig.class,
        DatabaseConfiguration.class,
        KafkaAdminConfig.class,
        EPortalMilestoneLoggingAOPConfig.class
})
@EnableConfigurationProperties({
        KafkaTopicsProperties.class
})
@ComponentScan({
        "lu.fujitsu.ts.cgdis.portal.processes.portal.functionoperational"
})
public class ProcessFunctionOperationalConfig {

    @Bean
    @Primary
    public ObjectMapper jsonMapper(Jackson2ObjectMapperBuilder builder){
        ObjectMapper build = builder.json().build();
        return build;
    }

}
