package lu.fujitsu.ts.cgdis.portal.business.services.prestation.allowance.v1;

import lu.fujitsu.ts.cgdis.portal.business.services.prestation.allowance.PrestationAllowanceRangeDateTimeWithOrderTest;
import lu.fujitsu.ts.cgdis.portal.business.services.utils.stack.IStackWithOrderBusinessService;
import lu.fujitsu.ts.cgdis.portal.core.pojo.date.IRangeDateTimeWithOrderAndTecid;
import lu.fujitsu.ts.cgdis.portal.core.pojo.prestation.allowance.PrestationAllowanceConfiguration;
import lu.fujitsu.ts.cgdis.portal.core.pojo.prestation.allowance.PrestationAllowanceResultList;
import lu.fujitsu.ts.cgdis.portal.core.pojo.prestation.allowance.PrestationForAllowance;
import lu.fujitsu.ts.cgdis.portal.core.pojo.prestation.allowance.v1.PrestationAllowanceConfigurationV1;
import lu.fujitsu.ts.cgdis.portal.core.pojo.prestation.allowance.v1.PrestationAllowanceResultDetailV1;
import lu.fujitsu.ts.cgdis.portal.core.pojo.prestation.allowance.v1.PrestationAllowanceResultV1;
import lu.fujitsu.ts.cgdis.portal.core.security.ICGDISSecurityConstraint;
import lu.fujitsu.ts.cgdis.portal.services.prestation.IPrestationAllowanceRepositoryService;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.*;

@ExtendWith(SpringExtension.class)
@DisplayName("PrestationAllowanceComputingService")
class PrestationAllowanceComputingV1ServiceTest {

    @Mock
    private IPrestationAllowanceRepositoryService prestationAllowanceRepositoryService;

    @Mock
    private IStackWithOrderBusinessService stackWithOrderBusinessService;

    @Mock
    private ICGDISSecurityConstraint securityConstraint;

    @InjectMocks
    private PrestationAllowanceComputingV1Service prestationAllowanceComputingService;

    @BeforeEach
    void setUp() {

    }

    @Test
    @DisplayName("Should return zero allowance when no prestations are found for the person and date")
    void computeAllowanceWhenNoPrestationsFound() {
        Long personId = 1L;
        LocalDate date = LocalDate.now();

        when(prestationAllowanceRepositoryService.findAllPrestationsForAllowance(Optional.of(personId), Optional.empty(), date, Boolean.FALSE, null))
                .thenReturn(Collections.emptyList());
        when(stackWithOrderBusinessService.stacks(anyList())).thenReturn(Collections.emptyList());


        PrestationAllowanceConfigurationV1 v1Config = PrestationAllowanceConfigurationV1.builder().barrackedAmount(new BigDecimal(100)).notBarrackedAmount(new BigDecimal(55)).build();
        PrestationAllowanceConfiguration<PrestationAllowanceConfigurationV1> configuration = PrestationAllowanceConfiguration.<PrestationAllowanceConfigurationV1>builder().configuration(v1Config).build();
        PrestationAllowanceResultList<PrestationAllowanceResultV1> result = prestationAllowanceComputingService.compute(personId, date, configuration);

        assertEquals(BigDecimal.ZERO, result.getTotal());
    }

    @Test
    @DisplayName("Should handle overlapping prestations correctly when computing allowance")
    void computeAllowanceWithOverlappingPrestations() {

        // Create test data
        Long personId = 12345L;
        LocalDate date = LocalDate.now();
        Long entityTecid = 100L;
        long servicePlanTecid = 100L;
        // Create prestationForAllowances list
        List<PrestationForAllowance> prestationForAllowances = new ArrayList<>();
        PrestationForAllowance prestation1 = new PrestationForAllowance();
        prestation1.setServicePlanTecid(servicePlanTecid);
        prestation1.setEntityTecid(entityTecid);
        prestation1.setTecid(1L);
        prestation1.setStartDateTime(LocalDateTime.of(date, LocalTime.of(8, 0)));
        prestation1.setEndDateTime(LocalDateTime.of(date, LocalTime.of(12, 0)));
        prestation1.setBarracked(false);
        prestationForAllowances.add(prestation1);
        PrestationForAllowance prestation2 = new PrestationForAllowance();
        prestation2.setTecid(2L);
        prestation2.setServicePlanTecid(servicePlanTecid);
        prestation2.setEntityTecid(entityTecid);
        prestation2.setStartDateTime(LocalDateTime.of(date, LocalTime.of(10, 0)));
        prestation2.setEndDateTime(LocalDateTime.of(date, LocalTime.of(14, 0)));
        prestation2.setBarracked(true);
        prestationForAllowances.add(prestation2);

        when(stackWithOrderBusinessService.stacks(anyList())).thenReturn(Arrays.asList(
                PrestationAllowanceRangeDateTimeWithOrderTest.builder().tecid(1L).startDateTime(prestation1.getStartDateTime()).endDateTime(prestation1.getEndDateTime()).stackOrder(1).build(),
                PrestationAllowanceRangeDateTimeWithOrderTest.builder().tecid(2L).startDateTime(prestation2.getStartDateTime()).endDateTime(prestation2.getEndDateTime()).stackOrder(0).build()
        ));

        // Mock the behavior of prestationAllowanceRepositoryService
        when(prestationAllowanceRepositoryService.findAllPrestationsForAllowance(Optional.of(personId), Optional.empty(), date, Boolean.FALSE, null))
                .thenReturn(prestationForAllowances);

        PrestationAllowanceConfigurationV1 v1configuration = PrestationAllowanceConfigurationV1.builder().barrackedAmount(new BigDecimal(100)).notBarrackedAmount(new BigDecimal(1)).build();
        PrestationAllowanceConfiguration<PrestationAllowanceConfigurationV1> configuration = PrestationAllowanceConfiguration.<PrestationAllowanceConfigurationV1>builder().configuration(v1configuration).build();
        // Call the compute method
        PrestationAllowanceResultList<PrestationAllowanceResultV1> allowance = prestationAllowanceComputingService.compute(personId, date, configuration);

        // Verify the behavior
        assertEquals(BigDecimal.valueOf(404).setScale(4), allowance.getTotal());
        verify(stackWithOrderBusinessService, times(1)).stacks(anyList());
        verify(prestationAllowanceRepositoryService, times(1))
                .findAllPrestationsForAllowance(Optional.of(personId), Optional.empty(), date, Boolean.FALSE, null);
    }


    @Test
    @DisplayName("Should compute the correct allowance for a given person and date")
    void computeAllowanceForPersonAndDate() {
        Long personId = 12345L;
        LocalDate date = LocalDate.now();
        long servicePlanTecid1 = 100L;
        long servicePlanTecid2 = 200L;
        Long entityId1 = 100L;
        Long entityId2 = 200L;
        List<PrestationForAllowance> prestationForAllowances = new ArrayList<>();
        PrestationForAllowance prestation1 = new PrestationForAllowance();
        prestation1.setServicePlanTecid(servicePlanTecid1);
        prestation1.setEntityTecid(entityId1);
        prestation1.setTecid(1L);
        LocalDateTime startDatetime1 = LocalDateTime.of(date, LocalTime.of(8, 0));
        prestation1.setStartDateTime(startDatetime1);
        LocalDateTime endDatetime1 = LocalDateTime.of(date, LocalTime.of(12, 0));
        prestation1.setEndDateTime(endDatetime1);
        prestation1.setBarracked(true);
        prestationForAllowances.add(prestation1);

        PrestationForAllowance prestation2 = new PrestationForAllowance();
        prestation2.setServicePlanTecid(servicePlanTecid2);
        prestation2.setEntityTecid(entityId2);
        prestation2.setTecid(2L);
        LocalDateTime startDatetime2 = LocalDateTime.of(date, LocalTime.of(13, 0));
        prestation2.setStartDateTime(startDatetime2);
        LocalDateTime endDatetime2 = LocalDateTime.of(date, LocalTime.of(17, 0));
        prestation2.setEndDateTime(endDatetime2);
        prestation2.setBarracked(false);
        prestationForAllowances.add(prestation2);

        when(prestationAllowanceRepositoryService.findAllPrestationsForAllowance(Optional.of(personId), Optional.empty(), date, Boolean.FALSE, null)).thenReturn(prestationForAllowances);

        IRangeDateTimeWithOrderAndTecid stack1 = mock(IRangeDateTimeWithOrderAndTecid.class);
        when(stack1.getStackOrder()).thenReturn(0);
        when(stack1.getTecid()).thenReturn(1L);
        when(stack1.getStartDateTime()).thenReturn(startDatetime1);
        when(stack1.getEndDateTime()).thenReturn(endDatetime1);

        IRangeDateTimeWithOrderAndTecid stack2 = mock(IRangeDateTimeWithOrderAndTecid.class);
        when(stack2.getStackOrder()).thenReturn(1);
        when(stack2.getTecid()).thenReturn(2L);
        when(stack2.getStartDateTime()).thenReturn(startDatetime2);
        when(stack2.getEndDateTime()).thenReturn(endDatetime2);


        when(stackWithOrderBusinessService.stacks(anyList())).thenReturn(Arrays.asList(stack1), Arrays.asList(stack2));
        PrestationAllowanceConfigurationV1 v1configuration = PrestationAllowanceConfigurationV1.builder()
                .barrackedAmount(new BigDecimal(100)).notBarrackedAmount(new BigDecimal(5)).build();
        PrestationAllowanceConfiguration<PrestationAllowanceConfigurationV1> configuration = PrestationAllowanceConfiguration.<PrestationAllowanceConfigurationV1>builder().configuration(v1configuration).build();
        PrestationAllowanceResultList<PrestationAllowanceResultV1> result = prestationAllowanceComputingService.compute(personId, date, configuration);

        BigDecimal expectedForServicePlan1 = BigDecimal.valueOf(4).multiply(configuration.getConfiguration().getBarrackedAmount());
        BigDecimal expectedForServicePlan2 = BigDecimal.valueOf(4).multiply(configuration.getConfiguration().getNotBarrackedAmount());
        BigDecimal expectedAllowance = expectedForServicePlan1.add(expectedForServicePlan2);

        // Convert result.getResults in Map<Long, BigDecimal> to compare with expected result
        Map<Object, BigDecimal> results = result.getResults().stream().flatMap(r->r.getDetails().stream())
                .collect(Collectors.toMap(PrestationAllowanceResultDetailV1::getEntityTecid, PrestationAllowanceResultDetailV1::getAllowance));

        Assertions.assertThat(expectedAllowance.setScale(4, BigDecimal.ROUND_HALF_UP)).isEqualTo(result.getTotal());
        assertEquals(expectedForServicePlan1.setScale(4, BigDecimal.ROUND_HALF_UP), results.get(servicePlanTecid1));
        assertEquals(expectedForServicePlan2.setScale(4, BigDecimal.ROUND_HALF_UP), results.get(servicePlanTecid2));
    }

    @Test
    @DisplayName("Should handle non-overlapping prestations correctly when computing allowance")
    void computeAllowanceWithNonOverlappingPrestations() {
        Long personId = 1L;
        LocalDate date = LocalDate.now();
        Long entityId = 150L;
        long servicePlanTecid = 100L;
        PrestationForAllowance prestation1 = PrestationForAllowance.builder()
                .personTecid(personId)
                .entityTecid(entityId)
                .tecid(1L)
                .prestationStartDate(date)
                .servicePlanTecid(servicePlanTecid)
                .startDateTime(date.atTime(8, 0))
                .endDateTime(date.atTime(12, 0))
                .barracked(false)
                .build();


        PrestationForAllowance prestation2 = PrestationForAllowance.builder()
                .personTecid(personId)
                .servicePlanTecid(servicePlanTecid)
                .tecid(2L)
                .entityTecid(entityId)
                .prestationStartDate(date)
                .startDateTime(date.atTime(13, 0))
                .endDateTime(date.atTime(17, 0))
                .barracked(false)
                .build();

        List<PrestationForAllowance> prestationList = Arrays.asList(prestation1, prestation2);

        when(prestationAllowanceRepositoryService.findAllPrestationsForAllowance(Optional.of(personId), Optional.empty(), date, Boolean.FALSE, null))
                .thenReturn(prestationList);
        when(stackWithOrderBusinessService.stacks(anyList())).thenReturn(Collections.emptyList());
        PrestationAllowanceConfigurationV1 v1configuration = PrestationAllowanceConfigurationV1.builder().barrackedAmount(new BigDecimal(100)).notBarrackedAmount(new BigDecimal(5)).build();
        PrestationAllowanceConfiguration<PrestationAllowanceConfigurationV1> configuration = PrestationAllowanceConfiguration.<PrestationAllowanceConfigurationV1>builder().configuration(v1configuration).build();
        PrestationAllowanceResultList<PrestationAllowanceResultV1> result = prestationAllowanceComputingService.compute(personId, date, configuration);

        assertEquals(BigDecimal.ZERO, result.getTotal());
        verify(prestationAllowanceRepositoryService, times(1)).findAllPrestationsForAllowance(Optional.of(personId), Optional.empty(), date, Boolean.FALSE, null);
        verify(stackWithOrderBusinessService, times(1)).stacks(anyList());
    }

    @Test
    @DisplayName("Should handle a mix of overlapping and non-overlapping prestations when computing allowance")
    void computeAllowanceWithMixedPrestations() {
        Long personId = 12345L;
        LocalDate date = LocalDate.now();

        PrestationForAllowance prestation1 = PrestationForAllowance.builder()
                .personTecid(personId)
                .prestationStartDate(date)
                .tecid(1L)
                .startDateTime(date.atTime(8, 0))
                .endDateTime(date.atTime(12, 0))
                .isProfessional(true)
                .barracked(false)
                .entityTecid(1L)
                .servicePlanTecid(1L)
                .build();

        PrestationForAllowance prestation2 = PrestationForAllowance.builder()
                .personTecid(personId)
                .prestationStartDate(date)
                .tecid(2L)
                .startDateTime(date.atTime(10, 0))
                .endDateTime(date.atTime(14, 0))
                .isProfessional(true)
                .barracked(false)
                .entityTecid(1L)
                .servicePlanTecid(1L)
                .build();

        PrestationForAllowance prestation3 = PrestationForAllowance.builder()
                .personTecid(personId)
                .prestationStartDate(date)
                .tecid(3L)
                .startDateTime(date.atTime(16, 0))
                .endDateTime(date.atTime(18, 0))
                .isProfessional(true)
                .barracked(false)
                .entityTecid(1L)
                .servicePlanTecid(1L)
                .build();

        List<PrestationForAllowance> prestationList = Arrays.asList(prestation1, prestation2, prestation3);

        when(prestationAllowanceRepositoryService.findAllPrestationsForAllowance(Optional.of(personId), Optional.empty(), date, Boolean.FALSE, null))
                .thenReturn(prestationList);
        when(stackWithOrderBusinessService.stacks(anyList())).thenReturn(Collections.emptyList());
        PrestationAllowanceConfigurationV1 v1Configuration = PrestationAllowanceConfigurationV1.builder().barrackedAmount(new BigDecimal(100)).notBarrackedAmount(new BigDecimal(5)).build();
        PrestationAllowanceConfiguration<PrestationAllowanceConfigurationV1> configuration = PrestationAllowanceConfiguration.<PrestationAllowanceConfigurationV1>builder().configuration(v1Configuration).build();
        PrestationAllowanceResultList<PrestationAllowanceResultV1> result = prestationAllowanceComputingService.compute(personId, date, configuration);

        assertEquals(BigDecimal.ZERO, result.getTotal());
        verify(prestationAllowanceRepositoryService, times(1)).findAllPrestationsForAllowance(Optional.of(personId), Optional.empty(), date, Boolean.FALSE, null);
        verify(stackWithOrderBusinessService, times(1)).stacks(anyList());
    }

    @Test
    @DisplayName("Should set 0 in allowance in there is no prestations")
    void computeAllowanceWithNoPrestations() {
        Long personId = 12345L;
        LocalDate date = LocalDate.now();



        List<PrestationForAllowance> prestationList = Arrays.asList();

        when(prestationAllowanceRepositoryService.findAllPrestationsForAllowance(Optional.of(personId), Optional.empty(), date, Boolean.FALSE, null))
                .thenReturn(prestationList);
        when(stackWithOrderBusinessService.stacks(anyList())).thenReturn(Collections.emptyList());
        PrestationAllowanceConfigurationV1 v1Configuration = PrestationAllowanceConfigurationV1.builder().barrackedAmount(new BigDecimal(100)).notBarrackedAmount(new BigDecimal(5)).build();
        PrestationAllowanceConfiguration<PrestationAllowanceConfigurationV1> configuration = PrestationAllowanceConfiguration.<PrestationAllowanceConfigurationV1>builder().configuration(v1Configuration).build();
        PrestationAllowanceResultList<PrestationAllowanceResultV1> result = prestationAllowanceComputingService.compute(personId, date, configuration);

        assertEquals(BigDecimal.ZERO, result.getTotal());
        verify(prestationAllowanceRepositoryService, times(1)).findAllPrestationsForAllowance(Optional.of(personId), Optional.empty(), date, Boolean.FALSE, null);
        verify(stackWithOrderBusinessService, times(0)).stacks(anyList());
    }

}
