package lu.fujitsu.ts.cgdis.portal.business.services.translation;

import lu.fujitsu.ts.cgdis.portal.core.domain.translation.Translation;
import lu.fujitsu.ts.cgdis.portal.services.translation.ITranslationRepositoryService;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class TranslationBusinessServiceTest {

    @InjectMocks
    private TranslationBusinessService businessService;

    @Mock
    private ITranslationRepositoryService translationRepositoryService;

    @Before
    public void init() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void getAllByLanguage() {
        String language = "fr";
        List<Translation> list = new ArrayList<>();

        Translation translation1 = new Translation();
        translation1.setTranslationKey("1");
        translation1.setTranslation("testTranslation1");
        translation1.setLanguage("fr");

        Translation translation2 = new Translation();
        translation2.setTranslationKey(".+*ç%&/(/)");
        translation2.setLanguage("fr");

        Translation translation3 = new Translation();
        translation3.setTranslation("testTranslation2");
        translation3.setLanguage("fr");

        Translation translation4 = new Translation();
        translation4.setTranslationKey("4");
        translation4.setLanguage("fr");

        list.add(translation1);
        list.add(translation2);
        list.add(translation3);
        list.add(translation4);

        List<Translation> listWellFormatted = new ArrayList<>();
        listWellFormatted.add(translation1);
        listWellFormatted.add(translation4);

        BDDMockito.given(translationRepositoryService.getAllByLanguage(Mockito.anyString())).willReturn(list);
        Assert.assertEquals(listWellFormatted, this.businessService.getAllByLanguage(language));
    }

    @Test
    public void getAllByLanguageAsMapWhenListEmpty() {
        String language = "fr";
        List<Translation> list = new ArrayList<>();
        BDDMockito.given(translationRepositoryService.getAllByLanguage(Mockito.anyString())).willReturn(list);

        Map<String,String> map = list.stream().collect(Collectors.toMap(
                Translation::getTranslationKey,
                Translation::getTranslation));
        Assert.assertEquals(map, this.businessService.getAllByLanguageAsMap(language));
    }

    @Test
    public void getAll() {
        List<Translation> list = new ArrayList<>();
        BDDMockito.given(translationRepositoryService.getAll()).willReturn(list);
        Assert.assertEquals(list, this.businessService.getAll());
    }

}
