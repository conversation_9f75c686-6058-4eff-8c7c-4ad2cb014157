package lu.fujitsu.ts.cgdis.portal.business.services.assignment;

import lu.fujitsu.ts.cgdis.portal.business.config.AbstractBusinessTest;
import lu.fujitsu.ts.cgdis.portal.business.config.WithBusinessCGDISPortalUser;
import lu.fujitsu.ts.cgdis.portal.core.domain.CGDISEntity;
import lu.fujitsu.ts.cgdis.portal.core.domain.CGDISSecurityConstraint;
import lu.fujitsu.ts.cgdis.portal.core.domain.assignement.Assignment;
import lu.fujitsu.ts.cgdis.portal.core.domain.person.Person;
import lu.fujitsu.ts.cgdis.portal.core.security.Permission;
import lu.fujitsu.ts.cgdis.portal.services.assignment.IAssignmentRepositoryService;
import lu.fujitsu.ts.cgdis.portal.services.entity.ICGDISEntityRepositoryService;
import lu.fujitsu.ts.eportal.server.core.domain.SearchCriterion;
import lu.fujitsu.ts.eportal.server.core.exceptions.NotFoundException;
import org.assertj.core.api.Assertions;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDate;
import java.util.*;

import static org.mockito.ArgumentMatchers.any;

@WithBusinessCGDISPortalUser(username = "test",allAuthorities = true)
public class AssignmentBusinessServiceTest extends AbstractBusinessTest {

    @InjectMocks
    private AssignmentBusinessService businessService ;

    @Mock
    private IAssignmentRepositoryService assignmentRepositoryService;

    @Mock
    private ICGDISEntityRepositoryService repositoryService;

    private static final Logger LOGGER = LoggerFactory.getLogger(AssignmentBusinessServiceTest.class);


    @Before
    public void setUp() {
        assignmentRepositoryService = Mockito.mock(IAssignmentRepositoryService.class);
        repositoryService = Mockito.mock(ICGDISEntityRepositoryService.class);
        businessService = new AssignmentBusinessService(assignmentRepositoryService, repositoryService);
        MockitoAnnotations.initMocks(this);
    }

    private Assignment createAssignment(Long personId, Long entityId){
        // Create person
        Person person = new Person();
        person.setTecid(personId);

        // Create entity
        CGDISEntity entity = new CGDISEntity();
        entity.setTecid(entityId);

        // Create assignment
        Assignment assignment= new Assignment();
        assignment.setPerson(person);
        assignment.setEntity(entity);
        return assignment;

    }

    @Test
    public void findAllCurrentAssignmentsForEntityAndPersonTest() throws NotFoundException {
        List<Assignment> assignments = new ArrayList<>();
        BDDMockito.willReturn(assignments).given(assignmentRepositoryService).findAllCurrentAssignmentsForEntityAndPerson(any(), Mockito.anyLong(), Mockito.anyLong());

        Assertions.assertThat(businessService.findAllCurrentAssignmentsForEntityAndPerson(null, 1L, 2L)).isEqualTo(assignments);
    }

    @Test
    public void findAllAssignmentsForPersonTest() throws NotFoundException {
        List<Assignment> assignments = new ArrayList<>();
        BDDMockito.willReturn(assignments).given(assignmentRepositoryService).findAllAssignmentsForPerson(any(), Mockito.anyLong());

        Assertions.assertThat(businessService.findAllAssignmentsForPerson(null, 1L)).isEqualTo(assignments);
    }

    @Test
    public void findAllCurrentAndFutureAssignmentsForPersonTest() throws NotFoundException {
        List<Assignment> assignments = new ArrayList<>();
        BDDMockito.willReturn(assignments).given(assignmentRepositoryService).findAllCurrentAndFutureAssignmentsForPerson(any(), Mockito.anyLong());

        Assertions.assertThat(businessService.findAllCurrentAndFutureAssignmentsForPerson(null, 1L)).isEqualTo(assignments);
    }

    @Test
    public void searchWithPersonIdTest() throws NotFoundException {

        List<SearchCriterion> searchCriteria = new ArrayList<>();
        SearchCriterion searchCriterion = new SearchCriterion();
        searchCriterion.setAttribute("person");
        searchCriterion.setOp(SearchCriterion.Operator.csLike);
        searchCriterion.setValue(1L);
        searchCriteria.add(searchCriterion);

        Assertions.assertThat(businessService.searchWithPersonId(searchCriteria, null)).isNull();
    }

    @Test
    public void isAssignedTest() {
        Assertions.assertThat(businessService.isAssigned(1L, 2L, LocalDate.now())).isFalse();
    }

    @Test
    public void getPrimaryEntityTecidTest() {
        Assertions.assertThat(businessService.getPrimaryEntityTecid(1L)).isEqualTo(0L);
    }

    @Test
    public void isOnlyExternalTest() {
        List<Assignment> assignments = new ArrayList<>();

        BDDMockito.willReturn(assignments).given(assignmentRepositoryService).findAllCurrentAssignmentsForPerson(Mockito.anyLong());

        Assertions.assertThat(businessService.isOnlyExternal(1L)).isFalse();
    }

    @Test
    public void existAssignmentTest() {
        Assertions.assertThat(businessService.existAssignment(1L, 2L, LocalDate.now(), LocalDate.now().plusDays(2), new ArrayList<>())).isFalse();
    }

    @Test
    public void testFindAllCurrentAndFutureAssignmentsForPersonsAndEnities() {
        // mock the repository service
        Mockito.when(businessService.findAllCurrentAndFutureAssignmentsForPersonsAndEnities(
                any(CGDISSecurityConstraint.class), Mockito.anyList(), Mockito.anyList(), Mockito.anyLong()))
                .thenReturn(Arrays.asList(new Assignment(), new Assignment()));

        // call the method and check the result
        List<Assignment> result = businessService.findAllCurrentAndFutureAssignmentsForPersonsAndEnities(
                new CGDISSecurityConstraint(Collections.singletonList(Permission.ROLE_PERMISSION_USER_MANAGEMENT_OP_FUNCT_UPDATE)), Arrays.asList(1L, 2L), Arrays.asList("a", "b"), 1L);
        Assert.assertEquals(2, result.size());
    }
    @Test
    public void testExistAssignmentForAllEntities(){
        Collection<Long> collection = new ArrayList<Long>() {{
            add(1L);
            add(2L);
        }};
        Assertions.assertThat(businessService.existAssignmentForAllEntities(1L, collection, LocalDate.now(), LocalDate.now().plusDays(2), new ArrayList<>())).isFalse();
    }
    @Test
    public void testGetAllAssignementStatusForEntityTest(){
        Assert.assertEquals(businessService.getAllAssignementStatusForEntity( null,null ,true , false),null);

    }

    @Test
    public void testFindAllAssignmentsForPersonsAndDates() throws NotFoundException {
        // mock the repository service
        Mockito.when(businessService.findAllAssignmentsForPersonsAndDates(
                        any(CGDISSecurityConstraint.class), Mockito.anyLong(), any(LocalDate.class), any(LocalDate.class)))
                .thenReturn(Arrays.asList(new Assignment(), new Assignment()));

        // call the method and check the result
        List<Assignment> result = businessService.findAllAssignmentsForPersonsAndDates(
                new CGDISSecurityConstraint(Collections.singletonList(Permission.ROLE_PERMISSION_USER_MANAGEMENT_OP_FUNCT_UPDATE)), 1L,LocalDate.now() , LocalDate.now().plusDays(1));
        Assert.assertEquals(2, result.size());
    }
    @Test
    public void testFindAllAssignmentsForDatesAndEntity() throws NotFoundException {
        // mock the repository service
        Mockito.when(businessService.findAllAssignmentsForDatesAndEntity(
                        any(CGDISSecurityConstraint.class), Mockito.anyLong(), any(LocalDate.class), any(LocalDate.class)))
                .thenReturn(Arrays.asList(new Assignment(), new Assignment()));

        // call the method and check the result
        List<Assignment> result = businessService.findAllAssignmentsForDatesAndEntity(
                new CGDISSecurityConstraint(Collections.singletonList(Permission.ROLE_PERMISSION_USER_MANAGEMENT_OP_FUNCT_UPDATE)), 1L,LocalDate.now() , LocalDate.now().plusDays(1));
        Assert.assertEquals(2, result.size());
    }
}
