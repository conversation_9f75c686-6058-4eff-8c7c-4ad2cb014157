package lu.fujitsu.ts.cgdis.portal.business.services.entity;

import lu.fujitsu.ts.cgdis.portal.business.config.AbstractBusinessTest;
import lu.fujitsu.ts.cgdis.portal.business.config.WithBusinessCGDISPortalUser;
import lu.fujitsu.ts.cgdis.portal.core.domain.CGDISEntity;
import lu.fujitsu.ts.cgdis.portal.core.domain.CGDISEntityWithAddress;
import lu.fujitsu.ts.cgdis.portal.core.domain.CGDISEntityWithParentAndMain;
import lu.fujitsu.ts.cgdis.portal.core.domain.enums.CGDISEntityType;
import lu.fujitsu.ts.cgdis.portal.services.entity.ICGDISEntityRepositoryService;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.slf4j.Logger;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.Mockito.*;

@WithBusinessCGDISPortalUser
public class CGDISEntityModifiableModifiableBusinessServiceTest2 extends AbstractBusinessTest {
    @Mock
    Logger LOGGER;
    @Mock
    ICGDISEntityRepositoryService repositoryService;
    @InjectMocks
    CGDISEntityModifiableModifiableBusinessService cGDISEntityModifiableModifiableBusinessService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testGetWithParent() throws Exception {
        CGDISEntityWithParentAndMain entity = new CGDISEntityWithParentAndMain();
        entity.setTecid(1L);
        when(repositoryService.getWithMain(anyLong())).thenReturn(entity);
        CGDISEntityWithParentAndMain result = cGDISEntityModifiableModifiableBusinessService.getWithParent(Long.valueOf(1));
        Assert.assertEquals(entity, result);
    }

    @Test
    public void testFindByParent() throws Exception {
        CGDISEntity entity = new CGDISEntity();
        entity.setTecid(1L);
        when(repositoryService.findByParentWithoutSecurity(anyLong())).thenReturn(Arrays.asList(entity));
        List<CGDISEntity> result = cGDISEntityModifiableModifiableBusinessService.findByParent(Long.valueOf(1));
        Assert.assertEquals(Arrays.<CGDISEntity>asList(entity), result);
    }

    @Test
    public void testExist() throws Exception {
        when(repositoryService.count(any())).thenReturn(1L);
        boolean result = cGDISEntityModifiableModifiableBusinessService.exist(Long.valueOf(1));
        Assert.assertEquals(true, result);
    }

    @Test
    public void testGetWithAddress() throws Exception {
        CGDISEntityWithAddress entity = new CGDISEntityWithAddress();
        entity.setTecid(1L);
        when(repositoryService.getWithAddress(anyLong())).thenReturn(entity);
        CGDISEntityWithAddress result = cGDISEntityModifiableModifiableBusinessService.getWithAddress(Long.valueOf(1));
        Assert.assertEquals(entity, result);
    }

    @Test
    public void testFindAllByParentWithoutSecurity() throws Exception {
        CGDISEntity entity = new CGDISEntity();
        entity.setTecid(1L);
        when(repositoryService.findByParentWithoutSecurity(anyLong())).thenReturn(Arrays.asList(entity));
        List<CGDISEntity> result = cGDISEntityModifiableModifiableBusinessService.findAllByParentWithoutSecurity(Long.valueOf(1));
        Assert.assertEquals(Arrays.<CGDISEntity>asList(entity), result);
    }

    @Test
    public void testGetAllWithoutSecurity() throws Exception {
        CGDISEntity entity = new CGDISEntity();
        entity.setTecid(1L);
        when(repositoryService.findAllWithoutSecurity()).thenReturn(Collections.emptyList());
        List<? extends CGDISEntity> result = cGDISEntityModifiableModifiableBusinessService.getAllWithoutSecurity();
        Assert.assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testFindAllPossibleParentByEntityWithoutSecurity() throws Exception {
        CGDISEntityWithParentAndMain entity = new CGDISEntityWithParentAndMain();
        entity.setTecid(1L);
        when(repositoryService.findAllPossibleParentsByEntityWithoutSecurity(anyLong(), anyList())).thenReturn(Arrays.asList(entity));
        List<CGDISEntity> result = cGDISEntityModifiableModifiableBusinessService.findAllPossibleParentByEntityWithoutSecurity(Long.valueOf(1), CGDISEntityType.CIS);
        List<CGDISEntity> result2 = cGDISEntityModifiableModifiableBusinessService.findAllPossibleParentByEntityWithoutSecurity(Long.valueOf(1), CGDISEntityType.NATIONAL);
        result2 = cGDISEntityModifiableModifiableBusinessService.findAllPossibleParentByEntityWithoutSecurity(Long.valueOf(1), CGDISEntityType.GS);
        result2 = cGDISEntityModifiableModifiableBusinessService.findAllPossibleParentByEntityWithoutSecurity(Long.valueOf(1), CGDISEntityType.GROUP);
        result2 = cGDISEntityModifiableModifiableBusinessService.findAllPossibleParentByEntityWithoutSecurity(Long.valueOf(1), CGDISEntityType.UNKNOWN);
        result2 = cGDISEntityModifiableModifiableBusinessService.findAllPossibleParentByEntityWithoutSecurity(Long.valueOf(1), CGDISEntityType.ZONE);
        result2 = cGDISEntityModifiableModifiableBusinessService.findAllPossibleParentByEntityWithoutSecurity(Long.valueOf(1), CGDISEntityType.SAMU);
        result2 = cGDISEntityModifiableModifiableBusinessService.findAllPossibleParentByEntityWithoutSecurity(Long.valueOf(1), CGDISEntityType.CS);
        Assert.assertEquals(Arrays.<CGDISEntity>asList(entity), result);
    }

    @Test
    public void testGetAllEntitiesByNames() throws Exception {
        CGDISEntityWithParentAndMain entity = new CGDISEntityWithParentAndMain();
        entity.setTecid(1L);
        when(repositoryService.findAllByNames(anyList())).thenReturn(Arrays.asList(entity));
        List<CGDISEntity> result = cGDISEntityModifiableModifiableBusinessService.getAllEntitiesByNames(Arrays.<String>asList("String"));
        Assert.assertEquals(Arrays.<CGDISEntity>asList(entity), result);
    }

    @Test
    @WithBusinessCGDISPortalUser(allAuthorities = true)
    public void testGetAllForRole() throws Exception {
        CGDISEntity entity = new CGDISEntity();
        entity.setTecid(1L);
        when(repositoryService.findAllByNamesWithoutSec(any())).thenReturn(Arrays.asList(entity));

        List<CGDISEntity> result = cGDISEntityModifiableModifiableBusinessService.getAllForRole("ROLE_ADMIN");
        Assert.assertEquals(Arrays.<CGDISEntity>asList(entity), result);
    }

    @Test
    public void testGetAllChildren() throws Exception {
        CGDISEntityWithParentAndMain entity = new CGDISEntityWithParentAndMain();
        entity.setTecid(1L);
        when(repositoryService.findAllChildren(anyLong())).thenReturn(Arrays.asList("domain1"));

        List<String> result = cGDISEntityModifiableModifiableBusinessService.getAllChildren(entity);
        Assert.assertEquals(Arrays.<String>asList("domain1"), result);
    }

    @Test
    public void testGetAllEntitiesByNamesWithoutSec() throws Exception {
        CGDISEntityWithParentAndMain entity = new CGDISEntityWithParentAndMain();
        entity.setTecid(1L);
        when(repositoryService.findAllByNamesWithoutSec(anyList())).thenReturn(Arrays.asList(entity));

        List<CGDISEntity> result = cGDISEntityModifiableModifiableBusinessService.getAllEntitiesByNamesWithoutSec(Arrays.<String>asList("domain1"));
        Assert.assertEquals(Arrays.<CGDISEntity>asList(entity), result);
    }
}
