package lu.fujitsu.ts.cgdis.portal.business.config;

import lu.fujitsu.ts.cgdis.portal.core.domain.CGDISPortalUserDetails;
import lu.fujitsu.ts.cgdis.portal.core.domain.EntityAndParent;
import lu.fujitsu.ts.cgdis.portal.core.domain.tree.Node;
import lu.fujitsu.ts.cgdis.portal.core.security.Permission;
import lu.fujitsu.ts.cgdis.portal.core.security.Role;
import lu.fujitsu.ts.eportal.server.security.model.EPortalAuthority;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.TestingAuthenticationToken;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.test.context.support.WithSecurityContextFactory;
import org.springframework.util.Assert;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;


final class WithBusinessCGDISPortalUserDetailsSecurityContextFactory implements WithSecurityContextFactory<WithBusinessCGDISPortalUser> {

    @Autowired
    public WithBusinessCGDISPortalUserDetailsSecurityContextFactory() {

    }

    @Override
    public SecurityContext createSecurityContext(WithBusinessCGDISPortalUser withUser) {
        String username = withUser.username();
        Assert.hasLength(username, "value() must be non-empty String");
        CGDISPortalUserDetails principal = new CGDISPortalUserDetails();
        principal.setTecid(withUser.tecid());
        principal.setUsername(withUser.username());
        setAuthorities(withUser, principal);
        TestEntityMapping[] testEntityMappings = withUser.entitiesNameMapping();
        principal.setEntitiesIdsByName(mapEntitiesNames(withUser));
        TestingAuthenticationToken authentication = new TestingAuthenticationToken(principal, principal.getUsername(), new ArrayList<>(principal.getAuthorities()));
        authentication.setAuthenticated(true);
        authentication.setDetails(principal);
        SecurityContext context = SecurityContextHolder.createEmptyContext();
        context.setAuthentication(authentication);
        return context;
    }

    private Map<String, Long> mapEntitiesNames(WithBusinessCGDISPortalUser withUser) {
        if (withUser.entitiesNameMapping() == null || withUser.entitiesNameMapping().length == 0) {
            return IntStream.of(1,2,3,4).mapToObj(Long::valueOf).collect(Collectors.toMap(entityId->String.format("domain%s", entityId),Function.identity()));
        }
        return Arrays.stream(withUser.entitiesNameMapping()).collect(Collectors.toMap(TestEntityMapping::name,TestEntityMapping::entityId));
    }
    private void setAuthorities(WithBusinessCGDISPortalUser withUser, CGDISPortalUserDetails principal) {

        Map<String, EPortalAuthority> roleAuthorities;
        Map<String, EPortalAuthority> permissionAuthorities;


        if (withUser.allAuthorities() || ArrayUtils.isNotEmpty(withUser.authoritiesToRemove())) {
            Set<Role> rolesToRemove = Arrays.stream(withUser.authoritiesToRemove()).flatMap(testAuthority -> Arrays.stream(testAuthority.roles()))
                    .collect(Collectors.toSet());
            Set<Permission> permissionsToRemove = Arrays.stream(withUser.authoritiesToRemove()).flatMap(testAuthority -> Arrays.stream(testAuthority.permisions()))
                    .collect(Collectors.toSet());


            roleAuthorities = Arrays.stream(Role.values())
                    .filter(role -> !rolesToRemove.contains(role))
                    .collect(Collectors.toMap(
                            role -> role.name(),
                            o -> {
                                HashSet<String> domains = new HashSet<>();
                                domains.add("domain1");
                                domains.add("domain2");
                                domains.add("domain3");
                                domains.add("domain4");
                                return new EPortalAuthority(o.name(), domains );
                            }));
            permissionAuthorities = Arrays.stream(Permission.values())
                    .filter(permission -> !permissionsToRemove.contains(permission))
                    .collect(Collectors.toMap(
                            permission -> permission.name(),
                            o -> {
                                HashSet<String> domains = new HashSet<>();
                                domains.add("domain1");
                                domains.add("domain2");
                                domains.add("domain3");
                                domains.add("domain4");
                                return new EPortalAuthority(o.name(), domains );
                            }));



        } else if (ArrayUtils.isNotEmpty(withUser.authorities())) {
            roleAuthorities = new HashMap<>();
            permissionAuthorities = new HashMap<>();

            Arrays.stream(withUser.authorities()).forEach(authority -> {
                if (ArrayUtils.isNotEmpty(authority.roles())) {
                    roleAuthorities.putAll(Arrays.stream(authority.roles()).collect(Collectors.toMap(
                            role -> role.name(), role -> new EPortalAuthority(role.name(), Arrays.stream(authority.domains()).collect(Collectors.toSet())))));
                }
                if (ArrayUtils.isNotEmpty(authority.permisions())) {
                    permissionAuthorities.putAll(Arrays.stream(authority.permisions()).collect(Collectors.toMap(
                            permission -> permission.name(), permission -> new EPortalAuthority(permission.name(), Arrays.stream(authority.domains()).collect(Collectors.toSet())))));
                }


            });

        } else {
            roleAuthorities = new HashMap<>();
            permissionAuthorities = new HashMap<>();
        }

        permissionAuthorities.putAll(roleAuthorities);
        principal.setAuthorities(permissionAuthorities);

        Set<String> allDomains =
                permissionAuthorities.values().stream().map(EPortalAuthority::getDomains)
                .flatMap(Collection::stream)
                        .collect(Collectors.toSet());
    principal.setEntities(allDomains);
    principal.setRoles(roleAuthorities);

    //Check if a generalParentDomains is set, if it is the case then set the generalParentDomains as the parent of all entities.
    if (Arrays.stream(withUser.authorities()).anyMatch((i)->i.generalParentDomains().length()>0)) {
        allDomains.add(Arrays.stream(withUser.authorities()).filter((i)->i.generalParentDomains().length()>0).findFirst().get().generalParentDomains());
        principal.setEntities(allDomains);
    if (principal.getEntities().size()>1) {
        List<EntityAndParent> entitiesAndParent = new ArrayList<>();
            for (int i = 1; i < principal.getEntities().size(); i++) {
                EntityAndParent entityAndParentElement = new EntityAndParent(principal.getEntities().stream().skip(i).findAny().get(),principal.getEntities().stream().findFirst().get());
                entitiesAndParent.add(entityAndParentElement);}
        Map<String, Node<String>> entitiesMap = new HashMap<>();
        entitiesAndParent.forEach(entityAndParent -> {
                    Node<String> childNode = entitiesMap.get(entityAndParent.getName());
            if (childNode == null) {
                // create this node
                childNode = new Node<>(entityAndParent.getName());
                entitiesMap.put(entityAndParent.getName(), childNode);
            }
                    Node<String> parentNode = entitiesMap.get(entityAndParent.getParentName());
                    if (parentNode == null) {
                        // create this node
                        parentNode = new Node<>(entityAndParent.getParentName());
                        entitiesMap.put(entityAndParent.getParentName(), parentNode);

                    }
                    // Add the child node to the parent node
                    parentNode.addChild(childNode);
                    childNode.setParent(parentNode);
                });
        principal.setEntitiesWithParentAndChildren(entitiesMap);

    }
    }
    }
}
