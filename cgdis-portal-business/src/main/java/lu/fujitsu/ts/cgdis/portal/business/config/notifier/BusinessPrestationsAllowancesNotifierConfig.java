package lu.fujitsu.ts.cgdis.portal.business.config.notifier;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import lu.fujitsu.ts.cgdis.portal.business.config.properties.BusinessNotifierProperties;
import lu.fujitsu.ts.cgdis.portal.business.notification.INotificationChannel;
import lu.fujitsu.ts.cgdis.portal.business.notification.NotificationChannel;
import lu.fujitsu.ts.cgdis.portal.business.services.notification.NotificationChannels;
import lu.fujitsu.ts.cgdis.portal.business.services.notification.prestation.allowance.PrestationsAllowancesNotifierRequest;
import lu.fujitsu.ts.cgdis.portal.business.services.notification.prestation.allowance.PrestationsAllowancesNotifierService;
import lu.fujitsu.ts.eportal.server.core.domain.OperationStatusMessage;
import org.aopalliance.aop.Advice;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpMethod;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.integration.dsl.IntegrationFlow;
import org.springframework.integration.dsl.IntegrationFlows;
import org.springframework.integration.http.dsl.Http;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageHeaders;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.Map;

@Configuration
public class BusinessPrestationsAllowancesNotifierConfig {

    @Autowired
    private BusinessNotifierProperties notifierProperties;

    @Autowired
    @Qualifier("retryExpressionAdvice")
    private Advice retryAdvice;

    public ObjectMapper objectInternalGatewayMapper() {
        ObjectMapper mapper = new ObjectMapper();

        // Registro global do serializer/deserializer para datas sem horário
        SimpleModule simpleModule = new SimpleModule();
        simpleModule.addSerializer(LocalDate.class, LocalDateSerializer.INSTANCE);

        mapper.registerModule(simpleModule);
        return mapper;
    }

    public RestTemplate restTemplateInternalGateway() {
        ObjectMapper objectMapper = objectInternalGatewayMapper();
        MappingJackson2HttpMessageConverter mappingJackson2HttpMessageConverter = new MappingJackson2HttpMessageConverter(objectMapper);
        return new RestTemplateBuilder().additionalMessageConverters(mappingJackson2HttpMessageConverter).build();
    }

    @Bean
    public IntegrationFlow prestationsAllowancesNotificationOutbound() {
//        RestTemplate toto = restTemplateInternalGateway();
        ObjectMapper objectMapper = objectInternalGatewayMapper();
        MappingJackson2HttpMessageConverter mappingJackson2HttpMessageConverter = new MappingJackson2HttpMessageConverter(objectMapper);
        return IntegrationFlows.from(prestationsAllowancesNotificationChannel())

                .enrichHeaders(headerEnricherSpec -> headerEnricherSpec.header(MessageHeaders.CONTENT_TYPE, "application/json"))
                .enrichHeaders(headerEnricherSpec -> headerEnricherSpec.headerFunction("originalPayload", Message::getPayload))
                .route(PrestationsAllowancesNotifierRequest.class, PrestationsAllowancesNotifierRequest::getType, router -> router
                        .subFlowMapping(PrestationsAllowancesNotifierRequest.PrestationsAllowancesNotifierRequestType.DATE,
                                sf -> sf.<PrestationsAllowancesNotifierRequest, ImmutablePair<String, LocalDate>>transform(source ->
                                        ImmutablePair.of(notifierProperties.getPrestationsAllowancesProperties().getByDateUrl(), source.getDate()))
                        )
                        .subFlowMapping(PrestationsAllowancesNotifierRequest.PrestationsAllowancesNotifierRequestType.PERSON,
                                sf -> sf.<PrestationsAllowancesNotifierRequest, ImmutablePair<String, LocalDate>>transform(source ->
                                        ImmutablePair.of(notifierProperties.getPrestationsAllowancesProperties().getByPersonUrl(), source.getDate())
                                ))
                        .subFlowMapping(PrestationsAllowancesNotifierRequest.PrestationsAllowancesNotifierRequestType.ENTITY,
                                sf -> sf.<PrestationsAllowancesNotifierRequest, ImmutablePair<String, LocalDate>>transform(source ->
                                        ImmutablePair.of(notifierProperties.getPrestationsAllowancesProperties().getByEntityUrl(), source.getDate())
                                ))

                )
                .enrichHeaders(headerEnricherSpec -> headerEnricherSpec.<ImmutablePair<String, LocalDate>>headerFunction("url", message -> message.getPayload().getLeft()))
                .<ImmutablePair<String, LocalDate>, LocalDate>transform(ImmutablePair::getRight)
                .handle(Http.<ImmutablePair<String, LocalDate>>outboundGateway(message -> message.getHeaders().get("url"))

                                .httpMethod(HttpMethod.POST)
                                 .messageConverters(mappingJackson2HttpMessageConverter)
                                .uriVariablesFunction(message -> {

                                    PrestationsAllowancesNotifierRequest originalPayload = message.getHeaders().get("originalPayload", PrestationsAllowancesNotifierRequest.class);
                                    Map<String, Object> uriVariables = new HashMap<>();
                                    uriVariables.put("personId", originalPayload.getPersonId()==null? StringUtils.EMPTY :originalPayload.getPersonId().toString());
                                    uriVariables.put("entityId", originalPayload.getEntityId()==null? StringUtils.EMPTY :originalPayload.getEntityId().toString());
                                    return uriVariables;
                                })
                                .extractPayload(true)
                                .expectedResponseType(OperationStatusMessage.class)

                        ,
                        c -> c.advice(retryAdvice)
                )
                .handle(prestationsAllowancesNotifierService(), "handleOperationStatusResponse")

                .get();
    }

    @Bean(name = NotificationChannels.PRESTATIONS_ALLOWANCES)
    public INotificationChannel prestationsAllowancesNotificationChannel() {
        return new NotificationChannel(NotificationChannels.PRESTATIONS_ALLOWANCES);
    }

    @Bean
    public PrestationsAllowancesNotifierService prestationsAllowancesNotifierService() {
        return new PrestationsAllowancesNotifierService(prestationsAllowancesNotificationChannel());
    }
}
