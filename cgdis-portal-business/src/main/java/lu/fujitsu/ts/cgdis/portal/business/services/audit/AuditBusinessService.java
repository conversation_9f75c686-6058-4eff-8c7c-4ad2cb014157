package lu.fujitsu.ts.cgdis.portal.business.services.audit;

import lu.fujitsu.ts.cgdis.portal.core.domain.audit.*;
import lu.fujitsu.ts.cgdis.portal.core.logging.LoggingCategory;
import lu.fujitsu.ts.cgdis.portal.core.logging.LoggingDomain;
import lu.fujitsu.ts.cgdis.portal.core.security.IAuditSecurityConstraint;
import lu.fujitsu.ts.cgdis.portal.core.service.audit.IAuditBusinessService;
import lu.fujitsu.ts.cgdis.portal.services.audit.IAuditRepositoryService;
import lu.fujitsu.ts.eportal.server.core.domain.SearchCriterion;
import lu.fujitsu.ts.eportal.server.logging.milestones.LogMilestone;
import lu.fujitsu.ts.eportal.server.logging.milestones.LoggableType;
import lu.fujitsu.ts.eportal.server.logging.milestones.LoggableValue;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Service implementation for audit business operations.
 * This service handles audit-related operations for various audit types in the system.
 */
@Service
@LogMilestone(category = LoggingCategory.BUSINESS_SERVICE, domain = LoggingDomain.AUDIT)
public class AuditBusinessService implements IAuditBusinessService {

    private static final Logger LOGGER = LoggerFactory.getLogger(AuditBusinessService.class);
    /**
     * Repository service for default audit operations
     */
    private final IAuditRepositoryService<Audit> defaultAuditRepository;
    /**
     * Map of repository services by audit type
     */
    private final Map<AuditType, IAuditRepositoryService<? extends Audit>> repositoryServices;

    /**
     * Constructs an AuditBusinessService with required dependencies.
     *
     * @param defaultAuditRepository The default repository for general audit operations
     * @param repositoryServices     Collection of specialized audit repository services
     */
    @Autowired
    public AuditBusinessService(IAuditRepositoryService<Audit> defaultAuditRepository, Collection<IAuditRepositoryService<? extends Audit>> repositoryServices) {
        this.defaultAuditRepository = defaultAuditRepository;
        this.repositoryServices = repositoryServices.stream()
                .filter(service -> service.getAuditType() != null)
                .collect(
                        Collectors.toMap(IAuditRepositoryService::getAuditType, Function.identity()))
        ;
    }

    /**
     * Searches for audit records based on specified criteria.
     *
     * @param security_constraint Security constraints to apply to the search
     * @param types               Collection of audit types to search for
     * @param criteria            Search criteria to filter results
     * @param pageRequest         Pagination information
     * @return Page of audit records matching the criteria
     */
    @Override
    @Transactional(readOnly = true)
    @LogMilestone(action = "search audit")
    public Page<Audit> search(IAuditSecurityConstraint security_constraint, @LoggableValue(type = LoggableType.COLLECTION) Collection<AuditType> types, Collection<SearchCriterion> criteria, PageRequest pageRequest) {
        return this.defaultAuditRepository.search(security_constraint, types, criteria, pageRequest);

    }

    /**
     * Searches for prestation audit records based on specified criteria.
     *
     * @param security_constraint Security constraints to apply to the search
     * @param criteria            Search criteria to filter results
     * @param pageRequest         Pagination information
     * @return Page of prestation audit records matching the criteria
     */
    @Override
    @Transactional(readOnly = true)
    @LogMilestone(action = "search audit prestations")
    public Page<AuditPrestation> searchPrestations(IAuditSecurityConstraint security_constraint, Collection<SearchCriterion> criteria, PageRequest pageRequest) {
        LOGGER.debug("Searching for prestations audits criteria: {}", criteria);
        return (Page<AuditPrestation>) repositoryServices.get(AuditType.PRESTATION).search(security_constraint, criteria, pageRequest);
    }

    /**
     * Searches for copy prestation audit records based on specified criteria.
     *
     * @param security_constraint Security constraints to apply to the search
     * @param criteria            Search criteria to filter results
     * @param pageRequest         Pagination information
     * @return Page of copy prestation audit records matching the criteria
     */
    @Override
    @Transactional(readOnly = true)
    @LogMilestone(action = "search audit copy prestations")
    public Page<AuditCopyPrestation> searchCopyPrestations(IAuditSecurityConstraint security_constraint, Collection<SearchCriterion> criteria, PageRequest pageRequest) {
        LOGGER.debug("Searching for copy prestations audits criteria: {}", criteria);
        return (Page<AuditCopyPrestation>) repositoryServices.get(AuditType.COPY_PRESTATION).search(security_constraint, criteria, pageRequest);
    }

    /**
     * Searches for time slot audit records based on specified criteria.
     *
     * @param security_constraint Security constraints to apply to the search
     * @param criteria            Search criteria to filter results
     * @param pageRequest         Pagination information
     * @return Page of time slot audit records matching the criteria
     */
    @Override
    @Transactional(readOnly = true)
    @LogMilestone(action = "search audit timeslots")
    public Page<AuditTimeSlot> searchTimeSlots(IAuditSecurityConstraint security_constraint, Collection<SearchCriterion> criteria, PageRequest pageRequest) {
        LOGGER.debug("Searching for timeslots audits criteria: {}", criteria);
        return (Page<AuditTimeSlot>) repositoryServices.get(AuditType.SLOT).search(security_constraint, criteria, pageRequest);
    }

    /**
     * Searches for permanent deployment plan audit records based on specified criteria.
     *
     * @param security_constraint Security constraints to apply to the search
     * @param criteria            Search criteria to filter results
     * @param pageRequest         Pagination information
     * @return Page of permanent deployment plan audit records matching the criteria
     */
    @Override
    @Transactional(readOnly = true)
    @LogMilestone(action = "search audit perm deployment plan")
    public Page<AuditPermDeploymentPlan> searchPermDeploymentPlan(IAuditSecurityConstraint security_constraint, Collection<SearchCriterion> criteria, PageRequest pageRequest) {
        LOGGER.debug("Searching for PermDeploymentPlan audits criteria: {}", criteria);
        return (Page<AuditPermDeploymentPlan>) repositoryServices.get(AuditType.PERM_DEPLOYMENT_PLAN).search(security_constraint, criteria, pageRequest);
    }

    /**
     * Searches for permanent service plan category audit records based on specified criteria.
     *
     * @param security_constraint Security constraints to apply to the search
     * @param criteria            Search criteria to filter results
     * @param pageRequest         Pagination information
     * @return Page of permanent service plan category audit records matching the criteria
     */
    @Override
    @Transactional(readOnly = true)
    @LogMilestone(action = "search audit perm service plan category")
    public Page<AuditPermServicePlanCategory> searchPermServicePlanCategory(IAuditSecurityConstraint security_constraint, Collection<SearchCriterion> criteria, PageRequest pageRequest) {
        LOGGER.debug("Searching for PermServicePlanCategory audits criteria: {}", criteria);
        return (Page<AuditPermServicePlanCategory>) repositoryServices.get(AuditType.PERM_SERVICE_PLAN).search(security_constraint, criteria, pageRequest);
    }

    /**
     * Searches for permanent DPCE configuration audit records based on specified criteria.
     *
     * @param security_constraint Security constraints to apply to the search
     * @param criteria            Search criteria to filter results
     * @param pageRequest         Pagination information
     * @return Page of permanent DPCE configuration audit records matching the criteria
     */
    @Override
    @Transactional(readOnly = true)
    @LogMilestone(action = "search audit perm config dpce")
    public Page<AuditPermConfigDpce> searchPermConfigDpce(IAuditSecurityConstraint security_constraint, Collection<SearchCriterion> criteria, PageRequest pageRequest) {
        LOGGER.debug("Searching for PermConfigDpce audits criteria: {}", criteria);
        return (Page<AuditPermConfigDpce>) repositoryServices.get(AuditType.PERM_CONFIG_DPCE).search(security_constraint, criteria, pageRequest);
    }

    /**
     * Searches for copies of permanent DPCE configuration audit records based on specified criteria.
     *
     * @param security_constraint Security constraints to apply to the search
     * @param criteria            Search criteria to filter results
     * @param pageRequest         Pagination information
     * @return Page of copied permanent DPCE configuration audit records matching the criteria
     */
    @Override
    @Transactional(readOnly = true)
    @LogMilestone(action = "search audit perm config dpce copy")
    public Page<AuditPermConfigDpceCopy> searchPermConfigDpceCopy(IAuditSecurityConstraint security_constraint, Collection<SearchCriterion> criteria, PageRequest pageRequest) {
        LOGGER.debug("Searching for PermConfigDpceCopy audits criteria: {}", criteria);
        return (Page<AuditPermConfigDpceCopy>) repositoryServices.get(AuditType.PERM_CONFIGDPCE_COPY).search(security_constraint, criteria, pageRequest);
    }

    @Override
    @Transactional(readOnly = true)
    @LogMilestone(action = "search audit rici sim card imports")
    public Page<AuditRiciSimCard> searchRiciSimCards(IAuditSecurityConstraint security_constraint, Collection<SearchCriterion> criteria, PageRequest pageRequest) {
        LOGGER.debug("Searching for RiciSimCard audits criteria: {}", criteria);
        return (Page<AuditRiciSimCard>) repositoryServices.get(AuditType.RICI_SIM_CARD).search(security_constraint, criteria, pageRequest);
    }

    @Override
    @Transactional(readOnly = true)
    @LogMilestone(action = "search audit rici sim card imports")
    public Page<AuditRiciSimCardImport> searchRiciSimCardImports(IAuditSecurityConstraint security_constraint, Collection<SearchCriterion> criteria, PageRequest pageRequest) {
        LOGGER.debug("Searching for RiciSimCardImport audits criteria: {}", criteria);
        return (Page<AuditRiciSimCardImport>) repositoryServices.get(AuditType.RICI_SIM_CARD_IMPORT).search(security_constraint, criteria, pageRequest);
    }

    @Override
    @Transactional(readOnly = true)
    @LogMilestone(action = "search audit rici pager imports")
    public Page<AuditRiciPagerImport> searchRiciPagerImports(IAuditSecurityConstraint security_constraint, Collection<SearchCriterion> criteria, PageRequest pageRequest) {
        LOGGER.debug("Searching for RiciPagerImport audits criteria: {}", criteria);
        return (Page<AuditRiciPagerImport>) repositoryServices.get(AuditType.RICI_PAGER_IMPORT).search(security_constraint, criteria, pageRequest);
    }


    @Override
    @Transactional(readOnly = true)
    @LogMilestone(action = "search audit perm config dpce copy")
    public Page<AuditRiciAlertGroup> searchAlertGroup(IAuditSecurityConstraint security_constraint, Collection<SearchCriterion> criteria, PageRequest pageRequest) {
        LOGGER.debug("Searching for Alert Groups audits criteria: {}", criteria);
        return (Page<AuditRiciAlertGroup>) repositoryServices.get(AuditType.RICI_ALERT_GROUP).search(security_constraint, criteria, pageRequest);
    }
}
