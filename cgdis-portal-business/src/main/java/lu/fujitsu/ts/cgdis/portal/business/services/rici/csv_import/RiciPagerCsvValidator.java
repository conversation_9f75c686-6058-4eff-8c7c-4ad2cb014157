package lu.fujitsu.ts.cgdis.portal.business.services.rici.csv_import;

import lu.fujitsu.ts.cgdis.portal.core.domain.csv_import.CsvValidatorLineInfo;
import lu.fujitsu.ts.cgdis.portal.core.validation.csv.CsvValidator;
import org.apache.commons.csv.CSVRecord;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Component
public class RiciPagerCsvValidator extends CsvValidator {

    // Canonical internal header names
    private static final String SERIAL_NUMBER = "SERIAL_NUMBER";
    private static final String PAGER_ID = "PAGER_ID";
    private static final String MANUFACTURER = "MANUFACTURER";
    private static final String MODEL = "MODEL";
    private static final String DELIVERY_DATE = "DELIVERY_DATE";
    private static final String SIM_ICCID = "SIM_ICCID";
    private static final String SIM_MSISDN = "SIM_MSISDN";
    private static final String SIM_PIN = "SIM_PIN";

    // Expected canonical headers for the simplified pager_import.csv
    public static final String[] EXPECTED_CANONICAL_HEADERS = {
            SERIAL_NUMBER, PAGER_ID, MANUFACTURER, MODEL, DELIVERY_DATE, SIM_ICCID, SIM_MSISDN, SIM_PIN
    };

    // Define which canonical headers are mandatory
    // STATUS column is removed. SERIAL_NUMBER, MANUFACTURER, MODEL, DELIVERY_DATE are mandatory.
    private static final List<String> MANDATORY_CANONICAL_HEADERS = Arrays.asList(
            SERIAL_NUMBER, MANUFACTURER, MODEL, DELIVERY_DATE
    );

    // Max lengths for fields from RICI_PAGER table
    private static final int SERIAL_NUMBER_MAX_LENGTH = 50;
    private static final int PAGER_ID_MAX_LENGTH = 50;
    private static final int MANUFACTURER_MAX_LENGTH = 50;
    private static final int MODEL_MAX_LENGTH = 50;
    private static final int SIM_ICCID_MAX_LENGTH = 22; // Align with SIM ICCID length

    // Basic validation patterns
    private static final Pattern SIM_MSISDN_PATTERN = Pattern.compile("^\\d{8,15}$");
    private static final Pattern SIM_PIN_PATTERN = Pattern.compile("^\\d{4,8}$");
    public static final DateTimeFormatter CSV_DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");



    @Override
    protected Map<String, String> getHeaderTranslations(String lang) {
        Map<String, String> translations = new HashMap<>();
        if ("fr".equalsIgnoreCase(lang)) {
            translations.put("Numéro de série", SERIAL_NUMBER);
            translations.put("Identifiant du pager", PAGER_ID);
            translations.put("Fabricant", MANUFACTURER);
            translations.put("Modèle", MODEL);
            translations.put("Date de livraison", DELIVERY_DATE);
            translations.put("Identifiant de carte SIM (ICCID)", SIM_ICCID);
            translations.put("Numéro MSISDN de la SIM", SIM_MSISDN);
            translations.put("Code PIN de la SIM", SIM_PIN);
        } else if ("de".equalsIgnoreCase(lang)) {
            translations.put("Seriennummer", SERIAL_NUMBER);
            translations.put("Pager-ID", PAGER_ID);
            translations.put("Hersteller", MANUFACTURER);
            translations.put("Modell", MODEL);
            translations.put("Lieferdatum", DELIVERY_DATE);
            translations.put("SIM-Karten-Identifikationsnummer (ICCID)", SIM_ICCID);
            translations.put("MSISDN-Nummer", SIM_MSISDN);
            translations.put("PIN-Code", SIM_PIN);
        }
        return translations;
    }

    @Override
    protected void validateCsvHeader(Map<String, Integer> headerMap, List<String> actualCanonicalHeaders, List<CsvValidatorLineInfo> lineInfos, List<String> originalActualHeaders) {
        List<String> missingMandatoryHeaders = MANDATORY_CANONICAL_HEADERS.stream()
                .filter(expectedHeader -> actualCanonicalHeaders.stream().noneMatch(h -> h.equalsIgnoreCase(expectedHeader)))
                .collect(Collectors.toList());

        if (!missingMandatoryHeaders.isEmpty()) {
            CsvValidatorLineInfo headerErrorLine = new CsvValidatorLineInfo(0, originalActualHeaders.toString());
            headerErrorLine.addError("CSV header is missing mandatory columns (after translation): " + String.join(", ", missingMandatoryHeaders) +
                    ". Expected canonical headers: " + String.join(", ", MANDATORY_CANONICAL_HEADERS));
            lineInfos.add(headerErrorLine);
        }

        List<String> unexpectedHeaders = actualCanonicalHeaders.stream()
                .filter(actualHeader -> Arrays.stream(EXPECTED_CANONICAL_HEADERS).noneMatch(expected -> expected.equalsIgnoreCase(actualHeader)))
                .collect(Collectors.toList());

        if (!unexpectedHeaders.isEmpty()) {
            CsvValidatorLineInfo headerInfoLine = lineInfos.stream().filter(li -> li.getLineNumber() == 0).findFirst().orElse(null);
            if (headerInfoLine == null) {
                headerInfoLine = new CsvValidatorLineInfo(0,  originalActualHeaders.toString());
                lineInfos.add(headerInfoLine);
            }
            headerInfoLine.addError("CSV header contains unexpected columns (after translation): " + String.join(", ", unexpectedHeaders) +
                    ". Expected canonical headers are: " + String.join(", ", EXPECTED_CANONICAL_HEADERS));
        }
    }

    @Override
    protected void performLineValidation(CSVRecord csvRecord, CsvValidatorLineInfo lineInfo, List<String> actualCanonicalHeaders) {
        validateSerialNumber(lineInfo);
        validatePagerId(lineInfo); // Optional field
        // validateStatus is removed as STATUS column is removed from CSV
        validateManufacturer(lineInfo);
        validateModel(lineInfo);
        validateDeliveryDate(lineInfo);
        validateSimIccid(lineInfo); // Optional field
        validateSimMsisdn(lineInfo);    // New call
        validateSimPin(lineInfo);       // New call
        validateConditionalSimFields(lineInfo); // New call for inter-SIM-field dependencies
    }

    private void validateSerialNumber(CsvValidatorLineInfo lineInfo) {
        String serialNumberValue = getOptionalField(lineInfo, SERIAL_NUMBER); // Already using lineInfo
        if (StringUtils.isBlank(serialNumberValue)) {
            lineInfo.addError(SERIAL_NUMBER + " is missing.");
        } else if (serialNumberValue.length() > SERIAL_NUMBER_MAX_LENGTH) {
            lineInfo.addError(SERIAL_NUMBER + " must not exceed " + SERIAL_NUMBER_MAX_LENGTH + " characters.");
        }
    }

    private void validatePagerId(CsvValidatorLineInfo lineInfo) { // Changed signature
        String pagerIdValue = getOptionalField(lineInfo, PAGER_ID); // Use lineInfo
        if (pagerIdValue != null && pagerIdValue.length() > PAGER_ID_MAX_LENGTH) {
            lineInfo.addError(PAGER_ID + " must not exceed " + PAGER_ID_MAX_LENGTH + " characters.");
        }
    }

    // validateStatus method removed

    private void validateManufacturer(CsvValidatorLineInfo lineInfo) { // Changed signature
        String manufacturerValue = getOptionalField(lineInfo, MANUFACTURER); // Use lineInfo
        if (StringUtils.isBlank(manufacturerValue)) {
            lineInfo.addError(MANUFACTURER + " is missing.");
        } else if (manufacturerValue.length() > MANUFACTURER_MAX_LENGTH) {
            lineInfo.addError(MANUFACTURER + " must not exceed " + MANUFACTURER_MAX_LENGTH + " characters.");
        }
    }

    private void validateModel(CsvValidatorLineInfo lineInfo) { // Changed signature
        String modelValue = getOptionalField(lineInfo, MODEL); // Use lineInfo
        if (StringUtils.isBlank(modelValue)) {
            lineInfo.addError(MODEL + " is missing.");
        } else if (modelValue.length() > MODEL_MAX_LENGTH) {
            lineInfo.addError(MODEL + " must not exceed " + MODEL_MAX_LENGTH + " characters.");
        }
    }

    private void validateDeliveryDate(CsvValidatorLineInfo lineInfo) { // Changed signature
        String dateStr = getOptionalField(lineInfo, DELIVERY_DATE); // Use lineInfo
        if (StringUtils.isBlank(dateStr)) {
            lineInfo.addError(DELIVERY_DATE + " is missing.");
        } else {
            try {
                LocalDate.parse(dateStr, CSV_DATE_FORMATTER);
            } catch (DateTimeParseException e) {
                lineInfo.addError(DELIVERY_DATE + " '" + dateStr + "' is not a valid date. Expected format: yyyy-MM-dd.");
            }
        }
    }

    private void validateSimIccid(CsvValidatorLineInfo lineInfo) { // Changed signature
        String simIccidValue = getOptionalField(lineInfo, SIM_ICCID); // Use lineInfo
        if (simIccidValue != null) { 
            if (simIccidValue.isEmpty()) {
                 // Allow empty string if column is present but no value, treat as not provided
            } else if (simIccidValue.length() < 18 || simIccidValue.length() > SIM_ICCID_MAX_LENGTH) { 
                lineInfo.addError(SIM_ICCID + " must be between 18 and " + SIM_ICCID_MAX_LENGTH + " characters.");
            } else if (!simIccidValue.matches("^[0-9a-zA-Z]+$")) { 
                lineInfo.addError(SIM_ICCID + " '" + simIccidValue + "' contains invalid characters. Only alphanumeric characters are allowed.");
            }
        }
    }

    private void validateSimMsisdn(CsvValidatorLineInfo lineInfo) {
        String simMsisdnValue = getOptionalField(lineInfo, SIM_MSISDN);
        if (simMsisdnValue != null) { // SIM_MSISDN is optional overall
            if (simMsisdnValue.length() > 15) {
                lineInfo.addError(SIM_MSISDN + " must not exceed 15 characters.");
            }
            if (!SIM_MSISDN_PATTERN.matcher(simMsisdnValue).matches()) {
                lineInfo.addError(SIM_MSISDN + " must contain only digits and be between 8 and 15 characters long.");
            }
        }
    }

    private void validateSimPin(CsvValidatorLineInfo lineInfo) {
        String simPinValue = getOptionalField(lineInfo, SIM_PIN);
        if (simPinValue != null) { // SIM_PIN is optional overall
            if (simPinValue.length() > 8) {
                lineInfo.addError(SIM_PIN + " must not exceed 8 characters.");
            }
            if (!SIM_PIN_PATTERN.matcher(simPinValue).matches()) {
                lineInfo.addError(SIM_PIN + " must contain only digits and be between 4 and 8 characters long.");
            }
        }
    }

    private void validateConditionalSimFields(CsvValidatorLineInfo lineInfo) {
        String simIccidValue = getOptionalField(lineInfo, SIM_ICCID);
        String simMsisdnValue = getOptionalField(lineInfo, SIM_MSISDN);
        String simPinValue = getOptionalField(lineInfo, SIM_PIN);

        boolean iccidPresent = StringUtils.isNotBlank(simIccidValue);
        boolean msisdnPresent = StringUtils.isNotBlank(simMsisdnValue);
        boolean pinPresent = StringUtils.isNotBlank(simPinValue);

        // If SIM_ICCID is provided, then SIM_MSISDN and SIM_PIN must also be provided.
        if (iccidPresent) {
            if (!msisdnPresent) {
                lineInfo.addError(SIM_MSISDN + " is required when " + SIM_ICCID + " is provided.");
            }
            if (!pinPresent) {
                lineInfo.addError(SIM_PIN + " is required when " + SIM_ICCID + " is provided.");
            }
        }

        // If SIM_MSISDN or SIM_PIN is present, then SIM_ICCID must also be present.
        // This handles cases where user provides MSISDN/PIN but forgets ICCID.
        if ((msisdnPresent || pinPresent) && !iccidPresent) {
            lineInfo.addError(SIM_ICCID + " is required if " + SIM_MSISDN + " or " + SIM_PIN + " are provided.");
        }
    }
}
