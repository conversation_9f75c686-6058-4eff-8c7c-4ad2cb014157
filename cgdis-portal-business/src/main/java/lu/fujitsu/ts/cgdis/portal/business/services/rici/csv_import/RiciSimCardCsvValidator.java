package lu.fujitsu.ts.cgdis.portal.business.services.rici.csv_import;

import lu.fujitsu.ts.cgdis.portal.core.domain.csv_import.CsvValidatorLineInfo;
import lu.fujitsu.ts.cgdis.portal.core.validation.csv.CsvValidator;
import org.apache.commons.csv.CSVRecord;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

public class RiciSimCardCsvValidator extends CsvValidator {

    // Canonical internal header names
    private static final String ICCID = "ICCID";
    private static final String MSISDN = "MSISDN";
    private static final String PIN = "PIN";
    private static final String PAGER_SERIAL_NUMBER = "PAGER_SERIAL_NUMBER"; // Changed from PAGER_ID

    // Expected canonical headers for the simplified sim_import.csv
    public static final String[] EXPECTED_CANONICAL_HEADERS = {
            ICCID, MSISDN, PIN, PAGER_SERIAL_NUMBER // Changed from PAGER_ID
    };
    // Define which canonical headers are mandatory
    private static final List<String> MANDATORY_CANONICAL_HEADERS = Arrays.asList(ICCID, MSISDN, PIN);


    // Basic validation patterns
    private static final Pattern MSISDN_PATTERN = Pattern.compile("^\\d{8,15}$");
    private static final Pattern PIN_PATTERN = Pattern.compile("^\\d{4,8}$");
    // PAGER_SERIAL_NUMBER max length, adjust if necessary (same as Pager's Serial Number)
    private static final int PAGER_SERIAL_NUMBER_MAX_LENGTH = 50; // Changed from PAGER_ID_MAX_LENGTH


    @Override
    protected Map<String, String> getHeaderTranslations(String lang) {
        Map<String, String> translations = new LinkedHashMap<>();
        if ("fr".equalsIgnoreCase(lang)) {
            translations.put("Identifiant de carte SIM (ICCID)", ICCID);
            translations.put("Numéro MSISDN", MSISDN);
            translations.put("Code PIN", PIN);
            translations.put("Numéro de série du Pager", PAGER_SERIAL_NUMBER); // Changed from "Identifiant du pager"
        } else if ("de".equalsIgnoreCase(lang)) {
            translations.put("SIM-Karten-Identifikationsnummer (ICCID)", ICCID);
            translations.put("MSISDN-Nummer", MSISDN);
            translations.put("PIN-Code", PIN);
            translations.put("Seriennummer des Pagers", PAGER_SERIAL_NUMBER); // Changed from "Pager-ID"
        }
        // For "en" or other languages, or if no lang is provided, it will use the direct canonical names.
        return translations;
    }

    @Override
    protected void validateCsvHeader(Map<String, Integer> headerMap, List<String> actualCanonicalHeaders, List<CsvValidatorLineInfo> lineInfos, List<String> originalActualHeaders) {
        // Validate against the canonical headers
        List<String> missingMandatoryHeaders = MANDATORY_CANONICAL_HEADERS.stream()
                .filter(expectedHeader -> actualCanonicalHeaders.stream().noneMatch(h -> h.equalsIgnoreCase(expectedHeader)))
                .collect(Collectors.toList());

        if (!missingMandatoryHeaders.isEmpty()) {
            CsvValidatorLineInfo headerErrorLine = new CsvValidatorLineInfo(0, originalActualHeaders.toString());
            headerErrorLine.addError("CSV header is missing mandatory columns (after translation to canonical names): " + String.join(", ", missingMandatoryHeaders) +
                    ". Expected canonical headers: " + String.join(", ", MANDATORY_CANONICAL_HEADERS));
            lineInfos.add(headerErrorLine);
        }

        // Check for unexpected columns (optional check)
        List<String> unexpectedHeaders = actualCanonicalHeaders.stream()
                .filter(actualHeader -> Arrays.stream(EXPECTED_CANONICAL_HEADERS).noneMatch(expected -> expected.equalsIgnoreCase(actualHeader)))
                .collect(Collectors.toList());

        if (!unexpectedHeaders.isEmpty()) {
            // This could be a warning or an error depending on strictness
            // For now, let's add it as a non-critical part of the header error if one exists, or a new one.
            CsvValidatorLineInfo headerInfoLine = lineInfos.stream().filter(li -> li.getLineNumber() == 0).findFirst().orElse(null);
            if (headerInfoLine == null) {
                headerInfoLine = new CsvValidatorLineInfo(0, originalActualHeaders.toString());
                lineInfos.add(headerInfoLine); // Add if not already present
            }
            // Adding as a general validation message, not necessarily an "error" that stops processing unless combined with missing mandatory.
            headerInfoLine.addError("CSV header contains unexpected columns (after translation): " + String.join(", ", unexpectedHeaders) +
                    ". Expected canonical headers are: " + String.join(", ", EXPECTED_CANONICAL_HEADERS));
        }
    }

    @Override
    protected void performLineValidation(CSVRecord csvRecord, CsvValidatorLineInfo lineInfo, List<String> actualCanonicalHeaders) {
        // Parsed data in lineInfo should have canonical keys now.
        validateIccid(lineInfo);
        validateMsisdn(lineInfo);
        validatePin(lineInfo);
        validatePagerSerialNumber(lineInfo); // Changed from validatePagerId, PAGER_SERIAL_NUMBER is optional
    }

    private void validateIccid(CsvValidatorLineInfo lineInfo) {
        String iccidValue = getOptionalField(lineInfo, ICCID); // Use canonical name
        if (StringUtils.isBlank(iccidValue)) { // ICCID is mandatory
            lineInfo.addError(ICCID + " is missing.");
            return;
        }
        if (iccidValue.length() < 18 || iccidValue.length() > 22) {
            lineInfo.addError(ICCID + " must be between 18 and 22 characters long.");
        }
        if (!iccidValue.matches("^[a-zA-Z0-9]+$")) {
            lineInfo.addError(ICCID + " must be alphanumeric.");
        }
    }

    private void validateMsisdn(CsvValidatorLineInfo lineInfo) {
        String msisdnValue = getOptionalField(lineInfo, MSISDN); // Use canonical name
        if (StringUtils.isBlank(msisdnValue)) { // MSISDN is mandatory
            lineInfo.addError(MSISDN + " is missing.");
            return;
        }
        if (msisdnValue.length() > 15) {
            lineInfo.addError(MSISDN + " must not exceed 15 characters.");
        }
        if (!MSISDN_PATTERN.matcher(msisdnValue).matches()) {
            lineInfo.addError(MSISDN + " must contain only digits and be between 8 and 15 characters long.");
        }
    }

    private void validatePin(CsvValidatorLineInfo lineInfo) {
        String pinValue = getOptionalField(lineInfo, PIN); // Use canonical name
        if (StringUtils.isBlank(pinValue)) { // PIN is mandatory
            lineInfo.addError(PIN + " is missing.");
            return;
        }
        if (pinValue.length() > 8) {
            lineInfo.addError(PIN + " must not exceed 8 characters.");
        }
        if (!PIN_PATTERN.matcher(pinValue).matches()) {
            lineInfo.addError(PIN + " must contain only digits and be between 4 and 8 characters long.");
        }
    }

    private void validatePagerSerialNumber(CsvValidatorLineInfo lineInfo) {
        String pagerSerialNumberValue = getOptionalField(lineInfo, PAGER_SERIAL_NUMBER); // Use canonical name
        if (pagerSerialNumberValue != null) { // PAGER_SERIAL_NUMBER is optional
            if (pagerSerialNumberValue.length() > PAGER_SERIAL_NUMBER_MAX_LENGTH) {
                lineInfo.addError(PAGER_SERIAL_NUMBER + " is too long (max " + PAGER_SERIAL_NUMBER_MAX_LENGTH + " characters).");
            }
            // Add any other specific validation for PAGER_SERIAL_NUMBER if necessary (e.g., format, characters)
            // For now, only length is checked, similar to Pager's own Serial Number validation.
        }
    }
}
