package lu.fujitsu.ts.cgdis.portal.business.services.utils.stack;

import lu.fujitsu.ts.cgdis.portal.core.pojo.date.IRangeDateTimeWithOrder;
import lu.fujitsu.ts.cgdis.portal.core.utils.DateTimeUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.BiFunction;
import java.util.function.Consumer;



public abstract class StackWithOrderBusinessService<R extends IRangeDateTimeWithOrder> extends AbstractStackBusinessService<R> implements IStackWithOrderBusinessService<R> {


    protected List<R> getRangesToKeep(R range, R toStack) {
        // Manage only overlapping between range and tostack
        if (range.getStackOrder() == toStack.getStackOrder()) {
            // In this case, the toStack is ignored. We keep the existing range
            return Collections.singletonList(range);
        }
        BiFunction<R, R, List<R>> split = (iToStack, iToStack2) -> {
            // Split the two stack in ranges
            return createRangesByOrder(iToStack, iToStack2);
        };

        List<R> sortedRanges = DateTimeUtils.sortRangesArray(toStack, range);
        return split.apply(sortedRanges.get(0), sortedRanges.get(1));

    }

    abstract protected R cloneToRangeOrder(R existingToStack, LocalDateTime newStart, LocalDateTime newEnd, int newOrder);

    private List<R> createRangesByOrder(R iToStack, R iToStack2) {
        // Manage only overlapping between range and tostack
        List<R> toProcess = new ArrayList<>();

        LocalDateTime firstBoundary = DateTimeUtils.min(iToStack.getEndDateTime(), iToStack2.getStartDateTime());
        LocalDateTime secondBoundary = DateTimeUtils.min(iToStack.getEndDateTime(), iToStack2.getEndDateTime());

        AtomicReference<R> a = new AtomicReference<>(null);

        Consumer<R> addRange = (range) -> {
            Optional<R> lastRange = Optional.empty();
            int lastIndex = toProcess.size() - 1;
            if (!toProcess.isEmpty()) {
                lastRange = Optional.ofNullable(toProcess.get(lastIndex));
            }

            if (!range.getStartDateTime().isEqual(range.getEndDateTime())) {
                if( lastRange.isPresent() && lastRange.get().getStackOrder() == range.getStackOrder() ){
                    // Merge these 2 ranges because they have the same order
                    R lastRangeFinal = this.cloneToRangeOrder(lastRange.get(),lastRange.get().getStartDateTime(), range.getEndDateTime(), range.getStackOrder());
                    toProcess.set(lastIndex, lastRangeFinal);
                } else {
                    toProcess.add(range);
                }
            }
        };

        addRange.accept(cloneToRange(iToStack, iToStack.getStartDateTime(), firstBoundary));
        int order1 = iToStack.getStackOrder();
        int order2 = iToStack2.getStackOrder();
        R toClone = order1 <= order2 ? iToStack : iToStack2;
        addRange.accept(cloneToRange(toClone, firstBoundary, secondBoundary));

        toClone = secondBoundary.isEqual(iToStack2.getEndDateTime()) ? iToStack : iToStack2;
        addRange.accept(cloneToRange(toClone, secondBoundary, DateTimeUtils.max(iToStack.getEndDateTime(), iToStack2.getEndDateTime())));

        return toProcess;
    }

}
