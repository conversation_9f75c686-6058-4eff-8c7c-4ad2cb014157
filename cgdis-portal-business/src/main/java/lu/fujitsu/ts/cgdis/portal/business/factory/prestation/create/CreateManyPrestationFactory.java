package lu.fujitsu.ts.cgdis.portal.business.factory.prestation.create;

import lu.fujitsu.ts.cgdis.portal.business.services.serviceplan.prestation.create.CreateManyPrestationService;
import lu.fujitsu.ts.cgdis.portal.core.domain.serviceplan.ServicePlan;
import lu.fujitsu.ts.cgdis.portal.core.domain.serviceplan.ServicePlanVersion;
import lu.fujitsu.ts.cgdis.portal.core.security.ICGDISSecurityConstraint;
import org.springframework.beans.factory.ObjectFactory;
import org.springframework.stereotype.Component;


/**
 * Factory used to create instance of {@link lu.fujitsu.ts.cgdis.portal.business.services.serviceplan.prestation.create.ICreateManyPrestationService}.
 */
@Component
public class CreateManyPrestationFactory {

    /**
     * The Factory.
     */
    private final ObjectFactory<CreateManyPrestationService> factory;

    /**
     * Instantiates a new Create many prestation factory.
     *
     * @param factory the factory
     */
    public CreateManyPrestationFactory(ObjectFactory<CreateManyPrestationService> factory) {
        this.factory = factory;
    }

    /**
     * Gets service.
     *
     * @param servicePlan        the service plan
     * @param servicePlanVersion the service plan version
     * @return the service
     */
    public CreateManyPrestationService getService(ICGDISSecurityConstraint securityConstraint, ServicePlan servicePlan, ServicePlanVersion servicePlanVersion, boolean refreshCompletionStatus) {
        CreateManyPrestationService service = factory.getObject();
        service.setSecurityConstraint(securityConstraint);
        service.addServicePlan(servicePlan);
        service.addServicePlanVersion(servicePlanVersion);
        service.setRefreshCompletionStatus(refreshCompletionStatus);
        return service;
    }
}
