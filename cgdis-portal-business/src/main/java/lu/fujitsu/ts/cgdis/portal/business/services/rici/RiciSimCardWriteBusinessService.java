package lu.fujitsu.ts.cgdis.portal.business.services.rici;

import lu.fujitsu.ts.cgdis.portal.core.domain.rici.RiciSimCard;
import lu.fujitsu.ts.cgdis.portal.core.domain.rici.RiciSimCardPager;
import lu.fujitsu.ts.cgdis.portal.core.domain.rici.enums.RiciSimCardPagerStatus;
import lu.fujitsu.ts.cgdis.portal.core.domain.rici.enums.RiciSimCardStatus;
import lu.fujitsu.ts.cgdis.portal.core.logging.LoggingCategory;
import lu.fujitsu.ts.cgdis.portal.core.logging.LoggingDomain;
import lu.fujitsu.ts.cgdis.portal.core.security.ICGDISSecurityConstraint;
import lu.fujitsu.ts.cgdis.portal.core.service.rici.IRiciSimCardPagerWriteBusinessService;
import lu.fujitsu.ts.cgdis.portal.core.service.rici.IRiciSimCardROBusinessService;
import lu.fujitsu.ts.cgdis.portal.core.service.rici.IRiciSimCardWriteBusinessService;
import lu.fujitsu.ts.cgdis.portal.services.rici.IRiciSimCardPartialUpdate;
import lu.fujitsu.ts.cgdis.portal.services.rici.IRiciSimCardWriteRepositoryService;
import lu.fujitsu.ts.eportal.server.core.business.services.OnlyModifiableSecuredBusinessService;
import lu.fujitsu.ts.eportal.server.core.domain.IModelUpdate;
import lu.fujitsu.ts.eportal.server.core.exceptions.NotFoundException;
import lu.fujitsu.ts.eportal.server.logging.milestones.LogMilestone;

import java.util.Objects; // Import Objects
import lu.fujitsu.ts.eportal.server.logging.milestones.LoggableValue;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

@Service
@Transactional
@LogMilestone(category = LoggingCategory.BUSINESS_SERVICE, domain = LoggingDomain.RICI)
public class RiciSimCardWriteBusinessService extends OnlyModifiableSecuredBusinessService<ICGDISSecurityConstraint, RiciSimCard, Long, IRiciSimCardWriteRepositoryService> implements IRiciSimCardWriteBusinessService {

    private static final Logger LOGGER = LoggerFactory.getLogger(RiciSimCardWriteBusinessService.class);

    private final IRiciSimCardWriteRepositoryService simCardWriteRepositoryService;
    private final IRiciSimCardROBusinessService simCardROBusinessService;
    private final IRiciSimCardPagerWriteBusinessService simCardPagerWriteBusinessService;

    @Autowired
    public RiciSimCardWriteBusinessService(
            IRiciSimCardWriteRepositoryService simCardWriteRepositoryService,
            IRiciSimCardROBusinessService simCardROBusinessService,
            @Lazy IRiciSimCardPagerWriteBusinessService simCardPagerWriteBusinessService) { // Added @Lazy here
        super(simCardWriteRepositoryService, simCardROBusinessService);
        this.simCardWriteRepositoryService = simCardWriteRepositoryService;
        this.simCardROBusinessService = simCardROBusinessService;
        this.simCardPagerWriteBusinessService = simCardPagerWriteBusinessService;
    }

    @Override
    @LogMilestone(action = "create sim card")
    @Transactional
    public RiciSimCard createSimCard(
            ICGDISSecurityConstraint securityConstraint,
            @LoggableValue("tecid") RiciSimCard simCard) {
        LOGGER.debug("Creating SIM card: {}", simCard);
        return simCardWriteRepositoryService.create(securityConstraint, simCard);
    }

    @Override
    @LogMilestone(action = "update sim card")
    @Transactional
    public RiciSimCard updateSimCard(ICGDISSecurityConstraint securityConstraint, IModelUpdate<RiciSimCard, Long> simCardPatchForm) throws NotFoundException {
        RiciSimCard simCardFromFront = simCardPatchForm.update(new RiciSimCard());
        Long tecid = simCardPatchForm.getTechnicalId(); // Use getTechnicalId() from the form

        if (tecid == null) {
            throw new IllegalArgumentException("Tecid cannot be null for update.");
        }
        LOGGER.debug("Starting update for SIM card Tecid: {}", tecid);

        // 1. Fetch current state
        RiciSimCard currentSimCard = this.roBusinessService.get(securityConstraint, tecid);
        final String originalStatus = currentSimCard.getStatus(); // Use final local variable

        // 2. Determine the final status based on requested status and current status
        String requestedStatus = simCardFromFront.getStatus();
        final String calculatedFinalStatus; // Use final local variable
        if (Objects.equals(requestedStatus, RiciSimCardStatus.ASSOCIEE.name())) {
            if (Objects.equals(originalStatus, RiciSimCardStatus.DISPONIBLE.name()) ||
                Objects.equals(originalStatus, RiciSimCardStatus.INACTIVE.name())) {
                calculatedFinalStatus = originalStatus; // Keep original if trying to force ASSOCIEE
                LOGGER.debug("Requested status ASSOCIEE, but original was {}. Keeping original status.", originalStatus);
            } else if (Objects.equals(originalStatus, RiciSimCardStatus.ASSOCIEE.name())) {
                calculatedFinalStatus = RiciSimCardStatus.DISPONIBLE.name(); // Change ASSOCIEE to DISPONIBLE if requested again
                LOGGER.debug("Requested status ASSOCIEE, original was ASSOCIEE. Changing status to DISPONIBLE.");
            } else {
                // This case should ideally not be reachable if status transitions are enforced correctly elsewhere,
                // but handle defensively. If current status is neither DISPONIBLE, INACTIVE, nor ASSOCIEE,
                // and ASSOCIEE is requested, what should happen? Let's default to DISPONIBLE as it's safer.
                LOGGER.warn("Requested status ASSOCIEE, but original status was '{}'. Setting status to DISPONIBLE as a safeguard.", originalStatus);
                calculatedFinalStatus = RiciSimCardStatus.DISPONIBLE.name();
            }
        } else {
            calculatedFinalStatus = requestedStatus; // Allow other status changes (e.g., to DISPONIBLE, INACTIVE)
            LOGGER.debug("Requested status is '{}'. Setting final status to '{}'.", requestedStatus, calculatedFinalStatus);
        }

        // 3. Check if deassociation is needed (status changed FROM ASSOCIEE)
        boolean needsDeassociation = Objects.equals(originalStatus, RiciSimCardStatus.ASSOCIEE.name()) &&
                                     (Objects.equals(calculatedFinalStatus, RiciSimCardStatus.INACTIVE.name()) ||
                                      Objects.equals(calculatedFinalStatus, RiciSimCardStatus.DISPONIBLE.name()));

        if (needsDeassociation) {
            LOGGER.debug("SIM card {} status changing from ASSOCIEE to {}. Deassociating from pager BEFORE updating SIM.", tecid, calculatedFinalStatus);
            try {
                // Call deassociation logic BEFORE updating the SIM card itself
                simCardPagerWriteBusinessService.deassociateSimCardFromPager(securityConstraint, tecid);
                LOGGER.info("Successfully deassociated SIM card {} from pager.", tecid);
            } catch (NotFoundException e) {
                // Log warning, maybe the association was already gone or SIM/Pager not found?
                LOGGER.warn("Could not deassociate SIM card {} from pager during status update: {}. Proceeding with SIM update.", tecid, e.getMessage());
            } catch (Exception e) {
                // Log error for unexpected issues during deassociation
                LOGGER.error("Unexpected error during deassociation for SIM card {}: {}", tecid, e.getMessage(), e);
                // Depending on policy, might rethrow or continue
                // For now, let's proceed with SIM update but log the error.
            }
        }

        // 4. Apply partial update to the SIM card using the calculated final status
        LOGGER.debug("Applying partial update to SIM card {} with final status {}", tecid, calculatedFinalStatus);
        // Define the partial update implementation *before* calling the service
        IRiciSimCardPartialUpdate partialUpdate = new IRiciSimCardPartialUpdate() {
            @Override
            public Long getTechnicalId() { return tecid; }
            @Override
            public Long getTecLock(Long currentTeclock) { return simCardFromFront.getTeclock(); } // Pass teclock from form

            @Override
            public String getNewIccid(String currentIccid) {
                String newValue = simCardFromFront.getIccid();
                // Return new value ONLY if it's not null/blank AND different from current
                if (newValue != null && !newValue.trim().isEmpty() && !Objects.equals(currentIccid, newValue)) {
                    return newValue;
                }
                return null; // Indicate no update for this field
            }
            @Override
            public String getNewMsisdn(String currentMsisdn) {
                String newValue = simCardFromFront.getMsisdn();
                // Return new value ONLY if it's not null/blank AND different from current
                if (newValue != null && !newValue.trim().isEmpty() && !Objects.equals(currentMsisdn, newValue)) {
                    return newValue;
                }
                return null; // Indicate no update for this field
            }
            @Override
            public String getNewPin(String currentPin) {
                String newValue = simCardFromFront.getPin();
                // Return new value ONLY if it's not null/blank AND different from current
                if (newValue != null && !newValue.trim().isEmpty() && !Objects.equals(currentPin, newValue)) {
                    return newValue;
                }
                return null; // Indicate no update for this field
            }
            @Override
            public java.time.LocalDateTime getNewDeleteDateTime(java.time.LocalDateTime currentDeleteDateTime) {
                // DeleteDateTime is handled by deleteSimCard method, never update here
                return null;
            }
            @Override
            public String getNewStatus(String currentStatus) {
                // Return the calculated final status only if it's different from the current one
                return Objects.equals(currentStatus, calculatedFinalStatus) ? null : calculatedFinalStatus;
            }
        };

        // Call the repository service with the defined partial update
        RiciSimCard updatedSimCard = this.simCardWriteRepositoryService.updatePartial(securityConstraint, partialUpdate);

        LOGGER.debug("Successfully updated SIM card (Tecid: {}) partial data.", tecid);
        return updatedSimCard; // Return the result from updatePartial
    }

    @Override
    @LogMilestone(action = "deactivate sim card")
    @Transactional
    public RiciSimCard deactivateSimCard(
            ICGDISSecurityConstraint securityConstraint,
            @LoggableValue Long tecid) throws NotFoundException {
        LOGGER.debug("Deactivating SIM card with tecid: {}", tecid);

        // Verify SIM card exists
        RiciSimCard simCard = simCardROBusinessService.get(securityConstraint, tecid);
        if (simCard == null) {
            throw new NotFoundException("SIM card not found with tecid: " + tecid);
        }

        // Update the status to INACTIVE
        return simCardWriteRepositoryService.updatePartial(securityConstraint, new IRiciSimCardPartialUpdate() {
            @Override
            public Long getTechnicalId() {
                return tecid;
            }

            @Override
            public Long getTecLock(Long currentTeclock) {
                return simCard.getTeclock();
            }

            @Override
            public String getNewIccid(String currentIccid) {
                return currentIccid;
            }

            @Override
            public String getNewMsisdn(String currentMsisdn) {
                return currentMsisdn;
            }

            @Override
            public String getNewPin(String currentPin) {
                return currentPin;
            }

            @Override
            public String getNewStatus(String currentStatus) {
                return RiciSimCardStatus.INACTIVE.name();
            }

            @Override
            public LocalDateTime getNewDeleteDateTime(LocalDateTime currentDeleteDateTime) { return null; }
        });
    }

    @Override
    @LogMilestone(action = "delete sim card")
    @Transactional
    public RiciSimCard deleteSimCard(ICGDISSecurityConstraint securityConstraint, Long tecid) throws NotFoundException {
        LOGGER.debug("Deleting SIM card with tecid: {}", tecid);

        // Verify SIM card exists
        RiciSimCard simCard = simCardROBusinessService.get(securityConstraint, tecid);
        if (simCard == null) {
            throw new NotFoundException("SIM card not found with tecid: " + tecid);
        }

        // Call the repository service to handle the deletion logic
        return simCardWriteRepositoryService.deleteSimCard(securityConstraint, tecid);
    }

    @Override
    @LogMilestone(action = "reactivate sim card")
    @Transactional
    public RiciSimCard reactivateSimCard(
            ICGDISSecurityConstraint securityConstraint,
            @LoggableValue Long tecid) throws NotFoundException {
        LOGGER.debug("Reactivating SIM card with tecid: {}", tecid);

        // Verify SIM card exists
        RiciSimCard simCard = simCardROBusinessService.get(securityConstraint, tecid);
        if (simCard == null) {
            throw new NotFoundException("SIM card not found with tecid: " + tecid);
        }

        // Update the status to DISPONIBLE
        return simCardWriteRepositoryService.updatePartial(securityConstraint, new IRiciSimCardPartialUpdate() {
            @Override
            public Long getTechnicalId() {
                return tecid;
            }

            @Override
            public Long getTecLock(Long currentTeclock) {
                return simCard.getTeclock(); // Keep current teclock
            }

            @Override
            public String getNewIccid(String currentIccid) {
                return currentIccid; // Keep current ICCID
            }

            @Override
            public String getNewMsisdn(String currentMsisdn) {
                return currentMsisdn; // Keep current MSISDN
            }

            @Override
            public String getNewPin(String currentPin) {
                return currentPin; // Keep current PIN
            }

            @Override
            public String getNewStatus(String currentStatus) {
                // Only reactivate if currently INACTIVE
                if (RiciSimCardStatus.INACTIVE.name().equals(currentStatus)) {
                    return RiciSimCardStatus.DISPONIBLE.name(); // Set status to DISPONIBLE
                }
                return currentStatus; // Otherwise, keep current status
            }

            @Override
            public LocalDateTime getNewDeleteDateTime(LocalDateTime currentDeleteDateTime) {
                return currentDeleteDateTime; // Keep current deleteDateTime
            }
        });
    }

    @Override
    @LogMilestone(action = "update sim card status")
    @Transactional
    public RiciSimCard updateSimCardStatus(
            ICGDISSecurityConstraint securityConstraint,
            @LoggableValue Long tecid,
            @LoggableValue String status) throws NotFoundException {
        LOGGER.debug("Updating SIM card status with tecid: {} to status: {}", tecid, status);

        // Verify SIM card exists
        RiciSimCard simCard = simCardROBusinessService.get(securityConstraint, tecid);
        if (simCard == null) {
            throw new NotFoundException("SIM card not found with tecid: " + tecid);
        }

        // Validate the status
        try {
            RiciSimCardStatus.valueOf(status);
        } catch (IllegalArgumentException e) {
            throw new IllegalArgumentException("Invalid status: " + status);
        }

        // Update the status using the repository method
        return simCardWriteRepositoryService.updatePartial(securityConstraint, new IRiciSimCardPartialUpdate() {
            @Override
            public Long getTechnicalId() {
                return tecid;
            }

            @Override
            public Long getTecLock(Long currentTeclock) {
                return simCard.getTeclock(); // Use current teclock
            }

            @Override
            public String getNewIccid(String currentIccid) {
                return currentIccid; // Keep current ICCID
            }

            @Override
            public String getNewMsisdn(String currentMsisdn) {
                return currentMsisdn; // Keep current MSISDN
            }

            @Override
            public String getNewPin(String currentPin) {
                return currentPin; // Keep current PIN
            }

            @Override
            public String getNewStatus(String currentStatus) {
                return status; // Set the new status
            }

            @Override
            public LocalDateTime getNewDeleteDateTime(LocalDateTime currentDeleteDateTime) {
                return currentDeleteDateTime; // Keep current deleteDateTime
            }
        });
    }

    @Override
    @LogMilestone(action = "associate sim card to pager")
    @Transactional
    public RiciSimCardPager associateSimCardToPager(
            ICGDISSecurityConstraint securityConstraint,
            @LoggableValue Long simCardTecid,
            @LoggableValue Long pagerTecid) throws NotFoundException {
        LOGGER.debug("Associating SIM card with tecid: {} to pager with tecid: {}", simCardTecid, pagerTecid);

        // Use the common method from the SimCardPagerWriteBusinessService
        return this.simCardPagerWriteBusinessService.associateSimCardWithPagerByIds(
                securityConstraint,
                simCardTecid,
                pagerTecid,
                RiciSimCardPagerStatus.ACTIVE.name()
        );
    }
    
    @Override
    @LogMilestone(action = "deassociate sim card from pager")
    @Transactional
    public RiciSimCardPager deassociateSimCardFromPager(
            ICGDISSecurityConstraint securityConstraint,
            @LoggableValue Long simCardTecid) throws NotFoundException {
        LOGGER.debug("Deassociating SIM card with tecid: {} from pager", simCardTecid);

        // Use the common method from the SimCardPagerWriteBusinessService
        return this.simCardPagerWriteBusinessService.deassociateSimCardFromPager(
                securityConstraint,
                simCardTecid
        );
    }
}
