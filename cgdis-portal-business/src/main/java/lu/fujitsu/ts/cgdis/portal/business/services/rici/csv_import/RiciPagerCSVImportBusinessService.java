package lu.fujitsu.ts.cgdis.portal.business.services.rici.csv_import;

import lu.fujitsu.ts.cgdis.portal.business.dto.rici.RiciPagerCsvImportLineInfo;
import lu.fujitsu.ts.cgdis.portal.core.domain.CGDISSecurityConstraint;
import lu.fujitsu.ts.cgdis.portal.core.domain.csv_import.CsvImportReport;
import lu.fujitsu.ts.cgdis.portal.core.domain.csv_import.CsvValidatorLineInfo;
import lu.fujitsu.ts.cgdis.portal.core.domain.rici.RiciPager;
import lu.fujitsu.ts.cgdis.portal.core.domain.rici.RiciSimCard;
import lu.fujitsu.ts.cgdis.portal.core.domain.rici.enums.RiciPagerAssignmentType;
import lu.fujitsu.ts.cgdis.portal.core.domain.rici.enums.RiciPagerStatus;
import lu.fujitsu.ts.cgdis.portal.core.domain.rici.enums.RiciSimCardStatus;
import lu.fujitsu.ts.cgdis.portal.core.exception.rici.*; // Import all RICI exceptions
import lu.fujitsu.ts.cgdis.portal.core.service.rici.*; // Import all RICI services
import lu.fujitsu.ts.cgdis.portal.services.rici.IRiciSimCardWriteRepositoryService; // For pre-check
import lu.fujitsu.ts.eportal.server.core.exceptions.NotFoundException;
import lu.fujitsu.ts.cgdis.portal.core.security.Permission; // For SIM creation permission
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

import org.apache.commons.lang3.StringUtils;

@Service
@Transactional
public class RiciPagerCSVImportBusinessService implements IRiciPagerCSVImportBusinessService {

    private static final Logger log = LoggerFactory.getLogger(RiciPagerCSVImportBusinessService.class);

    private final RiciPagerCsvValidator pagerCsvValidator;
    private final IRiciPagerWriteBusinessService pagerWriteBusinessService;
    private final IRiciPagerROBusinessService pagerROBusinessService;
    private final IRiciSimCardROBusinessService simCardROBusinessService;
    private final IRiciSimCardWriteRepositoryService simCardWriteRepositoryService; // New
    private final IRiciSimCardWriteBusinessService simCardWriteBusinessService; // New

    @Autowired
    public RiciPagerCSVImportBusinessService(RiciPagerCsvValidator pagerCsvValidator,
                                             IRiciPagerWriteBusinessService pagerWriteBusinessService,
                                             IRiciPagerROBusinessService pagerROBusinessService,
                                             IRiciSimCardROBusinessService simCardROBusinessService,
                                             IRiciSimCardWriteRepositoryService simCardWriteRepositoryService, // New
                                             IRiciSimCardWriteBusinessService simCardWriteBusinessService) { // New
        this.pagerCsvValidator = pagerCsvValidator;
        this.pagerWriteBusinessService = pagerWriteBusinessService;
        this.pagerROBusinessService = pagerROBusinessService;
        this.simCardROBusinessService = simCardROBusinessService;
        this.simCardWriteRepositoryService = simCardWriteRepositoryService; // New
        this.simCardWriteBusinessService = simCardWriteBusinessService; // New
    }

    @Override
    @Transactional
    public CsvImportReport importCsvFile(MultipartFile file, String lang, CGDISSecurityConstraint createPagerConstraint) {
        log.info("Starting CSV import process for pagers from file: {}, language: {}", file.getOriginalFilename(), lang);
        LocalDateTime startTime = LocalDateTime.now();

        CsvImportReport report = new CsvImportReport();
        report.setFileName(file.getOriginalFilename());
        report.setStartTime(startTime);

        List<CsvValidatorLineInfo> validatedLineInfos = pagerCsvValidator.validateAndParseCsv(file, lang);
        // Adjust totalRecordsInFile count: if the first line is a header error (line 0), don't count it as a record.
        // Otherwise, if no header error, all lines are records.
        long recordLines = validatedLineInfos.stream().filter(lineInfo -> lineInfo.getLineNumber() > 0 || (lineInfo.getLineNumber() == 0 && lineInfo.isValid())).count();
        report.setTotalRecordsInFile((int) recordLines);


        for (CsvValidatorLineInfo validatorLineInfo : validatedLineInfos) {
            if (validatorLineInfo.getLineNumber() == 0 && !validatorLineInfo.isValid()) {
                // This is a header validation error.
                report.setValidationErrorCount(report.getValidationErrorCount() + 1);
                RiciPagerCsvImportLineInfo headerErrorImportLine = new RiciPagerCsvImportLineInfo(validatorLineInfo);
                headerErrorImportLine.setImportMessage(String.join("; ", validatorLineInfo.getValidationErrors()));
                report.getLineInfos().add(headerErrorImportLine);
                log.warn("CSV header validation failed. Aborting import. Errors: {}", String.join("; ", validatorLineInfo.getValidationErrors()));
                // Do not process further lines if header is invalid. The report will contain only this header error.
                // Update totalRecordsInFile to 0 as no data records will be processed.
                report.setTotalRecordsInFile(0);
                break;
            }

            RiciPagerCsvImportLineInfo importLineInfo = new RiciPagerCsvImportLineInfo(validatorLineInfo);

            if (!validatorLineInfo.isValid()) {
                report.incrementValidationErrorCount();
                importLineInfo.setSuccessfullyImported(false);
                importLineInfo.setImportMessage("Validation failed: " + String.join("; ", validatorLineInfo.getValidationErrors()));
                log.debug("Line {} validation failed: {}", validatorLineInfo.getLineNumber(), String.join("; ", validatorLineInfo.getValidationErrors()));
            } else {
                RiciSimCard simCardForAssociation = null;
                String simMsisdnFromCsv = null;
                String simPinFromCsv = null;
                try {
                    // Primary check for existing pager should be by SERIAL_NUMBER
                    List<RiciPager> existingPagers = pagerROBusinessService.getBySerialNumber(
                            createPagerConstraint,
                            importLineInfo.getSerialNumber()
                    );

                    if (!existingPagers.isEmpty()) {
                        // Serial number must be unique. If found, it's a conflict.
                        report.incrementImportErrorCount();
                        importLineInfo.setSuccessfullyImported(false);
                        importLineInfo.setImportMessage("Import failed: Pager with SERIAL_NUMBER '" + importLineInfo.getSerialNumber() + "' already exists.");
                        log.warn("Pager with SERIAL_NUMBER '{}' already exists. Skipping line {}.", importLineInfo.getSerialNumber(), validatorLineInfo.getLineNumber());
                    } else {
                        // If PAGER_ID is provided in CSV, also check if it's already in use, as it might also be intended to be unique or semi-unique.
                        // This depends on business rules for PAGER_ID. Assuming for now it also needs to be unique if provided.
                        if (StringUtils.isNotBlank(importLineInfo.getPagerId())) {
                            List<RiciPager> pagersByPagerId = pagerROBusinessService.getByPagerId(createPagerConstraint, importLineInfo.getPagerId());
                            if (!pagersByPagerId.isEmpty()) {
                                report.incrementImportErrorCount();
                                importLineInfo.setSuccessfullyImported(false);
                                importLineInfo.setImportMessage("Import failed: Pager with PAGER_ID '" + importLineInfo.getPagerId() + "' already exists. SERIAL_NUMBER was unique.");
                                log.warn("Pager with PAGER_ID '{}' already exists, although SERIAL_NUMBER '{}' was unique. Skipping line {}.", importLineInfo.getPagerId(), importLineInfo.getSerialNumber(), validatorLineInfo.getLineNumber());
                                report.getLineInfos().add(importLineInfo);
                                continue;
                            }
                        }

                        // SIM Card Handling Logic
                        Long simCardTecidToAssociate = null;
                        simCardForAssociation = null;
                        // Use fields from importLineInfo DTO
                        String simIccidFromCsv = importLineInfo.getSimIccid();
                        simMsisdnFromCsv = importLineInfo.getSimMsisdn();
                        simPinFromCsv = importLineInfo.getSimPin();

                        if (StringUtils.isNotBlank(simIccidFromCsv)) {
                            try {
                                simCardForAssociation = simCardROBusinessService.getByIccid(createPagerConstraint, simIccidFromCsv);
                                log.debug("Found existing SIM card with ICCID {} for line {}", simIccidFromCsv, importLineInfo.getLineNumber());
                            } catch (NotFoundException e) {
                                // SIM not found, try to create if MSISDN and PIN are provided
                                if (StringUtils.isNotBlank(simMsisdnFromCsv) && StringUtils.isNotBlank(simPinFromCsv)) {
                                    log.debug("SIM card with ICCID {} not found. Attempting to create new SIM for line {}.", simIccidFromCsv, importLineInfo.getLineNumber());
                                    RiciSimCard newSimToCreate = new RiciSimCard();
                                    newSimToCreate.setIccid(simIccidFromCsv);
                                    newSimToCreate.setMsisdn(simMsisdnFromCsv);
                                    newSimToCreate.setPin(simPinFromCsv);
                                    newSimToCreate.setStatus(RiciSimCardStatus.DISPONIBLE.name()); // Default status

                                    // Use a security constraint for SIM creation. Assuming it's similar to pager creation for now.
                                    CGDISSecurityConstraint createSimConstraint = new CGDISSecurityConstraint(Permission.ROLE_PERMISSION_RICI_SIM_CARD_CREATE);
                                    simCardWriteRepositoryService.checkIfcanCreate(createSimConstraint, newSimToCreate); // Throws if not unique
                                    simCardForAssociation = simCardWriteBusinessService.createSimCard(createSimConstraint, newSimToCreate);
                                    log.info("Successfully created new SIM card with ICCID {} for line {}", simIccidFromCsv, importLineInfo.getLineNumber());
                                    importLineInfo.setImportMessage("New SIM " + simIccidFromCsv + " created. "); // Append to this message later
                                } else {
                                    // ICCID provided, SIM not found, but MSISDN/PIN missing for creation
                                    throw new NotFoundException("SIM card with ICCID " + simIccidFromCsv + " not found, and MSISDN/PIN not provided for creation.");
                                }
                            }

                            // At this point, simCardForAssociation should be set if one was found or created.
                            // Check its status for association.
                            if (!simCardForAssociation.getStatus().equalsIgnoreCase(RiciSimCardStatus.DISPONIBLE.name())) {
                                throw new SimCardNotAvailableForAssociationException("SIM card with ICCID " + simIccidFromCsv + " is not DISPONIBLE (current status: " + simCardForAssociation.getStatus() + ").");
                            }
                            simCardTecidToAssociate = simCardForAssociation.getTecid();
                        }
                        // End of SIM Card Handling Logic

                        RiciPager newPager = new RiciPager();
                        newPager.setSerialNumber(importLineInfo.getSerialNumber());
                        newPager.setPagerId(importLineInfo.getPagerId()); // Can be null if not in CSV
                        newPager.setManufacturer(importLineInfo.getManufacturer());
                        newPager.setModel(importLineInfo.getModel());
                        if (StringUtils.isNotBlank(importLineInfo.getDeliveryDate())) {
                            newPager.setDeliveryDate(LocalDate.parse(importLineInfo.getDeliveryDate(), RiciPagerCsvValidator.CSV_DATE_FORMATTER));
                        }

                        // The createPager method handles SIM association and status updates.
                        // Default assignment type for imported pagers is STOCK.
                        RiciPager createdPager = pagerWriteBusinessService.createPager(
                                createPagerConstraint,
                                newPager,
                                simCardTecidToAssociate, // Pass SIM tecid if found and valid
                                RiciPagerAssignmentType.STOCK, // Default assignment type
                                null, // No person assignment from CSV
                                null  // No entity assignment from CSV
                        ).getLeft(); // createPager returns a Pair

                        if (createdPager != null && createdPager.getTecid() != null) {
                            report.incrementSuccessfullyImportedCount();
                            importLineInfo.setSuccessfullyImported(true);
                            importLineInfo.setTecId(createdPager.getTecid());
                            String currentImportMessage = importLineInfo.getImportMessage() == null ? "" : importLineInfo.getImportMessage();
                            if (simCardTecidToAssociate != null) {
                                currentImportMessage += "Pager successfully imported with serial number: " + createdPager.getSerialNumber() + " and associated with SIM ICCID: " + simIccidFromCsv + ".";
                            } else {
                                currentImportMessage += "Pager successfully imported with serial number: " + createdPager.getSerialNumber() + ". No SIM association.";
                            }
                            importLineInfo.setImportMessage(currentImportMessage);
                            log.debug("Line {} processed successfully. {}", importLineInfo.getLineNumber(), currentImportMessage);
                        } else {
                            report.incrementImportErrorCount();
                            importLineInfo.setSuccessfullyImported(false);
                            importLineInfo.setImportMessage("Import failed: Could not create pager in database.");
                            log.error("Failed to create pager for line {} due to an unexpected error during persistence.", importLineInfo.getLineNumber());
                            // If SIM was newly created for this failed pager, attempt to delete the SIM
                            if (simCardForAssociation != null && StringUtils.isNotBlank(simMsisdnFromCsv) && StringUtils.isNotBlank(simPinFromCsv) && importLineInfo.getImportMessage().startsWith("New SIM")) { // Heuristic: SIM was newly created
                                log.warn("Attempting to delete newly created SIM {} due to pager creation failure.", simCardForAssociation.getTecid());
                                try {
                                    simCardWriteBusinessService.deleteSimCard(new CGDISSecurityConstraint(Permission.ROLE_PERMISSION_RICI_SIM_CARD_DELETE), simCardForAssociation.getTecid());
                                    importLineInfo.setImportMessage(importLineInfo.getImportMessage() + " (Newly created SIM " + simCardForAssociation.getIccid() + " deleted).");
                                } catch (Exception simDeleteEx) {
                                    log.error("Failed to delete newly created SIM {}: {}", simCardForAssociation.getTecid(), simDeleteEx.getMessage());
                                    importLineInfo.setImportMessage(importLineInfo.getImportMessage() + " (Failed to delete newly created SIM " + simCardForAssociation.getIccid() + ").");
                                }
                            }
                        }
                    }
                } catch (SimCardICCIDExistsException | SimCardMSISDNExistsException | NotFoundException |
                         SimCardNotAvailableForAssociationException | SimCardAlreadyAssociatedException e) {
                    report.incrementImportErrorCount();
                    importLineInfo.setSuccessfullyImported(false);
                    importLineInfo.setImportMessage("Import failed: " + e.getMessage());
                    log.error("Error processing line {}: {}", importLineInfo.getLineNumber(), e.getMessage());
                } catch (IllegalArgumentException e) {
                    report.incrementImportErrorCount();
                    importLineInfo.setSuccessfullyImported(false);
                    importLineInfo.setImportMessage("Import failed: Invalid data - " + e.getMessage());
                    log.error("Error processing line {}: Invalid data - {}", importLineInfo.getLineNumber(), e.getMessage());
                } catch (Exception e) {
                    report.incrementImportErrorCount();
                    importLineInfo.setSuccessfullyImported(false);
                    importLineInfo.setImportMessage("Import failed: An unexpected error occurred - " + e.getMessage());
                    log.error("Unexpected error processing line {}: {}", importLineInfo.getLineNumber(), e.getMessage(), e);
                    // If SIM was newly created and an unexpected error occurred during pager processing
                    if (simCardForAssociation != null && StringUtils.isNotBlank(simMsisdnFromCsv) && StringUtils.isNotBlank(simPinFromCsv) && (importLineInfo.getImportMessage() == null || importLineInfo.getImportMessage().startsWith("New SIM"))) {
                        log.warn("Attempting to delete newly created SIM {} due to unexpected error during pager processing.", simCardForAssociation.getTecid());
                        try {
                            simCardWriteBusinessService.deleteSimCard(new CGDISSecurityConstraint(Permission.ROLE_PERMISSION_RICI_SIM_CARD_DELETE), simCardForAssociation.getTecid());
                            importLineInfo.setImportMessage(importLineInfo.getImportMessage() + " (Newly created SIM " + simCardForAssociation.getIccid() + " deleted due to error).");
                        } catch (Exception simDeleteEx) {
                            log.error("Failed to delete newly created SIM {}: {}", simCardForAssociation.getTecid(), simDeleteEx.getMessage());
                            importLineInfo.setImportMessage(importLineInfo.getImportMessage() + " (Failed to delete newly created SIM " + simCardForAssociation.getIccid() + " after error).");
                        }
                    }
                }
            }
            report.getLineInfos().add(importLineInfo);
        }

        report.setEndTime(LocalDateTime.now());
        report.setDurationMillis(Duration.between(startTime, report.getEndTime()).toMillis());
        log.info("Finished CSV import process for pagers. Report: {}", report);
        return report;
    }
}
