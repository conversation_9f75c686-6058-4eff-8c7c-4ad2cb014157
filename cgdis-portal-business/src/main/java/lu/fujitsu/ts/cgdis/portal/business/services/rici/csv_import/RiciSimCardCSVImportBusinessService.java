package lu.fujitsu.ts.cgdis.portal.business.services.rici.csv_import;

import lu.fujitsu.ts.cgdis.portal.business.dto.rici.RiciSimCardCsvImportLineInfo;
import lu.fujitsu.ts.cgdis.portal.core.domain.CGDISSecurityConstraint;
import lu.fujitsu.ts.cgdis.portal.core.domain.csv_import.CsvImportReport;
import lu.fujitsu.ts.cgdis.portal.core.domain.csv_import.CsvValidatorLineInfo;
import lu.fujitsu.ts.cgdis.portal.core.domain.rici.RiciPager;
import lu.fujitsu.ts.cgdis.portal.core.domain.rici.RiciSimCard;
import lu.fujitsu.ts.cgdis.portal.core.exception.rici.PagerNotAvailableForAssociationException;
import lu.fujitsu.ts.cgdis.portal.core.exception.rici.SimCardICCIDExistsException;
import lu.fujitsu.ts.cgdis.portal.core.exception.rici.SimCardMSISDNExistsException;
import lu.fujitsu.ts.cgdis.portal.core.exception.upload.FileParsingException;
import lu.fujitsu.ts.cgdis.portal.core.service.rici.*;
import lu.fujitsu.ts.cgdis.portal.core.utils.FileValidatorUtils;
import lu.fujitsu.ts.cgdis.portal.services.rici.IRiciSimCardWriteRepositoryService;
import lu.fujitsu.ts.eportal.server.core.exceptions.NotFoundException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Service
public class RiciSimCardCSVImportBusinessService implements IRiciSimCardCSVImportBusinessService {

    private static final Logger log = LoggerFactory.getLogger(RiciSimCardCSVImportBusinessService.class);

    private final IRiciSimCardWriteBusinessService simCardWriteBusinessService;
    private final IRiciPagerROBusinessService pagerROBusinessService;
    private final IRiciSimCardPagerWriteBusinessService riciSimCardPagerWriteBusinessService;
    private final IRiciPagerWriteBusinessService pagerWriteBusinessService;
    private final IRiciSimCardWriteRepositoryService simCardWriteRepositoryService;
    private final TransactionTemplate transactionTemplate;

    @Autowired
    public RiciSimCardCSVImportBusinessService(IRiciSimCardWriteBusinessService simCardWriteBusinessService,
                                               IRiciPagerROBusinessService pagerROBusinessService,
                                               IRiciSimCardPagerWriteBusinessService riciSimCardPagerWriteBusinessService,
                                               IRiciPagerWriteBusinessService pagerWriteBusinessService,
                                               IRiciSimCardWriteRepositoryService simCardWriteRepositoryService,
                                               PlatformTransactionManager transactionManager) {
        this.simCardWriteBusinessService = simCardWriteBusinessService;
        this.pagerROBusinessService = pagerROBusinessService;
        this.riciSimCardPagerWriteBusinessService = riciSimCardPagerWriteBusinessService;
        this.pagerWriteBusinessService = pagerWriteBusinessService;
        this.simCardWriteRepositoryService = simCardWriteRepositoryService;

        this.transactionTemplate = new TransactionTemplate(transactionManager);
        this.transactionTemplate.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
    }

    @Override
    public CsvImportReport importSimCardsFromCsv(MultipartFile file,
                                                 String lang,
                                                 CGDISSecurityConstraint createSimConstraint,
                                                 CGDISSecurityConstraint updateSimConstraint,
                                                 CGDISSecurityConstraint viewPagerConstraint,
                                                 CGDISSecurityConstraint updatePagerConstraint) {
        log.info("Starting CSV import process for file: {}, language: {}", file.getOriginalFilename(), lang);
        CsvImportReport report = new CsvImportReport();
        report.setFileName(file.getOriginalFilename());
        report.setStartTime(LocalDateTime.now());

        if (!performInitialFileValidation(file, report)) {
            finalizeReport(report);
            return report;
        }

        List<CsvValidatorLineInfo> validationResults = new RiciSimCardCsvValidator().validateAndParseCsv(file, lang);

        Optional<CsvValidatorLineInfo> fileLevelValidationError = validationResults.stream()
                .filter(vr -> vr.getLineNumber() == 0 && !vr.isValid())
                .findFirst();

        if (fileLevelValidationError.isPresent()) {
            RiciSimCardCsvImportLineInfo errorLine = new RiciSimCardCsvImportLineInfo(fileLevelValidationError.get());
            report.getLineInfos().add(errorLine);
            report.setTotalRecordsInFile(0);
            report.incrementValidationErrorCount();
            finalizeReport(report);
            return report;
        }

        report.setTotalRecordsInFile((int) validationResults.stream().filter(vr -> vr.getLineNumber() > 0).count());
        log.info("Starting to process {} data rows from file {}", report.getTotalRecordsInFile(), report.getFileName());

        for (CsvValidatorLineInfo valInfo : validationResults) {
            if (valInfo.getLineNumber() == 0) continue; // Skip header pseudo-line if present

            try {
                log.info("Attempting to process line {} in new transaction. Raw data: '{}'", valInfo.getLineNumber(), valInfo.getRawLine());
                transactionTemplate.execute(status -> {
                    processValidatedCsvLine(valInfo, report, createSimConstraint, updateSimConstraint, viewPagerConstraint, updatePagerConstraint);
                    return null;
                });
                log.info("Successfully processed and committed line {}.", valInfo.getLineNumber());
            } catch (Exception e) {
                log.error("Processing of line {} failed and was rolled back. Error: {}", valInfo.getLineNumber(), e.getMessage());
                // The report is updated inside processValidatedCsvLine before it throws the exception
            }
        }

        finalizeReport(report);
        log.info("CSV import finished for file {}. Total Records: {}, Successfully Imported: {}, Validation Errors: {}, Import Errors: {}",
                report.getFileName(), report.getTotalRecordsInFile(), report.getSuccessfullyImportedCount(),
                report.getValidationErrorCount(), report.getImportErrorCount());
        return report;
    }

    public boolean performInitialFileValidation(MultipartFile file, CsvImportReport report) {
        try {
            if (!FileValidatorUtils.validateCsv(file, 5 * 1024 * 1024)) { // 5MB limit
                log.warn("CSV file validation failed for file: {}", file.getOriginalFilename());
                CsvValidatorLineInfo fileLevelError = new CsvValidatorLineInfo(0, "File Level Error");
                fileLevelError.addError("Invalid file type or size. Only CSV files up to 5MB are allowed.");
                RiciSimCardCsvImportLineInfo errorLine = new RiciSimCardCsvImportLineInfo(fileLevelError);
                report.getLineInfos().add(errorLine);
                report.setTotalRecordsInFile(0); // No records to process
                report.incrementValidationErrorCount();
                return false;
            }
        } catch (FileParsingException e) {
            log.error("Error during basic file validation (FileParsingException) for {}: {}", file.getOriginalFilename(), e.getMessage(), e);
            CsvValidatorLineInfo fileLevelError = new CsvValidatorLineInfo(0, "File Level Error");
            fileLevelError.addError("Error validating file structure or content: " + e.getMessage());
            RiciSimCardCsvImportLineInfo errorLine = new RiciSimCardCsvImportLineInfo(fileLevelError);
            report.getLineInfos().add(errorLine);
            report.incrementValidationErrorCount();
            return false;
        } catch (Exception e) {
            log.error("Unexpected error during basic file validation for {}: {}", file.getOriginalFilename(), e.getMessage(), e);
            CsvValidatorLineInfo fileLevelError = new CsvValidatorLineInfo(0, "File Level Error");
            fileLevelError.addError("Unexpected error validating file: " + e.getMessage());
            RiciSimCardCsvImportLineInfo errorLine = new RiciSimCardCsvImportLineInfo(fileLevelError);
            report.getLineInfos().add(errorLine);
            report.incrementValidationErrorCount();
            return false;
        }
        return true;
    }

    public void processValidatedCsvLine(CsvValidatorLineInfo valInfo, CsvImportReport report,
                                        CGDISSecurityConstraint createSimConstraint, CGDISSecurityConstraint updateSimConstraint,
                                        CGDISSecurityConstraint viewPagerConstraint, CGDISSecurityConstraint updatePagerConstraint) {
        RiciSimCardCsvImportLineInfo importLineInfo = new RiciSimCardCsvImportLineInfo(valInfo);
        report.getLineInfos().add(importLineInfo);

        if (!valInfo.isValid()) {
            importLineInfo.setSuccessfullyImported(false);
            importLineInfo.setImportMessage("Validation failed: " + String.join("; ", valInfo.getValidationErrors()));
            report.incrementValidationErrorCount();
            log.warn("Line {} failed validation: {}", valInfo.getLineNumber(), String.join("; ", valInfo.getValidationErrors()));
            return;
        }

        try {
            Map<String, String> parsedData = valInfo.getParsedData();
            Optional<RiciSimCard> createdSimCardOpt = createSimCardFromCsvData(parsedData, importLineInfo, report, createSimConstraint);

            if (createdSimCardOpt.isPresent()) {
                RiciSimCard createdSimCard = createdSimCardOpt.get();
                importLineInfo.setTecId(createdSimCard.getTecid()); // Store tecid of created SIM

                String baseMessage = "SIM Card created successfully with ICCID: " + createdSimCard.getIccid();
                String associationMessage = "";

                try {
                    Optional<String> assocMsgOpt = associateSimCardWithPagerFromCsvData(createdSimCard, parsedData, updateSimConstraint, viewPagerConstraint, updatePagerConstraint);
                    if (assocMsgOpt.isPresent()) {
                        associationMessage = assocMsgOpt.get();
                    }
                    importLineInfo.setSuccessfullyImported(true);
                    importLineInfo.setImportMessage(baseMessage + associationMessage);
                    report.incrementSuccessfullyImportedCount();

                } catch (PagerNotAvailableForAssociationException e) {
                    log.error("Association failed for SIM card {} on line {}. The transaction for this line will be rolled back. Error: {}", createdSimCard.getTecid(), valInfo.getLineNumber(), e.getMessage());
                    importLineInfo.setSuccessfullyImported(false);
                    importLineInfo.setImportMessage("SIM Card creation failed: " + e.getMessage());
                    report.incrementImportErrorCount();
                    // Re-throw to trigger transaction rollback. The created SIM card will be removed.
                    throw new RuntimeException(e);
                }
            }
        } catch (Exception e) {
            log.error("Error processing validated CSV line {}: {}. Raw data: '{}'. The transaction for this line will be rolled back.", valInfo.getLineNumber(), e.getMessage(), valInfo.getRawLine(), e);
            importLineInfo.setSuccessfullyImported(false);
            importLineInfo.setImportMessage("Processing error: " + e.getMessage());
            report.incrementImportErrorCount();
            // Re-throw to trigger transaction rollback
            throw new RuntimeException(e);
        }
    }

    public Optional<RiciSimCard> createSimCardFromCsvData(Map<String, String> parsedData, RiciSimCardCsvImportLineInfo importLineInfo,
                                                          CsvImportReport report, CGDISSecurityConstraint createSimConstraint) {
        RiciSimCard simCard = new RiciSimCard();
        simCard.setIccid(parsedData.get("ICCID"));
        simCard.setMsisdn(parsedData.get("MSISDN"));
        simCard.setPin(parsedData.get("PIN"));
        simCard.setStatus(lu.fujitsu.ts.cgdis.portal.core.domain.rici.enums.RiciSimCardStatus.DISPONIBLE.name());

        try {
            this.simCardWriteRepositoryService.checkIfcanCreate(createSimConstraint, simCard);
            return Optional.of(simCardWriteBusinessService.createSimCard(createSimConstraint, simCard));
        } catch (SimCardICCIDExistsException e) {
            log.warn("Import error on line {}: SIM card with ICCID {} already exists.", importLineInfo.getLineNumber(), simCard.getIccid());
            importLineInfo.setSuccessfullyImported(false);
            importLineInfo.setImportMessage("SIM card with ICCID " + simCard.getIccid() + " already exists.");
            report.incrementImportErrorCount();
            return Optional.empty();
        } catch (SimCardMSISDNExistsException e) {
            log.warn("Import error on line {}: SIM card with MSISDN {} already exists.", importLineInfo.getLineNumber(), simCard.getMsisdn());
            importLineInfo.setSuccessfullyImported(false);
            importLineInfo.setImportMessage("SIM card with MSISDN " + simCard.getMsisdn() + " already exists.");
            report.incrementImportErrorCount();
            return Optional.empty();
        }
    }

    public Optional<String> associateSimCardWithPagerFromCsvData(RiciSimCard createdSimCard, Map<String, String> parsedData,
                                                                  CGDISSecurityConstraint updateSimConstraint,
                                                                  CGDISSecurityConstraint viewPagerConstraint,
                                                                  CGDISSecurityConstraint updatePagerConstraint) throws NotFoundException {
        String pagerSerialNumber = parsedData.get("PAGER_SERIAL_NUMBER");
        String linkStartDateTimeStr = parsedData.get("LINK_START_DATETIME");
        String linkEndDateTimeStr = parsedData.get("LINK_END_DATETIME");

        if (pagerSerialNumber == null || pagerSerialNumber.isEmpty()) {
            return Optional.empty();
        }

        List<RiciPager> pagers = pagerROBusinessService.getBySerialNumber(viewPagerConstraint, pagerSerialNumber);

        if (pagers != null && !pagers.isEmpty()) {
            RiciPager pagerToAssociate = pagers.get(0);

            if (pagerToAssociate.getSimCard() != null) {
                if (!(pagerToAssociate.getSimCard().getTecid().equals(createdSimCard.getTecid()))) {
                    throw new PagerNotAvailableForAssociationException("Pager " + pagerToAssociate.getTecid() + " (Serial Number: " + pagerSerialNumber + ") already has an associated SIM card (ICCID: " + pagerToAssociate.getSimCard().getIccid() + "), therefore it cannot be associated with SIM card ICCID: " + createdSimCard.getIccid() + ").");
                } else {
                    log.info("Pager {} (Serial Number: {}) already associated with this SIM card {}.", pagerToAssociate.getTecid(), pagerSerialNumber, createdSimCard.getTecid());
                    return Optional.of(". Pager " + pagerToAssociate.getTecid() + " (Serial Number: " + pagerSerialNumber + ") already associated with this SIM card.");
                }
            }

            riciSimCardPagerWriteBusinessService.associateActiveSimCardWithPagerByIds(
                    updateSimConstraint,
                    createdSimCard.getTecid(),
                    pagerToAssociate.getTecid()
            );
            pagerWriteBusinessService.updatePagerSimCard(updatePagerConstraint, pagerToAssociate.getTecid(), createdSimCard.getTecid());

            String associationMessage = ". Associated with Pager serial number: " + pagerSerialNumber + ".";
            if (linkStartDateTimeStr != null && !linkStartDateTimeStr.isEmpty()) {
                associationMessage += ". CSV Start: " + linkStartDateTimeStr;
            }
            if (linkEndDateTimeStr != null && !linkEndDateTimeStr.isEmpty()) {
                associationMessage += ". CSV End: " + linkEndDateTimeStr;
            }
            return Optional.of(associationMessage);

        } else {
            throw new PagerNotAvailableForAssociationException("Pager with Serial Number '" + pagerSerialNumber + "' not found.");
        }
    }

    private void finalizeReport(CsvImportReport report) {
        report.setEndTime(LocalDateTime.now());
        if (report.getStartTime() != null && report.getEndTime() != null) {
            report.setDurationMillis(ChronoUnit.MILLIS.between(report.getStartTime(), report.getEndTime()));
        }
    }
}
