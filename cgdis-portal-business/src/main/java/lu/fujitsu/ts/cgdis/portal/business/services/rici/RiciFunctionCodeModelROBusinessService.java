package lu.fujitsu.ts.cgdis.portal.business.services.rici;

import lu.fujitsu.ts.cgdis.portal.core.domain.rici.RiciRicSchemaFunctionCodeModel;
import lu.fujitsu.ts.cgdis.portal.core.logging.LoggingCategory;
import lu.fujitsu.ts.cgdis.portal.core.logging.LoggingDomain;
import lu.fujitsu.ts.cgdis.portal.core.security.ICGDISSecurityConstraint;
import lu.fujitsu.ts.cgdis.portal.core.service.rici.IRiciFunctionCodeModelROBusinessService;
import lu.fujitsu.ts.cgdis.portal.services.rici.IRiciFunctionCodeModelRORepositoryService;
import lu.fujitsu.ts.eportal.server.core.business.services.SecuredROBusinessService;
import lu.fujitsu.ts.eportal.server.logging.milestones.LogMilestone;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@LogMilestone(category = LoggingCategory.BUSINESS_SERVICE, domain = LoggingDomain.RICI)
@Transactional(readOnly = true)
public class RiciFunctionCodeModelROBusinessService extends SecuredROBusinessService<ICGDISSecurityConstraint, RiciRicSchemaFunctionCodeModel, Long, IRiciFunctionCodeModelRORepositoryService> implements IRiciFunctionCodeModelROBusinessService {
    public RiciFunctionCodeModelROBusinessService(IRiciFunctionCodeModelRORepositoryService repositoryService) {
        super(repositoryService);
    }
}
