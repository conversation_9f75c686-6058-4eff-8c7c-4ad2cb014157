package lu.fujitsu.ts.cgdis.portal.business.services.positiontemplate;

import lu.fujitsu.ts.cgdis.portal.core.domain.PositionTemplateVersion;
import lu.fujitsu.ts.cgdis.portal.core.domain.PositionTemplateVersionWithEditable;
import lu.fujitsu.ts.cgdis.portal.core.logging.LoggingCategory;
import lu.fujitsu.ts.cgdis.portal.core.logging.LoggingDomain;
import lu.fujitsu.ts.cgdis.portal.core.security.Permission;
import lu.fujitsu.ts.cgdis.portal.core.security.utils.CGDISPortalSecurityHolder;
import lu.fujitsu.ts.cgdis.portal.core.service.positiontemplate.IAdminPositionTemplateVersionStatusBusinessService;
import lu.fujitsu.ts.eportal.server.core.utils.EPortalConverter;
import lu.fujitsu.ts.eportal.server.logging.milestones.LogMilestone;
import org.dozer.Mapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;

/**
 * The type Admin service plan model business service.
 */
@Service
@LogMilestone(category = LoggingCategory.BUSINESS_SERVICE, domain = LoggingDomain.POSITION_TEMPLATE_VERSION)
public class AdminPositionTemplateVersionStatusBusinessService implements IAdminPositionTemplateVersionStatusBusinessService {

    private final EPortalConverter<PositionTemplateVersion, PositionTemplateVersionWithEditable> modelToModelEditableConverter;

    @Autowired
    public AdminPositionTemplateVersionStatusBusinessService(Mapper mapper) {
        this.modelToModelEditableConverter = new EPortalConverter<>(PositionTemplateVersion.class, PositionTemplateVersionWithEditable.class, mapper);
    }

    @Override
    public PositionTemplateVersionWithEditable getStatus(PositionTemplateVersion version) {
        PositionTemplateVersionWithEditable modelEditable = modelToModelEditableConverter.convert(version);
        modelEditable.setEditable(canEditVersion(modelEditable));
        modelEditable.setFullEditable(LocalDate.now().isBefore(modelEditable.getStartDate()));
        modelEditable.setDeletable(canDeleteVersion(modelEditable));
        modelEditable.setClosurable(canClosureVersion(modelEditable));
        return modelEditable;
    }

    /**
     * Check if version can be edited
     * NOTE: can edit current and future versions
     * @param version the version
     * @return boolean
     */
    public boolean canEditVersion(PositionTemplateVersion version) {
        if (!CGDISPortalSecurityHolder.getConnectedUserDetails().hasAuthorityForAnyEntity(Permission.ROLE_PERMISSION_ADMIN_POSITION_TEMPLATE_VERSION_UPDATE.name())) {
            return false;
        } else {
            LocalDate now = LocalDate.now();
            return version.getEndDate() == null || version.getEndDate().isAfter(now) || version.getStartDate().isAfter(now);
        }

    }

    /**
     * Check if version can be deleted
     *
     * @param version the version
     * @return boolean
     */
    public boolean canDeleteVersion(PositionTemplateVersion version) {
        if (!CGDISPortalSecurityHolder.getConnectedUserDetails().hasAuthorityForAnyEntity(Permission.ROLE_PERMISSION_ADMIN_POSITION_TEMPLATE_VERSION_DELETE.name())) {
            return false;
        } else {
            return version.getStartDate() != null && version.getStartDate().isAfter(LocalDate.now());
        }
    }

    /**
     * Check if version can be closed
     * @param version the version
     * @return boolean
     */
    public boolean canClosureVersion(PositionTemplateVersion version) {
        if (!CGDISPortalSecurityHolder.getConnectedUserDetails().hasAuthorityForAnyEntity(Permission.ROLE_PERMISSION_ADMIN_POSITION_TEMPLATE_VERSION_CLOSURE.name())) {
            return false;
        } else {
            return version.getEndDate() == null;
        }
    }


}
