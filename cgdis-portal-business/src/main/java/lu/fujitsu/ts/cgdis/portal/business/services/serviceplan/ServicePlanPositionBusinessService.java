package lu.fujitsu.ts.cgdis.portal.business.services.serviceplan;

import lu.fujitsu.ts.cgdis.portal.core.domain.ServicePlanPositionWithFunctions;
import lu.fujitsu.ts.cgdis.portal.core.logging.LoggingCategory;
import lu.fujitsu.ts.cgdis.portal.core.logging.LoggingDomain;
import lu.fujitsu.ts.cgdis.portal.core.security.ICGDISSecurityConstraint;
import lu.fujitsu.ts.cgdis.portal.core.service.serviceplan.IServicePlanPositionBusinessService;
import lu.fujitsu.ts.cgdis.portal.services.serviceplanmodel.IServicePlanPositionRepositoryService;
import lu.fujitsu.ts.eportal.server.core.business.services.ModifiableSecuredBusinessService;
import lu.fujitsu.ts.eportal.server.logging.milestones.LogMilestone;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * The type Service plan position business service.
 */
@Service
@LogMilestone(category = LoggingCategory.BUSINESS_SERVICE, domain = LoggingDomain.SERVICE_PLAN_MODEL_VERSION)
public class ServicePlanPositionBusinessService extends ModifiableSecuredBusinessService<ICGDISSecurityConstraint, ServicePlanPositionWithFunctions, Long, IServicePlanPositionRepositoryService> implements IServicePlanPositionBusinessService {

    /**
     * Instantiates a new service plan position business service.
     * @param repositoryService the repository service
     */
    @Autowired
    public ServicePlanPositionBusinessService(IServicePlanPositionRepositoryService repositoryService) {
        super(repositoryService);
    }

}
