package lu.fujitsu.ts.cgdis.portal.business.services.serviceplan.completionstatus;

import lu.fujitsu.ts.eportal.server.core.exceptions.EPortalServerBusinessException;

/**
 * The type Prestation does not match position requirements exception.
 */
public class PrestationDoesNotMatchPositionRequirementsException extends EPortalServerBusinessException {
    /**
     * Instantiates a new Prestation does not match position requirements exception.
     */
    public PrestationDoesNotMatchPositionRequirementsException() {
    }

    /**
     * Instantiates a new Prestation does not match position requirements exception.
     *
     * @param message the message
     */
    public PrestationDoesNotMatchPositionRequirementsException(String message) {
        super(message);
    }

    /**
     * Instantiates a new Prestation does not match position requirements exception.
     *
     * @param message the message
     * @param cause   the cause
     */
    public PrestationDoesNotMatchPositionRequirementsException(String message, Throwable cause) {
        super(message, cause);
    }

    /**
     * Instantiates a new Prestation does not match position requirements exception.
     *
     * @param cause the cause
     */
    public PrestationDoesNotMatchPositionRequirementsException(Throwable cause) {
        super(cause);
    }

    /**
     * Instantiates a new Prestation does not match position requirements exception.
     *
     * @param message            the message
     * @param cause              the cause
     * @param enableSuppression  the enable suppression
     * @param writableStackTrace the writable stack trace
     */
    public PrestationDoesNotMatchPositionRequirementsException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }
}
