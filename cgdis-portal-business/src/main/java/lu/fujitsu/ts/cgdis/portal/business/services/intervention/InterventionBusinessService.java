package lu.fujitsu.ts.cgdis.portal.business.services.intervention;

import lu.fujitsu.ts.cgdis.portal.core.domain.intervention.Intervention;
import lu.fujitsu.ts.cgdis.portal.core.service.intervention.IInterventionBusinessService;
import lu.fujitsu.ts.cgdis.portal.core.utils.DateTimeUtils;
import lu.fujitsu.ts.cgdis.portal.services.intervention.IInterventionRepositoryService;
import lu.fujitsu.ts.eportal.server.core.business.services.SimpleROBusinessService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.temporal.ChronoUnit;
import java.util.List;

/**
 * The Intervention business service
 */
@Service
public class InterventionBusinessService extends SimpleROBusinessService<Intervention, Long, IInterventionRepositoryService> implements IInterventionBusinessService {

    private static final Logger LOGGER = LoggerFactory.getLogger(InterventionBusinessService.class);

    /**
     * Instanciate an Intervention business service
     * @param repositoryService the repository service
     */
    public InterventionBusinessService(IInterventionRepositoryService repositoryService) {
        super(repositoryService);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Duration getInterventionDurationBetweenDates(Long personId, LocalDate startDate, LocalDate endDate) {

        LOGGER.debug("get interventions duration between {} and {} for person with id {}", startDate, endDate, personId);

        List<Intervention> interventions = this.repositoryService.findAllBetweenDates(personId, startDate, endDate);

        final Duration[] duration = {Duration.of(0, ChronoUnit.MINUTES)};
        interventions.forEach(intervention -> {

            // Duration between intervention start and end dates
            long minutes = ChronoUnit.MINUTES.between(DateTimeUtils.max(startDate.atTime(LocalTime.MIDNIGHT), intervention.getStartDatetime()), DateTimeUtils.min(endDate.plusDays(1).atTime(LocalTime.MIDNIGHT), intervention.getEndDatetime()));

            // Add duration
            duration[0] = duration[0].plusMinutes(minutes);

        });

        return duration[0];
    }

}
