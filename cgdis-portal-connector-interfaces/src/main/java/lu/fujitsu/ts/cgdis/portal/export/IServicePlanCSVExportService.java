package lu.fujitsu.ts.cgdis.portal.export;

import lu.fujitsu.ts.cgdis.portal.core.security.ICGDISSecurityConstraint;

import java.io.Writer;
import java.time.LocalDate;

/**
 * The interface Service plan csv export service.
 */
public interface IServicePlanCSVExportService {

    /**
     * Generate service plan csv.
     *
     * @param securityConstraint the security constraint
     * @param writer             the writer
     * @param servicePlanId      the service plan id
     * @param from               the from
     * @param to                 the to
     */
    void generateServicePlanCSV(ICGDISSecurityConstraint securityConstraint, Writer writer, long servicePlanId, LocalDate from, LocalDate to, boolean smsi);

    /**
     * Generate filename string.
     *
     * @param servicePlanTecid the service plan tecid
     * @return the string
     */
    String generateFilename(long servicePlanTecid);
}
