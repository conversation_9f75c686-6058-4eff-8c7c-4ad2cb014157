package lu.fujitsu.ts.cgdis.portal.services.person;

import lu.fujitsu.ts.cgdis.portal.core.domain.person.DrivingLicense;
import lu.fujitsu.ts.cgdis.portal.services.IBaseCGDISSecuredRepositoryService;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface IDrivingLicenseRepositoryService extends IBaseCGDISSecuredRepositoryService<DrivingLicense>{

    /**
     * Get all ongoing driving licenses for given person
     * @param personId the person id
     * @return list of driving licenses
     */
    List<DrivingLicense> getAllOngoingForPerson(Long personId);

}
