package lu.fujitsu.ts.cgdis.portal.services.person;

import lu.fujitsu.ts.cgdis.portal.core.domain.person.Diploma;
import lu.fujitsu.ts.cgdis.portal.services.IBaseCGDISSecuredRepositoryService;
import lu.fujitsu.ts.eportal.server.core.exceptions.NotFoundException;
import org.springframework.stereotype.Repository;

/**
 * The interface Person diploma repository service.
 */
@Repository
public interface IDiplomaRepositoryService extends IBaseCGDISSecuredRepositoryService<Diploma> {


    /**
     * Gets by external id.
     *
     * @param name the name
     * @param ident theidentification
     * @return the by name and ident
     */
    Diploma getByNameAndIdent(String name, String ident) throws NotFoundException;
}
