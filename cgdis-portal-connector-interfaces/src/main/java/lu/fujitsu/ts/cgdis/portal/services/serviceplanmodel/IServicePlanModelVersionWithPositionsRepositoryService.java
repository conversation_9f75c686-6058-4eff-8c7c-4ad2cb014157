package lu.fujitsu.ts.cgdis.portal.services.serviceplanmodel;

import lu.fujitsu.ts.cgdis.portal.core.domain.serviceplanmodel.ServicePlanModel;
import lu.fujitsu.ts.cgdis.portal.core.domain.serviceplanmodel.ServicePlanModelVersion;
import lu.fujitsu.ts.cgdis.portal.core.domain.serviceplanmodel.ServicePlanModelVersionWithModel;
import lu.fujitsu.ts.cgdis.portal.core.domain.serviceplanmodel.ServicePlanModelVersionWithPositions;
import lu.fujitsu.ts.cgdis.portal.core.security.ICGDISSecurityConstraint;
import lu.fujitsu.ts.eportal.server.core.exceptions.NotFoundException;
import lu.fujitsu.ts.eportal.server.core.services.ISecuredRepositoryService;

import java.time.LocalDate;
import java.util.Collection;
import java.util.List;
import java.util.Optional;

/**
 * The interface Service plan model version repository service.
 */
public interface IServicePlanModelVersionWithPositionsRepositoryService extends ISecuredRepositoryService<ICGDISSecurityConstraint, ServicePlanModelVersionWithPositions, Long> {

    /**
     * Get number of position usage
     * @param positionId the service plan model position id
     * @return the number of position usage
     */
    long countPositionUsage(ICGDISSecurityConstraint securityConstraint, Long positionId);

    /**
     * Find next start date for service plan model version
     * @param securityConstraint the security constraint
     * @param modelId the service plan model id
     * @param startDate the start date
     * @param excludedVersionId version excluded
     * @return the next start date
     */
    LocalDate findNextStartDate(ICGDISSecurityConstraint securityConstraint, Long modelId, LocalDate startDate, Long excludedVersionId);

    /**
     * Find applicable version by date
     * @param securityConstraint the security constraint
     * @param modelId the service plan model id
     * @param date the date
     * @param versionIdExcluded the version id that is excluded from search
     * @return the service plan model version with positions
     */
    ServicePlanModelVersionWithPositions findApplicableVersionByDate(ICGDISSecurityConstraint securityConstraint, Long modelId, LocalDate date, Long versionIdExcluded) throws NotFoundException;

    /**
     * Find applicable version by date
     * @param modelId the service plan model id
     * @param date the date
     * @param versionIdExcluded the version id that is excluded from search
     * @return the service plan model version with positions
     */
    ServicePlanModelVersionWithPositions findApplicableVersionByDateWithoutSecurity(Long modelId, LocalDate date, Long versionIdExcluded) throws NotFoundException;

    /**
     * Get the nearest version ended before the date in parameter.
     * Can exclude version id of result
     * @param securityConstraint the security constraint
     * @param modelId the service plan model if
     * @param date the date
     * @param versionId the service plan model version id
     * @return the service plan model version with positions
     */
    ServicePlanModelVersionWithPositions findNearestVersionEndedBeforeDate(ICGDISSecurityConstraint securityConstraint, Long modelId, LocalDate date, Long versionId);

    /**
     * Update the end date of service plan model version
     * @param securityConstraint the security constraint
     * @param modelId the service plan model if
     * @param previousDate the previous service plan model version end date
     * @param newDate the new service plan model version end date
     * @return the model versions
     */
    List<ServicePlanModelVersion> updateEndDates(ICGDISSecurityConstraint securityConstraint, Long modelId, LocalDate previousDate, LocalDate newDate);

    /**
     * Get all service plan model version with functions
     * @param securityConstraint the security constraint
     * @param versionIds the service plan model version ids
     * @return the service plan model version with positions
     */
    List<ServicePlanModelVersionWithPositions> getAllWithFunctions(ICGDISSecurityConstraint securityConstraint, Collection<Long> versionIds);

    /**
     * Get current model version of service plan
     * @param securityConstraint the security constraint
     * @param servicePlanId the service plan id
     * @return the current service plan model version
     */
    ServicePlanModelVersion getCurrent(ICGDISSecurityConstraint securityConstraint, Long servicePlanId);

    /**
     * Get first and next version of service plan model
     * @param securityConstraint the security constraint
     * @param servicePlanId the service plan id
     * @return the service plan model versions
     */
    Optional<ServicePlanModelVersion> getFirstNextVersion(ICGDISSecurityConstraint securityConstraint, Long servicePlanId);

    /**
     * CHeck if service plan model is closable
     * @param securityConstraint the security constraint
     * @param modelId the service plan model id
     * @return is closable ?
     */
    boolean checkClosableStatusServicePlanModel(ICGDISSecurityConstraint securityConstraint, Long modelId);

    /**
     * Close service plan model
     * @param securityConstraint the security constraint
     * @param modelId the service plan model id
     * @param closureDate the closure date
     * @throws NotFoundException service plan model not found
     */
    void closeServicePlanModel(ICGDISSecurityConstraint securityConstraint, Long modelId, LocalDate closureDate) throws NotFoundException;

    /**
     * Get unique service plan model for given label
     * @param securityConstraint the security constraint
     * @param label service plan model label
     * @param modelId service plan model id
     * @param versionId service plan model version id
     * @return list of service plan model version with positions
     */
    List<ServicePlanModelVersionWithPositions> getUniqueModelVersionsByLabelExceptIdWithoutVersion(ICGDISSecurityConstraint securityConstraint, String label, Long modelId, Long versionId);

    /**
     * Delete all service plan model version of given model
     * @param securityConstraint the security constraint
     * @param model the service plan model
     */
    void deleteAllVersionOfModel(ICGDISSecurityConstraint securityConstraint, ServicePlanModel model);

    /**
     * Get current service plan model version that include date
     * @param securityConstraint the security constraint
     * @param modelId the service plan model id
     * @param startDate the date
     * @return the service plan model version matching the date
     */
    ServicePlanModelVersion getCurrentByDate(ICGDISSecurityConstraint securityConstraint, Long modelId, LocalDate startDate) throws NotFoundException;

    /**
     * Get current service plan model version that include date
     * @param modelId the service plan model id
     * @param startDate the date
     * @return the service plan model version matching the date
     */
    Boolean checkCurrentByDateWithoutSecurity(Long modelId, LocalDate startDate) throws NotFoundException;

    /**
     *
     * @param positionTemplateId
     * @param date
     * @return
     */
    List<ServicePlanModelVersionWithModel> findServicePlanModelVersionWithPositionTemplateId(Long positionTemplateId, LocalDate date);

}
