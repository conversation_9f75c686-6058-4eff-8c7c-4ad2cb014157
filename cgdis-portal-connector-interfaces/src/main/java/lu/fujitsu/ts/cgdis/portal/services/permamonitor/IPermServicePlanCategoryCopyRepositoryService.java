package lu.fujitsu.ts.cgdis.portal.services.permamonitor;

import lu.fujitsu.ts.cgdis.portal.core.domain.permamonitor.PermDeploymentPlan;
import lu.fujitsu.ts.cgdis.portal.core.security.ICGDISSecurityConstraint;

/**
 * Service interface for managing PermServicePlanCategory entities with security constraints.
 * Extends the ISecuredRORepositoryService interface to provide read-only operations
 * with security constraints.
 */
public interface IPermServicePlanCategoryCopyRepositoryService {


    /**
     * Copies all entities from one deployment plan to another with the given security constraint.
     *
     * @param securityConstraint the security constraint to apply
     * @param fromDeploymentPlan the source deployment plan
     * @param toDeploymentPlan   the target deployment plan
     */
    void copyAll(ICGDISSecurityConstraint securityConstraint, PermDeploymentPlan fromDeploymentPlan, PermDeploymentPlan toDeploymentPlan);


}
