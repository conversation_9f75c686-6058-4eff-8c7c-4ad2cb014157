package lu.fujitsu.ts.cgdis.portal.services.rici;

import lu.fujitsu.ts.cgdis.portal.core.domain.rici.RiciAlertGroup;
import lu.fujitsu.ts.cgdis.portal.core.domain.rici.RiciPager;
import lu.fujitsu.ts.cgdis.portal.core.domain.rici.RiciPersonWithPagerAndAlertGroups;
import lu.fujitsu.ts.cgdis.portal.core.security.ICGDISSecurityConstraint;
import lu.fujitsu.ts.eportal.server.core.domain.SearchCriterion;
import lu.fujitsu.ts.eportal.server.core.exceptions.NotFoundException;
import lu.fujitsu.ts.eportal.server.core.services.ISecuredRORepositoryService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;

import java.util.List;
import java.util.Set;

/**
 * Service interface for managing RiciPager entities.
 * Extends the ISecuredRORepositoryService interface to provide
 * read-only operations with security constraints.
 */
public interface IRiciPagerRORepositoryService extends ISecuredRORepositoryService<ICGDISSecurityConstraint, RiciPager, Long> {
    /**
     * Retrieves all RiciPager entities.
     *
     * @return a list of all RiciPager entities
     */
    List<RiciPager> getAllPagers();

    /**
     * Retrieves a paginated list of RiciPager entities matching the given filters.
     *
     * @param securityConstraint the security constraint to apply
     * @param searchCriterions   the list of search criteria to filter the pagers
     * @param pageRequest        the pagination and sorting information
     * @return a page of RiciPager entities matching the specified filters
     */
    Page<RiciPager> getAllPagersWithFilters(ICGDISSecurityConstraint securityConstraint, List<SearchCriterion> searchCriterions, PageRequest pageRequest);

    /**
     * Finds all persons with their associated pagers and alert groups using filters.
     *
     * @param securityConstraint the security constraint to apply
     * @param searchCriterions   the list of search criteria
     * @param pageRequest        the pagination and sorting information
     * @return a Page containing matched persons with pagers and alert groups
     * @throws NotFoundException if no persons matching the filters are found
     */
    Page<RiciPersonWithPagerAndAlertGroups> getAllPersonsWithPagersAndAlertGroupsWithFilters(ICGDISSecurityConstraint securityConstraint, List<SearchCriterion> searchCriterions, PageRequest pageRequest) throws NotFoundException;

    /**
     * Finds a pager by its ID.
     *
     * @param securityConstraint the security constraint to apply
     * @param pagerId            the pager ID to search for
     * @return a list of matching pagers
     */
    List<RiciPager> findByPagerId(ICGDISSecurityConstraint securityConstraint, String pagerId);
    /**
     * Finds a pager by its ID.
     *
     * @param securityConstraint the security constraint to apply
     * @param serialNumber            the serial number to search for
     * @return a list of matching pagers
     */
    List<RiciPager> findBySerialNumber(ICGDISSecurityConstraint securityConstraint, String serialNumber);

    /**
     * Finds all pagers that are assignable (status INACTIVE and not associated with a SIM card).
     *
     * @param securityConstraint the security constraint to apply
     * @return a list of assignable RiciPager domain objects
     */
    List<RiciPager> findAssignablePagers(ICGDISSecurityConstraint securityConstraint);


    /**
     * Finds all pagers that can be assigned to a person within a specific entity.
     *
     * @param securityConstraint the security constraint to apply
     * @param entityTecid        the technical entity ID to filter pagers by
     * @return a list of assignable RiciPager objects for the specified entity
     */
    List<RiciPager> findAllPersonAssignablePagers(ICGDISSecurityConstraint securityConstraint, Long entityTecid);


    /**
     * Retrieves all alert groups associated with a given entity.
     *
     * @param securityConstraint the security constraint to apply
     * @param entityTecid        the technical entity ID
     * @return a set of RiciAlertGroup objects associated with the entity
     * @throws NotFoundException if the entity or alert groups are not found
     */
    Set<RiciAlertGroup> getAllEntityAlertGroupsByTecid(ICGDISSecurityConstraint securityConstraint, Long entityTecid) throws NotFoundException;
}
