package lu.fujitsu.ts.cgdis.portal.services.rici;

import lu.fujitsu.ts.cgdis.portal.core.domain.rici.RiciRicRangeType;
import lu.fujitsu.ts.eportal.server.core.domain.IBaseModel;

/**
 * Interface for partial updates to RiciRicRange.
 */
public interface IRiciRicRangePartialUpdate extends IBaseModel<Long> {

    /**
     * Gets the technical lock identifier.
     *
     * @param currentTeclock the current technical lock
     * @return the technical lock identifier
     */
    Long getTecLock(Long currentTeclock);

    /**
     * Gets the new name.
     *
     * @param currentName the current name
     * @return the new name
     */
    String getNewName(String currentName);

    /**
     * Gets the new range start.
     *
     * @param currentRangeStart the current range start
     * @return the new range start
     */
    Long getNewRangeStart(Long currentRangeStart);

    /**
     * Gets the new range end.
     *
     * @param currentRangeEnd the current range end
     * @return the new range end
     */
    Long getNewRangeEnd(Long currentRangeEnd);

    /**
     * Gets the new status.
     *
     * @param currentType the current status
     * @return the new status
     */
    RiciRicRangeType getNewType(RiciRicRangeType currentType);

//    /**
//     * Gets the new entity.
//     *
//     * @param currentEntity the current entity tecid
//     * @return the new entity
//     */
//    CGDISEntity getNewEntity(CGDISEntity currentEntity);

    /**
     * Gets the new entity.
     *
     * @param currentEntityTecid the current entity tecid
     * @return the new entity
     */
    Long getNewEntityTecid(Long currentEntityTecid);
}
