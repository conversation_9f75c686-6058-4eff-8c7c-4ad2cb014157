package lu.fujitsu.ts.cgdis.portal.export.pdf;

import lu.fujitsu.ts.cgdis.portal.core.domain.document.DocumentInformation;
import lu.fujitsu.ts.cgdis.portal.core.exception.export.ExportException;
import lu.fujitsu.ts.cgdis.portal.core.security.ICGDISSecurityConstraint;

import java.io.IOException;
import java.time.LocalDate;
import java.util.concurrent.ExecutionException;

/**
 * The interface Service plan prestations pdf export.
 */
public interface IServicePlanPrestationsPDFExport {
    /**
     * Export document information.
     *
     * @param servicePlanId      the service plan id
     * @param securityConstraint the security constraint
     * @param from               the from
     * @param to                 the to
     * @return the document information
     * @throws ExportException      the export exception
     * @throws IOException          the io exception
     * @throws ExecutionException   the execution exception
     * @throws InterruptedException the interrupted exception
     */
    DocumentInformation export( Long servicePlanId, ICGDISSecurityConstraint securityConstraint,  LocalDate from,  LocalDate to) throws ExportException, IOException, ExecutionException, InterruptedException;
}
