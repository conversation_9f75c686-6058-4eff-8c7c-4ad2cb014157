package lu.fujitsu.ts.cgdis.portal.services.permamonitor;

import lu.fujitsu.ts.cgdis.portal.core.domain.permamonitor.PermDeploymentPlan;
import lu.fujitsu.ts.cgdis.portal.core.security.ICGDISSecurityConstraint;
import lu.fujitsu.ts.cgdis.portal.services.ISecuredOnlyWriteAndPartialRepositoryService;

/**
 * Interface for write operations on PermDeploymentPlan entities.
 * This interface extends ISecuredOnlyWRepositoryService to provide secured write operations.
 *
 * @param <ICGDISSecurityConstraint> the type of security constraint used.
 * @param <PermDeploymentPlan> the type of entity being managed.
 * @param <Long> the type of the entity's identifier.
 */
public interface IPermDeploymentPlanWriteRepositoryService extends ISecuredOnlyWriteAndPartialRepositoryService<ICGDISSecurityConstraint, PermDeploymentPlan, Long, IPermDeploymentPlanPartialUpdate> {
}
