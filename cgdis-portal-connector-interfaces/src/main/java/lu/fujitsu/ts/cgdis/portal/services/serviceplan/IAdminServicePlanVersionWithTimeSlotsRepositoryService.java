package lu.fujitsu.ts.cgdis.portal.services.serviceplan;

import lu.fujitsu.ts.cgdis.portal.core.domain.serviceplan.AdminServicePlanVersionWithTimeSlots;
import lu.fujitsu.ts.cgdis.portal.core.domain.serviceplan.timeslot.ServicePlanTimeSlot;
import lu.fujitsu.ts.cgdis.portal.core.security.ICGDISSecurityConstraint;
import lu.fujitsu.ts.eportal.server.core.exceptions.NotFoundException;
import lu.fujitsu.ts.eportal.server.core.services.ISecuredRepositoryService;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

/**
 * The Service plan version repository service interface.
 */
public interface IAdminServicePlanVersionWithTimeSlotsRepositoryService extends ISecuredRepositoryService<ICGDISSecurityConstraint, AdminServicePlanVersionWithTimeSlots, Long> {




    /**
     * Find a possible application version by date
     *
     * @param servicePlanId the service plan id
     * @param startDate     the start date
     * @return a possible service plan version with its time slots
     */
    Optional<AdminServicePlanVersionWithTimeSlots> findApplicableVersionByDate(Long servicePlanId, LocalDate startDate);

    /**
     * Find the nearest version ended before date
     *
     * @param servicePlanId     the service plan id
     * @param date              the date
     * @param versionIdExcluded service plan version to exclude
     * @return the nearest service plan version
     */
    AdminServicePlanVersionWithTimeSlots findNearestVersionEndedBeforeDate(Long servicePlanId, LocalDate date, Long versionIdExcluded);



    /**
     * Find the next start date
     *
     * @param securityConstraint the security constraint
     * @param servicePlanId      the service plan id
     * @param startDate          the date
     * @param excludedVersionId  version excluded
     * @return the next start date
     */
    LocalDate findNextStartDate(ICGDISSecurityConstraint securityConstraint, Long servicePlanId, LocalDate startDate, Long excludedVersionId);


    /**
     * Update time slots list.
     *
     * @param securityConstraint      the security constraint
     * @param servicePlanVersionTecid the service plan version tecid
     * @param timeslots               the new from split slot
     * @return the list
     */
    List<ServicePlanTimeSlot> updateTimeSlots(ICGDISSecurityConstraint securityConstraint, Long servicePlanVersionTecid, List<ServicePlanTimeSlot> timeslots) throws NotFoundException;
}
