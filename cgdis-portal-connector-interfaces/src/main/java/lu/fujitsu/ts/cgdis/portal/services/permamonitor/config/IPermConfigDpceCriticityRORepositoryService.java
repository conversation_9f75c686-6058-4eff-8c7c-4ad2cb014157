package lu.fujitsu.ts.cgdis.portal.services.permamonitor.config;

import lu.fujitsu.ts.cgdis.portal.core.domain.permamonitor.PermCategoryName;
import lu.fujitsu.ts.cgdis.portal.core.domain.permamonitor.config.PermConfigDpceCriticity;
import lu.fujitsu.ts.cgdis.portal.core.pojo.permamonitor.config.PermConfigDpceCriticityDboInfo;
import lu.fujitsu.ts.cgdis.portal.core.pojo.permamonitor.config.PermConfigDpceCriticityDboInfoList;
import lu.fujitsu.ts.cgdis.portal.core.security.ICGDISSecurityConstraint;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.Map;
import java.util.Optional;

/**
 * Service interface for read-only operations on PermConfigDpceCriticity.
 */
public interface IPermConfigDpceCriticityRORepositoryService {

    /**
     * Retrieves the criticity configuration for a given deployment plan and category.
     *
     * @param securityConstraint  the security constraint to be applied
     * @param deploymentPLanTecid the technical ID of the deployment plan
     * @param category            the category name
     * @param entityTecid         the technical ID of the entity
     * @param criticityTecid      the technical ID of the criticity
     * @return the criticity configuration
     */
    Optional<PermConfigDpceCriticity> getCriticityConfig(ICGDISSecurityConstraint securityConstraint, Long deploymentPLanTecid, PermCategoryName category, Long entityTecid, Long criticityTecid);

    /**
     * Sums the criticity configuration for a given category within a date range.
     *
     * @param securityConstraint the security constraint to be applied
     * @param category           the category name
     * @param from               the start date of the range
     * @param toIncluded         the end date of the range (inclusive)
     * @param baseEntityTecid    the optional technical ID of the base entity
     * @return the summed criticity configuration
     */
    PermConfigDpceCriticityDboInfoList sumCriticityConfig(ICGDISSecurityConstraint securityConstraint, PermCategoryName category, LocalDate from, LocalDate toIncluded, Optional<Long> baseEntityTecid);

    /**
     * Retrieves the criticity configuration for a given category and a collection of entity technical IDs at a specific date and time.
     *
     * @param securityConstraint the security constraint to be applied
     * @param category           the category name
     * @param entityTecids       the collection of entity technical IDs
     * @param dateTime           the date and time for which the configuration is retrieved
     * @return a map of entity technical IDs to their corresponding criticity configuration
     */
    Map<Long, PermConfigDpceCriticityDboInfo> getCriticityConfigForMainEntity(ICGDISSecurityConstraint securityConstraint, PermCategoryName category, Collection<Long> entityTecids, LocalDateTime dateTime);
}
