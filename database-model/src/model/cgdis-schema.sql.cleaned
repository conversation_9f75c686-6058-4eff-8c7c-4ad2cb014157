-- My<PERSON><PERSON> Script generated by <PERSON><PERSON><PERSON> Workbench
-- Wed Apr 17 16:22:49 2024
-- Model: New Model    Version: 1.0
-- MySQL Workbench Forward Engineering

SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0;
SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0;
SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION';

-- -----------------------------------------------------
-- Schema mydb
-- -----------------------------------------------------
-- -----------------------------------------------------
-- Schema cgdis
-- -----------------------------------------------------

-- -----------------------------------------------------
-- Schema cgdis
-- -----------------------------------------------------
CREATE SCHEMA IF NOT EXISTS `cgdis_portal_db` DEFAULT CHARACTER SET utf8 ;
USE `cgdis_portal_db` ;

-- -----------------------------------------------------
-- Table `cgdis_portal_db`.`PERSON`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis_portal_db`.`PERSON` ;

CREATE TABLE IF NOT EXISTS `cgdis_portal_db`.`PERSON` (
                                                `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
    `TECLOCK` BIGINT(20) NOT NULL DEFAULT 0,
    `TIC` VARCHAR(200) NULL,
    `FIRSTNAME` VARCHAR(200) NOT NULL,
    `LASTNAME` VARCHAR(200) NOT NULL,
    `TITLE` VARCHAR(30) NULL,
    `IS_PROFESSIONAL` CHAR(1) NOT NULL DEFAULT 'Y',
    `IS_VOLUNTEER` CHAR(1) NOT NULL DEFAULT 'Y',
    `IS_EXTERNAL` CHAR(1) NULL DEFAULT 'Y',
    `IS_RETIRED` CHAR(1) NULL DEFAULT 'Y',
    `IS_YOUNG_FIREFIGHTER` CHAR(1) NULL DEFAULT 'Y',
    `IS_VETERAN` CHAR(1) NULL DEFAULT 'Y',
    `CGDIS_HIRING_DATE` DATE NULL,
    `CGDIS_VACATION_DATE` DATE NULL,
    `IAM_NUMBER` VARCHAR(10) NULL DEFAULT NULL,
    `FOREIGN_REGISTRATION_NUMBER` VARCHAR(50) NULL,
    `BIRTHDATE` DATE NULL DEFAULT NULL,
    `GENDER` CHAR(1) NULL DEFAULT NULL,
    `BIRTH_PLACE` VARCHAR(200) NULL,
    `NATIONAL_REGISTRATION_NUMBER` BLOB NULL,
    `CGDIS_REGISTRATION_NUMBER` VARCHAR(20) NULL,
    `HR_REGISTRATION_NUMBER` VARCHAR(20) NULL,
    `IS_PROFESSIONAL_OPERATIONAL` CHAR(1) NULL DEFAULT 'Y',
    `IS_PROFESSIONAL_ADM_TECH` CHAR(1) NULL DEFAULT 'Y',
    `IS_INTERN` CHAR(1) NULL DEFAULT 'Y',
    `IS_CANDIDATE` CHAR(1) NULL DEFAULT 'Y',
    `IS_OPERATIONAL_FIREFIGHTER` CHAR(1) NULL DEFAULT 'Y',
    `IS_SUPPORT_FIREFIGHTER` CHAR(1) NULL DEFAULT 'Y',
    `DELETE_DATETIME` DATETIME NULL DEFAULT NULL,
    `IS_SAMU` CHAR(1) NULL DEFAULT 'Y',
    PRIMARY KEY (`TECID`))
    ENGINE = InnoDB
    DEFAULT CHARACTER SET = utf8;

CREATE UNIQUE INDEX `U_PERSON_TECID` ON `cgdis_portal_db`.`PERSON` (`TECID` ASC);

CREATE UNIQUE INDEX `TIC_UNIQUE` ON `cgdis_portal_db`.`PERSON` (`TIC` ASC);


-- -----------------------------------------------------
-- Table `cgdis_portal_db`.`ADDRESS`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis_portal_db`.`ADDRESS` ;

CREATE TABLE IF NOT EXISTS `cgdis_portal_db`.`ADDRESS` (
                                                 `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
    `TECLOCK` BIGINT(20) NOT NULL,
    `STREET_NUMBER` VARCHAR(10) NULL DEFAULT NULL,
    `STREET` VARCHAR(250) NULL DEFAULT NULL,
    `ZIP_CODE` VARCHAR(15) NULL DEFAULT NULL,
    `CITY` VARCHAR(100) NULL DEFAULT NULL,
    `COUNTRY` VARCHAR(3) NULL DEFAULT NULL,
    `PERSON_TECID` BIGINT(20) NOT NULL,
    PRIMARY KEY (`TECID`),
    CONSTRAINT `FK_ADDRESS_PERSON`
    FOREIGN KEY (`PERSON_TECID`)
    REFERENCES `cgdis_portal_db`.`PERSON` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
    ENGINE = InnoDB
    DEFAULT CHARACTER SET = utf8;

CREATE INDEX `I_FK_ADDRESS_PERSON` ON `cgdis_portal_db`.`ADDRESS` (`PERSON_TECID` ASC);


-- -----------------------------------------------------
-- Table `cgdis_portal_db`.`ENTITY_CATEGORY`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis_portal_db`.`ENTITY_CATEGORY` ;

CREATE TABLE IF NOT EXISTS `cgdis_portal_db`.`ENTITY_CATEGORY` (
                                                         `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
    `TECLOCK` BIGINT(20) NOT NULL,
    `LABEL` VARCHAR(100) NOT NULL,
    PRIMARY KEY (`TECID`))
    ENGINE = InnoDB
    DEFAULT CHARACTER SET = utf8;

CREATE UNIQUE INDEX `U_ENTITY_CATEGORY_TECID` ON `cgdis_portal_db`.`ENTITY_CATEGORY` (`TECID` ASC);

CREATE UNIQUE INDEX `U_ENTITY_CATEGORY_LABEL` ON `cgdis_portal_db`.`ENTITY_CATEGORY` (`LABEL` ASC);


-- -----------------------------------------------------
-- Table `cgdis_portal_db`.`ENTITY_ADDRESS`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis_portal_db`.`ENTITY_ADDRESS` ;

CREATE TABLE IF NOT EXISTS `cgdis_portal_db`.`ENTITY_ADDRESS` (
                                                        `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
    `TECLOCK` BIGINT(20) NOT NULL,
    `ADDRESS` VARCHAR(500) NULL,
    `ZIP_CODE` VARCHAR(10) NULL,
    `CITY` VARCHAR(200) NULL,
    `COUNTRY` VARCHAR(3) NULL,
    PRIMARY KEY (`TECID`))
    ENGINE = InnoDB;


-- -----------------------------------------------------
-- Table `cgdis_portal_db`.`ENTITY`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis_portal_db`.`ENTITY` ;

CREATE TABLE IF NOT EXISTS `cgdis_portal_db`.`ENTITY` (
                                                `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
    `TECLOCK` BIGINT(20) NOT NULL,
    `GLOBAL_ID` VARCHAR(100) NOT NULL,
    `NAME` VARCHAR(255) NOT NULL,
    `ARMING_PRIORITY` INT(11) NOT NULL,
    `PARENT_ENTITY_TECID` BIGINT(20) NULL DEFAULT NULL,
    `TYPE` VARCHAR(50) NOT NULL,
    `ENTITY_CATEGORY` BIGINT(20) NULL DEFAULT NULL,
    `ENTITY_ADDRESS_TECID` BIGINT(20) NULL,
    `HELP_TYPE_KEY` VARCHAR(100) NULL,
    `HELP_ORGANIZATION_FOREIGN_KEY` VARCHAR(100) NULL,
    `MAIN_ENTITY_TECID` BIGINT(20) NULL,
    PRIMARY KEY (`TECID`),
    CONSTRAINT `FK_ENTITY_ENTITY`
    FOREIGN KEY (`PARENT_ENTITY_TECID`)
    REFERENCES `cgdis_portal_db`.`ENTITY` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
    CONSTRAINT `FK_ENTITY_ENTITY_CATEGORY`
    FOREIGN KEY (`ENTITY_CATEGORY`)
    REFERENCES `cgdis_portal_db`.`ENTITY_CATEGORY` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
    CONSTRAINT `fk_entity_ENTITY_ADDRESS1`
    FOREIGN KEY (`ENTITY_ADDRESS_TECID`)
    REFERENCES `cgdis_portal_db`.`ENTITY_ADDRESS` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
    CONSTRAINT `FK_ENTITY_MAIN_ENTITY`
    FOREIGN KEY (`MAIN_ENTITY_TECID`)
    REFERENCES `cgdis_portal_db`.`ENTITY` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
    ENGINE = InnoDB
    DEFAULT CHARACTER SET = utf8;

CREATE UNIQUE INDEX `U_ENTITY_NAME` ON `cgdis_portal_db`.`ENTITY` (`NAME` ASC);

CREATE INDEX `I_FK_ENTITY_ENTITY` ON `cgdis_portal_db`.`ENTITY` (`PARENT_ENTITY_TECID` ASC);

CREATE INDEX `I_FK_ENTITY_ENTITY_CATEGORY` ON `cgdis_portal_db`.`ENTITY` (`ENTITY_CATEGORY` ASC);

CREATE INDEX `fk_entity_ENTITY_ADDRESS1_idx` ON `cgdis_portal_db`.`ENTITY` (`ENTITY_ADDRESS_TECID` ASC);

CREATE UNIQUE INDEX `GLOBAL_ID_UNIQUE` ON `cgdis_portal_db`.`ENTITY` (`GLOBAL_ID` ASC);

CREATE INDEX `FK_ENTITY_MAIN_ENTITY_idx` ON `cgdis_portal_db`.`ENTITY` (`MAIN_ENTITY_TECID` ASC);


-- -----------------------------------------------------
-- Table `cgdis_portal_db`.`ASSIGNMENT`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis_portal_db`.`ASSIGNMENT` ;

CREATE TABLE IF NOT EXISTS `cgdis_portal_db`.`ASSIGNMENT` (
  `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
  `TECLOCK` BIGINT(20) NOT NULL,
  `PERSON_TECID` BIGINT(20) NOT NULL,
  `ENTITY_TECID` BIGINT(20) NOT NULL,
  `PRIMARY_TYPE` VARCHAR(50) NULL DEFAULT 'PRIMARY',
  `ASSIGNMENT_TYPE` VARCHAR(50) NULL DEFAULT 'VOL',
  `START_DATE` DATE NOT NULL,
  `END_DATE` DATE NULL,
  `ASSIGNMENT_EXTERNAL_TECID` BIGINT(20) NOT NULL,
  `DELETE_DATETIME` DATETIME NULL DEFAULT NULL,
  PRIMARY KEY (`TECID`),
  CONSTRAINT `FK_ASSIGNMENT_ENTITY`
    FOREIGN KEY (`ENTITY_TECID`)
    REFERENCES `cgdis_portal_db`.`ENTITY` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `FK_ASSIGNMENT_PERSON`
    FOREIGN KEY (`PERSON_TECID`)
    REFERENCES `cgdis_portal_db`.`PERSON` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB
DEFAULT CHARACTER SET = utf8;

CREATE INDEX `I_FK_ASSIGNMENT_PERSON` ON `cgdis_portal_db`.`ASSIGNMENT` (`PERSON_TECID` ASC);

CREATE INDEX `I_FK_ASSIGNMENT_ENTITY` ON `cgdis_portal_db`.`ASSIGNMENT` (`ENTITY_TECID` ASC);


-- -----------------------------------------------------
-- Table `cgdis_portal_db`.`INTERVENTION_TYPE`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis_portal_db`.`INTERVENTION_TYPE` ;

CREATE TABLE IF NOT EXISTS `cgdis_portal_db`.`INTERVENTION_TYPE` (
                                                           `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
    `TECLOCK` BIGINT(20) NOT NULL,
    `LABEL` VARCHAR(50) NOT NULL,
    PRIMARY KEY (`TECID`))
    ENGINE = InnoDB
    DEFAULT CHARACTER SET = utf8;

CREATE UNIQUE INDEX `U_INTERVENTION_TYPE_TECID` ON `cgdis_portal_db`.`INTERVENTION_TYPE` (`TECID` ASC);


-- -----------------------------------------------------
-- Table `cgdis_portal_db`.`SERVICE_PLAN_TYPE`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis_portal_db`.`SERVICE_PLAN_TYPE` ;

CREATE TABLE IF NOT EXISTS `cgdis_portal_db`.`SERVICE_PLAN_TYPE` (
                                                           `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
    `TECLOCK` BIGINT(20) NULL DEFAULT NULL,
    `LABEL` VARCHAR(50) NULL DEFAULT NULL COMMENT 'Caserné/Non Caserné/Professionnel\n',
    `ACCEPT_VOLUNTEER` CHAR(1) NULL,
    `ACCEPT_PRO` CHAR(1) NULL,
    `IS_BARRACKED` CHAR(1) NULL,
    PRIMARY KEY (`TECID`))
    ENGINE = InnoDB
    DEFAULT CHARACTER SET = utf8;


-- -----------------------------------------------------
-- Table `cgdis_portal_db`.`LEGAL_CONSTRAINT`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis_portal_db`.`LEGAL_CONSTRAINT` ;

CREATE TABLE IF NOT EXISTS `cgdis_portal_db`.`LEGAL_CONSTRAINT` (
                                                          `INTERVENTION_TYPE_ID` BIGINT(20) NOT NULL,
    `PDS_TYPE_TECID` BIGINT(20) NOT NULL,
    `ARMING_MAXIMUM_DELAY` INT(11) NULL DEFAULT NULL,
    `ENTITY_CATEGORY_ID` BIGINT(20) NOT NULL,
    PRIMARY KEY (`INTERVENTION_TYPE_ID`, `PDS_TYPE_TECID`, `ENTITY_CATEGORY_ID`),
    CONSTRAINT `FK_LEGAL_CONSTRAINT_ENTITY_CATEGORY`
    FOREIGN KEY (`ENTITY_CATEGORY_ID`)
    REFERENCES `cgdis_portal_db`.`ENTITY_CATEGORY` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
    CONSTRAINT `FK_LEGAL_CONSTRAINT_INTERVENTION_TYPE`
    FOREIGN KEY (`INTERVENTION_TYPE_ID`)
    REFERENCES `cgdis_portal_db`.`INTERVENTION_TYPE` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
    CONSTRAINT `FK_LEGAL_CONSTRAINT_PDS_TYPE`
    FOREIGN KEY (`PDS_TYPE_TECID`)
    REFERENCES `cgdis_portal_db`.`SERVICE_PLAN_TYPE` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
    ENGINE = InnoDB
    DEFAULT CHARACTER SET = utf8;

CREATE INDEX `I_FK_LEGAL_CONSTRAINT_INTERVENTION_TYPE` ON `cgdis_portal_db`.`LEGAL_CONSTRAINT` (`INTERVENTION_TYPE_ID` ASC);

CREATE INDEX `I_FK_LEGAL_CONSTRAINT_PDS_TYPE` ON `cgdis_portal_db`.`LEGAL_CONSTRAINT` (`PDS_TYPE_TECID` ASC);

CREATE INDEX `I_FK_LEGAL_CONSTRAINT_ENTITY_CATEGORY` ON `cgdis_portal_db`.`LEGAL_CONSTRAINT` (`ENTITY_CATEGORY_ID` ASC);


-- -----------------------------------------------------
-- Table `cgdis_portal_db`.`OPERATIONAL_FUNCTION`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis_portal_db`.`OPERATIONAL_FUNCTION` ;

CREATE TABLE IF NOT EXISTS `cgdis_portal_db`.`OPERATIONAL_FUNCTION` (
                                                              `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
    `TECLOCK` BIGINT(20) NOT NULL,
    `LABEL` VARCHAR(100) NOT NULL,
    `PARTIAL_FULFILL` CHAR(1) NULL,
    PRIMARY KEY (`TECID`))
    ENGINE = InnoDB
    DEFAULT CHARACTER SET = utf8;


-- -----------------------------------------------------
-- Table `cgdis_portal_db`.`OPERATIONAL_OCCUPATION`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis_portal_db`.`OPERATIONAL_OCCUPATION` ;

CREATE TABLE IF NOT EXISTS `cgdis_portal_db`.`OPERATIONAL_OCCUPATION` (
                                                                `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
    `TECLOCK` BIGINT(20) NOT NULL,
    `LABEL` VARCHAR(100) NOT NULL,
    `CODE` VARCHAR(50) NOT NULL,
    `TYPE` VARCHAR(100) NOT NULL,
    PRIMARY KEY (`TECID`))
    ENGINE = InnoDB
    DEFAULT CHARACTER SET = utf8;

CREATE INDEX `IX_OPERATION_OCCUPATION_TYPE` ON `cgdis_portal_db`.`OPERATIONAL_OCCUPATION` (`TYPE` ASC);

CREATE UNIQUE INDEX `TECID_UNIQUE` ON `cgdis_portal_db`.`OPERATIONAL_OCCUPATION` (`TECID` ASC);


-- -----------------------------------------------------
-- Table `cgdis_portal_db`.`PERSON_OPERATIONAL_OCCUPATIONS`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis_portal_db`.`PERSON_OPERATIONAL_OCCUPATIONS` ;

CREATE TABLE IF NOT EXISTS `cgdis_portal_db`.`PERSON_OPERATIONAL_OCCUPATIONS` (
                                                                        `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
    `TECLOCK` BIGINT(20) NOT NULL,
    `OPERATIONAL_OCCUPATION_TECID` BIGINT(20) NOT NULL,
    `PERSON_TECID` BIGINT(20) NOT NULL,
    `START_DATE` DATE NOT NULL,
    `END_DATE` DATE NULL,
    `IS_VOLUNTEER` CHAR(1) NOT NULL DEFAULT 'Y',
    `IS_PROFESSIONAL` CHAR(1) NOT NULL DEFAULT 'Y',
    `STATUS` VARCHAR(20) NOT NULL DEFAULT 'ONGOING',
    `NOMINATION_DATE` DATE NOT NULL,
    `PERSON_OCCUPATION_EXTERNAL_TECID` BIGINT(20) NOT NULL,
    `ENTITY_TECID` BIGINT(20) NULL,
    `IS_EXTERNAL` CHAR(1) NOT NULL DEFAULT 'Y',
    PRIMARY KEY (`TECID`),
    CONSTRAINT `FK_PERSON_OP_OCCS_OP_OCC`
    FOREIGN KEY (`OPERATIONAL_OCCUPATION_TECID`)
    REFERENCES `cgdis_portal_db`.`OPERATIONAL_OCCUPATION` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
    CONSTRAINT `FK_PERSON_OP_OCCS_PERSON`
    FOREIGN KEY (`PERSON_TECID`)
    REFERENCES `cgdis_portal_db`.`PERSON` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
    CONSTRAINT `FK_PERSON_OP_OCCS_ENTITY`
    FOREIGN KEY (`ENTITY_TECID`)
    REFERENCES `cgdis_portal_db`.`ENTITY` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
    ENGINE = InnoDB
    DEFAULT CHARACTER SET = utf8;

CREATE INDEX `I_FK_PERSON_OP_OCCS_PERSON` ON `cgdis_portal_db`.`PERSON_OPERATIONAL_OCCUPATIONS` (`PERSON_TECID` ASC);

CREATE INDEX `I_FK_PERSON_OP_OCCS_OP_OCC` ON `cgdis_portal_db`.`PERSON_OPERATIONAL_OCCUPATIONS` (`OPERATIONAL_OCCUPATION_TECID` ASC);

CREATE INDEX `I_FK_PERSON_OP_OCCS_ENTITY` ON `cgdis_portal_db`.`PERSON_OPERATIONAL_OCCUPATIONS` (`ENTITY_TECID` ASC);


-- -----------------------------------------------------
-- Table `cgdis_portal_db`.`SERVICE_PLAN_MODEL`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis_portal_db`.`SERVICE_PLAN_MODEL` ;

CREATE TABLE IF NOT EXISTS `cgdis_portal_db`.`SERVICE_PLAN_MODEL` (
                                                            `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
    `TECLOCK` BIGINT(20) NOT NULL,
    `NAME` VARCHAR(200) NOT NULL,
    `HAS_VEHICLE` CHAR(1) NULL DEFAULT NULL,
    `INTERVENTION_TYPE_ID` BIGINT(20) NOT NULL,
    `ENTITY_TECID` BIGINT(20) NULL,
    PRIMARY KEY (`TECID`),
    CONSTRAINT `FK_SP_MODEL_INTERVENTION_TYPE`
    FOREIGN KEY (`INTERVENTION_TYPE_ID`)
    REFERENCES `cgdis_portal_db`.`INTERVENTION_TYPE` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
    CONSTRAINT `FK_SP_MODEL_ENTITY`
    FOREIGN KEY (`ENTITY_TECID`)
    REFERENCES `cgdis_portal_db`.`ENTITY` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
    ENGINE = InnoDB
    DEFAULT CHARACTER SET = utf8;

CREATE INDEX `I_FK_SP_MODEL_INTERVENTION_TYPE` ON `cgdis_portal_db`.`SERVICE_PLAN_MODEL` (`INTERVENTION_TYPE_ID` ASC);

CREATE INDEX `I_FK_SP_MODEL_ENTITY` ON `cgdis_portal_db`.`SERVICE_PLAN_MODEL` (`ENTITY_TECID` ASC);


-- -----------------------------------------------------
-- Table `cgdis_portal_db`.`SERVICE_PLAN_MODEL_VERSION`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis_portal_db`.`SERVICE_PLAN_MODEL_VERSION` ;

CREATE TABLE IF NOT EXISTS `cgdis_portal_db`.`SERVICE_PLAN_MODEL_VERSION` (
                                                                    `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
    `TECLOCK` BIGINT(20) NOT NULL,
    `LABEL` VARCHAR(100) NULL,
    `STARTDATE` DATE NOT NULL,
    `ENDDATE` DATE NULL DEFAULT NULL,
    `SERVICE_PLAN_MODEL_TECID` BIGINT(20) NOT NULL,
    PRIMARY KEY (`TECID`),
    CONSTRAINT `FK_SERVICE_PLAN_MODEL_VERSION_SERVICE_PLAN_MODEL`
    FOREIGN KEY (`SERVICE_PLAN_MODEL_TECID`)
    REFERENCES `cgdis_portal_db`.`SERVICE_PLAN_MODEL` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
    ENGINE = InnoDB
    DEFAULT CHARACTER SET = utf8;

CREATE UNIQUE INDEX `U_SERVICE_PLAN_MODEL_VERSION_TECID` ON `cgdis_portal_db`.`SERVICE_PLAN_MODEL_VERSION` (`TECID` ASC);

CREATE INDEX `FK_SERVICE_PLAN_MODEL_VERSION_SERVICE_PLAN_MODEL` ON `cgdis_portal_db`.`SERVICE_PLAN_MODEL_VERSION` (`SERVICE_PLAN_MODEL_TECID` ASC);

CREATE UNIQUE INDEX `U_SERVICE_PLAN_MODEL_VERSION_MODEL_AND_STARTDATE` ON `cgdis_portal_db`.`SERVICE_PLAN_MODEL_VERSION` (`STARTDATE` ASC, `SERVICE_PLAN_MODEL_TECID` ASC);


-- -----------------------------------------------------
-- Table `cgdis_portal_db`.`SERVICE_PLAN_POSITION`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis_portal_db`.`SERVICE_PLAN_POSITION` ;

CREATE TABLE IF NOT EXISTS `cgdis_portal_db`.`SERVICE_PLAN_POSITION` (
                                                               `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
    `TECLOCK` BIGINT(20) NOT NULL,
    `LABEL` VARCHAR(100) NULL DEFAULT NULL,
    `COMPLETION_TYPE` VARCHAR(10) NULL DEFAULT 'REQUIRED' COMMENT 'REQUIRED/OPTIMAL/OPTIONAL',
    `SERVICE_PLAN_MODEL_VERSION_TECID` BIGINT(20) NULL,
    `POSITION_ORDER` INT NULL,
    `ICON` VARCHAR(100) NULL,
    PRIMARY KEY (`TECID`),
    CONSTRAINT `FK_SERVICE_PLAN_POSITION_SERVICE_PLAN_MODEL_VERSION`
    FOREIGN KEY (`SERVICE_PLAN_MODEL_VERSION_TECID`)
    REFERENCES `cgdis_portal_db`.`SERVICE_PLAN_MODEL_VERSION` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
    ENGINE = InnoDB
    DEFAULT CHARACTER SET = utf8;

CREATE INDEX `I_FK_SERVICE_PLAN_POSITION_SERVICE_PLAN_MODEL_VERSION1` ON `cgdis_portal_db`.`SERVICE_PLAN_POSITION` (`SERVICE_PLAN_MODEL_VERSION_TECID` ASC);


-- -----------------------------------------------------
-- Table `cgdis_portal_db`.`VEHICLE_STATUS`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis_portal_db`.`VEHICLE_STATUS` ;

CREATE TABLE IF NOT EXISTS `cgdis_portal_db`.`VEHICLE_STATUS` (
                                                        `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
    `ELS_VALUE` VARCHAR(3) NULL,
    `USE_IN_PORTAL` CHAR(1) NULL,
    `USE_IN_ELS` CHAR(1) NULL,
    PRIMARY KEY (`TECID`))
    ENGINE = InnoDB
    DEFAULT CHARACTER SET = utf8;

CREATE UNIQUE INDEX `U_VEHICLE_STATUS_TECID` ON `cgdis_portal_db`.`VEHICLE_STATUS` (`TECID` ASC);

CREATE UNIQUE INDEX `U_VEHICLE_STATUS_ELS` ON `cgdis_portal_db`.`VEHICLE_STATUS` (`ELS_VALUE` ASC, `USE_IN_ELS` ASC);

CREATE UNIQUE INDEX `U_VEHICLE_STATUS_PORTAL` ON `cgdis_portal_db`.`VEHICLE_STATUS` (`ELS_VALUE` ASC, `USE_IN_PORTAL` ASC);


-- -----------------------------------------------------
-- Table `cgdis_portal_db`.`VEHICLE_STATUS_HISTORIC`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis_portal_db`.`VEHICLE_STATUS_HISTORIC` ;

CREATE TABLE IF NOT EXISTS `cgdis_portal_db`.`VEHICLE_STATUS_HISTORIC` (
                                                                 `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
    `VEHICLE_TECID` BIGINT(20) NULL,
    `VEHICLE_STATUS_TECID` BIGINT(20) NULL,
    `DATE` DATETIME NOT NULL,
    `FROM_USER` VARCHAR(30) NULL,
    PRIMARY KEY (`TECID`),
    CONSTRAINT `FK_VEHICLE_STATUS_HISTO_VEHICLE`
    FOREIGN KEY (`VEHICLE_TECID`)
    REFERENCES `cgdis_portal_db`.`VEHICLE` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
    CONSTRAINT `FK_VEHICLE_STATUS_HISTO_VEHICLE_STATUS`
    FOREIGN KEY (`VEHICLE_STATUS_TECID`)
    REFERENCES `cgdis_portal_db`.`VEHICLE_STATUS` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
    ENGINE = InnoDB
    DEFAULT CHARACTER SET = utf8;

CREATE UNIQUE INDEX `U_VEHICLE_STATUS_HISTORIC_TECID` ON `cgdis_portal_db`.`VEHICLE_STATUS_HISTORIC` (`TECID` ASC);

CREATE INDEX `FK_VEHICLE_STATUS_HISTO_VEHICLE_idx` ON `cgdis_portal_db`.`VEHICLE_STATUS_HISTORIC` (`VEHICLE_TECID` ASC);

CREATE INDEX `FK_VEHICLE_STATUS_HISTO_VEHICLE_STATUS_idx` ON `cgdis_portal_db`.`VEHICLE_STATUS_HISTORIC` (`VEHICLE_STATUS_TECID` ASC);


-- -----------------------------------------------------
-- Table `cgdis_portal_db`.`VEHICLE`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis_portal_db`.`VEHICLE` ;

CREATE TABLE IF NOT EXISTS `cgdis_portal_db`.`VEHICLE` (
                                                 `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
    `TECLOCK` BIGINT(20) NOT NULL,
    `TYPE` VARCHAR(100) NOT NULL,
    `REGISTRATION` VARCHAR(20) NULL,
    `ENTITY_TECID` BIGINT(20) NOT NULL,
    `ID` VARCHAR(100) NOT NULL,
    `NAME` VARCHAR(200) NULL DEFAULT NULL,
    `DESCRIPTION` VARCHAR(512) NULL DEFAULT NULL,
    `DELETE_DATETIME` DATETIME NULL,
    `CURRENT_STATUS` BIGINT(20) NULL,
    PRIMARY KEY (`TECID`),
    CONSTRAINT `FK_VEHICLE_ENTITY`
    FOREIGN KEY (`ENTITY_TECID`)
    REFERENCES `cgdis_portal_db`.`ENTITY` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
    CONSTRAINT `FK_VEHICLE_VEHICLE_STATUS_HISTO`
    FOREIGN KEY (`CURRENT_STATUS`)
    REFERENCES `cgdis_portal_db`.`VEHICLE_STATUS_HISTORIC` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
    ENGINE = InnoDB
    DEFAULT CHARACTER SET = utf8;

CREATE UNIQUE INDEX `U_VEHICLE_TECID` ON `cgdis_portal_db`.`VEHICLE` (`TECID` ASC);

CREATE UNIQUE INDEX `U_VEHICLE_ID` ON `cgdis_portal_db`.`VEHICLE` (`ID` ASC);

CREATE INDEX `I_FK_VEHICLE_ENTITY1` ON `cgdis_portal_db`.`VEHICLE` (`ENTITY_TECID` ASC);

CREATE INDEX `FK_VEHICLE_VEHICLE_STATUS_HISTO_idx` ON `cgdis_portal_db`.`VEHICLE` (`CURRENT_STATUS` ASC);


-- -----------------------------------------------------
-- Table `cgdis_portal_db`.`BOX`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis_portal_db`.`BOX` ;

CREATE TABLE IF NOT EXISTS `cgdis_portal_db`.`BOX` (
                                             `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
                                             `TECLOCK` BIGINT(20) NOT NULL,
                                             `GLOBAL_ID` VARCHAR(45) NOT NULL,
                                             `NAME` VARCHAR(100) NULL,
                                             `ISMASTER` CHAR(1) NULL DEFAULT NULL,
                                             PRIMARY KEY (`TECID`))
    ENGINE = InnoDB;

CREATE UNIQUE INDEX `GLOBAL_ID_UNIQUE` ON `cgdis_portal_db`.`BOX` (`GLOBAL_ID` ASC);

CREATE UNIQUE INDEX `TECID_UNIQUE` ON `cgdis_portal_db`.`BOX` (`TECID` ASC);


-- -----------------------------------------------------
-- Table `cgdis_portal_db`.`SERVICE_PLAN`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis_portal_db`.`SERVICE_PLAN` ;

CREATE TABLE IF NOT EXISTS `cgdis_portal_db`.`SERVICE_PLAN`
(
    `TECID`                    BIGINT(20)   NOT NULL AUTO_INCREMENT,
    `TECLOCK`                  BIGINT(20)   NOT NULL,
    `LABEL`                    VARCHAR(255) NOT NULL,
    `PORTAL_LABEL`             VARCHAR(255) NOT NULL,
    `ARMING_DELAY`             INT          NULL,
    `BACKUP_GROUP`             VARCHAR(255) NULL,
    `OPTIONAL_GROUP`           VARCHAR(255) NULL,
    `ENTITY_TECID`             BIGINT(20)   NOT NULL,
    `VEHICLE_TECID`            BIGINT(20)   NULL,
    `ARMING_PRIORITY`          INT          NOT NULL,
    `SERVICE_PLAN_MODEL_TECID` BIGINT(20)   NOT NULL,
    `SERVICE_PLAN_TYPE_TECID`  BIGINT(20)   NOT NULL,
    `IS_EXCLUSIVE`             CHAR(1)      NULL DEFAULT NULL,
    `FIRST_DAY`                INT          NULL DEFAULT 1,
    `BOX_TECID` BIGINT(20) NULL,
    PRIMARY KEY (`TECID`),
    CONSTRAINT `fk_service_plan_entity`
    FOREIGN KEY (`ENTITY_TECID`)
    REFERENCES `cgdis_portal_db`.`ENTITY` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
    CONSTRAINT `fk_service_plan_vehicle`
    FOREIGN KEY (`VEHICLE_TECID`)
    REFERENCES `cgdis_portal_db`.`VEHICLE` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
    CONSTRAINT `fk_service_plan_service_plan_model`
    FOREIGN KEY (`SERVICE_PLAN_MODEL_TECID`)
    REFERENCES `cgdis_portal_db`.`SERVICE_PLAN_MODEL` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
    CONSTRAINT `fk_service_plan_service_plan_type`
    FOREIGN KEY (`SERVICE_PLAN_TYPE_TECID`)
    REFERENCES `cgdis_portal_db`.`SERVICE_PLAN_TYPE` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
    ENGINE = InnoDB;

CREATE INDEX `fk_service_plan_entity1_idx` ON `cgdis_portal_db`.`SERVICE_PLAN` (`ENTITY_TECID` ASC);

CREATE INDEX `fk_service_plan_vehicle1_idx` ON `cgdis_portal_db`.`SERVICE_PLAN` (`VEHICLE_TECID` ASC);

CREATE INDEX `fk_service_plan_service_plan_model1_idx` ON `cgdis_portal_db`.`SERVICE_PLAN` (`SERVICE_PLAN_MODEL_TECID` ASC);

CREATE INDEX `fk_service_plan_service_plan_type1_idx` ON `cgdis_portal_db`.`SERVICE_PLAN` (`SERVICE_PLAN_TYPE_TECID` ASC);

CREATE INDEX `fk_service_plan_BOX1_idx` ON `cgdis_portal_db`.`SERVICE_PLAN` (`BOX_TECID` ASC);


-- -----------------------------------------------------
-- Table `cgdis_portal_db`.`SERVICE_PLAN_VERSION`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis_portal_db`.`SERVICE_PLAN_VERSION` ;

CREATE TABLE IF NOT EXISTS `cgdis_portal_db`.`SERVICE_PLAN_VERSION` (
                                                              `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
    `TECLOCK` BIGINT(20) NOT NULL,
    `STARTDATE` DATE NULL DEFAULT NULL,
    `ENDDATE` DATE NULL DEFAULT NULL,
    `AUTOMATIC` CHAR(1) NULL DEFAULT 'N',
    `SERVICE_PLAN_TECID` BIGINT(20) NOT NULL,
    `LABEL` VARCHAR(100) NOT NULL,
    `SPLIT` CHAR(1) NULL DEFAULT 0,
    `MANUAL_CLOSURE` CHAR(1) NULL DEFAULT 0,
    PRIMARY KEY (`TECID`),
    CONSTRAINT `fk_service_plan_version_service_plan1`
    FOREIGN KEY (`SERVICE_PLAN_TECID`)
    REFERENCES `cgdis_portal_db`.`SERVICE_PLAN` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
    ENGINE = InnoDB
    DEFAULT CHARACTER SET = utf8;

CREATE INDEX `I_FK_SP_VERSION_SP` ON `cgdis_portal_db`.`SERVICE_PLAN_VERSION` (`SERVICE_PLAN_TECID` ASC);

CREATE UNIQUE INDEX `U_SERVICE_PLAN_VERSION_PLAN_AND_STARTDATE` ON `cgdis_portal_db`.`SERVICE_PLAN_VERSION` (`STARTDATE` ASC, `SERVICE_PLAN_TECID` ASC);


-- -----------------------------------------------------
-- Table `cgdis_portal_db`.`SERVICE_PLAN_TIME_SLOT`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis_portal_db`.`SERVICE_PLAN_TIME_SLOT` ;

CREATE TABLE IF NOT EXISTS `cgdis_portal_db`.`SERVICE_PLAN_TIME_SLOT` (
                                                                `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
    `TECLOCK` BIGINT(20) NOT NULL,
    `START_TIME` TIME(1) NULL DEFAULT NULL,
    `END_TIME` TIME(1) NULL DEFAULT NULL,
    `SERVICE_PLAN_VERSION_TECID` BIGINT(20) NOT NULL,
    `SLOT_ORDER` INT NULL,
    `ONLY_FIRST_DAY` CHAR(1) NULL DEFAULT '0',
    PRIMARY KEY (`TECID`),
    CONSTRAINT `fk_service_plan_time_slot_service_plan_version1`
    FOREIGN KEY (`SERVICE_PLAN_VERSION_TECID`)
    REFERENCES `cgdis_portal_db`.`SERVICE_PLAN_VERSION` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
    ENGINE = InnoDB
    DEFAULT CHARACTER SET = utf8;

CREATE INDEX `fk_service_plan_time_slot_service_plan_version1_idx` ON `cgdis_portal_db`.`SERVICE_PLAN_TIME_SLOT` (`SERVICE_PLAN_VERSION_TECID` ASC);


-- -----------------------------------------------------
-- Table `cgdis_portal_db`.`PRESTATION`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis_portal_db`.`PRESTATION` ;

CREATE TABLE IF NOT EXISTS `cgdis_portal_db`.`PRESTATION` (
                                                    `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
    `TECLOCK` BIGINT(20) NOT NULL,
    `START_DATETIME` DATETIME NOT NULL,
    `END_DATETIME` DATETIME NOT NULL,
    `PERSON_TECID` BIGINT(20) NOT NULL,
    `SERVICE_PLAN_TIME_SLOT_TECID` BIGINT(20) NOT NULL,
    `SERVICE_PLAN_POSITION_TECID` BIGINT(20) NOT NULL,
    `AVAILABLE` TINYINT(1) NOT NULL DEFAULT 0,
    `IS_PROFESSIONAL` TINYINT(1) NOT NULL DEFAULT 0,
    `BARRACKED` TINYINT(1) NOT NULL DEFAULT 0,
    `UNAFFECTED` TINYINT(1) NULL DEFAULT 0,
    PRIMARY KEY (`TECID`),
    CONSTRAINT `FK_PRESTATION_PERSON`
    FOREIGN KEY (`PERSON_TECID`)
    REFERENCES `cgdis_portal_db`.`PERSON` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
    CONSTRAINT `FK_PRESTATION_SERVICE_PLAN_POSITION`
    FOREIGN KEY (`SERVICE_PLAN_POSITION_TECID`)
    REFERENCES `cgdis_portal_db`.`SERVICE_PLAN_POSITION` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
    CONSTRAINT `FK_PRESTATION_SERVICE_PLAN_TIME_SLOT`
    FOREIGN KEY (`SERVICE_PLAN_TIME_SLOT_TECID`)
    REFERENCES `cgdis_portal_db`.`SERVICE_PLAN_TIME_SLOT` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
    ENGINE = InnoDB
    DEFAULT CHARACTER SET = utf8;

CREATE INDEX `I_FK_PRESTATION_PERSON` ON `cgdis_portal_db`.`PRESTATION` (`PERSON_TECID` ASC);

CREATE INDEX `I_FK_PRESTATION_SERVICE_PLAN_TIME_SLOT` ON `cgdis_portal_db`.`PRESTATION` (`SERVICE_PLAN_TIME_SLOT_TECID` ASC);

CREATE INDEX `I_FK_PRESTATION_SERVICE_PLAN_POSITION` ON `cgdis_portal_db`.`PRESTATION` (`SERVICE_PLAN_POSITION_TECID` ASC);

CREATE INDEX `I_PRESTATION_SLOT_PERSON` ON `cgdis_portal_db`.`PRESTATION` (`SERVICE_PLAN_TIME_SLOT_TECID` ASC, `PERSON_TECID` ASC);

CREATE INDEX `I_PRESTATION_SLOT_POSITION` ON `cgdis_portal_db`.`PRESTATION` (`SERVICE_PLAN_TIME_SLOT_TECID` ASC, `SERVICE_PLAN_POSITION_TECID` ASC);


-- -----------------------------------------------------
-- Table `cgdis_portal_db`.`PUSHABLE_DATA_STATE`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis_portal_db`.`PUSHABLE_DATA_STATE` ;

CREATE TABLE IF NOT EXISTS `cgdis_portal_db`.`PUSHABLE_DATA_STATE` (
                                                             `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
    `TECLOCK` BIGINT(20) NOT NULL,
    `OBJECT_TYPE` VARCHAR(50) NOT NULL,
    `OBJECT_TECID` BIGINT(20) NOT NULL,
    `PUSH_TYPE` VARCHAR(50) NULL DEFAULT NULL,
    `OBJECT_VERSION` BIGINT(20) NOT NULL,
    `OBJECT_LAST_PUSHED_VERSION` BIGINT(20) NULL DEFAULT NULL,
    PRIMARY KEY (`TECID`))
    ENGINE = InnoDB
    DEFAULT CHARACTER SET = utf8;


-- -----------------------------------------------------
-- Table `cgdis_portal_db`.`SERVICE_PLAN_POSITION_FUNCTIONS`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis_portal_db`.`SERVICE_PLAN_POSITION_FUNCTIONS` ;

CREATE TABLE IF NOT EXISTS `cgdis_portal_db`.`SERVICE_PLAN_POSITION_FUNCTIONS` (
                                                                         `SERVICE_PLAN_POSITION_TECID` BIGINT(20) NOT NULL,
    `FUNCTION_TECID` BIGINT(20) NOT NULL,
    PRIMARY KEY (`SERVICE_PLAN_POSITION_TECID`, `FUNCTION_TECID`),
    CONSTRAINT `FK_SERVICE_PLAN_POSITION_FCTS_OP_FCT`
    FOREIGN KEY (`FUNCTION_TECID`)
    REFERENCES `cgdis_portal_db`.`OPERATIONAL_FUNCTION` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
    CONSTRAINT `FK_SERVICE_PLAN_POSITION_FCTS_SP_POS`
    FOREIGN KEY (`SERVICE_PLAN_POSITION_TECID`)
    REFERENCES `cgdis_portal_db`.`SERVICE_PLAN_POSITION` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
    ENGINE = InnoDB
    DEFAULT CHARACTER SET = utf8;

CREATE INDEX `I_FK_SERVICE_PLAN_POSITION_FCTS_OP_FCT` ON `cgdis_portal_db`.`SERVICE_PLAN_POSITION_FUNCTIONS` (`FUNCTION_TECID` ASC);

CREATE INDEX `I_FK_SERVICE_PLAN_POSITION_FCTS_SP_POS` ON `cgdis_portal_db`.`SERVICE_PLAN_POSITION_FUNCTIONS` (`SERVICE_PLAN_POSITION_TECID` ASC);


-- -----------------------------------------------------
-- Table `cgdis_portal_db`.`TEAM`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis_portal_db`.`TEAM` ;

CREATE TABLE IF NOT EXISTS `cgdis_portal_db`.`TEAM` (
                                              `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
    `TECLOCK` BIGINT(20) NOT NULL DEFAULT 0,
    `LABEL` VARCHAR(100) NOT NULL,
    `SHORT_LABEL` VARCHAR(3) NOT NULL,
    `SERVICE_PLAN_TECID` BIGINT(20) NOT NULL,
    PRIMARY KEY (`TECID`),
    CONSTRAINT `fk_team_service_plan1`
    FOREIGN KEY (`SERVICE_PLAN_TECID`)
    REFERENCES `cgdis_portal_db`.`SERVICE_PLAN` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
    ENGINE = InnoDB
    DEFAULT CHARACTER SET = utf8;

CREATE INDEX `FK_TEAM_SERVICE_PLAN_TECID` ON `cgdis_portal_db`.`TEAM` (`SERVICE_PLAN_TECID` ASC);


-- -----------------------------------------------------
-- Table `cgdis_portal_db`.`AVAILABILITY`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis_portal_db`.`AVAILABILITY` ;

CREATE TABLE IF NOT EXISTS `cgdis_portal_db`.`AVAILABILITY` (
                                                      `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
    `TECLOCK` BIGINT(20) NOT NULL,
    `START_DATETIME` DATETIME NOT NULL,
    `END_DATETIME` DATETIME NULL DEFAULT NULL,
    `PERSON_TECID` BIGINT(20) NOT NULL,
    `TYPE` VARCHAR(45) NULL,
    PRIMARY KEY (`TECID`),
    CONSTRAINT `FK_VOLUNTEER_AVAILABILITY_PERSON`
    FOREIGN KEY (`PERSON_TECID`)
    REFERENCES `cgdis_portal_db`.`PERSON` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
    ENGINE = InnoDB
    DEFAULT CHARACTER SET = utf8;

CREATE INDEX `I_FK_VOLUNTEER_AVAILABILITY_PERSON` ON `cgdis_portal_db`.`AVAILABILITY` (`PERSON_TECID` ASC);


-- -----------------------------------------------------
-- Table `cgdis_portal_db`.`AVAILABILITY_ENTITY`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis_portal_db`.`AVAILABILITY_ENTITY` ;

CREATE TABLE IF NOT EXISTS `cgdis_portal_db`.`AVAILABILITY_ENTITY` (
                                                             `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
    `TECLOCK` BIGINT(20) NOT NULL,
    `ENTITY_TECID` BIGINT(20) NOT NULL,
    `AVAILABILITY_TECID` BIGINT(20) NOT NULL,
    `ACCEPT_BARRACKED` CHAR(1) NULL,
    PRIMARY KEY (`TECID`),
    CONSTRAINT `FK_VOLUNTEER_AVAILABILITY_ENTITY_ENTITY`
    FOREIGN KEY (`ENTITY_TECID`)
    REFERENCES `cgdis_portal_db`.`ENTITY` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
    CONSTRAINT `FK_VOLUNTEER_AVAILABILITY_ENTITY_VOLUNTEER_AVAILABILITY`
    FOREIGN KEY (`AVAILABILITY_TECID`)
    REFERENCES `cgdis_portal_db`.`AVAILABILITY` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
    ENGINE = InnoDB
    DEFAULT CHARACTER SET = utf8;

CREATE INDEX `I_FK_VOLUNTEER_AVAILABILITY_ENTITY_VOLUNTEER_AVAILABILITY` ON `cgdis_portal_db`.`AVAILABILITY_ENTITY` (`AVAILABILITY_TECID` ASC);

CREATE INDEX `I_FK_VOLUNTEER_AVAILABILITY_ENTITY_ENTITY` ON `cgdis_portal_db`.`AVAILABILITY_ENTITY` (`ENTITY_TECID` ASC);


-- -----------------------------------------------------
-- Table `cgdis_portal_db`.`AVAILABILITY_INTERVENTION_TYPES`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis_portal_db`.`AVAILABILITY_INTERVENTION_TYPES` ;

CREATE TABLE IF NOT EXISTS `cgdis_portal_db`.`AVAILABILITY_INTERVENTION_TYPES` (
                                                                         `AVAILABILITY_TECID` BIGINT(20) NOT NULL,
    `INTERVENTION_TYPE_TECID` BIGINT(20) NOT NULL,
    PRIMARY KEY (`AVAILABILITY_TECID`, `INTERVENTION_TYPE_TECID`),
    CONSTRAINT `FK_VOLUNTEER_AVAILABILITY_INT_TYPES_INT_TYPE`
    FOREIGN KEY (`INTERVENTION_TYPE_TECID`)
    REFERENCES `cgdis_portal_db`.`INTERVENTION_TYPE` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
    CONSTRAINT `FK_VOLUNTEER_AVAILABILITY_INT_TYPES_VOLUNTEER_AV`
    FOREIGN KEY (`AVAILABILITY_TECID`)
    REFERENCES `cgdis_portal_db`.`AVAILABILITY` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
    ENGINE = InnoDB
    DEFAULT CHARACTER SET = utf8;

CREATE INDEX `I_FK_VOLUNTEER_AVAILABILITY_INT_TYPES_INT_TYPE` ON `cgdis_portal_db`.`AVAILABILITY_INTERVENTION_TYPES` (`INTERVENTION_TYPE_TECID` ASC);

CREATE INDEX `I_FK_VOLUNTEER_AVAILABILITY_INT_TYPES_VOLUNTEER_AV` ON `cgdis_portal_db`.`AVAILABILITY_INTERVENTION_TYPES` (`AVAILABILITY_TECID` ASC);


-- -----------------------------------------------------
-- Table `cgdis_portal_db`.`SERVICE_PLAN_COMPLETION_STATUS`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis_portal_db`.`SERVICE_PLAN_COMPLETION_STATUS` ;

CREATE TABLE IF NOT EXISTS `cgdis_portal_db`.`SERVICE_PLAN_COMPLETION_STATUS` (
                                                                        `TECID` BIGINT(20) UNIQUE NOT NULL AUTO_INCREMENT,
    `TECLOCK` BIGINT(20) NOT NULL,
    `STATUS_DATE` DATE NOT NULL,
    `SERVICE_PLAN_TIME_SLOT_TECID` BIGINT(20) NOT NULL,
    `STATUS` VARCHAR(45) NOT NULL,
    `SERVICE_PLAN_MODEL_VERSION_TECID` BIGINT(20) NOT NULL,
    PRIMARY KEY (`TECID`),
    CONSTRAINT `FK_SP_COMPLSTATUS_SP_TIMESLOT`
    FOREIGN KEY (`SERVICE_PLAN_TIME_SLOT_TECID`)
    REFERENCES `cgdis_portal_db`.`SERVICE_PLAN_TIME_SLOT` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
    CONSTRAINT `fk_SERVICE_PLAN_COMPLETION_STATUS_service_plan_model_version1`
    FOREIGN KEY (`SERVICE_PLAN_MODEL_VERSION_TECID`)
    REFERENCES `cgdis_portal_db`.`SERVICE_PLAN_MODEL_VERSION` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
    ENGINE = InnoDB;

CREATE INDEX `I_FK_SP_COMPLSTATUS_SP_TIMESLOT` ON `cgdis_portal_db`.`SERVICE_PLAN_COMPLETION_STATUS` (`SERVICE_PLAN_TIME_SLOT_TECID` ASC);

CREATE INDEX `I_SP_COMPLSTATUS_SPTS_DATE` ON `cgdis_portal_db`.`SERVICE_PLAN_COMPLETION_STATUS` (`STATUS_DATE` ASC, `SERVICE_PLAN_TIME_SLOT_TECID` ASC);

CREATE INDEX `fk_SERVICE_PLAN_COMPLETION_STATUS_service_plan_model_versio_idx` ON `cgdis_portal_db`.`SERVICE_PLAN_COMPLETION_STATUS` (`SERVICE_PLAN_MODEL_VERSION_TECID` ASC);

CREATE UNIQUE INDEX `U_SP_TIME_SLOT_SLOT_TECID_DATE` ON `cgdis_portal_db`.`SERVICE_PLAN_COMPLETION_STATUS` (`SERVICE_PLAN_TIME_SLOT_TECID` ASC, `STATUS_DATE` ASC);


-- -----------------------------------------------------
-- Table `cgdis_portal_db`.`SERVICE_PLAN_LAST_PUSH`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis_portal_db`.`SERVICE_PLAN_LAST_PUSH` ;

CREATE TABLE IF NOT EXISTS `cgdis_portal_db`.`SERVICE_PLAN_LAST_PUSH` (
                                                                `TECID` BIGINT(20) NOT NULL,
    `TECLOCK` BIGINT(20) NOT NULL,
    `LAST_PUSH_DATETIME` DATETIME NULL,
    PRIMARY KEY (`TECID`),
    CONSTRAINT `FK_SP_LAST_PUSH_SP`
    FOREIGN KEY (`TECID`)
    REFERENCES `cgdis_portal_db`.`SERVICE_PLAN` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
    ENGINE = InnoDB;


-- -----------------------------------------------------
-- Table `cgdis_portal_db`.`GENERAL_CONTACT_INFORMATION`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis_portal_db`.`GENERAL_CONTACT_INFORMATION` ;

CREATE TABLE IF NOT EXISTS `cgdis_portal_db`.`GENERAL_CONTACT_INFORMATION` (
                                                                     `PERSON_TECID` BIGINT(20) NOT NULL,
    `TECLOCK` BIGINT(20) NOT NULL,
    `PRIVATE_PHONE_NUMBER` VARCHAR(20) NULL,
    `PROFESSIONAL_PHONE_NUMBER` VARCHAR(20) NULL,
    `PRIVATE_MOBILE_PHONE` VARCHAR(20) NULL,
    `PROFESSIONAL_MOBILE_PHONE` VARCHAR(20) NULL,
    `PRIVATE_EMAIL` VARCHAR(150) NULL,
    `PROFESSIONAL_EMAIL` VARCHAR(150) NULL,
    `RIC` VARCHAR(7) NULL,
    `SEND_PRIVATE_MOBILE` TINYINT NULL DEFAULT 1,
    `SEND_PRIVATE_EMAIL` TINYINT NULL DEFAULT 1,
    `SEND_PROFESSIONAL_MOBILE` TINYINT NULL DEFAULT 0,
    `SEND_PROFESSIONAL_EMAIL` TINYINT NULL DEFAULT 0,
    `SEND_PRIVATE_EMAIL_NOTIFICATION` TINYINT NULL DEFAULT 1,
    `SEND_PROFESSIONAL_EMAIL_NOTIFICATION` TINYINT NULL DEFAULT 0,
    `RIC_MOBILE` VARCHAR(16) NULL,
    PRIMARY KEY (`PERSON_TECID`),
    CONSTRAINT `FK_GENERAL_CONTACT_PERSON`
    FOREIGN KEY (`PERSON_TECID`)
    REFERENCES `cgdis_portal_db`.`PERSON` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
    ENGINE = InnoDB;


-- -----------------------------------------------------
-- Table `cgdis_portal_db`.`OPERATIONAL_CONTACT_INFORMATION`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis_portal_db`.`OPERATIONAL_CONTACT_INFORMATION` ;

CREATE TABLE IF NOT EXISTS `cgdis_portal_db`.`OPERATIONAL_CONTACT_INFORMATION` (
                                                                         `PERSON_TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
    `TECLOCK` BIGINT(20) NOT NULL,
    `PHONE_NUMBER` VARCHAR(20) NULL,
    `MOBILE_NUMBER` VARCHAR(20) NULL,
    `EMAIL` VARCHAR(150) NULL,
    `PAGER_RIC` VARCHAR(7) NULL,
    `ISSI_RENITA` VARCHAR(50) NULL,
    `PAGER_MOBILE` VARCHAR(16) NULL,
    `ISSI_2` VARCHAR(50) NULL,
    `SEND_ALARM_CLOCK` TINYINT(1) NULL DEFAULT 0,
    PRIMARY KEY (`PERSON_TECID`),
    CONSTRAINT `FK_OP_CONTACT_INFO_PERSON`
    FOREIGN KEY (`PERSON_TECID`)
    REFERENCES `cgdis_portal_db`.`PERSON` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
    ENGINE = InnoDB;


-- -----------------------------------------------------
-- Table `cgdis_portal_db`.`SUSPENSION_PERIOD`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis_portal_db`.`SUSPENSION_PERIOD` ;

CREATE TABLE IF NOT EXISTS `cgdis_portal_db`.`SUSPENSION_PERIOD` (
                                                           `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
    `TECLOCK` BIGINT(20) NOT NULL,
    `START_DATE` DATE NOT NULL,
    `END_DATE` DATE NULL,
    `PERSON_TECID` BIGINT(20) NOT NULL,
    PRIMARY KEY (`TECID`, `PERSON_TECID`),
    CONSTRAINT `fk_SUSPENSION_PERIOD_person1`
    FOREIGN KEY (`PERSON_TECID`)
    REFERENCES `cgdis_portal_db`.`PERSON` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
    ENGINE = InnoDB;

CREATE INDEX `fk_SUSPENSION_PERIOD_person1_idx` ON `cgdis_portal_db`.`SUSPENSION_PERIOD` (`PERSON_TECID` ASC);


-- -----------------------------------------------------
-- Table `cgdis_portal_db`.`DRIVING_LICENSE`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis_portal_db`.`DRIVING_LICENSE` ;

CREATE TABLE IF NOT EXISTS `cgdis_portal_db`.`DRIVING_LICENSE` (
                                                         `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
    `TECLOCK` BIGINT(20) NOT NULL,
    `CATEGORY` VARCHAR(50) NOT NULL,
    `START_DATE` DATE NOT NULL,
    `END_DATE` DATE NULL,
    `PERSON_TECID` BIGINT(20) NOT NULL,
    PRIMARY KEY (`TECID`),
    CONSTRAINT `FK_DRIVING_LICENCE_PERSON`
    FOREIGN KEY (`PERSON_TECID`)
    REFERENCES `cgdis_portal_db`.`PERSON` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
    ENGINE = InnoDB;

CREATE INDEX `FK_DRIVING_LICENCE_PERSON_idx` ON `cgdis_portal_db`.`DRIVING_LICENSE` (`PERSON_TECID` ASC);


-- -----------------------------------------------------
-- Table `cgdis_portal_db`.`DIPLOMA`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis_portal_db`.`DIPLOMA` ;

CREATE TABLE IF NOT EXISTS `cgdis_portal_db`.`DIPLOMA` (
                                                 `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
    `TECLOCK` BIGINT(20) NOT NULL,
    `EXTERNAL_TECID` BIGINT(20) NOT NULL,
    `DOMAIN` VARCHAR(200) NOT NULL,
    `TYPE` VARCHAR(200) NOT NULL,
    `NAME` VARCHAR(200) NOT NULL,
    PRIMARY KEY (`TECID`))
    ENGINE = InnoDB;

CREATE UNIQUE INDEX `EXTERNAL_TECID_UNIQUE` ON `cgdis_portal_db`.`DIPLOMA` (`EXTERNAL_TECID` ASC);


-- -----------------------------------------------------
-- Table `cgdis_portal_db`.`PERSON_DIPLOMA`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis_portal_db`.`PERSON_DIPLOMA` ;

CREATE TABLE IF NOT EXISTS `cgdis_portal_db`.`PERSON_DIPLOMA` (
                                                        `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
    `TECLOCK` BIGINT(20) NOT NULL,
    `EXTERNAL_TECID` BIGINT(20) NOT NULL,
    `DIPLOMA_DATE` DATE NOT NULL,
    `START_DATE` DATE NOT NULL,
    `END_DATE` DATE NULL,
    `STATUS` VARCHAR(100) NOT NULL,
    `DIPLOMA_ID` BIGINT(20) NOT NULL,
    `PERSON_TECID` BIGINT(20) NOT NULL,
    `STATUS_DATE` DATE NULL,
    PRIMARY KEY (`TECID`),
    CONSTRAINT `FK_PERSON_DIPLOMA_DIPLOMA_ID`
    FOREIGN KEY (`DIPLOMA_ID`)
    REFERENCES `cgdis_portal_db`.`DIPLOMA` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
    CONSTRAINT `FK_PERSON_DIPLOMA_PERSON_ID`
    FOREIGN KEY (`PERSON_TECID`)
    REFERENCES `cgdis_portal_db`.`PERSON` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
    ENGINE = InnoDB;

CREATE UNIQUE INDEX `EXTERNAL_TECID_UNIQUE` ON `cgdis_portal_db`.`PERSON_DIPLOMA` (`EXTERNAL_TECID` ASC);

CREATE INDEX `FK_PERSON_DIPLOMA_DIPLOMA_ID_idx` ON `cgdis_portal_db`.`PERSON_DIPLOMA` (`DIPLOMA_ID` ASC);

CREATE INDEX `FK_PERSON_DIPLOMA_PERSON_ID_idx` ON `cgdis_portal_db`.`PERSON_DIPLOMA` (`PERSON_TECID` ASC);


-- -----------------------------------------------------
-- Table `cgdis_portal_db`.`SECURITY_ROLE`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis_portal_db`.`SECURITY_ROLE` ;

CREATE TABLE IF NOT EXISTS `cgdis_portal_db`.`SECURITY_ROLE` (
                                                       `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
    `TECLOCK` BIGINT(20) NOT NULL,
    `ROLE` VARCHAR(50) NOT NULL,
    `PERMISSION` VARCHAR(100) NOT NULL,
    `ENTITY_INHERITANCE` CHAR(1) NULL,
    PRIMARY KEY (`TECID`))
    ENGINE = InnoDB
    DEFAULT CHARACTER SET = utf8;

CREATE UNIQUE INDEX `SECURITY_ROLE_ROLE_IDX` ON `cgdis_portal_db`.`SECURITY_ROLE` (`ROLE` ASC, `PERMISSION` ASC);


-- -----------------------------------------------------
-- Table `cgdis_portal_db`.`OPTIONAL_BACKUP_GROUP`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis_portal_db`.`OPTIONAL_BACKUP_GROUP` ;

CREATE TABLE IF NOT EXISTS `cgdis_portal_db`.`OPTIONAL_BACKUP_GROUP` (
                                                               `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
    `TECLOCK` BIGINT(20) NOT NULL,
    `NAME` VARCHAR(255) NOT NULL,
    `FOREIGN_KEY` VARCHAR(100) NOT NULL,
    `ENTITY_GLOBAL_ID` VARCHAR(100) NOT NULL,
    `CREATED_BY` VARCHAR(10) NOT NULL DEFAULT '-',
    `CREATED_ON` DATETIME NULL,
    `UPDATED_BY` VARCHAR(10) NOT NULL DEFAULT '-',
    `UPDATED_ON` DATETIME NULL,
    `AVAILABLE` SMALLINT NOT NULL DEFAULT 1,
    PRIMARY KEY (`TECID`))
    ENGINE = InnoDB
    DEFAULT CHARACTER SET = utf8;

CREATE UNIQUE INDEX `SECURITY_ROLE_ROLE_IDX` ON `cgdis_portal_db`.`OPTIONAL_BACKUP_GROUP` (`NAME` ASC, `FOREIGN_KEY` ASC);

CREATE UNIQUE INDEX `FOREIGN_KEY_UNIQUE` ON `cgdis_portal_db`.`OPTIONAL_BACKUP_GROUP` (`FOREIGN_KEY` ASC);

CREATE INDEX `IX_OPTIONAL_BACKUP_GROUP_ENTITY_ID` ON `cgdis_portal_db`.`OPTIONAL_BACKUP_GROUP` (`ENTITY_GLOBAL_ID` ASC);


-- -----------------------------------------------------
-- Table `cgdis_portal_db`.`PERSON_PREFERENCE`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis_portal_db`.`PERSON_PREFERENCE` ;

CREATE TABLE IF NOT EXISTS `cgdis_portal_db`.`PERSON_PREFERENCE` (
                                                           `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
    `TECLOCK` BIGINT(20) NULL,
    `PREFERENCE_KEY` VARCHAR(100) NOT NULL,
    `PREFERENCE_VALUE` VARCHAR(200) NOT NULL,
    `PERSON_TECID` BIGINT(20) NOT NULL,
    PRIMARY KEY (`TECID`, `TECLOCK`),
    CONSTRAINT `FK_PERSON`
    FOREIGN KEY (`PERSON_TECID`)
    REFERENCES `cgdis_portal_db`.`PERSON` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
    ENGINE = InnoDB
    DEFAULT CHARACTER SET = utf8;

CREATE UNIQUE INDEX `SECURITY_ROLE_ROLE_IDX` ON `cgdis_portal_db`.`PERSON_PREFERENCE` (`PREFERENCE_KEY` ASC, `PERSON_TECID` ASC);

CREATE INDEX `FK_PERSON_idx` ON `cgdis_portal_db`.`PERSON_PREFERENCE` (`PERSON_TECID` ASC);


-- -----------------------------------------------------
-- Table `cgdis_portal_db`.`ERROR_MESSAGE`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis_portal_db`.`ERROR_MESSAGE` ;

CREATE TABLE IF NOT EXISTS `cgdis_portal_db`.`ERROR_MESSAGE` (
                                                       `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
    `TECLOCK` BIGINT(20) NOT NULL,
    `TYPE` VARCHAR(50) NOT NULL,
    `STATUS` VARCHAR(20) NOT NULL,
    `CREATION_DATE` DATETIME NOT NULL,
    `UPDATE_DATE` DATETIME NOT NULL,
    `MESSAGE` BLOB NOT NULL,
    `CAUSE` BLOB NULL,
    `OBJECT_IDENTIFIER` VARCHAR(100) NULL,
    `RUNNABLE` CHAR(1) NULL DEFAULT '1',
    PRIMARY KEY (`TECID`))
    ENGINE = InnoDB
    DEFAULT CHARACTER SET = utf8;

CREATE INDEX `ERROR_MESSAGE_TYPE_IDX` ON `cgdis_portal_db`.`ERROR_MESSAGE` (`TYPE` ASC);

CREATE INDEX `ERROR_MESSAGE_IDX` ON `cgdis_portal_db`.`ERROR_MESSAGE` (`STATUS` ASC, `TYPE` ASC);


-- -----------------------------------------------------
-- Table `cgdis_portal_db`.`NOTIFICATION_MAIL`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis_portal_db`.`NOTIFICATION_MAIL` ;

CREATE TABLE IF NOT EXISTS `cgdis_portal_db`.`NOTIFICATION_MAIL` (
                                                           `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
    `TECLOCK` BIGINT(20) NOT NULL,
    `TYPE` VARCHAR(50) NOT NULL,
    `CREATION_DATE` DATETIME NOT NULL,
    `UPDATE_DATE` DATETIME NOT NULL,
    `SUBJECT` VARCHAR(500) NOT NULL,
    `BODY` BLOB NOT NULL,
    `HTML` CHAR(1) NOT NULL DEFAULT '0',
    `LANGUAGE` VARCHAR(2) NOT NULL,
    PRIMARY KEY (`TECID`))
    ENGINE = InnoDB
    DEFAULT CHARACTER SET = utf8;

CREATE UNIQUE INDEX `NOTIFICATION_MAIL_TYPE_LANGUAGE_IDX` ON `cgdis_portal_db`.`NOTIFICATION_MAIL` (`TYPE` ASC, `LANGUAGE` ASC);


-- -----------------------------------------------------
-- Table `cgdis_portal_db`.`APTITUDE`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis_portal_db`.`APTITUDE` ;

CREATE TABLE IF NOT EXISTS `cgdis_portal_db`.`APTITUDE` (
                                                  `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
    `TECLOCK` BIGINT(20) NOT NULL,
    `EXTERNAL_ID` VARCHAR(20) NOT NULL,
    `NAME` VARCHAR(20) NOT NULL,
    PRIMARY KEY (`TECID`))
    ENGINE = InnoDB
    DEFAULT CHARACTER SET = utf8;

CREATE UNIQUE INDEX `LABEL_UNIQUE` ON `cgdis_portal_db`.`APTITUDE` (`EXTERNAL_ID` ASC);


-- -----------------------------------------------------
-- Table `cgdis_portal_db`.`RESTRICTION`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis_portal_db`.`RESTRICTION` ;

CREATE TABLE IF NOT EXISTS `cgdis_portal_db`.`RESTRICTION` (
                                                     `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
    `TECLOCK` BIGINT(20) NOT NULL,
    `EXTERNAL_ID` VARCHAR(20) NOT NULL,
    `DESCRIPTION` VARCHAR(100) NULL,
    PRIMARY KEY (`TECID`))
    ENGINE = InnoDB
    DEFAULT CHARACTER SET = utf8;

CREATE UNIQUE INDEX `LABEL_UNIQUE` ON `cgdis_portal_db`.`RESTRICTION` (`EXTERNAL_ID` ASC);


-- -----------------------------------------------------
-- Table `cgdis_portal_db`.`APTITUDE_STATUT`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis_portal_db`.`APTITUDE_STATUT` ;

CREATE TABLE IF NOT EXISTS `cgdis_portal_db`.`APTITUDE_STATUT` (
                                                         `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
    `TECLOCK` BIGINT(20) NOT NULL,
    `EXTERNAL_ID` VARCHAR(20) NOT NULL,
    `NAME` VARCHAR(100) NOT NULL,
    PRIMARY KEY (`TECID`))
    ENGINE = InnoDB
    DEFAULT CHARACTER SET = utf8;

CREATE UNIQUE INDEX `LABEL_UNIQUE` ON `cgdis_portal_db`.`APTITUDE_STATUT` (`EXTERNAL_ID` ASC);


-- -----------------------------------------------------
-- Table `cgdis_portal_db`.`PERSON_APTITUDE`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis_portal_db`.`PERSON_APTITUDE` ;

CREATE TABLE IF NOT EXISTS `cgdis_portal_db`.`PERSON_APTITUDE` (
                                                         `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
    `TECLOCK` BIGINT(20) NOT NULL,
    `APTITUDE_TECID` BIGINT(20) NOT NULL,
    `EXTERNAL_ID` BIGINT(20) NOT NULL,
    `START_DATE` DATE NOT NULL,
    `END_DATE` DATE NULL,
    `USER_COMMENT` BLOB NULL,
    `APTITUDE_STATUT_TECID` BIGINT(20) NOT NULL,
    `PERSON_TECID` BIGINT(20) NOT NULL,
    PRIMARY KEY (`TECID`),
    CONSTRAINT `FK_PERSON_APTITUDE_APTITUDE`
    FOREIGN KEY (`APTITUDE_TECID`)
    REFERENCES `cgdis_portal_db`.`APTITUDE` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
    CONSTRAINT `FK_PERSON_APTITUDE_STATUT`
    FOREIGN KEY (`APTITUDE_STATUT_TECID`)
    REFERENCES `cgdis_portal_db`.`APTITUDE_STATUT` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
    CONSTRAINT `fk_person_aptitude_person1`
    FOREIGN KEY (`PERSON_TECID`)
    REFERENCES `cgdis_portal_db`.`PERSON` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
    ENGINE = InnoDB
    DEFAULT CHARACTER SET = utf8;

CREATE UNIQUE INDEX `UX_PERSON_APTITUDE_EXTERNAL` ON `cgdis_portal_db`.`PERSON_APTITUDE` (`EXTERNAL_ID` ASC);

CREATE INDEX `UX_PERSON_APTITUDE_DATE` ON `cgdis_portal_db`.`PERSON_APTITUDE` (`START_DATE` ASC);

CREATE INDEX `FK_PERSON_APTITUDE_STATUT_idx` ON `cgdis_portal_db`.`PERSON_APTITUDE` (`APTITUDE_STATUT_TECID` ASC);

CREATE INDEX `fk_person_aptitude_person1_idx` ON `cgdis_portal_db`.`PERSON_APTITUDE` (`PERSON_TECID` ASC);


-- -----------------------------------------------------
-- Table `cgdis_portal_db`.`PERSON_RESTRICTION`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis_portal_db`.`PERSON_RESTRICTION` ;

CREATE TABLE IF NOT EXISTS `cgdis_portal_db`.`PERSON_RESTRICTION` (
                                                            `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
    `TECLOCK` BIGINT(20) NOT NULL,
    `RESTRICTION_TECID` BIGINT(20) NOT NULL,
    `EXTERNAL_ID` BIGINT(20) NOT NULL,
    `START_DATE` DATE NOT NULL,
    `USER_COMMENT` BLOB NULL,
    `PERSON_TECID` BIGINT(20) NOT NULL,
    PRIMARY KEY (`TECID`),
    CONSTRAINT `FK_PERSON_RESTRICTION_RESTRICTION`
    FOREIGN KEY (`RESTRICTION_TECID`)
    REFERENCES `cgdis_portal_db`.`RESTRICTION` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
    CONSTRAINT `fk_person_restriction_person1`
    FOREIGN KEY (`PERSON_TECID`)
    REFERENCES `cgdis_portal_db`.`PERSON` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
    ENGINE = InnoDB
    DEFAULT CHARACTER SET = utf8;

CREATE UNIQUE INDEX `UX_PERSON_RESTRICTION_EXTERNAL` ON `cgdis_portal_db`.`PERSON_RESTRICTION` (`EXTERNAL_ID` ASC);

CREATE INDEX `IX_PERSON_RESTRICTION__TYPE` ON `cgdis_portal_db`.`PERSON_RESTRICTION` (`START_DATE` ASC);

CREATE INDEX `UX_PERSON_RESTRICTION_RESTRICTION_idx` ON `cgdis_portal_db`.`PERSON_RESTRICTION` (`RESTRICTION_TECID` ASC);

CREATE INDEX `fk_person_restriction_person1_idx` ON `cgdis_portal_db`.`PERSON_RESTRICTION` (`PERSON_TECID` ASC);


-- -----------------------------------------------------
-- Table `cgdis_portal_db`.`PERSON_MEDICAL_REMARK`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis_portal_db`.`PERSON_MEDICAL_REMARK` ;

CREATE TABLE IF NOT EXISTS `cgdis_portal_db`.`PERSON_MEDICAL_REMARK` (
                                                               `tecid` BIGINT(20) NOT NULL AUTO_INCREMENT,
    `teclock` BIGINT(20) NOT NULL,
    `REMARK` BLOB NULL,
    `PERSON_TECID` BIGINT(20) NOT NULL,
    PRIMARY KEY (`tecid`),
    CONSTRAINT `fk_person_medical_remark_person1`
    FOREIGN KEY (`PERSON_TECID`)
    REFERENCES `cgdis_portal_db`.`PERSON` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
    ENGINE = InnoDB;

CREATE INDEX `fk_person_medical_remark_person1_idx` ON `cgdis_portal_db`.`PERSON_MEDICAL_REMARK` (`PERSON_TECID` ASC);


-- -----------------------------------------------------
-- Table `cgdis_portal_db`.`TRANSLATION`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis_portal_db`.`TRANSLATION` ;

CREATE TABLE IF NOT EXISTS `cgdis_portal_db`.`TRANSLATION` (
                                                     `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
    `TECLOCK` BIGINT(20) NOT NULL,
    `DESCRIPTION` VARCHAR(255) NULL,
    `LANGUAGE` VARCHAR(255) NULL,
    `TRANSLATION` VARCHAR(255) NULL,
    `TRANSLATION_KEY` VARCHAR(255) NULL,
    PRIMARY KEY (`TECID`))
    ENGINE = InnoDB
    DEFAULT CHARACTER SET = utf8;


-- -----------------------------------------------------
-- Table `cgdis_portal_db`.`CGDIS_CONFIGURATION`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis_portal_db`.`CGDIS_CONFIGURATION` ;

CREATE TABLE IF NOT EXISTS `cgdis_portal_db`.`CGDIS_CONFIGURATION` (
                                                             `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
    `TECLOCK` BIGINT(20) NOT NULL,
    `KEY` VARCHAR(255) NOT NULL,
    `VALUE` VARCHAR(255) NOT NULL,
    `SEND_FRONT` TINYINT NOT NULL DEFAULT 0,
    PRIMARY KEY (`TECID`))
    ENGINE = InnoDB;

CREATE UNIQUE INDEX `KEY_UNIQUE` ON `cgdis_portal_db`.`CGDIS_CONFIGURATION` (`KEY` ASC);


-- -----------------------------------------------------
-- Table `cgdis_portal_db`.`EXPORT_PROFILE`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis_portal_db`.`EXPORT_PROFILE` ;

CREATE TABLE IF NOT EXISTS `cgdis_portal_db`.`EXPORT_PROFILE` (
                                                        `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
    `TECLOCK` BIGINT(20) NOT NULL,
    `NAME` VARCHAR(200) NOT NULL,
    `CRON` VARCHAR(1000) NOT NULL,
    `LAST_EXECUTION` DATETIME NULL,
    `NEXT_EXECUTION` DATETIME NULL,
    `ENTITY_ID` BIGINT(20) NOT NULL,
    `EXPORT_PERSONS` TINYINT(1) NULL DEFAULT 0,
    `EXPORT_VEHICLES` TINYINT(1) NULL DEFAULT 0,
    `EXPORT_SERVICE_PLANS` TINYINT(1) NULL DEFAULT 0,
    `EXPORT_ON_SERVER` TINYINT(1) NULL DEFAULT 0,
    `EXPORT_NUMBER_DAYS` INT NULL,
    `EXPORT_FORMAT` VARCHAR(45) NULL DEFAULT 'CSV',
    PRIMARY KEY (`TECID`),
    CONSTRAINT `FK_EXP_ENTITY`
    FOREIGN KEY (`ENTITY_ID`)
    REFERENCES `cgdis_portal_db`.`ENTITY` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
    ENGINE = InnoDB;

CREATE INDEX `FK_ENTITY_idx` ON `cgdis_portal_db`.`EXPORT_PROFILE` (`ENTITY_ID` ASC);


-- -----------------------------------------------------
-- Table `cgdis_portal_db`.`EXPORT_PROFILE_EMAIL`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis_portal_db`.`EXPORT_PROFILE_EMAIL` ;

CREATE TABLE IF NOT EXISTS `cgdis_portal_db`.`EXPORT_PROFILE_EMAIL` (
                                                              `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
    `TECLOCK` BIGINT(20) NOT NULL,
    `EXPORT_PROFILE_TECID` BIGINT(20) NULL,
    `EMAIL` VARCHAR(200) NOT NULL,
    PRIMARY KEY (`TECID`),
    CONSTRAINT `FK_EXPE_PROFILID`
    FOREIGN KEY (`EXPORT_PROFILE_TECID`)
    REFERENCES `cgdis_portal_db`.`EXPORT_PROFILE` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
    ENGINE = InnoDB;

CREATE INDEX `FK_EXPE_PROFILID_idx` ON `cgdis_portal_db`.`EXPORT_PROFILE_EMAIL` (`EXPORT_PROFILE_TECID` ASC);


-- -----------------------------------------------------
-- Table `cgdis_portal_db`.`SERVICE_PLAN_MODEL_VEHICLE_TYPE`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis_portal_db`.`SERVICE_PLAN_MODEL_VEHICLE_TYPE` ;

CREATE TABLE IF NOT EXISTS `cgdis_portal_db`.`SERVICE_PLAN_MODEL_VEHICLE_TYPE` (
                                                                         `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
    `TECLOCK` BIGINT(20) NOT NULL,
    `SERVICE_PLAN_MODEL_TECID` BIGINT(20) NOT NULL,
    `VEHICLE_TYPE` VARCHAR(100) NOT NULL,
    PRIMARY KEY (`TECID`),
    CONSTRAINT `FK_SPMVT_MODEL`
    FOREIGN KEY (`SERVICE_PLAN_MODEL_TECID`)
    REFERENCES `cgdis_portal_db`.`SERVICE_PLAN_MODEL` (`TECID`)
    ON DELETE CASCADE
    ON UPDATE CASCADE)
    ENGINE = InnoDB;

CREATE INDEX `FK_SPMVT_MODEL_idx` ON `cgdis_portal_db`.`SERVICE_PLAN_MODEL_VEHICLE_TYPE` (`SERVICE_PLAN_MODEL_TECID` ASC);


-- -----------------------------------------------------
-- Table `cgdis_portal_db`.`SERVICE_PLAN_MODEL_BACKUP_STATUS`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis_portal_db`.`SERVICE_PLAN_MODEL_BACKUP_STATUS` ;

CREATE TABLE IF NOT EXISTS `cgdis_portal_db`.`SERVICE_PLAN_MODEL_BACKUP_STATUS` (
                                                                          `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
    `TECLOCK` BIGINT(20) NOT NULL,
    `SERVICE_PLAN_MODEL_TECID` BIGINT(20) NOT NULL,
    `STATUS` VARCHAR(45) NOT NULL,
    PRIMARY KEY (`TECID`),
    CONSTRAINT `FK_SPMBS_MODEL_TECID`
    FOREIGN KEY (`SERVICE_PLAN_MODEL_TECID`)
    REFERENCES `cgdis_portal_db`.`SERVICE_PLAN_MODEL` (`TECID`)
    ON DELETE CASCADE
    ON UPDATE CASCADE)
    ENGINE = InnoDB;

CREATE INDEX `FK_SPMBS_MODEL_TECID_idx` ON `cgdis_portal_db`.`SERVICE_PLAN_MODEL_BACKUP_STATUS` (`SERVICE_PLAN_MODEL_TECID` ASC);


-- -----------------------------------------------------
-- Table `cgdis_portal_db`.`INTERVENTION`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis_portal_db`.`INTERVENTION` ;

CREATE TABLE IF NOT EXISTS `cgdis_portal_db`.`INTERVENTION` (
                                                      `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
    `TECLOCK` BIGINT(20) NOT NULL,
    `INTERVENTION_NUMBER` VARCHAR(12) NULL,
    `INTERVENTION_KEYWORD` VARCHAR(255) NULL,
    `ALARM_DATETIME` DATETIME NULL,
    `START_DATETIME` DATETIME NULL,
    `END_DATETIME` DATETIME NULL,
    PRIMARY KEY (`TECID`))
    ENGINE = InnoDB;


-- -----------------------------------------------------
-- Table `cgdis_portal_db`.`INTERVENTION_VEHICLE`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis_portal_db`.`INTERVENTION_VEHICLE` ;

CREATE TABLE IF NOT EXISTS `cgdis_portal_db`.`INTERVENTION_VEHICLE` (
                                                              `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
    `TECLOCK` BIGINT(20) NOT NULL,
    `NAME` VARCHAR(255) NULL,
    `TYPE` VARCHAR(255) NULL,
    `ENTITY_TECID` BIGINT(20) NULL,
    `MISSION_NUMBER` VARCHAR(12) NULL,
    `ALARM_DATETIME` DATETIME NULL,
    `START_DATETIME` DATETIME NULL,
    `END_DATETIME` DATETIME NULL,
    `INTERVENTION_TECID` BIGINT(20) NOT NULL,
    PRIMARY KEY (`TECID`),
    CONSTRAINT `fk_intervention_vehicle_entity`
    FOREIGN KEY (`ENTITY_TECID`)
    REFERENCES `cgdis_portal_db`.`ENTITY` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
    CONSTRAINT `fk_intervention_vehicle_intervention_tecid`
    FOREIGN KEY (`INTERVENTION_TECID`)
    REFERENCES `cgdis_portal_db`.`INTERVENTION` (`TECID`)
    ON DELETE CASCADE
    ON UPDATE CASCADE)
    ENGINE = InnoDB;

CREATE INDEX `fk_intervention_vehicle_entity_idx` ON `cgdis_portal_db`.`INTERVENTION_VEHICLE` (`ENTITY_TECID` ASC);

CREATE INDEX `fk_intervention_vehicle_intervention_tecid_idx` ON `cgdis_portal_db`.`INTERVENTION_VEHICLE` (`INTERVENTION_TECID` ASC);


-- -----------------------------------------------------
-- Table `cgdis_portal_db`.`INTERVENTION_CREW_MEMBER`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis_portal_db`.`INTERVENTION_CREW_MEMBER` ;

CREATE TABLE IF NOT EXISTS `cgdis_portal_db`.`INTERVENTION_CREW_MEMBER` (
                                                                  `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
    `TECLOCK` BIGINT(20) NOT NULL,
    `PERSON_TECID` BIGINT(20) NOT NULL,
    `INTERVENTION_VEHICLE_TECID` BIGINT(20) NOT NULL,
    `VOLUNTEER_REGISTRATION_NUMBER` VARCHAR(5) NULL,
    `PROFESSIONAL_REGISTRATION_NUMBER` VARCHAR(5) NULL,
    `FUNCTION_NUMBER` INT NULL,
    `FUNCTION_NAME` VARCHAR(50) NULL,
    PRIMARY KEY (`TECID`),
    CONSTRAINT `fk_intervention_crew_member_intervention_vehicle`
    FOREIGN KEY (`INTERVENTION_VEHICLE_TECID`)
    REFERENCES `cgdis_portal_db`.`INTERVENTION_VEHICLE` (`TECID`)
    ON DELETE CASCADE
    ON UPDATE CASCADE,
    CONSTRAINT `fk_intervention_crew_member_person`
    FOREIGN KEY (`PERSON_TECID`)
    REFERENCES `cgdis_portal_db`.`PERSON` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
    ENGINE = InnoDB;

CREATE INDEX `fk_intervention_crew_member_intervention_vehicle_idx` ON `cgdis_portal_db`.`INTERVENTION_CREW_MEMBER` (`INTERVENTION_VEHICLE_TECID` ASC);

CREATE INDEX `fk_intervention_crew_member_person_idx` ON `cgdis_portal_db`.`INTERVENTION_CREW_MEMBER` (`PERSON_TECID` ASC);


-- -----------------------------------------------------
-- Table `cgdis_portal_db`.`BANK_ACCOUNT`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis_portal_db`.`BANK_ACCOUNT` ;

CREATE TABLE IF NOT EXISTS `cgdis_portal_db`.`BANK_ACCOUNT` (
                                                      `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
    `TECLOCK` BIGINT(20) NOT NULL,
    `IBAN` VARCHAR(30) NOT NULL,
    `BIC` VARCHAR(11) NOT NULL,
    `PERSON_TECID` BIGINT(20) NOT NULL,
    PRIMARY KEY (`TECID`),
    CONSTRAINT `fk_bank_account_person`
    FOREIGN KEY (`PERSON_TECID`)
    REFERENCES `cgdis_portal_db`.`PERSON` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
    ENGINE = InnoDB;

CREATE INDEX `fk_bank_account_person_idx` ON `cgdis_portal_db`.`BANK_ACCOUNT` (`PERSON_TECID` ASC);

CREATE UNIQUE INDEX `TECID_UNIQUE` ON `cgdis_portal_db`.`BANK_ACCOUNT` (`TECID` ASC);


-- -----------------------------------------------------
-- Table `cgdis_portal_db`.`OPERATIONAL_GRADE`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis_portal_db`.`OPERATIONAL_GRADE` ;

CREATE TABLE IF NOT EXISTS `cgdis_portal_db`.`OPERATIONAL_GRADE` (
                                                           `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
    `TECLOCK` BIGINT(20) NOT NULL,
    `CODE` VARCHAR(20) NOT NULL,
    `TITLE` VARCHAR(10) NOT NULL,
    `GRADE_TYPE` VARCHAR(45) NOT NULL,
    PRIMARY KEY (`TECID`))
    ENGINE = InnoDB;

CREATE UNIQUE INDEX `TECID_UNIQUE` ON `cgdis_portal_db`.`OPERATIONAL_GRADE` (`TECID` ASC);


-- -----------------------------------------------------
-- Table `cgdis_portal_db`.`PERSON_OPERATIONAL_GRADES`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis_portal_db`.`PERSON_OPERATIONAL_GRADES` ;

CREATE TABLE IF NOT EXISTS `cgdis_portal_db`.`PERSON_OPERATIONAL_GRADES` (
                                                                   `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
    `TECLOCK` BIGINT(20) NOT NULL,
    `START_DATE` DATE NOT NULL,
    `END_DATE` DATE NULL,
    `IS_VOLUNTEER` CHAR(1) NOT NULL,
    `IS_PROFESSIONNAL` CHAR(1) NOT NULL,
    `IS_SAMU` CHAR(1) NOT NULL,
    `ECHELON` VARCHAR(10) NULL,
    `PERSON_GRADE_EXTERNAL_TECID` BIGINT(20) NOT NULL,
    `DECREE_DATE` DATE NULL,
    `PERSON_TECID` BIGINT(20) NOT NULL,
    `OPERATIONAL_GRADE_TECID` BIGINT(20) NOT NULL,
    PRIMARY KEY (`TECID`),
    CONSTRAINT `fk_person_operational_grades_person1`
    FOREIGN KEY (`PERSON_TECID`)
    REFERENCES `cgdis_portal_db`.`PERSON` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
    CONSTRAINT `fk_person_operational_grades_operational_grade1`
    FOREIGN KEY (`OPERATIONAL_GRADE_TECID`)
    REFERENCES `cgdis_portal_db`.`OPERATIONAL_GRADE` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
    ENGINE = InnoDB
    COMMENT = '	';

CREATE UNIQUE INDEX `TECID_UNIQUE` ON `cgdis_portal_db`.`PERSON_OPERATIONAL_GRADES` (`TECID` ASC);

CREATE INDEX `fk_person_operational_grades_person1_idx` ON `cgdis_portal_db`.`PERSON_OPERATIONAL_GRADES` (`PERSON_TECID` ASC);

CREATE INDEX `fk_person_operational_grades_operational_grade1_idx` ON `cgdis_portal_db`.`PERSON_OPERATIONAL_GRADES` (`OPERATIONAL_GRADE_TECID` ASC);


-- -----------------------------------------------------
-- Table `cgdis_portal_db`.`PERSON_OPERATIONAL_DATES`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis_portal_db`.`PERSON_OPERATIONAL_DATES` ;

CREATE TABLE IF NOT EXISTS `cgdis_portal_db`.`PERSON_OPERATIONAL_DATES` (
                                                                  `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
    `TECLOCK` BIGINT(20) NOT NULL,
    `START_DATE` DATE NOT NULL,
    `END_DATE` DATE NOT NULL,
    `PERSON_TECID` BIGINT(20) NOT NULL,
    `STATUS` VARCHAR(45) NOT NULL,
    PRIMARY KEY (`TECID`),
    CONSTRAINT `fk_person_operational_dates_person1`
    FOREIGN KEY (`PERSON_TECID`)
    REFERENCES `cgdis_portal_db`.`PERSON` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
    ENGINE = InnoDB;

CREATE INDEX `fk_person_operational_dates_person1_idx` ON `cgdis_portal_db`.`PERSON_OPERATIONAL_DATES` (`PERSON_TECID` ASC);

CREATE UNIQUE INDEX `TECID_UNIQUE` ON `cgdis_portal_db`.`PERSON_OPERATIONAL_DATES` (`TECID` ASC);


-- -----------------------------------------------------
-- Table `cgdis_portal_db`.`OPERATIONAL_VOLUNTEER_INTERNSHIP`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis_portal_db`.`OPERATIONAL_VOLUNTEER_INTERNSHIP` ;

CREATE TABLE IF NOT EXISTS `cgdis_portal_db`.`OPERATIONAL_VOLUNTEER_INTERNSHIP` (
                                                                          `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
    `TECLOCK` BIGINT(20) NOT NULL,
    `START_DATE` DATE NOT NULL,
    `END_DATE` DATE NULL,
    `DURATION` INT NOT NULL,
    `PERSON_TECID` BIGINT(20) NOT NULL,
    PRIMARY KEY (`TECID`),
    CONSTRAINT `fk_operational_volunteer_internship_person1`
    FOREIGN KEY (`PERSON_TECID`)
    REFERENCES `cgdis_portal_db`.`PERSON` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
    ENGINE = InnoDB;

CREATE INDEX `fk_operational_volunteer_internship_person1_idx` ON `cgdis_portal_db`.`OPERATIONAL_VOLUNTEER_INTERNSHIP` (`PERSON_TECID` ASC);

CREATE UNIQUE INDEX `TECID_UNIQUE` ON `cgdis_portal_db`.`OPERATIONAL_VOLUNTEER_INTERNSHIP` (`TECID` ASC);


-- -----------------------------------------------------
-- Table `cgdis_portal_db`.`OPERATIONAL_YOUNG_FIREFIGHTER`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis_portal_db`.`OPERATIONAL_YOUNG_FIREFIGHTER` ;

CREATE TABLE IF NOT EXISTS `cgdis_portal_db`.`OPERATIONAL_YOUNG_FIREFIGHTER` (
                                                                       `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
    `TECLOCK` BIGINT(20) NOT NULL,
    `PARENTAL_CONSENT` CHAR(1) NULL DEFAULT 'Y',
    `PERSON_TECID` BIGINT(20) NOT NULL,
    PRIMARY KEY (`TECID`),
    CONSTRAINT `fk_operationa_young_firefighter_person1`
    FOREIGN KEY (`PERSON_TECID`)
    REFERENCES `cgdis_portal_db`.`PERSON` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
    ENGINE = InnoDB;

CREATE INDEX `fk_operationa_young_firefighter_person1_idx` ON `cgdis_portal_db`.`OPERATIONAL_YOUNG_FIREFIGHTER` (`PERSON_TECID` ASC);

CREATE UNIQUE INDEX `TECID_UNIQUE` ON `cgdis_portal_db`.`OPERATIONAL_YOUNG_FIREFIGHTER` (`TECID` ASC);


-- -----------------------------------------------------
-- Table `cgdis_portal_db`.`TUTOR`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis_portal_db`.`TUTOR` ;

CREATE TABLE IF NOT EXISTS `cgdis_portal_db`.`TUTOR` (
                                               `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
    `TECLOCK` BIGINT(20) NOT NULL,
    `LASTNAME` VARCHAR(45) NULL,
    `FIRSTNAME` VARCHAR(45) NULL,
    `MOBILE_PHONE` VARCHAR(20) NULL,
    PRIMARY KEY (`TECID`))
    ENGINE = InnoDB;

CREATE UNIQUE INDEX `TECID_UNIQUE` ON `cgdis_portal_db`.`TUTOR` (`TECID` ASC);


-- -----------------------------------------------------
-- Table `cgdis_portal_db`.`OPERATIONAL_YOUNG_FIREFIGHTER_TUTOR`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis_portal_db`.`OPERATIONAL_YOUNG_FIREFIGHTER_TUTOR` ;

CREATE TABLE IF NOT EXISTS `cgdis_portal_db`.`OPERATIONAL_YOUNG_FIREFIGHTER_TUTOR` (
                                                                             `OPERATIONAL_YOUNG_FIREFIGHTER_TECID` BIGINT(20) NOT NULL,
    `TUTOR_TECID` BIGINT(20) NOT NULL,
    PRIMARY KEY (`OPERATIONAL_YOUNG_FIREFIGHTER_TECID`, `TUTOR_TECID`),
    CONSTRAINT `fk_operationa_young_firefighter_has_tutor_operationa_young_fi1`
    FOREIGN KEY (`OPERATIONAL_YOUNG_FIREFIGHTER_TECID`)
    REFERENCES `cgdis_portal_db`.`OPERATIONAL_YOUNG_FIREFIGHTER` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
    CONSTRAINT `fk_operationa_young_firefighter_has_tutor_tutor1`
    FOREIGN KEY (`TUTOR_TECID`)
    REFERENCES `cgdis_portal_db`.`TUTOR` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
    ENGINE = InnoDB;

CREATE INDEX `fk_operationa_young_firefighter_tutor_tutor_idx` ON `cgdis_portal_db`.`OPERATIONAL_YOUNG_FIREFIGHTER_TUTOR` (`TUTOR_TECID` ASC);

CREATE INDEX `fk_operationa_young_firefighter_tutor_operational_young__idx` ON `cgdis_portal_db`.`OPERATIONAL_YOUNG_FIREFIGHTER_TUTOR` (`OPERATIONAL_YOUNG_FIREFIGHTER_TECID` ASC);


-- -----------------------------------------------------
-- Table `cgdis_portal_db`.`MANAGERIAL_OCCUPATION`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis_portal_db`.`MANAGERIAL_OCCUPATION` ;

CREATE TABLE IF NOT EXISTS `cgdis_portal_db`.`MANAGERIAL_OCCUPATION` (
                                                               `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
    `TECLOCK` BIGINT(20) NOT NULL,
    `CODE` VARCHAR(20) NOT NULL,
    `TYPE` VARCHAR(100) NOT NULL,
    `LABEL` VARCHAR(100) NOT NULL,
    PRIMARY KEY (`TECID`))
    ENGINE = InnoDB;

CREATE UNIQUE INDEX `TECID_UNIQUE` ON `cgdis_portal_db`.`MANAGERIAL_OCCUPATION` (`TECID` ASC);


-- -----------------------------------------------------
-- Table `cgdis_portal_db`.`PERSON_MANAGERIAL_OCCUPATIONS`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis_portal_db`.`PERSON_MANAGERIAL_OCCUPATIONS` ;

CREATE TABLE IF NOT EXISTS `cgdis_portal_db`.`PERSON_MANAGERIAL_OCCUPATIONS` (
                                                                       `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
    `TECLOCK` BIGINT(20) NOT NULL,
    `START_DATE` DATE NOT NULL,
    `END_DATE` DATE NULL,
    `IS_PROFESSIONAL` CHAR(1) NOT NULL DEFAULT 'Y',
    `IS_VOLUNTEER` CHAR(1) NOT NULL DEFAULT 'Y',
    `IS_EXTERNAL` CHAR(1) NOT NULL DEFAULT 'Y',
    `STATUS` VARCHAR(20) NOT NULL DEFAULT 'ONGOING',
    `NOMINATION_DATE` DATE NOT NULL,
    `PERSON_OCCUPATION_EXTERNAL_TECID` BIGINT(20) NOT NULL,
    `ASSIGNMENT` VARCHAR(20) NOT NULL,
    `ASSIGNMENT_DESCRIPTION` VARCHAR(75) NOT NULL,
    `person_TECID` BIGINT(20) NOT NULL,
    `managerial_occupation_TECID` BIGINT(20) NOT NULL,
    PRIMARY KEY (`TECID`),
    CONSTRAINT `fk_person_managerial_occupations_person1`
    FOREIGN KEY (`person_TECID`)
    REFERENCES `cgdis_portal_db`.`PERSON` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
    CONSTRAINT `fk_person_managerial_occupations_managerial_occupation1`
    FOREIGN KEY (`managerial_occupation_TECID`)
    REFERENCES `cgdis_portal_db`.`MANAGERIAL_OCCUPATION` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
    ENGINE = InnoDB
    COMMENT = '	';

CREATE UNIQUE INDEX `TECID_UNIQUE` ON `cgdis_portal_db`.`PERSON_MANAGERIAL_OCCUPATIONS` (`TECID` ASC);

CREATE INDEX `fk_person_managerial_occupations_person1_idx` ON `cgdis_portal_db`.`PERSON_MANAGERIAL_OCCUPATIONS` (`person_TECID` ASC);

CREATE INDEX `fk_person_managerial_occupations_managerial_occupation1_idx` ON `cgdis_portal_db`.`PERSON_MANAGERIAL_OCCUPATIONS` (`managerial_occupation_TECID` ASC);


-- -----------------------------------------------------
-- Table `cgdis_portal_db`.`PRESTATION_QUEUE`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis_portal_db`.`PRESTATION_QUEUE` ;

CREATE TABLE IF NOT EXISTS `cgdis_portal_db`.`PRESTATION_QUEUE` (
                                                          `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
    `TECLOCK` BIGINT(20) NOT NULL DEFAULT 0,
    `PRESTATION_TECID` BIGINT(20) NOT NULL,
    `PERSON_TECID` BIGINT(20) NULL,
    `START_DATETIME` DATETIME NULL,
    `END_DATETIME` DATETIME NULL,
    `SERVICE_PLAN_NAME` VARCHAR(255) NULL,
    `POSITION_NAME` VARCHAR(100) NULL,
    `ACTION_TYPE` VARCHAR(45) NULL,
    `CREATION_DATE` DATETIME NULL,
    PRIMARY KEY (`TECID`))
    ENGINE = InnoDB
    DEFAULT CHARACTER SET = utf8;


-- -----------------------------------------------------
-- Table `cgdis_portal_db`.`AUDIT`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis_portal_db`.`AUDIT` ;

CREATE TABLE IF NOT EXISTS `cgdis_portal_db`.`AUDIT` (
                                               `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
    `TECLOCK` BIGINT(20) NOT NULL DEFAULT 0,
    `ACTION_TYPE` VARCHAR(10) NOT NULL,
    `ACTION_PERSON_TECID` BIGINT(20) NOT NULL,
    `ACTION_DATETIME` DATETIME NOT NULL,
    `AUDIT_TYPE` VARCHAR(20) NOT NULL,
    PRIMARY KEY (`TECID`))
    ENGINE = InnoDB;


-- -----------------------------------------------------
-- Table `cgdis_portal_db`.`AUDIT_PRESTATION`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis_portal_db`.`AUDIT_PRESTATION` ;

CREATE TABLE IF NOT EXISTS `cgdis_portal_db`.`AUDIT_PRESTATION` (
                                                          `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
    `PRESTATION_TECID` BIGINT(20) NULL,
    `PRESTATION_PERSON_TECID` BIGINT(20) NULL,
    `POSITION_NAME` VARCHAR(100) NULL,
    `START_DATETIME` DATETIME NULL,
    `END_DATETIME` DATETIME NULL,
    `SERVICE_PLAN_PORTAL_LABEL` VARCHAR(255) NULL,
    `PRESTATION_DATE` DATE NULL,
    PRIMARY KEY (`TECID`),
    CONSTRAINT `FK_AUDIT_PRESTATION_AUDIT`
    FOREIGN KEY (`TECID`)
    REFERENCES `cgdis_portal_db`.`AUDIT` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
    ENGINE = InnoDB;

CREATE INDEX `IX_AUDIT_PRESTATION` ON `cgdis_portal_db`.`AUDIT_PRESTATION` (`SERVICE_PLAN_PORTAL_LABEL` ASC, `PRESTATION_DATE` ASC);


-- -----------------------------------------------------
-- Table `cgdis_portal_db`.`AUDIT_MODEL`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis_portal_db`.`AUDIT_MODEL` ;

CREATE TABLE IF NOT EXISTS `cgdis_portal_db`.`AUDIT_MODEL` (
                                                     `TECID` BIGINT(20) NOT NULL,
    `MODEL_TECID` BIGINT(20) NULL,
    `MODEL_NAME` VARCHAR(45) NULL,
    `MODEL_ENTITY` VARCHAR(45) NULL,
    `MODEL_INTERVENTION_TYPE` VARCHAR(45) NULL,
    PRIMARY KEY (`TECID`),
    CONSTRAINT `fk_AUDIT_MODEL_AUDIT1`
    FOREIGN KEY (`TECID`)
    REFERENCES `cgdis_portal_db`.`AUDIT` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
    ENGINE = InnoDB;


-- -----------------------------------------------------
-- Table `cgdis_portal_db`.`AUDIT_SERVICE_PLAN`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis_portal_db`.`AUDIT_SERVICE_PLAN` ;

CREATE TABLE IF NOT EXISTS `cgdis_portal_db`.`AUDIT_SERVICE_PLAN` (
                                                            `TECID` BIGINT(20) NOT NULL,
    `SERVICE_PLAN_NAME` VARCHAR(45) NULL,
    `SERVICE_PLAN_ENTITY` VARCHAR(45) NULL,
    `SERVICE_PLAN_TYPE` VARCHAR(45) NULL,
    `SERVICE_PLAN_TECID` BIGINT(20) NULL,
    PRIMARY KEY (`TECID`),
    CONSTRAINT `fk_AUDIT_SERVICE_PLAN_AUDIT1`
    FOREIGN KEY (`TECID`)
    REFERENCES `cgdis_portal_db`.`AUDIT` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
    ENGINE = InnoDB;


-- -----------------------------------------------------
-- Table `cgdis_portal_db`.`AUDIT_MODEL_VERSION`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis_portal_db`.`AUDIT_MODEL_VERSION` ;

CREATE TABLE IF NOT EXISTS `cgdis_portal_db`.`AUDIT_MODEL_VERSION` (
                                                             `TECID` BIGINT(20) NOT NULL,
    `MODEL_VERSION_TECID` BIGINT(20) NULL,
    `MODEL_NAME` VARCHAR(45) NULL,
    `VERSION_LABEL` VARCHAR(45) NULL,
    `START_DATE` DATE NULL,
    `END_DATE` DATE NULL,
    PRIMARY KEY (`TECID`),
    CONSTRAINT `fk_AUDIT_MODEL_VERSION_AUDIT1`
    FOREIGN KEY (`TECID`)
    REFERENCES `cgdis_portal_db`.`AUDIT` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
    ENGINE = InnoDB;


-- -----------------------------------------------------
-- Table `cgdis_portal_db`.`AUDIT_PDS_VERSION`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis_portal_db`.`AUDIT_PDS_VERSION` ;

CREATE TABLE IF NOT EXISTS `cgdis_portal_db`.`AUDIT_PDS_VERSION` (
                                                           `TECID` BIGINT(20) NOT NULL,
    `SERVICE_PLAN_VERSION_TECID` BIGINT(20) NULL,
    `SERVICE_PLAN_NAME` VARCHAR(45) NULL,
    `VERSION_LABEL` VARCHAR(45) NULL,
    `START_DATE` DATE NULL,
    `END_DATE` DATE NULL,
    PRIMARY KEY (`TECID`),
    CONSTRAINT `fk_AUDIT_PDS_VERSION_AUDIT1`
    FOREIGN KEY (`TECID`)
    REFERENCES `cgdis_portal_db`.`AUDIT` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
    ENGINE = InnoDB;


-- -----------------------------------------------------
-- Table `cgdis_portal_db`.`WARNING_MESSAGE`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis_portal_db`.`WARNING_MESSAGE` ;

CREATE TABLE IF NOT EXISTS `cgdis_portal_db`.`WARNING_MESSAGE` (
                                                         `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
    `TECLOCK` BIGINT(20) NOT NULL,
    `CREATION_DATE` DATETIME NOT NULL,
    `WARNING_TYPE` VARCHAR(50) NOT NULL,
    PRIMARY KEY (`TECID`))
    ENGINE = InnoDB
    DEFAULT CHARACTER SET = utf8;


-- -----------------------------------------------------
-- Table `cgdis_portal_db`.`WARNING_MESSAGE_VEHICLE`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis_portal_db`.`WARNING_MESSAGE_VEHICLE` ;

CREATE TABLE IF NOT EXISTS `cgdis_portal_db`.`WARNING_MESSAGE_VEHICLE` (
                                                                 `VEHICLE_TECID` BIGINT(20) NOT NULL,
    `WARNING_MESSAGE_TECID` BIGINT(20) NOT NULL,
    PRIMARY KEY (`VEHICLE_TECID`, `WARNING_MESSAGE_TECID`),
    CONSTRAINT `FK_WARNING_MESSAGE_VEHICLE_VEHICLE`
    FOREIGN KEY (`VEHICLE_TECID`)
    REFERENCES `cgdis_portal_db`.`VEHICLE` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
    CONSTRAINT `FK_WARNING_MESSAGE_VEHICLE_WARNING`
    FOREIGN KEY (`WARNING_MESSAGE_TECID`)
    REFERENCES `cgdis_portal_db`.`WARNING_MESSAGE` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
    ENGINE = InnoDB
    DEFAULT CHARACTER SET = utf8;

CREATE INDEX `FK_WARNING_MESSAGE_VEHICLE_WARNING_idx` ON `cgdis_portal_db`.`WARNING_MESSAGE_VEHICLE` (`WARNING_MESSAGE_TECID` ASC);


-- -----------------------------------------------------
-- Table `cgdis_portal_db`.`AUDIT_COPY_PRESTATION`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis_portal_db`.`AUDIT_COPY_PRESTATION` ;

CREATE TABLE IF NOT EXISTS `cgdis_portal_db`.`AUDIT_COPY_PRESTATION` (
                                                               `TECID` BIGINT(20) NOT NULL,
    `FROM_DATETIME` DATETIME NULL,
    `TARGET_DATETIME` DATETIME NULL,
    `SERVICE_PLAN_NAME` VARCHAR(45) NULL,
    PRIMARY KEY (`TECID`),
    CONSTRAINT `fk_table1_AUDIT1`
    FOREIGN KEY (`TECID`)
    REFERENCES `cgdis_portal_db`.`AUDIT` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
    ENGINE = InnoDB;


-- -----------------------------------------------------
-- Table `cgdis_portal_db`.`AUDIT_SERVICE_PLAN_TECID`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis_portal_db`.`AUDIT_SERVICE_PLAN_TECID` ;

CREATE TABLE IF NOT EXISTS `cgdis_portal_db`.`AUDIT_SERVICE_PLAN_TECID` (
                                                                  `SERVICE_PLAN_TECID` BIGINT(20) NOT NULL,
    `AUDIT_TECID` BIGINT(20) NOT NULL,
    PRIMARY KEY (`SERVICE_PLAN_TECID`, `AUDIT_TECID`),
    CONSTRAINT `fk_service_plan_has_AUDIT_service_plan1`
    FOREIGN KEY (`SERVICE_PLAN_TECID`)
    REFERENCES `cgdis_portal_db`.`SERVICE_PLAN` (`TECID`)
    ON DELETE CASCADE
    ON UPDATE CASCADE,
    CONSTRAINT `fk_service_plan_has_AUDIT_AUDIT1`
    FOREIGN KEY (`AUDIT_TECID`)
    REFERENCES `cgdis_portal_db`.`AUDIT` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
    ENGINE = InnoDB;

CREATE INDEX `fk_service_plan_has_AUDIT_AUDIT1_idx` ON `cgdis_portal_db`.`AUDIT_SERVICE_PLAN_TECID` (`AUDIT_TECID` ASC);

CREATE INDEX `fk_service_plan_has_AUDIT_service_plan1_idx` ON `cgdis_portal_db`.`AUDIT_SERVICE_PLAN_TECID` (`SERVICE_PLAN_TECID` ASC);


-- -----------------------------------------------------
-- Table `cgdis_portal_db`.`TEAM_MEMBER`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis_portal_db`.`TEAM_MEMBER` ;

CREATE TABLE IF NOT EXISTS `cgdis_portal_db`.`TEAM_MEMBER` (
                                                     `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
    `TECLOCK` BIGINT(20) NOT NULL DEFAULT 0,
    `TEAM_TECID` BIGINT(20) NULL,
    `SERVICE_PLAN_POSITION_TECID` BIGINT(20) NULL,
    `PERSON_TECID` BIGINT(20) NULL,
    PRIMARY KEY (`TECID`),
    CONSTRAINT `FK_TEAM_MEMBER_TEAM`
    FOREIGN KEY (`TEAM_TECID`)
    REFERENCES `cgdis_portal_db`.`TEAM` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
    CONSTRAINT `FK_TEAM_MEMBER_SERVICE_PLAN_POSITION`
    FOREIGN KEY (`SERVICE_PLAN_POSITION_TECID`)
    REFERENCES `cgdis_portal_db`.`SERVICE_PLAN_POSITION` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
    CONSTRAINT `fk_TEAM_MEMBER_person1`
    FOREIGN KEY (`PERSON_TECID`)
    REFERENCES `cgdis_portal_db`.`PERSON` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
    ENGINE = InnoDB
    DEFAULT CHARACTER SET = utf8;

CREATE INDEX `FK_TEAM_MEMBER_TEAM` ON `cgdis_portal_db`.`TEAM_MEMBER` (`TEAM_TECID` ASC);

CREATE INDEX `FK_TEAM_MEMBER_SERVICE_PLAN_POSITION` ON `cgdis_portal_db`.`TEAM_MEMBER` (`SERVICE_PLAN_POSITION_TECID` ASC);

CREATE UNIQUE INDEX `TEAM_MEMBER_U` ON `cgdis_portal_db`.`TEAM_MEMBER` (`TEAM_TECID` ASC, `SERVICE_PLAN_POSITION_TECID` ASC);

CREATE INDEX `FK_TEAM_MEMBER_PERSON` ON `cgdis_portal_db`.`TEAM_MEMBER` (`PERSON_TECID` ASC);


-- -----------------------------------------------------
-- Table `cgdis_portal_db`.`GENERAL_MESSAGE`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis_portal_db`.`GENERAL_MESSAGE` ;

CREATE TABLE IF NOT EXISTS `cgdis_portal_db`.`GENERAL_MESSAGE` (
                                                         `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
    `TECLOCK` BIGINT(20) NOT NULL,
    `MESSAGE` VARCHAR(1000) NOT NULL,
    `START_DATE_TIME` DATETIME NOT NULL,
    `END_DATE_TIME` DATETIME NOT NULL,
    `NAME` VARCHAR(120) NOT NULL,
    PRIMARY KEY (`TECID`))
    ENGINE = InnoDB;


-- -----------------------------------------------------
-- Table `cgdis_portal_db`.`VEHICLE_STATUS_CHANGE_ACKNOWLEDGE`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis_portal_db`.`VEHICLE_STATUS_CHANGE_ACKNOWLEDGE` ;

CREATE TABLE IF NOT EXISTS `cgdis_portal_db`.`VEHICLE_STATUS_CHANGE_ACKNOWLEDGE` (
                                                                           `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
    `TECLOCK` BIGINT(20) NOT NULL,
    `STATUS_NOTIFIED` BIGINT(20) NOT NULL,
    `PERSON_TECID` BIGINT(20) NOT NULL,
    `ACKNOWLEDGE_DATE` DATETIME NULL,
    PRIMARY KEY (`TECID`),
    CONSTRAINT `fk_VEHICLE_STATUS_CHANGE_NOTIFICATION_VEHICLE_STATUS_HISTORIC1`
    FOREIGN KEY (`STATUS_NOTIFIED`)
    REFERENCES `cgdis_portal_db`.`VEHICLE_STATUS_HISTORIC` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
    CONSTRAINT `fk_VEHICLE_STATUS_CHANGE_NOTIFICATION_person1`
    FOREIGN KEY (`PERSON_TECID`)
    REFERENCES `cgdis_portal_db`.`PERSON` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
    ENGINE = InnoDB
    DEFAULT CHARACTER SET = utf8;

CREATE UNIQUE INDEX `U_VEHICLE_STATUS_CHANGE_ACKNOWLEDGE_TECID` ON `cgdis_portal_db`.`VEHICLE_STATUS_CHANGE_ACKNOWLEDGE` (`TECID` ASC);

CREATE INDEX `fk_VEHICLE_STATUS_CHANGE_NOTIFICATION_VEHICLE_STATUS_HISTOR_idx` ON `cgdis_portal_db`.`VEHICLE_STATUS_CHANGE_ACKNOWLEDGE` (`STATUS_NOTIFIED` ASC);

CREATE INDEX `fk_VEHICLE_STATUS_CHANGE_NOTIFICATION_person1_idx` ON `cgdis_portal_db`.`VEHICLE_STATUS_CHANGE_ACKNOWLEDGE` (`PERSON_TECID` ASC);


-- -----------------------------------------------------
-- Table `cgdis_portal_db`.`ASSIGNMENT_QUEUE`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis_portal_db`.`ASSIGNMENT_QUEUE` ;

CREATE TABLE IF NOT EXISTS `cgdis_portal_db`.`ASSIGNMENT_QUEUE` (
                                                          `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
    `TECLOCK` BIGINT(20) NOT NULL,
    `CREATION_DATE` DATETIME NOT NULL,
    `ASSIGNMENT_TECID` BIGINT(20) NOT NULL,
    PRIMARY KEY (`TECID`),
    CONSTRAINT `fk_ASSIGNMENT_QUEUE_assignment1`
    FOREIGN KEY (`ASSIGNMENT_TECID`)
    REFERENCES `cgdis_portal_db`.`ASSIGNMENT` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
    ENGINE = InnoDB;

CREATE INDEX `fk_ASSIGNMENT_QUEUE_assignment1_idx` ON `cgdis_portal_db`.`ASSIGNMENT_QUEUE` (`ASSIGNMENT_TECID` ASC);


-- -----------------------------------------------------
-- Table `cgdis_portal_db`.`AUDIT_SLOT`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis_portal_db`.`AUDIT_SLOT` ;

CREATE TABLE IF NOT EXISTS `cgdis_portal_db`.`AUDIT_SLOT` (
                                                    `TECID` BIGINT(20) NOT NULL,
    `START_DATETIME` DATETIME NULL,
    `END_DATETIME` DATETIME NULL,
    `TARGET_DATETIME` DATETIME NULL,
    PRIMARY KEY (`TECID`),
    CONSTRAINT `fk_AUDIT_SLOT_AUDIT1`
    FOREIGN KEY (`TECID`)
    REFERENCES `cgdis_portal_db`.`AUDIT` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
    ENGINE = InnoDB;


-- -----------------------------------------------------
-- Table `cgdis_portal_db`.`PUBLIC_HOLIDAY`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis_portal_db`.`PUBLIC_HOLIDAY` ;

CREATE TABLE IF NOT EXISTS `cgdis_portal_db`.`PUBLIC_HOLIDAY` (
                                                        `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
    `TECLOCK` BIGINT(20) NOT NULL,
    `NAME` VARCHAR(250) NOT NULL,
    `DATE` DATE NOT NULL,
    `YEAR` INT NOT NULL,
    PRIMARY KEY (`TECID`))
    ENGINE = InnoDB;


-- -----------------------------------------------------
-- Table `cgdis_portal_db`.`OPTIONAL_BACKUP_GROUP_PERSON`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis_portal_db`.`OPTIONAL_BACKUP_GROUP_PERSON` ;

CREATE TABLE IF NOT EXISTS `cgdis_portal_db`.`OPTIONAL_BACKUP_GROUP_PERSON` (
                                                                      `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
    `TECLOCK` BIGINT(20) NOT NULL,
    `NAME` VARCHAR(500) NULL,
    `CGDIS_REGISTRATION_NUMBER` VARCHAR(20) NULL,
    `OPTIONAL_BACKUP_GROUP_TECID` BIGINT(20) NOT NULL,
    PRIMARY KEY (`TECID`),
    CONSTRAINT `fk_optional_backup_group_person_optional_backup_group1`
    FOREIGN KEY (`OPTIONAL_BACKUP_GROUP_TECID`)
    REFERENCES `cgdis_portal_db`.`OPTIONAL_BACKUP_GROUP` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
    ENGINE = InnoDB;

CREATE INDEX `fk_optional_backup_group_person_optional_backup_group1_idx` ON `cgdis_portal_db`.`OPTIONAL_BACKUP_GROUP_PERSON` (`OPTIONAL_BACKUP_GROUP_TECID` ASC);


-- -----------------------------------------------------
-- Table `cgdis_portal_db`.`AUDIT_LOGAS`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis_portal_db`.`AUDIT_LOGAS` ;

CREATE TABLE IF NOT EXISTS `cgdis_portal_db`.`AUDIT_LOGAS` (
                                                     `TECID` BIGINT(20) NOT NULL,
    `ORIGINAL_USER_NAME` VARCHAR(100) NULL,
    `IMPERSONATED_USER_NAME` VARCHAR(100) NULL,
    `SESSION_ID` VARCHAR(100) NULL,
    PRIMARY KEY (`TECID`),
    CONSTRAINT `fk_AUDIT_LOGAS_AUDIT1`
    FOREIGN KEY (`TECID`)
    REFERENCES `cgdis_portal_db`.`AUDIT` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
    ENGINE = InnoDB;


-- -----------------------------------------------------
-- Table `cgdis_portal_db`.`ASSIGNMENT_FUNCTION_OPERATIONAL`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis_portal_db`.`ASSIGNMENT_FUNCTION_OPERATIONAL` ;

CREATE TABLE IF NOT EXISTS `cgdis_portal_db`.`ASSIGNMENT_FUNCTION_OPERATIONAL` (
                                                                         `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
    `TECLOCK` BIGINT(20) NOT NULL,
    `ASSIGNMENT_TECID` BIGINT(20) NOT NULL,
    `FUNCTION_OPERATIONAL_TECID` BIGINT(20) NOT NULL,
    PRIMARY KEY (`TECID`),
    CONSTRAINT `fk_assignment_function_assignment`
    FOREIGN KEY (`ASSIGNMENT_TECID`)
    REFERENCES `cgdis_portal_db`.`ASSIGNMENT` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
    CONSTRAINT `fk_assignment_function_fct`
    FOREIGN KEY (`FUNCTION_OPERATIONAL_TECID`)
    REFERENCES `cgdis_portal_db`.`OPERATIONAL_FUNCTION` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
    ENGINE = InnoDB
    DEFAULT CHARACTER SET = utf8;

CREATE INDEX `fk_assignment_has_operational_function_operational_function_idx` ON `cgdis_portal_db`.`ASSIGNMENT_FUNCTION_OPERATIONAL` (`FUNCTION_OPERATIONAL_TECID` ASC);

CREATE INDEX `fk_assignment_has_operational_function_assignment1_idx` ON `cgdis_portal_db`.`ASSIGNMENT_FUNCTION_OPERATIONAL` (`ASSIGNMENT_TECID` ASC);

USE `cgdis_portal_db` ;

-- -----------------------------------------------------
-- Placeholder table for view `cgdis_portal_db`.`VW_EXPORT_PRESTATIONS`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `cgdis_portal_db`.`VW_EXPORT_PRESTATIONS` (`PRESTATION_TECID` INT, `ENTITY_NAME` INT, `LABEL` INT, `PORTAL_LABEL` INT, `START_DATE` INT, `START_DATETIME` INT, `END_DATETIME` INT, `FIRSTNAME` INT, `LASTNAME` INT, `TIC` INT, `CGDIS_REGISTRATION_NUMBER` INT, `PAGER_RIC` INT, `MOBILE_NUMBER` INT, `PHONE_NUMBER` INT, `EMAIL` INT, `SLOT_START_TIME` INT, `SLOT_END_TIME` INT, `VEHICLE_REGISTRATION` INT);

-- -----------------------------------------------------
-- View `cgdis_portal_db`.`VW_EXPORT_PRESTATIONS`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis_portal_db`.`VW_EXPORT_PRESTATIONS`;
DROP VIEW IF EXISTS `cgdis_portal_db`.`VW_EXPORT_PRESTATIONS` ;
USE `cgdis_portal_db`;
CREATE  OR REPLACE VIEW `VW_EXPORT_PRESTATIONS` AS
SELECT
    `PRESTATION`.`TECID` AS `PRESTATION_TECID`,
    `ENTITY`.`NAME` AS `ENTITY_NAME`,
    `SERVICE_PLAN`.`LABEL` AS `LABEL`,
    `SERVICE_PLAN`.`PORTAL_LABEL` AS `PORTAL_LABEL`,
    CAST(`PRESTATION`.`START_DATETIME` AS DATE) AS `START_DATE`,
    `PRESTATION`.`START_DATETIME` AS `START_DATETIME`,
    `PRESTATION`.`END_DATETIME` AS `END_DATETIME`,
    `PERSON`.`FIRSTNAME` AS `FIRSTNAME`,
    `PERSON`.`LASTNAME` AS `LASTNAME`,
    `PERSON`.`TIC` AS `TIC`,
    `PERSON`.`CGDIS_REGISTRATION_NUMBER` AS `CGDIS_REGISTRATION_NUMBER`,
    `OPERATIONAL_CONTACT_INFORMATION`.`PAGER_RIC` AS `PAGER_RIC`,
    `OPERATIONAL_CONTACT_INFORMATION`.`MOBILE_NUMBER` AS `MOBILE_NUMBER`,
    `OPERATIONAL_CONTACT_INFORMATION`.`PHONE_NUMBER` AS `PHONE_NUMBER`,
    `OPERATIONAL_CONTACT_INFORMATION`.`EMAIL` AS `EMAIL`,
    `SERVICE_PLAN_TIME_SLOT`.`START_TIME` AS `SLOT_START_TIME`,
    `SERVICE_PLAN_TIME_SLOT`.`END_TIME` AS `SLOT_END_TIME`,
    `VEHICLE`.`REGISTRATION` AS `VEHICLE_REGISTRATION`
FROM
    (((((((`PRESTATION`
        JOIN `SERVICE_PLAN_TIME_SLOT` ON (`PRESTATION`.`SERVICE_PLAN_TIME_SLOT_TECID` = `SERVICE_PLAN_TIME_SLOT`.`TECID`))
        JOIN `SERVICE_PLAN_VERSION` ON (`SERVICE_PLAN_TIME_SLOT`.`SERVICE_PLAN_VERSION_TECID` = `SERVICE_PLAN_VERSION`.`TECID`))
        JOIN `SERVICE_PLAN` ON (`SERVICE_PLAN_VERSION`.`SERVICE_PLAN_TECID` = `SERVICE_PLAN`.`TECID`))
        JOIN `PERSON` ON (`PRESTATION`.`PERSON_TECID` = `PERSON`.`TECID`))
        JOIN `ENTITY` ON (`SERVICE_PLAN`.`ENTITY_TECID` = `ENTITY`.`TECID`))
        JOIN `OPERATIONAL_CONTACT_INFORMATION` ON (`OPERATIONAL_CONTACT_INFORMATION`.`PERSON_TECID` = `PERSON`.`TECID`))
        left JOIN `VEHICLE` ON (`SERVICE_PLAN`.`VEHICLE_TECID` = `VEHICLE`.`TECID`))

ORDER BY CAST(`PRESTATION`.`START_DATETIME` AS DATE) , `ENTITY`.`NAME` , `SERVICE_PLAN`.`LABEL` , `PRESTATION`.`START_DATETIME`;

SET SQL_MODE=@OLD_SQL_MODE;
SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS;
SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS;
