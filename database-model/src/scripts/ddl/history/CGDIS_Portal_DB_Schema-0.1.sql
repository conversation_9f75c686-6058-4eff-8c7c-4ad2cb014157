-- My<PERSON><PERSON> Script generated by <PERSON><PERSON><PERSON> Workbench
-- Fri Jan 19 09:50:46 2018
-- Model: New Model    Version: 1.0
-- MySQL Workbench Forward Engineering

SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0;
SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0;
SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='TRADITIONAL,ALLOW_INVALID_DATES';

-- -----------------------------------------------------
-- Schema cgdis_portal_db
-- -----------------------------------------------------

-- -----------------------------------------------------
-- Table `INTERVENTION_TYPE`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `INTERVENTION_TYPE` ;

CREATE TABLE IF NOT EXISTS `INTERVENTION_TYPE` (
  `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
  `TECLOCK` BIGINT(20) NOT NULL,
  `LABEL` VARCHAR(50) NOT NULL,
  PRIMARY KEY (`TECID`))
ENGINE = InnoDB;

CREATE UNIQUE INDEX `U_INTERVENTION_TYPE_TECID` ON `INTERVENTION_TYPE` (`TECID` ASC);


-- -----------------------------------------------------
-- Table `SERVICE_PLAN_MODEL`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `SERVICE_PLAN_MODEL` ;

CREATE TABLE IF NOT EXISTS `SERVICE_PLAN_MODEL` (
  `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
  `TECLOCK` BIGINT(20) NOT NULL,
  `ID` VARCHAR(50) NOT NULL,
  `NAME` VARCHAR(200) NOT NULL,
  `HAS_VEHICLE` CHAR(1) NULL,
  `VEHICLE_TYPE` VARCHAR(100) NULL,
  `IS_EXCLUSIVE` CHAR(1) NULL,
  `INTERVENTION_TYPE_ID` BIGINT(20) NOT NULL,
  PRIMARY KEY (`TECID`),
  CONSTRAINT `FK_SP_MODEL_INTERVENTION_TYPE`
    FOREIGN KEY (`INTERVENTION_TYPE_ID`)
    REFERENCES `INTERVENTION_TYPE` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `I_FK_SP_MODEL_INTERVENTION_TYPE` ON `SERVICE_PLAN_MODEL` (`INTERVENTION_TYPE_ID` ASC);

CREATE UNIQUE INDEX `U_SERVICE_PLAN_MODEL_ID` ON `SERVICE_PLAN_MODEL` (`ID` ASC);


-- -----------------------------------------------------
-- Table `BANK_ACCOUNT`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `BANK_ACCOUNT` ;

CREATE TABLE IF NOT EXISTS `BANK_ACCOUNT` (
  `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
  `TECLOCK` BIGINT(20) NOT NULL,
  `BANK_CODE` VARCHAR(10) NULL,
  `IBAN` VARCHAR(40) NULL,
  `BIC` VARCHAR(15) NULL,
  PRIMARY KEY (`TECID`))
ENGINE = InnoDB;


-- -----------------------------------------------------
-- Table `PERSON`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `PERSON` ;

CREATE TABLE IF NOT EXISTS `PERSON` (
  `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
  `TECLOCK` BIGINT(20) NOT NULL,
  `FIRSTNAME` VARCHAR(200) NOT NULL,
  `LASTNAME` VARCHAR(200) NOT NULL,
  `IS_PROFESSIONAL` CHAR(1) NOT NULL DEFAULT 'Y',
  `IS_VOLUNTEER` CHAR(1) NOT NULL DEFAULT 'Y',
  `NUMBER` VARCHAR(100) NULL,
  `REGISTRATION_NUMBER` VARCHAR(100) NULL,
  `IAM_NUMBER` VARCHAR(10) NULL,
  `BIRTHDATE` DATE NULL,
  `RNPP_REGISTRATION_NUMBER` VARCHAR(20) NULL,
  `NATIONALITY` VARCHAR(3) NULL,
  `GENDER` CHAR(1) NULL,
  `SECOND_FIRSTNAME` VARCHAR(200) NULL,
  `SPOUSE_NAME` VARCHAR(200) NULL,
  `CIVIL_STATUS` VARCHAR(50) NULL,
  `BANK_ACCOUNT_TECID` BIGINT(20) NOT NULL,
  PRIMARY KEY (`TECID`),
  CONSTRAINT `FK_PERSON_BANK_ACCOUNT`
    FOREIGN KEY (`BANK_ACCOUNT_TECID`)
    REFERENCES `BANK_ACCOUNT` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE UNIQUE INDEX `U_PERSON_TECID` ON `PERSON` (`TECID` ASC);

CREATE INDEX `IF_FK_PERSON_BANK_ACCOUNT` ON `PERSON` (`BANK_ACCOUNT_TECID` ASC);


-- -----------------------------------------------------
-- Table `ENTITY_CATEGORY`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `ENTITY_CATEGORY` ;

CREATE TABLE IF NOT EXISTS `ENTITY_CATEGORY` (
  `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
  `TECLOCK` BIGINT(20) NOT NULL,
  `LABEL` VARCHAR(100) NOT NULL,
  PRIMARY KEY (`TECID`))
ENGINE = InnoDB;

CREATE UNIQUE INDEX `U_ENTITY_CATEGORY_TECID` ON `ENTITY_CATEGORY` (`TECID` ASC);
CREATE UNIQUE INDEX `U_ENTITY_CATEGORY_LABEL` ON `ENTITY_CATEGORY` (`LABEL` ASC);


-- -----------------------------------------------------
-- Table `ENTITY`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `ENTITY` ;

CREATE TABLE IF NOT EXISTS `ENTITY` (
  `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
  `TECLOCK` BIGINT(20) NOT NULL,
  `NAME` VARCHAR(255) NOT NULL,
  `ARMING_PRIORITY` INT NOT NULL,
  `SUB_ENTITIES` BIGINT(20) NULL,
  `TYPE` VARCHAR(50) NOT NULL,
  `ENTITY_CATEGORY` BIGINT(20) NULL,
  PRIMARY KEY (`TECID`),
  CONSTRAINT `FK_ENTITY_ENTITY`
    FOREIGN KEY (`SUB_ENTITIES`)
    REFERENCES `ENTITY` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `FK_ENTITY_ENTITY_CATEGORY`
    FOREIGN KEY (`ENTITY_CATEGORY`)
    REFERENCES `ENTITY_CATEGORY` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `I_FK_ENTITY_ENTITY` ON `ENTITY` (`SUB_ENTITIES` ASC);

CREATE INDEX `I_FK_ENTITY_ENTITY_CATEGORY` ON `ENTITY` (`ENTITY_CATEGORY` ASC);

CREATE UNIQUE INDEX `U_ENTITY_NAME`ON  (`NAME` ASC);
-- -----------------------------------------------------
-- Table `TEAM`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `TEAM` ;

CREATE TABLE IF NOT EXISTS `TEAM` (
  `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
  `TECLOCK` BIGINT(20) NOT NULL,
  `LABEL` VARCHAR(100) NOT NULL,
  `ENTITY_ID` BIGINT(20) NOT NULL,
  PRIMARY KEY (`TECID`),
  CONSTRAINT `FK_TEAM_ENTITY`
    FOREIGN KEY (`ENTITY_ID`)
    REFERENCES `ENTITY` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `I_FK_TEAM_ENTITY` ON `TEAM` (`ENTITY_ID` ASC);

CREATE UNIQUE INDEX `U_TEAM_TECID` ON `TEAM` (`TECID` ASC);
CREATE UNIQUE INDEX `U_TEAM_LABEL_BY_ENTITY` ON `TEAM` (`ENITTY_ID` ASC,`LABEL` ASC);


-- -----------------------------------------------------
-- Table `VEHICLE`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `VEHICLE` ;

CREATE TABLE IF NOT EXISTS `VEHICLE` (
  `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
  `TECLOCK` BIGINT NOT NULL,
  `TYPE` VARCHAR(100) NOT NULL,
  `REGISTRATION` VARCHAR(20) NOT NULL,
  `ENTITY_ID` BIGINT(20) NOT NULL,
  `ID` VARCHAR(100) NOT NULL,
  `NAME` VARCHAR(200) NULL,
  `DESCRIPTION` VARCHAR(512) NULL,
  PRIMARY KEY (`TECID`),
  CONSTRAINT `FK_VEHICLE_ENTITY`
    FOREIGN KEY (`ENTITY_ID`)
    REFERENCES `ENTITY` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `I_FK_VEHICLE_ENTITY1` ON `VEHICLE` (`ENTITY_ID` ASC);
CREATE UNIQUE INDEX `U_VEHICLE_TECID` ON `VEHICLE` (`TECID` ASC);
CREATE UNIQUE INDEX `U_VEHICLE_ID` ON `VEHICLE` (`ID` ASC);


-- -----------------------------------------------------
-- Table `SERVICE_PLAN_TYPE`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `SERVICE_PLAN_TYPE` ;

CREATE TABLE IF NOT EXISTS `SERVICE_PLAN_TYPE` (
  `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
  `TECLOCK` BIGINT(20) NULL,
  `LABEL` VARCHAR(50) NULL COMMENT 'Caserné/Non Caserné/Professionnel\n',
  PRIMARY KEY (`TECID`))
ENGINE = InnoDB;


-- -----------------------------------------------------
-- Table `LEGAL_CONSTRAINT`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `LEGAL_CONSTRAINT` ;

CREATE TABLE IF NOT EXISTS `LEGAL_CONSTRAINT` (
  `INTERVENTION_TYPE_ID` BIGINT(20) NOT NULL,
  `PDS_TYPE_TECID` BIGINT(20) NOT NULL,
  `ARMING_MAXIMUM_DELAY` INT NULL,
  `ENTITY_CATEGORY_ID` BIGINT(20) NOT NULL,
  PRIMARY KEY (`INTERVENTION_TYPE_ID`, `PDS_TYPE_TECID`, `ENTITY_CATEGORY_ID`),
  CONSTRAINT `FK_LEGAL_CONSTRAINT_INTERVENTION_TYPE`
    FOREIGN KEY (`INTERVENTION_TYPE_ID`)
    REFERENCES `INTERVENTION_TYPE` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `FK_LEGAL_CONSTRAINT_PDS_TYPE`
    FOREIGN KEY (`PDS_TYPE_TECID`)
    REFERENCES `SERVICE_PLAN_TYPE` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `FK_LEGAL_CONSTRAINT_ENTITY_CATEGORY`
    FOREIGN KEY (`ENTITY_CATEGORY_ID`)
    REFERENCES `ENTITY_CATEGORY` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `I_FK_LEGAL_CONSTRAINT_INTERVENTION_TYPE` ON `LEGAL_CONSTRAINT` (`INTERVENTION_TYPE_ID` ASC);

CREATE INDEX `I_FK_LEGAL_CONSTRAINT_PDS_TYPE` ON `LEGAL_CONSTRAINT` (`PDS_TYPE_TECID` ASC);

CREATE INDEX `I_FK_LEGAL_CONSTRAINT_ENTITY_CATEGORY` ON `LEGAL_CONSTRAINT` (`ENTITY_CATEGORY_ID` ASC);


-- -----------------------------------------------------
-- Table `SERVICE_PLAN_MODEL_VERSION`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `SERVICE_PLAN_MODEL_VERSION` ;

CREATE TABLE IF NOT EXISTS `SERVICE_PLAN_MODEL_VERSION` (
  `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
  `TECLOCK` BIGINT(20) NOT NULL,
  `VERSION` INT NOT NULL,
  `STARTDATE` DATE NULL,
  `ENDDATE` DATE NULL,
  `SERVICE_PLAN_MODEL_TECID` BIGINT(20) NOT NULL,
  PRIMARY KEY (`TECID`),
  CONSTRAINT `FK_SERVICE_PLAN_MODEL_VERSION_SERVICE_PLAN_MODEL`
    FOREIGN KEY (`SERVICE_PLAN_MODEL_TECID`)
    REFERENCES `SERVICE_PLAN_MODEL` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE UNIQUE INDEX `U_SERVICE_PLAN_MODEL_VERSION_TECID` ON `SERVICE_PLAN_MODEL_VERSION` (`TECID` ASC);
CREATE UNIQUE INDEX `U_SERVICE_PLAN_MODEL_MODEL_AND_VERSION` ON `SERVICE_PLAN_MODEL_VERSION` (`SERVICE_PLAN_MODEL_TECID`, `VERSION` ASC);

CREATE INDEX `FK_SERVICE_PLAN_MODEL_VERSION_SERVICE_PLAN_MODEL` ON `SERVICE_PLAN_MODEL_VERSION` (`SERVICE_PLAN_MODEL_TECID` ASC);


-- -----------------------------------------------------
-- Table `SERVICE_PLAN_POSITION`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `SERVICE_PLAN_POSITION` ;

CREATE TABLE IF NOT EXISTS `SERVICE_PLAN_POSITION` (
  `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
  `TECLOCK` BIGINT(20) NOT NULL,
  `LABEL` VARCHAR(100) NULL,
  `COMPLETION_TYPE` VARCHAR(10) NULL DEFAULT 'REQUIRED' COMMENT 'REQUIRED/OPTIMAL/OPTIONAL',
  `ENTITY_ID` BIGINT(20) NOT NULL,
  `SERVICE_PLAN_MODEL_VERSION_TECID` BIGINT(20) NOT NULL,
  PRIMARY KEY (`TECID`),
  CONSTRAINT `FK_SERVICE_PLAN_POSITION_ENTITY`
    FOREIGN KEY (`ENTITY_ID`)
    REFERENCES `ENTITY` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `FK_SERVICE_PLAN_POSITION_SERVICE_PLAN_MODEL_VERSION`
    FOREIGN KEY (`SERVICE_PLAN_MODEL_VERSION_TECID`)
    REFERENCES `SERVICE_PLAN_MODEL_VERSION` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `I_FK_SERVICE_PLAN_POSITION_ENTITY` ON `SERVICE_PLAN_POSITION` (`ENTITY_ID` ASC);

CREATE INDEX `I_FK_SERVICE_PLAN_POSITION_SERVICE_PLAN_MODEL_VERSION1` ON `SERVICE_PLAN_POSITION` (`SERVICE_PLAN_MODEL_VERSION_TECID` ASC);


-- -----------------------------------------------------
-- Table `OPERATIONAL_FUNCTION`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `OPERATIONAL_FUNCTION` ;

CREATE TABLE IF NOT EXISTS `OPERATIONAL_FUNCTION` (
  `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
  `TECLOCK` BIGINT(20) NOT NULL,
  `LABEL` VARCHAR(100) NOT NULL,
  PRIMARY KEY (`TECID`))
ENGINE = InnoDB;


-- -----------------------------------------------------
-- Table `SERVICE_PLAN_POSITION_FUNCTIONS`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `SERVICE_PLAN_POSITION_FUNCTIONS` ;

CREATE TABLE IF NOT EXISTS `SERVICE_PLAN_POSITION_FUNCTIONS` (
  `SERVICE_PLAN_POSITION_TECID` BIGINT(20) NOT NULL,
  `FUNCTION_TECID` BIGINT(20) NOT NULL,
  `PARTIAL_FULFILL` CHAR(1) NOT NULL DEFAULT 'N',
  PRIMARY KEY (`SERVICE_PLAN_POSITION_TECID`, `FUNCTION_TECID`),
  CONSTRAINT `FK_SERVICE_PLAN_POSITION_FCTS_SP_POS`
    FOREIGN KEY (`SERVICE_PLAN_POSITION_TECID`)
    REFERENCES `SERVICE_PLAN_POSITION` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `FK_SERVICE_PLAN_POSITION_FCTS_OP_FCT`
    FOREIGN KEY (`FUNCTION_TECID`)
    REFERENCES `OPERATIONAL_FUNCTION` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `I_FK_SERVICE_PLAN_POSITION_FCTS_OP_FCT` ON `SERVICE_PLAN_POSITION_FUNCTIONS` (`FUNCTION_TECID` ASC);

CREATE INDEX `I_FK_SERVICE_PLAN_POSITION_FCTS_SP_POS` ON `SERVICE_PLAN_POSITION_FUNCTIONS` (`SERVICE_PLAN_POSITION_TECID` ASC);


-- -----------------------------------------------------
-- Table `WORK_PLAN_MODEL`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `WORK_PLAN_MODEL` ;

CREATE TABLE IF NOT EXISTS `WORK_PLAN_MODEL` (
  `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
  `TECLOCK` BIGINT(20) NOT NULL,
  `LABEL` VARCHAR(200) NULL,
  `ENTITY_TECID` BIGINT(20) NOT NULL,
  PRIMARY KEY (`TECID`),
  CONSTRAINT `FK_WORK_PLAN_MODEL_ENTITY`
    FOREIGN KEY (`ENTITY_TECID`)
    REFERENCES `ENTITY` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `I_FK_WORK_PLAN_MODEL_ENTITY` ON `WORK_PLAN_MODEL` (`ENTITY_TECID` ASC);
CREATE UNIQUE INDEX `U_WORK_PLAN_MODEL_LABEL` ON `WORK_PLAN_MODEL` (`LABEL` ASC);

-- -----------------------------------------------------
-- Table `WORK_PLAN_MODEL_SHIFT`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `WORK_PLAN_MODEL_SHIFT` ;

CREATE TABLE IF NOT EXISTS `WORK_PLAN_MODEL_SHIFT` (
  `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
  `TECLOCK` BIGINT(20) NOT NULL,
  `LABEL` VARCHAR(100) NOT NULL,
  `START_HOUR` TIME(1) NOT NULL,
  `END_HOUR` TIME(1) NOT NULL,
  `ICON` VARCHAR(100) NULL,
  `COLOUR` VARCHAR(50) NULL,
  `WORK_PLAN_MODEL_TECID` BIGINT(20) NOT NULL,
  PRIMARY KEY (`TECID`),
  CONSTRAINT `FK_WORK_PLAN_MODEL_SHIFT_WORK_PLAN_MODEL`
    FOREIGN KEY (`WORK_PLAN_MODEL_TECID`)
    REFERENCES `WORK_PLAN_MODEL` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `I_FK_WORK_PLAN_MODEL_SHIFT_WORK_PLAN_MODEL` ON `WORK_PLAN_MODEL_SHIFT` (`WORK_PLAN_MODEL_TECID` ASC);


-- -----------------------------------------------------
-- Table `WORK_PLAN_MODEL_PATTERN_ITEM`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `WORK_PLAN_MODEL_PATTERN_ITEM` ;

CREATE TABLE IF NOT EXISTS `WORK_PLAN_MODEL_PATTERN_ITEM` (
  `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
  `TECLOCK` BIGINT(20) NOT NULL,
  `LABEL` VARCHAR(200) NOT NULL,
  `TYPE` VARCHAR(50) NOT NULL,
  `ITEM_ORDER` INT NOT NULL,
  `WORK_PLAN_PATTERN_TECID` BIGINT(20) NOT NULL,
  `WORK_PLAN_SHIFT_TECID` BIGINT(20) NOT NULL,
  `WORK_PLAN_MODEL_TECID` BIGINT(20) NOT NULL,
  PRIMARY KEY (`TECID`),
  CONSTRAINT `FK_WORK_PLAN_PATTERN_ITEM_WORK_PLAN_SHIFT`
    FOREIGN KEY (`WORK_PLAN_SHIFT_TECID`)
    REFERENCES `WORK_PLAN_MODEL_SHIFT` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `FK_WORK_PLAN_MODEL_PATTERN_ITEM_WORK_PLAN_MODEL`
    FOREIGN KEY (`WORK_PLAN_MODEL_TECID`)
    REFERENCES `WORK_PLAN_MODEL` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `I_FK_WORK_PLAN_PATTERN_ITEM_WORK_PLAN_SHIFT` ON `WORK_PLAN_MODEL_PATTERN_ITEM` (`WORK_PLAN_SHIFT_TECID` ASC);

CREATE INDEX `I_FK_WORK_PLAN_MODEL_PATTERN_ITEM_WORK_PLAN_MODEL` ON `WORK_PLAN_MODEL_PATTERN_ITEM` (`WORK_PLAN_MODEL_TECID` ASC);


-- -----------------------------------------------------
-- Table `WORK_PLAN`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `WORK_PLAN` ;

CREATE TABLE IF NOT EXISTS `WORK_PLAN` (
  `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
  `TECLOCK` BIGINT(20) NOT NULL,
  `LABEL` VARCHAR(200) NOT NULL,
  `STARTDATE` DATE NOT NULL,
  `END_DATE` DATE NULL,
  `ENTITY_TECID` BIGINT(20) NOT NULL,
  PRIMARY KEY (`TECID`),
  CONSTRAINT `FK_WORK_PLAN_ENTITY`
    FOREIGN KEY (`ENTITY_TECID`)
    REFERENCES `ENTITY` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `I_FK_WORK_PLAN_ENTITY` ON `WORK_PLAN` (`ENTITY_TECID` ASC);


-- -----------------------------------------------------
-- Table `PROFESSIONAL_AVAILABILITY`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `PROFESSIONAL_AVAILABILITY` ;

CREATE TABLE IF NOT EXISTS `PROFESSIONAL_AVAILABILITY` (
  `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
  `TECLOCK` BIGINT(20) NOT NULL,
  `WORK_PLAN_TECID` BIGINT(20) NOT NULL,
  `START_DATETIME` DATETIME NOT NULL,
  `END_DATE_TIME` DATETIME NOT NULL,
  `DETACHED` CHAR(1) NULL DEFAULT 'N' COMMENT 'est ce que cette disponibilité est détaché du centre principal',
  `PROFESSIONAL_AVAILABILITY` VARCHAR(45) NULL,
  `LABEL` VARCHAR(100) NOT NULL,
  `ICON` VARCHAR(100) NOT NULL,
  `COLOUR` VARCHAR(50) NULL,

  `PERSON_TECID` BIGINT(20) NOT NULL,
  PRIMARY KEY (`TECID`),
  CONSTRAINT `FK_PROFESSIONAL_AVAILABILITY_WORK_PLAN`
    FOREIGN KEY (`WORK_PLAN_TECID`)
    REFERENCES `WORK_PLAN` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `FK_PROFESSIONAL_AVAILABILITY_PERSON`
    FOREIGN KEY (`PERSON_TECID`)
    REFERENCES `PERSON` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `I_FK_PROFESSIONAL_AVAILABILITY_WORK_PLAN` ON `PROFESSIONAL_AVAILABILITY` (`WORK_PLAN_TECID` ASC);

CREATE INDEX `I_FK_PROFESSIONAL_AVAILABILITY_PERSON` ON `PROFESSIONAL_AVAILABILITY` (`PERSON_TECID` ASC);


-- -----------------------------------------------------
-- Table `PROFESSIONAL_UNAVAILABILITY`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `PROFESSIONAL_UNAVAILABILITY` ;

CREATE TABLE IF NOT EXISTS `PROFESSIONAL_UNAVAILABILITY` (
  `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
  `TECLOCK` BIGINT(20) NOT NULL,
  `TYPE` VARCHAR(50) NOT NULL,
  `START_DATE` DATE NOT NULL,
  `END_DATE` DATE NOT NULL,
  `PERSON_TECID` BIGINT(20) NOT NULL,
  PRIMARY KEY (`TECID`),
  CONSTRAINT `FK_PROFESSIONAL_UNAVAILABILITY_PERSON`
    FOREIGN KEY (`PERSON_TECID`)
    REFERENCES `PERSON` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `I_FK_PROFESSIONAL_UNAVAILABILITY_PERSON` ON `PROFESSIONAL_UNAVAILABILITY` (`PERSON_TECID` ASC);


-- -----------------------------------------------------
-- Table `VOLUNTEER_AVAILABILITY`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `VOLUNTEER_AVAILABILITY` ;

CREATE TABLE IF NOT EXISTS `VOLUNTEER_AVAILABILITY` (
  `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
  `TECLOCK` BIGINT(20) NOT NULL,
  `START_DATETIME` DATETIME NOT NULL,
  `END_DATETIME` DATETIME NULL,
  `PERSON_TECID` BIGINT(20) NOT NULL,
  PRIMARY KEY (`TECID`),
  CONSTRAINT `FK_VOLUNTEER_AVAILABILITY_PERSON`
    FOREIGN KEY (`PERSON_TECID`)
    REFERENCES `PERSON` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `I_FK_VOLUNTEER_AVAILABILITY_PERSON` ON `VOLUNTEER_AVAILABILITY` (`PERSON_TECID` ASC);


-- -----------------------------------------------------
-- Table `VOLUNTEER_AVAILABILITY_INTERVENTION_TYPES`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `VOLUNTEER_AVAILABILITY_INTERVENTION_TYPES` ;

CREATE TABLE IF NOT EXISTS `VOLUNTEER_AVAILABILITY_INTERVENTION_TYPES` (
  `VOLUNTEER_AVAILABILITY_TECID` BIGINT(20) NOT NULL,
  `INTERVENTION_TYPE_TECID` BIGINT(20) NOT NULL,
  PRIMARY KEY (`VOLUNTEER_AVAILABILITY_TECID`, `INTERVENTION_TYPE_TECID`),
  CONSTRAINT `FK_VOLUNTEER_AVAILABILITY_INT_TYPES_VOLUNTEER_AV`
    FOREIGN KEY (`VOLUNTEER_AVAILABILITY_TECID`)
    REFERENCES `VOLUNTEER_AVAILABILITY` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `FK_VOLUNTEER_AVAILABILITY_INT_TYPES_INT_TYPE`
    FOREIGN KEY (`INTERVENTION_TYPE_TECID`)
    REFERENCES `INTERVENTION_TYPE` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `I_FK_VOLUNTEER_AVAILABILITY_INT_TYPES_INT_TYPE` ON `VOLUNTEER_AVAILABILITY_INTERVENTION_TYPES` (`INTERVENTION_TYPE_TECID` ASC);

CREATE INDEX `I_FK_VOLUNTEER_AVAILABILITY_INT_TYPES_VOLUNTEER_AV` ON `VOLUNTEER_AVAILABILITY_INTERVENTION_TYPES` (`VOLUNTEER_AVAILABILITY_TECID` ASC);


-- -----------------------------------------------------
-- Table `SERVICE_PLAN`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `SERVICE_PLAN` ;

CREATE TABLE IF NOT EXISTS `SERVICE_PLAN` (
  `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
  `TECLOCK` BIGINT(20) NOT NULL,
  `LABEL` VARCHAR(255) NOT NULL,
  `STARTDATE` DATE NULL,
  `ENDDATE` DATE NULL,
  `AUTOMATIC` CHAR(1) NULL DEFAULT 'N',
  `ARMING_DELAY` VARCHAR(45) NULL,
  `BACKUP_GROUP` VARCHAR(255) NULL,
  `OPTIONAL_GROUP` VARCHAR(255) NULL,
  `SERVICE_PLAN_TYPE_TECID` BIGINT(20) NOT NULL,
  `VEHICLE_TECID` BIGINT(20) NOT NULL,
  `ENTITY_TECID` BIGINT(20) NOT NULL,
  `MODEL_ID` VARCHAR(50) NOT NULL,
  `ENABLED` CHAR(1) NOT NULL DEFAULT 'Y',
  PRIMARY KEY (`TECID`),
  CONSTRAINT `FK_SERVICE_PLAN_SERVICE_PLAN_TYPE`
    FOREIGN KEY (`SERVICE_PLAN_TYPE_TECID`)
    REFERENCES `SERVICE_PLAN_TYPE` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `FK_SERVICE_PLAN_VEHICLE`
    FOREIGN KEY (`VEHICLE_TECID`)
    REFERENCES `VEHICLE` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `FK_SERVICE_PLAN_ENTITY`
    FOREIGN KEY (`ENTITY_TECID`)
    REFERENCES `ENTITY` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE UNIQUE INDEX `LABEL_UNIQUE` ON `SERVICE_PLAN` (`LABEL` ASC);

CREATE INDEX `I_FK_SERVICE_PLAN_SERVICE_PLAN_TYPE` ON `SERVICE_PLAN` (`SERVICE_PLAN_TYPE_TECID` ASC);

CREATE INDEX `I_FK_SERVICE_PLAN_VEHICLE` ON `SERVICE_PLAN` (`VEHICLE_TECID` ASC);

CREATE INDEX `I_FK_SERVICE_PLAN_ENTITY` ON `SERVICE_PLAN` (`ENTITY_TECID` ASC);


-- -----------------------------------------------------
-- Table `SERVICE_PLAN_TIME_SLOTS`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `SERVICE_PLAN_TIME_SLOTS` ;

CREATE TABLE IF NOT EXISTS `SERVICE_PLAN_TIME_SLOTS` (
  `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
  `TECLOCK` BIGINT(20) NOT NULL,
  `SERVICE_PLAN_TECID` BIGINT(20) NOT NULL,
  `STARTDATE` DATE NULL,
  `ENDDATE` DATE NULL,
  PRIMARY KEY (`TECID`),
  CONSTRAINT `FK_SERVICE_PLAN_TIME_SLOTS_SERVICE_PLAN`
    FOREIGN KEY (`SERVICE_PLAN_TECID`)
    REFERENCES `SERVICE_PLAN` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `I_FK_SERVICE_PLAN_TIME_SLOTS_SERVICE_PLAN` ON `SERVICE_PLAN_TIME_SLOTS` (`SERVICE_PLAN_TECID` ASC);


-- -----------------------------------------------------
-- Table `SERVICE_PLAN_TIME_SLOT`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `SERVICE_PLAN_TIME_SLOT` ;

CREATE TABLE IF NOT EXISTS `SERVICE_PLAN_TIME_SLOT` (
  `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
  `TECLOCK` BIGINT(20) NOT NULL,
  `START_TIME` TIME(1) NULL,
  `END_TIME` TIME(1) NULL,
  `SERVICE_PLAN_TIME_SLOTS_TECID` BIGINT(20) NOT NULL,
  PRIMARY KEY (`TECID`),
  CONSTRAINT `FK_SERVICE_PLAN_TIME_SLOT_SERVICE_PLAN_TIME_SLOTS`
    FOREIGN KEY (`SERVICE_PLAN_TIME_SLOTS_TECID`)
    REFERENCES `SERVICE_PLAN_TIME_SLOTS` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `I_FK_SERVICE_PLAN_TIME_SLOT_SERVICE_PLAN_TIME_SLOTS` ON `SERVICE_PLAN_TIME_SLOT` (`SERVICE_PLAN_TIME_SLOTS_TECID` ASC);


-- -----------------------------------------------------
-- Table `PRESTATION`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `PRESTATION` ;

CREATE TABLE IF NOT EXISTS `PRESTATION` (
  `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
  `TECLOCK` BIGINT(20) NOT NULL,
  `START_DATETIME` DATETIME NOT NULL,
  `END_DATETIME` DATETIME NOT NULL,
  `PERSON_TECID` BIGINT(20) NOT NULL,
  `SERVICE_PLAN_TIME_SLOT_TECID` BIGINT(20) NOT NULL,
  `SERVICE_PLAN_POSITION_TECID` BIGINT(20) NOT NULL,
  PRIMARY KEY (`TECID`),
  CONSTRAINT `FK_PRESTATION_PERSON`
    FOREIGN KEY (`PERSON_TECID`)
    REFERENCES `PERSON` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `FK_PRESTATION_SERVICE_PLAN_TIME_SLOT`
    FOREIGN KEY (`SERVICE_PLAN_TIME_SLOT_TECID`)
    REFERENCES `SERVICE_PLAN_TIME_SLOT` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `FK_PRESTATION_SERVICE_PLAN_POSITION`
    FOREIGN KEY (`SERVICE_PLAN_POSITION_TECID`)
    REFERENCES `SERVICE_PLAN_POSITION` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `I_FK_PRESTATION_PERSON` ON `PRESTATION` (`PERSON_TECID` ASC);

CREATE INDEX `I_FK_PRESTATION_SERVICE_PLAN_TIME_SLOT` ON `PRESTATION` (`SERVICE_PLAN_TIME_SLOT_TECID` ASC);

CREATE INDEX `I_FK_PRESTATION_SERVICE_PLAN_POSITION` ON `PRESTATION` (`SERVICE_PLAN_POSITION_TECID` ASC);


-- -----------------------------------------------------
-- Table `PUSHABLE_DATA_STATE`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `PUSHABLE_DATA_STATE` ;

CREATE TABLE IF NOT EXISTS `PUSHABLE_DATA_STATE` (
  `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
  `TECLOCK` BIGINT(20) NOT NULL,
  `OBJECT_TYPE` VARCHAR(50) NOT NULL,
  `OBJECT_TECID` BIGINT(20) NOT NULL,
  `PUSH_TYPE` VARCHAR(50) NULL,
  `OBJECT_VERSION` BIGINT(20) NOT NULL,
  `OBJECT_LAST_PUSHED_VERSION` BIGINT(20) NULL,
  PRIMARY KEY (`TECID`))
ENGINE = InnoDB;


-- -----------------------------------------------------
-- Table `ASSIGNMENT`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `ASSIGNMENT` ;

CREATE TABLE IF NOT EXISTS `ASSIGNMENT` (
  `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
  `TECLOCK` BIGINT(20) NOT NULL,
  `PERSON_TECID` BIGINT(20) NOT NULL,
  `ENTITY_TECID` BIGINT(20) NOT NULL,
  PRIMARY KEY (`TECID`),
  CONSTRAINT `FK_ASSIGNMENT_PERSON`
    FOREIGN KEY (`PERSON_TECID`)
    REFERENCES `PERSON` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `FK_ASSIGNMENT_ENTITY`
    FOREIGN KEY (`ENTITY_TECID`)
    REFERENCES `ENTITY` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `I_FK_ASSIGNMENT_PERSON` ON `ASSIGNMENT` (`PERSON_TECID` ASC);

CREATE INDEX `I_FK_ASSIGNMENT_ENTITY` ON `ASSIGNMENT` (`ENTITY_TECID` ASC);


-- -----------------------------------------------------
-- Table `ASSIGNMENT_FUNCTIONS`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `ASSIGNMENT_FUNCTIONS` ;

CREATE TABLE IF NOT EXISTS `ASSIGNMENT_FUNCTIONS` (
  `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
  `TECLOCK` BIGINT(20) NOT NULL,
  `ASSIGNMENT_TECID` BIGINT(20) NOT NULL,
  `OPERATIONAL_FUNCTION_TECID` BIGINT(20) NOT NULL,
  `FUNCTCION_GRANTED` CHAR(1) NOT NULL DEFAULT 'N',
  PRIMARY KEY (`TECID`),
  CONSTRAINT `FK_ASSIGNMENT_FUNCTONS_ASSIGNMENT`
    FOREIGN KEY (`ASSIGNMENT_TECID`)
    REFERENCES `ASSIGNMENT` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `FK_ASSIGNMENT_FUNCTONS_OPERATIONAL_FUNCTION`
    FOREIGN KEY (`OPERATIONAL_FUNCTION_TECID`)
    REFERENCES `OPERATIONAL_FUNCTION` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `I_FK_ASSIGNMENT_FUNCTONS_ASSIGNMENT` ON `ASSIGNMENT_FUNCTIONS` (`ASSIGNMENT_TECID` ASC);

CREATE INDEX `I_FK_ASSIGNMENT_FUNCTONS_OPERATIONAL_FUNCTION` ON `ASSIGNMENT_FUNCTIONS` (`OPERATIONAL_FUNCTION_TECID` ASC);


-- -----------------------------------------------------
-- Table `VOLUNTEER_AVAILABILITY_ENTITY`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `VOLUNTEER_AVAILABILITY_ENTITY` ;

CREATE TABLE IF NOT EXISTS `VOLUNTEER_AVAILABILITY_ENTITY` (
  `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
  `TECLOCK` BIGINT(20) NOT NULL,
  `ENTITY_TECID` BIGINT(20) NOT NULL,
  `VOLUNTEER_AVAILABILITY_TECID` BIGINT(20) NOT NULL,
  `SERVICE_PLAN_TYPE_TECID` BIGINT(20) NOT NULL,
  PRIMARY KEY (`TECID`),
  CONSTRAINT `FK_VOLUNTEER_AVAILABILITY_ENTITY_VOLUNTEER_AVAILABILITY`
    FOREIGN KEY (`VOLUNTEER_AVAILABILITY_TECID`)
    REFERENCES `VOLUNTEER_AVAILABILITY` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `FK_VOLUNTEER_AVAILABILITY_ENTITY_ENTITY`
    FOREIGN KEY (`ENTITY_TECID`)
    REFERENCES `ENTITY` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `FK_VOLUNTEER_AVAILABILITY_ENTITY_SERVICE_PLAN_TYPE`
    FOREIGN KEY (`SERVICE_PLAN_TYPE_TECID`)
    REFERENCES `SERVICE_PLAN_TYPE` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `I_FK_VOLUNTEER_AVAILABILITY_ENTITY_VOLUNTEER_AVAILABILITY` ON `VOLUNTEER_AVAILABILITY_ENTITY` (`VOLUNTEER_AVAILABILITY_TECID` ASC);

CREATE INDEX `I_FK_VOLUNTEER_AVAILABILITY_ENTITY_ENTITY` ON `VOLUNTEER_AVAILABILITY_ENTITY` (`ENTITY_TECID` ASC);

CREATE INDEX `I_FK_VOLUNTEER_AVAILABILITY_ENTITY_SERVICE_PLAN_TYPE` ON `VOLUNTEER_AVAILABILITY_ENTITY` (`SERVICE_PLAN_TYPE_TECID` ASC);


-- -----------------------------------------------------
-- Table `OPERATIONAL_OCCUPATION`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `OPERATIONAL_OCCUPATION` ;

CREATE TABLE IF NOT EXISTS `OPERATIONAL_OCCUPATION` (
  `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
  `TECLOCK` BIGINT(20) NOT NULL,
  PRIMARY KEY (`TECID`))
ENGINE = InnoDB;


-- -----------------------------------------------------
-- Table `PERSON_OPERATIONAL_OCCUPATIONS`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `PERSON_OPERATIONAL_OCCUPATIONS` ;

CREATE TABLE IF NOT EXISTS `PERSON_OPERATIONAL_OCCUPATIONS` (
  `TECID` BIGINT(20) NOT NULL,
  `TECLOCK` BIGINT(20) NOT NULL,
  `OPERATIONAL_OCCUPATION_TECID` BIGINT(20) NOT NULL,
  `PERSON_TECID` BIGINT(20) NOT NULL,
  `JOB_TYPE` CHAR(1) NULL DEFAULT 'V',
  PRIMARY KEY (`TECID`),
  CONSTRAINT `FK_PERSON_OP_OCCS_OP_OCC`
    FOREIGN KEY (`OPERATIONAL_OCCUPATION_TECID`)
    REFERENCES `OPERATIONAL_OCCUPATION` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `FK_PERSON_OP_OCCS_PERSON`
    FOREIGN KEY (`PERSON_TECID`)
    REFERENCES `PERSON` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `I_FK_PERSON_OP_OCCS_PERSON` ON `PERSON_OPERATIONAL_OCCUPATIONS` (`PERSON_TECID` ASC);

CREATE INDEX `I_FK_PERSON_OP_OCCS_OP_OCC` ON `PERSON_OPERATIONAL_OCCUPATIONS` (`OPERATIONAL_OCCUPATION_TECID` ASC);


-- -----------------------------------------------------
-- Table `ADDRESS`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `ADDRESS` ;

CREATE TABLE IF NOT EXISTS `ADDRESS` (
  `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
  `TECLOCK` BIGINT(20) NOT NULL,
  `LABEL` VARCHAR(200) NULL,
  `ADDRESS` VARCHAR(500) NULL,
  `ZIP_CODE` VARCHAR(10) NULL,
  `CITY` VARCHAR(200) NULL,
  `COUNTRY` VARCHAR(3) NULL,
  `PERSON_TECID` BIGINT(20) NOT NULL,
  PRIMARY KEY (`TECID`),
  CONSTRAINT `FK_ADDRESS_PERSON`
    FOREIGN KEY (`PERSON_TECID`)
    REFERENCES `PERSON` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `I_FK_ADDRESS_PERSON` ON `ADDRESS` (`PERSON_TECID` ASC);


SET SQL_MODE=@OLD_SQL_MODE;
SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS;
SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS;
