-- My<PERSON><PERSON> Script generated by <PERSON><PERSON><PERSON> Workbench
-- Wed Mar 28 14:14:24 2018
-- Model: New Model    Version: 1.0
-- MySQL Workbench Forward Engineering

SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0;
SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0;
SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='TRADITIONAL,ALLOW_INVALID_DATES';

-- -----------------------------------------------------
-- Schema mydb
-- -----------------------------------------------------
-- -----------------------------------------------------
-- Schema cgdis
-- -----------------------------------------------------

-- -----------------------------------------------------
-- Schema cgdis
-- -----------------------------------------------------
CREATE SCHEMA IF NOT EXISTS `cgdis` DEFAULT CHARACTER SET utf8 ;
USE `cgdis` ;

-- -----------------------------------------------------
-- Table `cgdis`.`bank_account`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis`.`bank_account` ;

CREATE TABLE IF NOT EXISTS `cgdis`.`bank_account` (
  `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
  `TECLOCK` BIGINT(20) NOT NULL,
  `BANK_CODE` VARCHAR(10) NULL DEFAULT NULL,
  `IBAN` VARCHAR(40) NULL DEFAULT NULL,
  `BIC` VARCHAR(15) NULL DEFAULT NULL,
  PRIMARY KEY (`TECID`))
ENGINE = InnoDB
DEFAULT CHARACTER SET = utf8;


-- -----------------------------------------------------
-- Table `cgdis`.`person`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis`.`person` ;

CREATE TABLE IF NOT EXISTS `cgdis`.`person` (
  `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
  `TECLOCK` BIGINT(20) NOT NULL DEFAULT 0,
  `FIRSTNAME` VARCHAR(200) NOT NULL,
  `LASTNAME` VARCHAR(200) NOT NULL,
  `IS_PROFESSIONAL` CHAR(1) NOT NULL DEFAULT 'Y',
  `IS_VOLUNTEER` CHAR(1) NOT NULL DEFAULT 'Y',
  `NUMBER` VARCHAR(100) NULL DEFAULT NULL,
  `REGISTRATION_NUMBER` VARCHAR(100) NULL DEFAULT NULL,
  `IAM_NUMBER` VARCHAR(10) NULL DEFAULT NULL,
  `BIRTHDATE` DATE NULL DEFAULT NULL,
  `RNPP_REGISTRATION_NUMBER` VARCHAR(20) NULL DEFAULT NULL,
  `NATIONALITY` VARCHAR(3) NULL DEFAULT NULL,
  `GENDER` CHAR(1) NULL DEFAULT NULL,
  `SECOND_FIRSTNAME` VARCHAR(200) NULL DEFAULT NULL,
  `SPOUSE_NAME` VARCHAR(200) NULL DEFAULT NULL,
  `CIVIL_STATUS` VARCHAR(50) NULL DEFAULT NULL,
  `BANK_ACCOUNT_TECID` BIGINT(20) NOT NULL,
  PRIMARY KEY (`TECID`),
  CONSTRAINT `FK_PERSON_BANK_ACCOUNT`
    FOREIGN KEY (`BANK_ACCOUNT_TECID`)
    REFERENCES `cgdis`.`bank_account` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB
DEFAULT CHARACTER SET = utf8;

CREATE UNIQUE INDEX `U_PERSON_TECID` ON `cgdis`.`person` (`TECID` ASC);

CREATE INDEX `IF_FK_PERSON_BANK_ACCOUNT` ON `cgdis`.`person` (`BANK_ACCOUNT_TECID` ASC);


-- -----------------------------------------------------
-- Table `cgdis`.`address`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis`.`address` ;

CREATE TABLE IF NOT EXISTS `cgdis`.`address` (
  `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
  `TECLOCK` BIGINT(20) NOT NULL,
  `LABEL` VARCHAR(200) NULL DEFAULT NULL,
  `ADDRESS` VARCHAR(500) NULL DEFAULT NULL,
  `ZIP_CODE` VARCHAR(10) NULL DEFAULT NULL,
  `CITY` VARCHAR(200) NULL DEFAULT NULL,
  `COUNTRY` VARCHAR(3) NULL DEFAULT NULL,
  `PERSON_TECID` BIGINT(20) NOT NULL,
  PRIMARY KEY (`TECID`),
  CONSTRAINT `FK_ADDRESS_PERSON`
    FOREIGN KEY (`PERSON_TECID`)
    REFERENCES `cgdis`.`person` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB
DEFAULT CHARACTER SET = utf8;

CREATE INDEX `I_FK_ADDRESS_PERSON` ON `cgdis`.`address` (`PERSON_TECID` ASC);


-- -----------------------------------------------------
-- Table `cgdis`.`entity_category`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis`.`entity_category` ;

CREATE TABLE IF NOT EXISTS `cgdis`.`entity_category` (
  `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
  `TECLOCK` BIGINT(20) NOT NULL,
  `LABEL` VARCHAR(100) NOT NULL,
  PRIMARY KEY (`TECID`))
ENGINE = InnoDB
DEFAULT CHARACTER SET = utf8;

CREATE UNIQUE INDEX `U_ENTITY_CATEGORY_TECID` ON `cgdis`.`entity_category` (`TECID` ASC);

CREATE UNIQUE INDEX `U_ENTITY_CATEGORY_LABEL` ON `cgdis`.`entity_category` (`LABEL` ASC);


-- -----------------------------------------------------
-- Table `cgdis`.`ENTITY_ADDRESS`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis`.`ENTITY_ADDRESS` ;

CREATE TABLE IF NOT EXISTS `cgdis`.`ENTITY_ADDRESS` (
  `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
  `TECLOCK` BIGINT(20) NOT NULL,
  `ADDRESS` VARCHAR(500) NULL,
  `ZIP_CODE` VARCHAR(10) NULL,
  `CITY` VARCHAR(200) NULL,
  `COUNTRY` VARCHAR(3) NULL,
  PRIMARY KEY (`TECID`))
ENGINE = InnoDB;


-- -----------------------------------------------------
-- Table `cgdis`.`entity`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis`.`entity` ;

CREATE TABLE IF NOT EXISTS `cgdis`.`entity` (
  `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
  `TECLOCK` BIGINT(20) NOT NULL,
  `NAME` VARCHAR(255) NOT NULL,
  `ARMING_PRIORITY` INT(11) NOT NULL,
  `PARENT_ENTITY_TECID` BIGINT(20) NULL DEFAULT NULL,
  `TYPE` VARCHAR(50) NOT NULL,
  `ENTITY_CATEGORY` BIGINT(20) NULL DEFAULT NULL,
  `ENTITY_ADDRESS_TECID` BIGINT(20) NULL,
  PRIMARY KEY (`TECID`),
  CONSTRAINT `FK_ENTITY_ENTITY`
    FOREIGN KEY (`PARENT_ENTITY_TECID`)
    REFERENCES `cgdis`.`entity` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `FK_ENTITY_ENTITY_CATEGORY`
    FOREIGN KEY (`ENTITY_CATEGORY`)
    REFERENCES `cgdis`.`entity_category` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_entity_ENTITY_ADDRESS1`
    FOREIGN KEY (`ENTITY_ADDRESS_TECID`)
    REFERENCES `cgdis`.`ENTITY_ADDRESS` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB
DEFAULT CHARACTER SET = utf8;

CREATE UNIQUE INDEX `U_ENTITY_NAME` ON `cgdis`.`entity` (`NAME` ASC);

CREATE INDEX `I_FK_ENTITY_ENTITY` ON `cgdis`.`entity` (`PARENT_ENTITY_TECID` ASC);

CREATE INDEX `I_FK_ENTITY_ENTITY_CATEGORY` ON `cgdis`.`entity` (`ENTITY_CATEGORY` ASC);

CREATE INDEX `fk_entity_ENTITY_ADDRESS1_idx` ON `cgdis`.`entity` (`ENTITY_ADDRESS_TECID` ASC);


-- -----------------------------------------------------
-- Table `cgdis`.`assignment`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis`.`assignment` ;

CREATE TABLE IF NOT EXISTS `cgdis`.`assignment` (
  `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
  `TECLOCK` BIGINT(20) NOT NULL,
  `PERSON_TECID` BIGINT(20) NOT NULL,
  `ENTITY_TECID` BIGINT(20) NOT NULL,
  `IS_PRIMARY` CHAR(1) NULL,
  `IS_VOLUNTEER` CHAR(1) NULL,
  PRIMARY KEY (`TECID`),
  CONSTRAINT `FK_ASSIGNMENT_ENTITY`
    FOREIGN KEY (`ENTITY_TECID`)
    REFERENCES `cgdis`.`entity` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `FK_ASSIGNMENT_PERSON`
    FOREIGN KEY (`PERSON_TECID`)
    REFERENCES `cgdis`.`person` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB
DEFAULT CHARACTER SET = utf8;

CREATE INDEX `I_FK_ASSIGNMENT_PERSON` ON `cgdis`.`assignment` (`PERSON_TECID` ASC);

CREATE INDEX `I_FK_ASSIGNMENT_ENTITY` ON `cgdis`.`assignment` (`ENTITY_TECID` ASC);


-- -----------------------------------------------------
-- Table `cgdis`.`operational_function`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis`.`operational_function` ;

CREATE TABLE IF NOT EXISTS `cgdis`.`operational_function` (
  `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
  `TECLOCK` BIGINT(20) NOT NULL,
  `LABEL` VARCHAR(100) NOT NULL,
  `PARTIAL_FULFILL` CHAR(1) NULL,
  PRIMARY KEY (`TECID`))
ENGINE = InnoDB
DEFAULT CHARACTER SET = utf8;


-- -----------------------------------------------------
-- Table `cgdis`.`person_operational_functions`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis`.`person_operational_functions` ;

CREATE TABLE IF NOT EXISTS `cgdis`.`person_operational_functions` (
  `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
  `TECLOCK` BIGINT(20) NOT NULL,
  `STARTDATE` DATE NOT NULL,
  `ENDDATE` DATE NULL,
  `OPERATIONAL_FUNCTION_TECID` BIGINT(20) NOT NULL,
  `PERSON_TECID` BIGINT(20) NOT NULL,
  PRIMARY KEY (`TECID`),
  CONSTRAINT `FK_PERSON_OP_FCTS_OP_FCT`
    FOREIGN KEY (`OPERATIONAL_FUNCTION_TECID`)
    REFERENCES `cgdis`.`operational_function` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `FK_PERSON_OP_FCTS_PERSON`
    FOREIGN KEY (`PERSON_TECID`)
    REFERENCES `cgdis`.`person` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_person_operational_functions_operational_function1_idx` ON `cgdis`.`person_operational_functions` (`OPERATIONAL_FUNCTION_TECID` ASC);

CREATE INDEX `fk_person_operational_functions_person1_idx` ON `cgdis`.`person_operational_functions` (`PERSON_TECID` ASC);


-- -----------------------------------------------------
-- Table `cgdis`.`assignment_functions`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis`.`assignment_functions` ;

CREATE TABLE IF NOT EXISTS `cgdis`.`assignment_functions` (
  `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
  `TECLOCK` BIGINT(20) NOT NULL,
  `ASSIGNMENT_TECID` BIGINT(20) NOT NULL,
  `FUNCTION_GRANTED` CHAR(1) NOT NULL DEFAULT 'N',
  `PERSON_OPERATIONAL_FUNCTIONS_TECID` BIGINT(20) NOT NULL,
  `RATING` INT NULL,
  PRIMARY KEY (`TECID`),
  CONSTRAINT `FK_ASSIGNMENT_FUNCTONS_ASSIGNMENT`
    FOREIGN KEY (`ASSIGNMENT_TECID`)
    REFERENCES `cgdis`.`assignment` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `FK_ASSIGNEMENT_FCTS_PERSON_OP_FCTS`
    FOREIGN KEY (`PERSON_OPERATIONAL_FUNCTIONS_TECID`)
    REFERENCES `cgdis`.`person_operational_functions` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB
DEFAULT CHARACTER SET = utf8;

CREATE INDEX `I_FK_ASSIGNMENT_FUNCTONS_ASSIGNMENT` ON `cgdis`.`assignment_functions` (`ASSIGNMENT_TECID` ASC);

CREATE INDEX `fk_assignment_functions_person_operational_functions1_idx` ON `cgdis`.`assignment_functions` (`PERSON_OPERATIONAL_FUNCTIONS_TECID` ASC);


-- -----------------------------------------------------
-- Table `cgdis`.`intervention_type`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis`.`intervention_type` ;

CREATE TABLE IF NOT EXISTS `cgdis`.`intervention_type` (
  `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
  `TECLOCK` BIGINT(20) NOT NULL,
  `LABEL` VARCHAR(50) NOT NULL,
  PRIMARY KEY (`TECID`))
ENGINE = InnoDB
DEFAULT CHARACTER SET = utf8;

CREATE UNIQUE INDEX `U_INTERVENTION_TYPE_TECID` ON `cgdis`.`intervention_type` (`TECID` ASC);


-- -----------------------------------------------------
-- Table `cgdis`.`service_plan_type`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis`.`service_plan_type` ;

CREATE TABLE IF NOT EXISTS `cgdis`.`service_plan_type` (
  `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
  `TECLOCK` BIGINT(20) NULL DEFAULT NULL,
  `LABEL` VARCHAR(50) NULL DEFAULT NULL COMMENT 'Caserné/Non Caserné/Professionnel\n',
  `ACCEPT_VOLUNTEER` CHAR(1) NULL,
  `ACCEPT_PRO` CHAR(1) NULL,
  `IS_BARRACKED` CHAR(1) NULL,
  PRIMARY KEY (`TECID`))
ENGINE = InnoDB
DEFAULT CHARACTER SET = utf8;


-- -----------------------------------------------------
-- Table `cgdis`.`legal_constraint`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis`.`legal_constraint` ;

CREATE TABLE IF NOT EXISTS `cgdis`.`legal_constraint` (
  `INTERVENTION_TYPE_ID` BIGINT(20) NOT NULL,
  `PDS_TYPE_TECID` BIGINT(20) NOT NULL,
  `ARMING_MAXIMUM_DELAY` INT(11) NULL DEFAULT NULL,
  `ENTITY_CATEGORY_ID` BIGINT(20) NOT NULL,
  PRIMARY KEY (`INTERVENTION_TYPE_ID`, `PDS_TYPE_TECID`, `ENTITY_CATEGORY_ID`),
  CONSTRAINT `FK_LEGAL_CONSTRAINT_ENTITY_CATEGORY`
    FOREIGN KEY (`ENTITY_CATEGORY_ID`)
    REFERENCES `cgdis`.`entity_category` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `FK_LEGAL_CONSTRAINT_INTERVENTION_TYPE`
    FOREIGN KEY (`INTERVENTION_TYPE_ID`)
    REFERENCES `cgdis`.`intervention_type` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `FK_LEGAL_CONSTRAINT_PDS_TYPE`
    FOREIGN KEY (`PDS_TYPE_TECID`)
    REFERENCES `cgdis`.`service_plan_type` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB
DEFAULT CHARACTER SET = utf8;

CREATE INDEX `I_FK_LEGAL_CONSTRAINT_INTERVENTION_TYPE` ON `cgdis`.`legal_constraint` (`INTERVENTION_TYPE_ID` ASC);

CREATE INDEX `I_FK_LEGAL_CONSTRAINT_PDS_TYPE` ON `cgdis`.`legal_constraint` (`PDS_TYPE_TECID` ASC);

CREATE INDEX `I_FK_LEGAL_CONSTRAINT_ENTITY_CATEGORY` ON `cgdis`.`legal_constraint` (`ENTITY_CATEGORY_ID` ASC);


-- -----------------------------------------------------
-- Table `cgdis`.`operational_occupation`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis`.`operational_occupation` ;

CREATE TABLE IF NOT EXISTS `cgdis`.`operational_occupation` (
  `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
  `TECLOCK` BIGINT(20) NOT NULL,
  `LABEL` VARCHAR(100) NOT NULL,
  PRIMARY KEY (`TECID`))
ENGINE = InnoDB
DEFAULT CHARACTER SET = utf8;

CREATE UNIQUE INDEX `LABEL_UNIQUE` ON `cgdis`.`operational_occupation` (`LABEL` ASC);


-- -----------------------------------------------------
-- Table `cgdis`.`person_operational_occupations`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis`.`person_operational_occupations` ;

CREATE TABLE IF NOT EXISTS `cgdis`.`person_operational_occupations` (
  `TECID` BIGINT(20) NOT NULL,
  `TECLOCK` BIGINT(20) NOT NULL,
  `OPERATIONAL_OCCUPATION_TECID` BIGINT(20) NOT NULL,
  `PERSON_TECID` BIGINT(20) NOT NULL,
  `JOB_TYPE` CHAR(1) NULL DEFAULT 'V',
  PRIMARY KEY (`TECID`),
  CONSTRAINT `FK_PERSON_OP_OCCS_OP_OCC`
    FOREIGN KEY (`OPERATIONAL_OCCUPATION_TECID`)
    REFERENCES `cgdis`.`operational_occupation` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `FK_PERSON_OP_OCCS_PERSON`
    FOREIGN KEY (`PERSON_TECID`)
    REFERENCES `cgdis`.`person` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB
DEFAULT CHARACTER SET = utf8;

CREATE INDEX `I_FK_PERSON_OP_OCCS_PERSON` ON `cgdis`.`person_operational_occupations` (`PERSON_TECID` ASC);

CREATE INDEX `I_FK_PERSON_OP_OCCS_OP_OCC` ON `cgdis`.`person_operational_occupations` (`OPERATIONAL_OCCUPATION_TECID` ASC);


-- -----------------------------------------------------
-- Table `cgdis`.`service_plan_model`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis`.`service_plan_model` ;

CREATE TABLE IF NOT EXISTS `cgdis`.`service_plan_model` (
  `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
  `TECLOCK` BIGINT(20) NOT NULL,
  `NAME` VARCHAR(200) NOT NULL,
  `HAS_VEHICLE` CHAR(1) NULL DEFAULT NULL,
  `VEHICLE_TYPE` VARCHAR(100) NULL DEFAULT NULL,
  `IS_EXCLUSIVE` CHAR(1) NULL DEFAULT NULL,
  `INTERVENTION_TYPE_ID` BIGINT(20) NOT NULL,
  `ENTITY_TECID` BIGINT(20) NULL,
  PRIMARY KEY (`TECID`),
  CONSTRAINT `FK_SP_MODEL_INTERVENTION_TYPE`
    FOREIGN KEY (`INTERVENTION_TYPE_ID`)
    REFERENCES `cgdis`.`intervention_type` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `FK_SP_MODEL_ENTITY`
    FOREIGN KEY (`ENTITY_TECID`)
    REFERENCES `cgdis`.`entity` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB
DEFAULT CHARACTER SET = utf8;

CREATE INDEX `I_FK_SP_MODEL_INTERVENTION_TYPE` ON `cgdis`.`service_plan_model` (`INTERVENTION_TYPE_ID` ASC);

CREATE INDEX `I_FK_SP_MODEL_ENTITY` ON `cgdis`.`service_plan_model` (`ENTITY_TECID` ASC);


-- -----------------------------------------------------
-- Table `cgdis`.`service_plan_model_version`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis`.`service_plan_model_version` ;

CREATE TABLE IF NOT EXISTS `cgdis`.`service_plan_model_version` (
  `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
  `TECLOCK` BIGINT(20) NOT NULL,
  `LABEL` VARCHAR(100) NULL,
  `STARTDATE` DATE NOT NULL,
  `ENDDATE` DATE NULL DEFAULT NULL,
  `SERVICE_PLAN_MODEL_TECID` BIGINT(20) NOT NULL,
  PRIMARY KEY (`TECID`),
  CONSTRAINT `FK_SERVICE_PLAN_MODEL_VERSION_SERVICE_PLAN_MODEL`
    FOREIGN KEY (`SERVICE_PLAN_MODEL_TECID`)
    REFERENCES `cgdis`.`service_plan_model` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB
DEFAULT CHARACTER SET = utf8;

CREATE UNIQUE INDEX `U_SERVICE_PLAN_MODEL_VERSION_TECID` ON `cgdis`.`service_plan_model_version` (`TECID` ASC);

CREATE INDEX `FK_SERVICE_PLAN_MODEL_VERSION_SERVICE_PLAN_MODEL` ON `cgdis`.`service_plan_model_version` (`SERVICE_PLAN_MODEL_TECID` ASC);

CREATE UNIQUE INDEX `U_SERVICE_PLAN_MODEL_VERSION_MODEL_AND_STARTDATE` ON `cgdis`.`service_plan_model_version` (`STARTDATE` ASC, `SERVICE_PLAN_MODEL_TECID` ASC);


-- -----------------------------------------------------
-- Table `cgdis`.`service_plan_position`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis`.`service_plan_position` ;

CREATE TABLE IF NOT EXISTS `cgdis`.`service_plan_position` (
  `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
  `TECLOCK` BIGINT(20) NOT NULL,
  `LABEL` VARCHAR(100) NULL DEFAULT NULL,
  `COMPLETION_TYPE` VARCHAR(10) NULL DEFAULT 'REQUIRED' COMMENT 'REQUIRED/OPTIMAL/OPTIONAL',
  `SERVICE_PLAN_MODEL_VERSION_TECID` BIGINT(20) NULL,
  `POSITION_ORDER` INT NULL,
  `ICON` VARCHAR(100) NULL,
  PRIMARY KEY (`TECID`),
  CONSTRAINT `FK_SERVICE_PLAN_POSITION_SERVICE_PLAN_MODEL_VERSION`
    FOREIGN KEY (`SERVICE_PLAN_MODEL_VERSION_TECID`)
    REFERENCES `cgdis`.`service_plan_model_version` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB
DEFAULT CHARACTER SET = utf8;

CREATE INDEX `I_FK_SERVICE_PLAN_POSITION_SERVICE_PLAN_MODEL_VERSION1` ON `cgdis`.`service_plan_position` (`SERVICE_PLAN_MODEL_VERSION_TECID` ASC);


-- -----------------------------------------------------
-- Table `cgdis`.`vehicle`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis`.`vehicle` ;

CREATE TABLE IF NOT EXISTS `cgdis`.`vehicle` (
  `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
  `TECLOCK` BIGINT(20) NOT NULL,
  `TYPE` VARCHAR(100) NOT NULL,
  `REGISTRATION` VARCHAR(20) NOT NULL,
  `ENTITY_TECID` BIGINT(20) NOT NULL,
  `ID` VARCHAR(100) NOT NULL,
  `NAME` VARCHAR(200) NULL DEFAULT NULL,
  `DESCRIPTION` VARCHAR(512) NULL DEFAULT NULL,
  PRIMARY KEY (`TECID`),
  CONSTRAINT `FK_VEHICLE_ENTITY`
    FOREIGN KEY (`ENTITY_TECID`)
    REFERENCES `cgdis`.`entity` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB
DEFAULT CHARACTER SET = utf8;

CREATE UNIQUE INDEX `U_VEHICLE_TECID` ON `cgdis`.`vehicle` (`TECID` ASC);

CREATE UNIQUE INDEX `U_VEHICLE_ID` ON `cgdis`.`vehicle` (`ID` ASC);

CREATE INDEX `I_FK_VEHICLE_ENTITY1` ON `cgdis`.`vehicle` (`ENTITY_TECID` ASC);


-- -----------------------------------------------------
-- Table `cgdis`.`service_plan`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis`.`service_plan` ;

CREATE TABLE IF NOT EXISTS `cgdis`.`service_plan` (
  `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
  `TECLOCK` BIGINT(20) NOT NULL,
  `LABEL` VARCHAR(255) NOT NULL,
  `ARMING_DELAY` INT NULL,
  `BACKUP_GROUP` VARCHAR(255) NULL,
  `OPTIONAL_GROUP` VARCHAR(255) NULL,
  `ENTITY_TECID` BIGINT(20) NOT NULL,
  `VEHICLE_TECID` BIGINT(20) NULL,
  `ARMING_PRIORITY` INT NOT NULL,
  `SERVICE_PLAN_MODEL_TECID` BIGINT(20) NOT NULL,
  `SERVICE_PLAN_TYPE_TECID` BIGINT(20) NOT NULL,
  PRIMARY KEY (`TECID`),
  CONSTRAINT `fk_service_plan_entity`
    FOREIGN KEY (`ENTITY_TECID`)
    REFERENCES `cgdis`.`entity` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_service_plan_vehicle`
    FOREIGN KEY (`VEHICLE_TECID`)
    REFERENCES `cgdis`.`vehicle` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_service_plan_service_plan_model`
    FOREIGN KEY (`SERVICE_PLAN_MODEL_TECID`)
    REFERENCES `cgdis`.`service_plan_model` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_service_plan_service_plan_type`
    FOREIGN KEY (`SERVICE_PLAN_TYPE_TECID`)
    REFERENCES `cgdis`.`service_plan_type` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_service_plan_entity1_idx` ON `cgdis`.`service_plan` (`ENTITY_TECID` ASC);

CREATE INDEX `fk_service_plan_vehicle1_idx` ON `cgdis`.`service_plan` (`VEHICLE_TECID` ASC);

CREATE INDEX `fk_service_plan_service_plan_model1_idx` ON `cgdis`.`service_plan` (`SERVICE_PLAN_MODEL_TECID` ASC);

CREATE INDEX `fk_service_plan_service_plan_type1_idx` ON `cgdis`.`service_plan` (`SERVICE_PLAN_TYPE_TECID` ASC);


-- -----------------------------------------------------
-- Table `cgdis`.`service_plan_version`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis`.`service_plan_version` ;

CREATE TABLE IF NOT EXISTS `cgdis`.`service_plan_version` (
  `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
  `TECLOCK` BIGINT(20) NOT NULL,
  `STARTDATE` DATE NULL DEFAULT NULL,
  `ENDDATE` DATE NULL DEFAULT NULL,
  `AUTOMATIC` CHAR(1) NULL DEFAULT 'N',
  `SERVICE_PLAN_TECID` BIGINT(20) NOT NULL,
  `LABEL` VARCHAR(100) NOT NULL,
  PRIMARY KEY (`TECID`),
  CONSTRAINT `fk_service_plan_version_service_plan1`
    FOREIGN KEY (`SERVICE_PLAN_TECID`)
    REFERENCES `cgdis`.`service_plan` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB
DEFAULT CHARACTER SET = utf8;

CREATE INDEX `I_FK_SP_VERSION_SP` ON `cgdis`.`service_plan_version` (`SERVICE_PLAN_TECID` ASC);

CREATE UNIQUE INDEX `U_SERVICE_PLAN_VERSION_PLAN_AND_STARTDATE` ON `cgdis`.`service_plan_version` (`STARTDATE` ASC, `SERVICE_PLAN_TECID` ASC);


-- -----------------------------------------------------
-- Table `cgdis`.`service_plan_time_slot`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis`.`service_plan_time_slot` ;

CREATE TABLE IF NOT EXISTS `cgdis`.`service_plan_time_slot` (
  `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
  `TECLOCK` BIGINT(20) NOT NULL,
  `START_TIME` TIME(1) NULL DEFAULT NULL,
  `END_TIME` TIME(1) NULL DEFAULT NULL,
  `SERVICE_PLAN_VERSION_TECID` BIGINT(20) NOT NULL,
  PRIMARY KEY (`TECID`),
  CONSTRAINT `fk_service_plan_time_slot_service_plan_version1`
    FOREIGN KEY (`SERVICE_PLAN_VERSION_TECID`)
    REFERENCES `cgdis`.`service_plan_version` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB
DEFAULT CHARACTER SET = utf8;

CREATE INDEX `fk_service_plan_time_slot_service_plan_version1_idx` ON `cgdis`.`service_plan_time_slot` (`SERVICE_PLAN_VERSION_TECID` ASC);


-- -----------------------------------------------------
-- Table `cgdis`.`prestation`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis`.`prestation` ;

CREATE TABLE IF NOT EXISTS `cgdis`.`prestation` (
  `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
  `TECLOCK` BIGINT(20) NOT NULL,
  `START_DATETIME` DATETIME NOT NULL,
  `END_DATETIME` DATETIME NOT NULL,
  `PERSON_TECID` BIGINT(20) NOT NULL,
  `SERVICE_PLAN_TIME_SLOT_TECID` BIGINT(20) NOT NULL,
  `SERVICE_PLAN_POSITION_TECID` BIGINT(20) NOT NULL,
  PRIMARY KEY (`TECID`),
  CONSTRAINT `FK_PRESTATION_PERSON`
    FOREIGN KEY (`PERSON_TECID`)
    REFERENCES `cgdis`.`person` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `FK_PRESTATION_SERVICE_PLAN_POSITION`
    FOREIGN KEY (`SERVICE_PLAN_POSITION_TECID`)
    REFERENCES `cgdis`.`service_plan_position` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `FK_PRESTATION_SERVICE_PLAN_TIME_SLOT`
    FOREIGN KEY (`SERVICE_PLAN_TIME_SLOT_TECID`)
    REFERENCES `cgdis`.`service_plan_time_slot` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB
DEFAULT CHARACTER SET = utf8;

CREATE INDEX `I_FK_PRESTATION_PERSON` ON `cgdis`.`prestation` (`PERSON_TECID` ASC);

CREATE INDEX `I_FK_PRESTATION_SERVICE_PLAN_TIME_SLOT` ON `cgdis`.`prestation` (`SERVICE_PLAN_TIME_SLOT_TECID` ASC);

CREATE INDEX `I_FK_PRESTATION_SERVICE_PLAN_POSITION` ON `cgdis`.`prestation` (`SERVICE_PLAN_POSITION_TECID` ASC);


-- -----------------------------------------------------
-- Table `cgdis`.`work_plan`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis`.`work_plan` ;

CREATE TABLE IF NOT EXISTS `cgdis`.`work_plan` (
  `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
  `TECLOCK` BIGINT(20) NOT NULL,
  `LABEL` VARCHAR(200) NOT NULL,
  `STARTDATE` DATE NOT NULL,
  `ENDDATE` DATE NULL DEFAULT NULL,
  `ENTITY_TECID` BIGINT(20) NOT NULL,
  PRIMARY KEY (`TECID`),
  CONSTRAINT `FK_WORK_PLAN_ENTITY`
    FOREIGN KEY (`ENTITY_TECID`)
    REFERENCES `cgdis`.`entity` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB
DEFAULT CHARACTER SET = utf8;

CREATE INDEX `I_FK_WORK_PLAN_ENTITY` ON `cgdis`.`work_plan` (`ENTITY_TECID` ASC);


-- -----------------------------------------------------
-- Table `cgdis`.`professional_availability`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis`.`professional_availability` ;

CREATE TABLE IF NOT EXISTS `cgdis`.`professional_availability` (
  `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
  `TECLOCK` BIGINT(20) NOT NULL,
  `WORK_PLAN_TECID` BIGINT(20) NOT NULL,
  `START_DATETIME` DATETIME NOT NULL,
  `END_DATETIME` DATETIME NOT NULL,
  `DETACHED` CHAR(1) NULL DEFAULT 'N' COMMENT 'est ce que cette disponibilité est détaché du centre principal',
  `PROFESSIONAL_AVAILABILITY` VARCHAR(45) NULL DEFAULT NULL,
  `LABEL` VARCHAR(100) NOT NULL,
  `ICON` VARCHAR(100) NOT NULL,
  `COLOUR` VARCHAR(50) NULL DEFAULT NULL,
  `PERSON_TECID` BIGINT(20) NOT NULL,
  PRIMARY KEY (`TECID`),
  CONSTRAINT `FK_PROFESSIONAL_AVAILABILITY_PERSON`
    FOREIGN KEY (`PERSON_TECID`)
    REFERENCES `cgdis`.`person` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `FK_PROFESSIONAL_AVAILABILITY_WORK_PLAN`
    FOREIGN KEY (`WORK_PLAN_TECID`)
    REFERENCES `cgdis`.`work_plan` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB
DEFAULT CHARACTER SET = utf8;

CREATE INDEX `I_FK_PROFESSIONAL_AVAILABILITY_WORK_PLAN` ON `cgdis`.`professional_availability` (`WORK_PLAN_TECID` ASC);

CREATE INDEX `I_FK_PROFESSIONAL_AVAILABILITY_PERSON` ON `cgdis`.`professional_availability` (`PERSON_TECID` ASC);


-- -----------------------------------------------------
-- Table `cgdis`.`professional_unavailability`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis`.`professional_unavailability` ;

CREATE TABLE IF NOT EXISTS `cgdis`.`professional_unavailability` (
  `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
  `TECLOCK` BIGINT(20) NOT NULL,
  `TYPE` VARCHAR(50) NOT NULL,
  `START_DATE` DATE NOT NULL,
  `END_DATE` DATE NOT NULL,
  `PERSON_TECID` BIGINT(20) NOT NULL,
  PRIMARY KEY (`TECID`),
  CONSTRAINT `FK_PROFESSIONAL_UNAVAILABILITY_PERSON`
    FOREIGN KEY (`PERSON_TECID`)
    REFERENCES `cgdis`.`person` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB
DEFAULT CHARACTER SET = utf8;

CREATE INDEX `I_FK_PROFESSIONAL_UNAVAILABILITY_PERSON` ON `cgdis`.`professional_unavailability` (`PERSON_TECID` ASC);


-- -----------------------------------------------------
-- Table `cgdis`.`pushable_data_state`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis`.`pushable_data_state` ;

CREATE TABLE IF NOT EXISTS `cgdis`.`pushable_data_state` (
  `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
  `TECLOCK` BIGINT(20) NOT NULL,
  `OBJECT_TYPE` VARCHAR(50) NOT NULL,
  `OBJECT_TECID` BIGINT(20) NOT NULL,
  `PUSH_TYPE` VARCHAR(50) NULL DEFAULT NULL,
  `OBJECT_VERSION` BIGINT(20) NOT NULL,
  `OBJECT_LAST_PUSHED_VERSION` BIGINT(20) NULL DEFAULT NULL,
  PRIMARY KEY (`TECID`))
ENGINE = InnoDB
DEFAULT CHARACTER SET = utf8;


-- -----------------------------------------------------
-- Table `cgdis`.`service_plan_position_functions`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis`.`service_plan_position_functions` ;

CREATE TABLE IF NOT EXISTS `cgdis`.`service_plan_position_functions` (
  `SERVICE_PLAN_POSITION_TECID` BIGINT(20) NOT NULL,
  `FUNCTION_TECID` BIGINT(20) NOT NULL,
  PRIMARY KEY (`SERVICE_PLAN_POSITION_TECID`, `FUNCTION_TECID`),
  CONSTRAINT `FK_SERVICE_PLAN_POSITION_FCTS_OP_FCT`
    FOREIGN KEY (`FUNCTION_TECID`)
    REFERENCES `cgdis`.`operational_function` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `FK_SERVICE_PLAN_POSITION_FCTS_SP_POS`
    FOREIGN KEY (`SERVICE_PLAN_POSITION_TECID`)
    REFERENCES `cgdis`.`service_plan_position` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB
DEFAULT CHARACTER SET = utf8;

CREATE INDEX `I_FK_SERVICE_PLAN_POSITION_FCTS_OP_FCT` ON `cgdis`.`service_plan_position_functions` (`FUNCTION_TECID` ASC);

CREATE INDEX `I_FK_SERVICE_PLAN_POSITION_FCTS_SP_POS` ON `cgdis`.`service_plan_position_functions` (`SERVICE_PLAN_POSITION_TECID` ASC);


-- -----------------------------------------------------
-- Table `cgdis`.`team`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis`.`team` ;

CREATE TABLE IF NOT EXISTS `cgdis`.`team` (
  `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
  `TECLOCK` BIGINT(20) NOT NULL,
  `LABEL` VARCHAR(100) NOT NULL,
  `ENTITY_ID` BIGINT(20) NOT NULL,
  PRIMARY KEY (`TECID`),
  CONSTRAINT `FK_TEAM_ENTITY`
    FOREIGN KEY (`ENTITY_ID`)
    REFERENCES `cgdis`.`entity` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB
DEFAULT CHARACTER SET = utf8;

CREATE UNIQUE INDEX `U_TEAM_TECID` ON `cgdis`.`team` (`TECID` ASC);

CREATE UNIQUE INDEX `U_TEAM_LABEL_BY_ENTITY` ON `cgdis`.`team` (`ENTITY_ID` ASC, `LABEL` ASC);

CREATE INDEX `I_FK_TEAM_ENTITY` ON `cgdis`.`team` (`ENTITY_ID` ASC);


-- -----------------------------------------------------
-- Table `cgdis`.`volunteer_availability`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis`.`volunteer_availability` ;

CREATE TABLE IF NOT EXISTS `cgdis`.`volunteer_availability` (
  `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
  `TECLOCK` BIGINT(20) NOT NULL,
  `START_DATETIME` DATETIME NOT NULL,
  `END_DATETIME` DATETIME NULL DEFAULT NULL,
  `PERSON_TECID` BIGINT(20) NOT NULL,
  PRIMARY KEY (`TECID`),
  CONSTRAINT `FK_VOLUNTEER_AVAILABILITY_PERSON`
    FOREIGN KEY (`PERSON_TECID`)
    REFERENCES `cgdis`.`person` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB
DEFAULT CHARACTER SET = utf8;

CREATE INDEX `I_FK_VOLUNTEER_AVAILABILITY_PERSON` ON `cgdis`.`volunteer_availability` (`PERSON_TECID` ASC);


-- -----------------------------------------------------
-- Table `cgdis`.`volunteer_availability_entity`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis`.`volunteer_availability_entity` ;

CREATE TABLE IF NOT EXISTS `cgdis`.`volunteer_availability_entity` (
  `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
  `TECLOCK` BIGINT(20) NOT NULL,
  `ENTITY_TECID` BIGINT(20) NOT NULL,
  `VOLUNTEER_AVAILABILITY_TECID` BIGINT(20) NOT NULL,
  `ACCEPT_BARRACKED` CHAR(1) NULL,
  PRIMARY KEY (`TECID`),
  CONSTRAINT `FK_VOLUNTEER_AVAILABILITY_ENTITY_ENTITY`
    FOREIGN KEY (`ENTITY_TECID`)
    REFERENCES `cgdis`.`entity` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `FK_VOLUNTEER_AVAILABILITY_ENTITY_VOLUNTEER_AVAILABILITY`
    FOREIGN KEY (`VOLUNTEER_AVAILABILITY_TECID`)
    REFERENCES `cgdis`.`volunteer_availability` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB
DEFAULT CHARACTER SET = utf8;

CREATE INDEX `I_FK_VOLUNTEER_AVAILABILITY_ENTITY_VOLUNTEER_AVAILABILITY` ON `cgdis`.`volunteer_availability_entity` (`VOLUNTEER_AVAILABILITY_TECID` ASC);

CREATE INDEX `I_FK_VOLUNTEER_AVAILABILITY_ENTITY_ENTITY` ON `cgdis`.`volunteer_availability_entity` (`ENTITY_TECID` ASC);


-- -----------------------------------------------------
-- Table `cgdis`.`volunteer_availability_intervention_types`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis`.`volunteer_availability_intervention_types` ;

CREATE TABLE IF NOT EXISTS `cgdis`.`volunteer_availability_intervention_types` (
  `VOLUNTEER_AVAILABILITY_TECID` BIGINT(20) NOT NULL,
  `INTERVENTION_TYPE_TECID` BIGINT(20) NOT NULL,
  PRIMARY KEY (`VOLUNTEER_AVAILABILITY_TECID`, `INTERVENTION_TYPE_TECID`),
  CONSTRAINT `FK_VOLUNTEER_AVAILABILITY_INT_TYPES_INT_TYPE`
    FOREIGN KEY (`INTERVENTION_TYPE_TECID`)
    REFERENCES `cgdis`.`intervention_type` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `FK_VOLUNTEER_AVAILABILITY_INT_TYPES_VOLUNTEER_AV`
    FOREIGN KEY (`VOLUNTEER_AVAILABILITY_TECID`)
    REFERENCES `cgdis`.`volunteer_availability` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB
DEFAULT CHARACTER SET = utf8;

CREATE INDEX `I_FK_VOLUNTEER_AVAILABILITY_INT_TYPES_INT_TYPE` ON `cgdis`.`volunteer_availability_intervention_types` (`INTERVENTION_TYPE_TECID` ASC);

CREATE INDEX `I_FK_VOLUNTEER_AVAILABILITY_INT_TYPES_VOLUNTEER_AV` ON `cgdis`.`volunteer_availability_intervention_types` (`VOLUNTEER_AVAILABILITY_TECID` ASC);


-- -----------------------------------------------------
-- Table `cgdis`.`work_plan_model`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis`.`work_plan_model` ;

CREATE TABLE IF NOT EXISTS `cgdis`.`work_plan_model` (
  `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
  `TECLOCK` BIGINT(20) NOT NULL,
  `LABEL` VARCHAR(200) NULL DEFAULT NULL,
  `ENTITY_TECID` BIGINT(20) NOT NULL,
  PRIMARY KEY (`TECID`),
  CONSTRAINT `FK_WORK_PLAN_MODEL_ENTITY`
    FOREIGN KEY (`ENTITY_TECID`)
    REFERENCES `cgdis`.`entity` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB
DEFAULT CHARACTER SET = utf8;

CREATE UNIQUE INDEX `U_WORK_PLAN_MODEL_LABEL` ON `cgdis`.`work_plan_model` (`LABEL` ASC);

CREATE INDEX `I_FK_WORK_PLAN_MODEL_ENTITY` ON `cgdis`.`work_plan_model` (`ENTITY_TECID` ASC);


-- -----------------------------------------------------
-- Table `cgdis`.`work_plan_model_shift`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis`.`work_plan_model_shift` ;

CREATE TABLE IF NOT EXISTS `cgdis`.`work_plan_model_shift` (
  `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
  `TECLOCK` BIGINT(20) NOT NULL,
  `LABEL` VARCHAR(100) NOT NULL,
  `START_HOUR` TIME(1) NOT NULL,
  `END_HOUR` TIME(1) NOT NULL,
  `ICON` VARCHAR(100) NULL DEFAULT NULL,
  `COLOUR` VARCHAR(50) NULL DEFAULT NULL,
  `WORK_PLAN_MODEL_TECID` BIGINT(20) NOT NULL,
  PRIMARY KEY (`TECID`),
  CONSTRAINT `FK_WORK_PLAN_MODEL_SHIFT_WORK_PLAN_MODEL`
    FOREIGN KEY (`WORK_PLAN_MODEL_TECID`)
    REFERENCES `cgdis`.`work_plan_model` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB
DEFAULT CHARACTER SET = utf8;

CREATE INDEX `I_FK_WORK_PLAN_MODEL_SHIFT_WORK_PLAN_MODEL` ON `cgdis`.`work_plan_model_shift` (`WORK_PLAN_MODEL_TECID` ASC);


-- -----------------------------------------------------
-- Table `cgdis`.`work_plan_model_pattern_item`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis`.`work_plan_model_pattern_item` ;

CREATE TABLE IF NOT EXISTS `cgdis`.`work_plan_model_pattern_item` (
  `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
  `TECLOCK` BIGINT(20) NOT NULL,
  `LABEL` VARCHAR(200) NOT NULL,
  `TYPE` VARCHAR(50) NOT NULL,
  `ITEM_ORDER` INT(11) NOT NULL,
  `WORK_PLAN_PATTERN_TECID` BIGINT(20) NOT NULL,
  `WORK_PLAN_SHIFT_TECID` BIGINT(20) NOT NULL,
  `WORK_PLAN_MODEL_TECID` BIGINT(20) NOT NULL,
  PRIMARY KEY (`TECID`),
  CONSTRAINT `FK_WORK_PLAN_MODEL_PATTERN_ITEM_WORK_PLAN_MODEL`
    FOREIGN KEY (`WORK_PLAN_MODEL_TECID`)
    REFERENCES `cgdis`.`work_plan_model` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `FK_WORK_PLAN_PATTERN_ITEM_WORK_PLAN_SHIFT`
    FOREIGN KEY (`WORK_PLAN_SHIFT_TECID`)
    REFERENCES `cgdis`.`work_plan_model_shift` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB
DEFAULT CHARACTER SET = utf8;

CREATE INDEX `I_FK_WORK_PLAN_PATTERN_ITEM_WORK_PLAN_SHIFT` ON `cgdis`.`work_plan_model_pattern_item` (`WORK_PLAN_SHIFT_TECID` ASC);

CREATE INDEX `I_FK_WORK_PLAN_MODEL_PATTERN_ITEM_WORK_PLAN_MODEL` ON `cgdis`.`work_plan_model_pattern_item` (`WORK_PLAN_MODEL_TECID` ASC);


-- -----------------------------------------------------
-- Table `cgdis`.`SERVICE_PLAN_COMPLETION_STATUS`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis`.`SERVICE_PLAN_COMPLETION_STATUS` ;

CREATE TABLE IF NOT EXISTS `cgdis`.`SERVICE_PLAN_COMPLETION_STATUS` (
  `TECID` BIGINT(20) UNIQUE NOT NULL AUTO_INCREMENT,
  `TECLOCK` BIGINT(20) NOT NULL,
  `STATUS_DATE` DATE NOT NULL,
  `SERVICE_PLAN_TIME_SLOT_TECID` BIGINT(20) NOT NULL,
  `STATUS` VARCHAR(45) NOT NULL,
  PRIMARY KEY (`TECID`),
  CONSTRAINT `FK_SP_COMPLSTATUS_SP_TIMESLOT`
    FOREIGN KEY (`SERVICE_PLAN_TIME_SLOT_TECID`)
    REFERENCES `cgdis`.`service_plan_time_slot` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `I_FK_SP_COMPLSTATUS_SP_TIMESLOT` ON `cgdis`.`SERVICE_PLAN_COMPLETION_STATUS` (`SERVICE_PLAN_TIME_SLOT_TECID` ASC);

CREATE INDEX `I_SP_COMPLSTATUS_SPTS_DATE` ON `cgdis`.`SERVICE_PLAN_COMPLETION_STATUS` (`STATUS_DATE` ASC, `SERVICE_PLAN_TIME_SLOT_TECID` ASC);


SET SQL_MODE=@OLD_SQL_MODE;
SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS;
SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS;
