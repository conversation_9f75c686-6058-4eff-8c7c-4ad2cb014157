insert into SECURITY_ROLE (TECLOCK, ROL<PERSON>, PERMISS<PERSON>, ENTITY_INHERITANCE)
values (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_DASHBOARD_MANAGER', 1),
       (0, 'R<PERSON><PERSON>_ADMIN_OPERATIONAL', 'R<PERSON><PERSON>_PERMISSION_DASHBOARD_MEMBER', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_DASHBOARD_MEMBER_WORKING_PLAN', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_ADMIN', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_ADMIN_MODEL_PDS', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_ADMIN_MODEL_PDS_CREATE', 1),
       (0, 'R<PERSON><PERSON>_ADMIN_OPERATIONAL', 'R<PERSON>E_PERMISSION_ADMIN_MODEL_PDS_OPY', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_ADMIN_MODEL_PDS_DELETE', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_ADMIN_MODEL_PDS_CLOSURE', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_ADMIN_MODEL_PDS_UPDATE', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_ADMIN_MODEL_PDS_VERSION', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_ADMIN_MODEL_PDS_VERSION_CREATE', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_ADMIN_MODEL_PDS_VERSION_DELETE', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_ADMIN_MODEL_PDS_VERSION_CLOSURE', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_ADMIN_MODEL_PDS_VERSION_UPDATE', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_ADMIN_PDS', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_ADMIN_PDS_VIEW', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_ADMIN_PDS_CREATE', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_ADMIN_PDS_DELETE', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_ADMIN_PDS_CLOSURE', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_ADMIN_PDS_UPDATE', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_ADMIN_PDS_VERSION', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_ADMIN_PDS_VERSION_CREATE', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_ADMIN_PDS_VERSION_DELETE', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_ADMIN_PDS_VERSION_CLOSURE', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_ADMIN_PDS_VERSION_UPDATE', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_ADMIN_VEHICLES', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_ADMIN_VEHICLES_VIEW', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_ADMIN_ENTITIES', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_ADMIN_ENTITIES_VIEW', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_USER_MANAGEMENT', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_USER_MANAGEMENT_OP_FUNCT_MAPPING', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_USER_MANAGEMENT_OP_FUNCT_MAPPING_VIEW', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_USER_MANAGEMENT_OP_FUNCT_MAPPING_UPDATE', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_USER_MANAGEMENT_LOGAS', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_VIEW', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_UPDATE', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_PDS_LIST', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_PDS_LIST_VIEW', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_PDS_FILL_', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_PDS_FILL_VIEW', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_PDS_FILL_ADD_PERSON', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_PDS_FILL_DELETE_PRESTATION', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_VIEW', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_CREATE', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_DELETE', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_UPDATE', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_COPY', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_MY_PROFILE_VIEW', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_MY_PROFILE_GENERAL_CONTACT_UPDATE', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_MY_PROFILE_PREFERENCES_VIEW', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_MY_PROFILE_PREFERENCES_UPDATE', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_LOGAS_VIEW', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_LOGAS_CREATE', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_LOGAS_UPDATE', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_LOGAS_DELETE', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_LOGAS_COPY', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_LOGAS', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_UPDATE_SEND_ALARM_CLOCK', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_ADMIN_ENTITIES_UPDATE', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_ADMIN_EXPORT_PROFILE_VIEW', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_ADMIN_EXPORT_PROFILE', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_ADMIN_EXPORT_PROFILE_CREATE', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_ADMIN_EXPORT_PROFILE_UPDATE', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_ADMIN_EXPORT_PROFILE_DELETE', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_ADMIN_PDS_UPDATE_NATIONAL', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_ADMIN_PDS_UPDATE_ZONAL', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_PDS_FILL_EXPORT', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_PDS_FILL_VIEW_ALL_AVAILABILITIES', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_PDS_FILL_EXPORT_PDF', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_ADMIN_PDS_VEHICLE_UPDATE', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_ADMIN_PDS_VERSION_VIEW', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_CURRENT_SITUATION', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_MY_PROFILE_GENERAL_VIEW', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_MY_PROFILE_OPERATIONAL_VIEW', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_MY_PROFILE_MEDICAL_VIEW', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_GENERAL_VIEW', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_OPERATIONAL_VIEW', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_MEDICAL_VIEW', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_ADMIN_VEHICLES_ID_VIEW', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_MY_PROFILE_ACTIVITY_VIEW', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_ADMIN_MODEL_PDS_VERSION_COPY', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_ADMIN_PDS_VERSION_COPY', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_ACTIVITY_VIEW', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_PDS_FILL_SPLIT_TIMESLOT', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_EXPORT', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_PDS_FILL_ADD_PERSON_FULL_AVAILABILITY', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_PDS_FILL_SEND_FILLING', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_ADMIN_PDS_TEAM', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_ADMIN_PDS_TEAM_VIEW', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_ADMIN_PDS_TEAM_CREATE', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_ADMIN_PDS_TEAM_DELETE', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_ADMIN_PDS_TEAM_UPDATE', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_PDS_FILL_ADD_TEAM', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_ADMIN_PDS_ELS_MANUAL_SYNCHRO', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_ADMIN_VEHICLES_STATUS_VIEW', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_ACTIVITY_EXPORT_PRESTATIONS', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_GLOBAL_EXPORT_PRESTATIONS', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_PREFERENCES_UPDATE_VEHICLE', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_PAGER_VIEW', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_PAGER_UPDATE', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_VEHICLE_UPDATE_STATUS_6_VIEW', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_MY_PROFILE_ACTIVITY_EXPORT_PRESTATIONS', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_VIEW_ROLE', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_ADMIN_MODEL_PDS_UPDATE_BACKUP', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_VIEW_ALL', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_USER_MEDICAL_INFORMATION_LIST', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_ADMIN_VEHICLES_VIEW_ALL', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_PDS_FILL_ADD_PERSON_VIEW_UNAFFECTED', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_OPTIONAL_BACKUP_GROUP_MANAGEMENT', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_OPTIONAL_BACKUP_GROUP_MANAGEMENT_VIEW', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_ACTIVITY_EXPORT_PRESTATIONS_PENALTY', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_MY_PROFILE_ACTIVITY_EXPORT_PRESTATIONS_PENALTY', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_ALLOWANCE_VIEW', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_ALLOWANCE_SEARCH', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_GENERATE_API_DESCRIPTION', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_ADMIN_FUNCTION_OPERATIONAL', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_ADMIN_FUNCTION_OPERATIONAL_VIEW', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_ADMIN_FUNCTION_OPERATIONAL_CREATE', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_ADMIN_FUNCTION_OPERATIONAL_DELETE', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_ADMIN_FUNCTION_OPERATIONAL_UPDATE', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_ADMIN_FUNCTION_OPERATIONAL_VALIDATE', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_ADMIN_FUNCTION_OPERATIONAL_CLOSE', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_ADMIN_POSITION_TEMPLATE', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_ADMIN_POSITION_TEMPLATE_CREATE', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_ADMIN_POSITION_TEMPLATE_DELETE', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_ADMIN_POSITION_TEMPLATE_CLOSURE', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_ADMIN_POSITION_TEMPLATE_UPDATE', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_ADMIN_POSITION_TEMPLATE_VERSION', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_ADMIN_POSITION_TEMPLATE_VERSION_CREATE', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_ADMIN_POSITION_TEMPLATE_VERSION_DELETE', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_ADMIN_POSITION_TEMPLATE_VERSION_UPDATE', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_ADMIN_POSITION_TEMPLATE_VERSION_CLOSURE', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_USER_MANAGEMENT_OP_FUNCT', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_USER_MANAGEMENT_OP_FUNCT_VIEW', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_USER_MANAGEMENT_OP_FUNCT_UPDATE', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_VIEW_FUNCTION_OPERATIONAL', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_MY_PROFILE_VIEW_FUNCTION_OPERATIONAL', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_PDS_STATE_VIEW', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_ADMIN_PUBLIC_HOLIDAY', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_ADMIN_PUBLIC_HOLIDAY_VIEW', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_ADMIN_PUBLIC_HOLIDAY_CREATE', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_ADMIN_PUBLIC_HOLIDAY_UPDATE', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_ADMIN_PUBLIC_HOLIDAY_DELETE', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_USER_RIGHTS', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_USER_RIGHTS_VIEW', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_PERMAMONITOR', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_PERMAMONITOR_ADMIN_VIEW', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_PERMAMONITOR_ADMIN_UPDATE_POJ', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_PERMAMONITOR_ADMIN_ACCESS_CONFIG', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_PERMAMONITOR_ADMIN_UPDATE_DEPLOYMENT_PLAN', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_PERMAMONITOR_ADMIN_UPDATE_DEPLOYMENT_PLAN_CONFIG', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_PERMAMONITOR_ADMIN_CREATE_DEPLOYMENT_PLAN', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_SYSTEM_MANAGEMENT', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_ADMIN_GENERAL_MESSAGE', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_ADMIN_GENERAL_MESSAGE_VIEW', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_ADMIN_GENERAL_MESSAGE_CREATE', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_ADMIN_GENERAL_MESSAGE_UPDATE', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_ADMIN_GENERAL_MESSAGE_DELETE', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_112_BACKUP', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_112_BACKUP_VIEW', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_112_BACKUP_ACTIVATE', 1)
