insert into SECURITY_ROLE (SECURITY_ROLE.TECLOCK, ROL<PERSON>, PERMISSION, ENTITY_INHERITANCE)
values (0, 'ROLE_DCO_DATA_ELS', 'ROLE_PERMISSION_ADMIN_AUDIT_PORTAIL', 1),
       (0, 'ROLE_DCO_DATA_ELS', 'ROLE_PERMISSION_ADMIN_AUDIT_PERMAMONITOR', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_ADMIN_AUDIT_PORTAIL', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_ADMIN_AUDIT_PORTAIL_PRESTATIONS', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_ADMIN_AUDIT_PORTAIL_SERVICE_PLAN', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_ADMIN_AUDIT_PORTAIL_SP_MODEL', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'R<PERSON>E_PERMISSION_ADMIN_AUDIT_PORTAIL_ALLOWANCE', 1),
       (0, 'ROLE_ADMIN_OPERATIONAL', 'ROLE_PERMISSION_ADMIN_AUDIT_PERMAMONITOR', 1),
       (0, 'ROLE_ADMIN', 'ROLE_PERMISSION_ADMIN_AUDIT_PORTAIL', 1),
       (0, 'ROLE_ADMIN', 'ROLE_PERMISSION_ADMIN_AUDIT_PORTAIL_PRESTATIONS', 1),
       (0, 'ROLE_ADMIN', 'ROLE_PERMISSION_ADMIN_AUDIT_PORTAIL_SERVICE_PLAN', 1),
       (0, 'ROLE_ADMIN', 'ROLE_PERMISSION_ADMIN_AUDIT_PORTAIL_SP_MODEL', 1),
       (0, 'ROLE_ADMIN', 'ROLE_PERMISSION_ADMIN_AUDIT_PORTAIL_ALLOWANCE', 1),
       (0, 'ROLE_ADMIN', 'ROLE_PERMISSION_ADMIN_AUDIT_PERMAMONITOR', 1),
       (0, 'ROLE_ADMIN', 'ROLE_PERMISSION_ADMIN_AUDIT_PORTAIL_LOGAS', 1)
;
;


