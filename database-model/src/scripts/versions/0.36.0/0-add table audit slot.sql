-- -----------------------------------------------------
-- Table `cgdis`.`AUDIT_SLOT`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis_portal_db`.`AUDIT_SLOT` ;

CREATE TABLE IF NOT EXISTS `cgdis_portal_db`.`AUDIT_SLOT` (
  `TECID` BIGINT(20) NOT NULL,
  `START_DATETIME` DATETIME NULL,
  `END_DATETIME` DATETIME NULL,
  `TARGET_DATETIME` DATETIME NULL,
  PRIMARY KEY (`TECID`),
  CONSTRAINT `fk_AUDIT_SLOT_AUDIT1`
    FOREIGN KEY (`TECID`)
    REFERENCES `cgdis_portal_db`.`AUDIT` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
;