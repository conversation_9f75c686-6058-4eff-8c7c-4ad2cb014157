
-- -----------------------------------------------------
-- Table `cgdis_portal_db`.`APTITUDE`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis_portal_db`.`APTITUDE` ;

CREATE TABLE IF NOT EXISTS `cgdis_portal_db`.`APTITUDE` (
  `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
  `TECLOCK` BIGINT(20) NOT NULL,
  `EXTERNAL_ID` INT NOT NULL,
  `NAME` VARCHAR(20) NOT NULL,
  `DESCRIPTION` VARCHAR(100) NULL,
  `PERIOD` INT NULL,
  PRIMARY KEY (`TECID`))
ENGINE = InnoDB
DEFAULT CHARACTER SET = utf8;

CREATE UNIQUE INDEX `LABEL_UNIQUE` ON `cgdis_portal_db`.`APTITUDE` (`EXTERNAL_ID` ASC);

CREATE UNIQUE INDEX `CODE_UNIQUE` ON `cgdis_portal_db`.`APTITUDE` (`NAME` ASC);

CREATE UNIQUE INDEX `IX_OPERATIONAL_OCCUPATION_CODE` ON `cgdis_portal_db`.`APTITUDE` (`NAME` ASC);

CREATE INDEX `IX_OPERATION_OCCUPATION_TYPE` ON `cgdis_portal_db`.`APTITUDE` (`DESCRIPTION` ASC);


-- -----------------------------------------------------
-- Table `cgdis_portal_db`.`RESTRICTION`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis_portal_db`.`RESTRICTION` ;

CREATE TABLE IF NOT EXISTS `cgdis_portal_db`.`RESTRICTION` (
  `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
  `TECLOCK` BIGINT(20) NOT NULL,
  `EXTERNAL_ID` INT NOT NULL,
  `TYPE` VARCHAR(20) NOT NULL,
  `DESCRIPTION` VARCHAR(100) NULL,
  PRIMARY KEY (`TECID`))
ENGINE = InnoDB
DEFAULT CHARACTER SET = utf8;

CREATE UNIQUE INDEX `LABEL_UNIQUE` ON `cgdis_portal_db`.`RESTRICTION` (`EXTERNAL_ID` ASC);

CREATE UNIQUE INDEX `CODE_UNIQUE` ON `cgdis_portal_db`.`RESTRICTION` (`TYPE` ASC);

CREATE UNIQUE INDEX `IX_OPERATIONAL_OCCUPATION_CODE` ON `cgdis_portal_db`.`RESTRICTION` (`TYPE` ASC);

CREATE INDEX `IX_OPERATION_OCCUPATION_TYPE` ON `cgdis_portal_db`.`RESTRICTION` (`DESCRIPTION` ASC);


-- -----------------------------------------------------
-- Table `cgdis_portal_db`.`PERSON_APTITUDE`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis_portal_db`.`PERSON_APTITUDE` ;

CREATE TABLE IF NOT EXISTS `cgdis_portal_db`.`PERSON_APTITUDE` (
  `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
  `TECLOCK` BIGINT(20) NOT NULL,
  `APTITUDE_TECID` BIGINT(20) NOT NULL,
  `EXTERNAL_ID` INT NOT NULL,
  `START_DATE` DATE NOT NULL,
  `END_DATE` DATE NULL,
  `VALIDITY` CHAR(1) NOT NULL,
  `USER_COMMENT` VARCHAR(200) NULL,
  `PERSON_TECID` BIGINT(20) NOT NULL,
  PRIMARY KEY (`TECID`),
  CONSTRAINT `FK_PERSON_APTITUDE_APTITUDE`
    FOREIGN KEY (`APTITUDE_TECID`)
    REFERENCES `cgdis_portal_db`.`APTITUDE` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `FK_PERSON_APTITUDE_PERSON`
    FOREIGN KEY (`PERSON_TECID`)
    REFERENCES `cgdis_portal_db`.`PERSON` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB
DEFAULT CHARACTER SET = utf8;

CREATE UNIQUE INDEX `UX_PERSON_APTITUDE_EXTERNAL` ON `cgdis_portal_db`.`PERSON_APTITUDE` (`EXTERNAL_ID` ASC);

CREATE INDEX `UX_PERSON_APTITUDE_DATE` ON `cgdis_portal_db`.`PERSON_APTITUDE` (`START_DATE` ASC);

CREATE INDEX `FK_PERSON_APTITUDE_PERSON_idx` ON `cgdis_portal_db`.`PERSON_APTITUDE` (`PERSON_TECID` ASC);


-- -----------------------------------------------------
-- Table `cgdis_portal_db`.`PERSON_RESTRICTION`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis_portal_db`.`PERSON_RESTRICTION` ;

CREATE TABLE IF NOT EXISTS `cgdis_portal_db`.`PERSON_RESTRICTION` (
  `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
  `TECLOCK` BIGINT(20) NOT NULL,
  `RESTRICTION_TECID` BIGINT(20) NOT NULL,
  `EXTERNAL_ID` INT NOT NULL,
  `START_DATE` DATE NOT NULL,
  `USER_COMMENT` VARCHAR(200) NULL,
  `PERSON_TECID` BIGINT(20) NOT NULL,
  PRIMARY KEY (`TECID`),
  CONSTRAINT `FK_PERSON_RESTRICTION_RESTRICTION`
    FOREIGN KEY (`RESTRICTION_TECID`)
    REFERENCES `cgdis_portal_db`.`RESTRICTION` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `FK_PERSON_RESTRICTION_PERSON0`
    FOREIGN KEY (`PERSON_TECID`)
    REFERENCES `cgdis_portal_db`.`PERSON` (`TECID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB
DEFAULT CHARACTER SET = utf8;

CREATE UNIQUE INDEX `UX_PERSON_RESTRICTION_EXTERNAL` ON `cgdis_portal_db`.`PERSON_RESTRICTION` (`EXTERNAL_ID` ASC);

CREATE INDEX `IX_PERSON_RESTRICTION__TYPE` ON `cgdis_portal_db`.`PERSON_RESTRICTION` (`START_DATE` ASC);

CREATE INDEX `FK_PERSON_RESTRICTION__PERSON_idx` ON `cgdis_portal_db`.`PERSON_RESTRICTION` (`PERSON_TECID` ASC);

CREATE INDEX `UX_PERSON_RESTRICTION_RESTRICTION_idx` ON `cgdis_portal_db`.`PERSON_RESTRICTION` (`RESTRICTION_TECID` ASC);