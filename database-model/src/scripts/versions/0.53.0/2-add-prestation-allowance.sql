create table cgdis_portal_db.PRESTATION_ALLOWANCE
(
    TECID          bigint auto_increment
        primary key,
    TECLOCK        bigint       not null default 0,
    PERSON_TECID        bigint       not null,
    ALLOWANCE_DATE    date         not null,
    ALLOWANCE         decimal(10,4) not null,
    ALLOWANCE_VERSION varchar(5)   not null,
    constraint I_PRESTATION_ALLOWANCE_PERSON_DATE_U
        unique (PERSON_TECID, ALLOWANCE_DATE)


    );

create index I_PRESTATION_ALLOWANCE_VERSION_IDX
    on PRESTATION_ALLOWANCE (ALLOWANCE_VERSION);

alter table cgdis_portal_db.PRESTATION_ALLOWANCE
add constraint FK_PRESTATION_ALLOWANCE_PERSON_TECID
    foreign key (PERSON_TECID) references cgdis_portal_db.PERSON (TECID);


create table cgdis_portal_db.PRESTATION_ALLOWANCE_ENTITY
(
    PRESTATION_ALLOWANCE_TECID        bigint  not null      ,
    ENTITY_TECID        bigint       not null
  );

  alter table PRESTATION_ALLOWANCE_ENTITY
    add constraint PRESTATION_ALLOWANCE_ENTITY_PK
        primary key (PRESTATION_ALLOWANCE_TECID, ENTITY_TECID);

alter table PRESTATION_ALLOWANCE_ENTITY
    add constraint PRESTATION_ALLOWANCE_ENTITY_ALLOWANCE__FK
        foreign key (PRESTATION_ALLOWANCE_TECID) references PRESTATION_ALLOWANCE (TECID)
            on delete cascade;


alter table PRESTATION_ALLOWANCE_ENTITY
    add constraint PRESTATION_ALLOWANCE_ENTITY__ENTITY_FK
        foreign key (ENTITY_TECID) references ENTITY (TECID);





create table cgdis_portal_db.PRESTATION_ALLOWANCE_DETAIL_V1
(
    TECID          bigint auto_increment
        primary key,
    PRESTATION_ALLOWANCE_TECID        bigint       ,
    ENTITY_TECID        bigint       not null,
    ALLOWANCE         decimal(10,4) not null,
    constraint I_PRESTATION_ALLOWANCE_DETAIL_V1_ENTITY_U
        unique (ENTITY_TECID, PRESTATION_ALLOWANCE_TECID)


    );

alter table cgdis_portal_db.PRESTATION_ALLOWANCE_DETAIL_V1
add constraint FK_PRESTATION_ALLOWANCE_DETAIL_V1_PARENT
    foreign key (PRESTATION_ALLOWANCE_TECID) references cgdis_portal_db.PRESTATION_ALLOWANCE (TECID)
    on delete cascade;



create table cgdis_portal_db.PRESTATION_ALLOWANCE_DETAIL_V10
(
    TECID          bigint auto_increment
        primary key,
    PRESTATION_ALLOWANCE_TECID        bigint       ,
    ENTITY_TECID        bigint       not null,
    ALLOWANCE         decimal(10,4) not null,
    constraint I_PRESTATION_ALLOWANCE_DETAIL_V10_ENTITY_U
        unique (ENTITY_TECID, PRESTATION_ALLOWANCE_TECID)


    );

alter table cgdis_portal_db.PRESTATION_ALLOWANCE_DETAIL_V10
add constraint FK_PRESTATION_ALLOWANCE_DETAIL_V10_PARENT
    foreign key (PRESTATION_ALLOWANCE_TECID) references cgdis_portal_db.PRESTATION_ALLOWANCE (TECID);
