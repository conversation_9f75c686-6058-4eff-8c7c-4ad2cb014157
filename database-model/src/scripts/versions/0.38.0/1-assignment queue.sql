-- -----------------------------------------------------
-- Table `cgdis`.`ASSIGNMENT_QUEUE`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis_portal_db`.`ASSIGNMENT_QUEUE` ;

CREATE TABLE  `cgdis_portal_db`.`ASSIGNMENT_QUEUE` (
                                                          `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
                                                          `TECLOCK` BIGINT(20) NOT NULL,
                                                          `CREATION_DATE` DATETIME NOT NULL,
                                                          `ASSIGNMENT_TECID` BIGINT(20) NOT NULL,
                                                          PRIMARY KEY (`TECID`),
                                                          CONSTRAINT `fk_ASSIGNMENT_QUEUE_assignment1`
                                                              FOREI<PERSON><PERSON> KEY (`ASSIGNMENT_TECID`)
                                                                  REFERENCES `cgdis_portal_db`.`ASSIGNMENT` (`TECID`)
                                                                  ON DELETE NO ACTION
                                                                  ON UPDATE NO ACTION)
  ;

CREATE INDEX `fk_ASSIGNMENT_QUEUE_assignment1_idx` ON `cgdis_portal_db`.`ASSIGNMENT_QUEUE` (`ASSIGNMENT_TECID` ASC);
