DROP TABLE IF EXISTS `cgdis_portal_db`.`POSITION_TEMPLATE` ;

CREATE TABLE IF NOT EXISTS `cgdis_portal_db`.`POSITION_TEMPLATE` (
                                                                     `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
                                                                     `T<PERSON><PERSON><PERSON><PERSON>` BIGINT(20) NOT NULL,
                                                                     `LABEL` VARCHAR(255) NULL,
                                                                     `PORTAL_LABEL` VARCHAR(255) NULL,
                                                                     `INTERVENTION_TYPE_ID` BIGINT(20) NOT NULL,
                                                                     PRIMARY KEY (`TECID`),
                                                                     CONSTRAINT `fk_POSITION_TEMPLATE_intervention_type1`
                                                                         FOREIGN KEY (`INTERVENTION_TYPE_ID`)
                                                                             REFERENCES `cgdis_portal_db`.`INTERVENTION_TYPE` (`TECID`)
                                                                             ON DELETE NO ACTION
                                                                             ON UPDATE NO ACTION)
    ENGINE = InnoDB;

CREATE INDEX `fk_POSITION_TEMPLATE_intervention_type1_idx` ON `cgdis_portal_db`.`POSITION_TEMPLATE` (`INTERVENTION_TYPE_ID` ASC);


-- -----------------------------------------------------
-- Table `cgdis_portal_db`.`POSITION_TEMPLATE_VERSION`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis_portal_db`.`POSITION_TEMPLATE_VERSION` ;

CREATE TABLE IF NOT EXISTS `cgdis_portal_db`.`POSITION_TEMPLATE_VERSION` (
                                                                             `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
                                                                             `TECLOCK` BIGINT(20) NOT NULL,
                                                                             `LABEL` VARCHAR(255) NULL,
                                                                             `STARTDATE` DATE NOT NULL,
                                                                             `ENDDATE` DATE NULL,
                                                                             `POSITION_TEMPLATE_ID` BIGINT(20) NOT NULL,
                                                                             PRIMARY KEY (`TECID`),
                                                                             CONSTRAINT `fk_POSITION_TEMPLATE_VERSION_POSITION_TEMPLATE1`
                                                                                 FOREIGN KEY (`POSITION_TEMPLATE_ID`)
                                                                                     REFERENCES `cgdis_portal_db`.`POSITION_TEMPLATE` (`TECID`)
                                                                                     ON DELETE NO ACTION
                                                                                     ON UPDATE NO ACTION)
    ENGINE = InnoDB;

CREATE INDEX `fk_POSITION_TEMPLATE_VERSION_POSITION_TEMPLATE1_idx` ON `cgdis_portal_db`.`POSITION_TEMPLATE_VERSION` (`POSITION_TEMPLATE_ID` ASC);


-- -----------------------------------------------------
-- Table `cgdis_portal_db`.`POSITION_TEMPLATE_VERSION_FUNCTION`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `cgdis_portal_db`.`POSITION_TEMPLATE_VERSION_FUNCTION` ;

CREATE TABLE IF NOT EXISTS `cgdis_portal_db`.`POSITION_TEMPLATE_VERSION_FUNCTION` (
                                                                                      `TECID` BIGINT(20) NOT NULL AUTO_INCREMENT,
                                                                                      `TECLOCK` BIGINT(20) NULL,
                                                                                      `FUNCTION_OPERATIONAL_TECID` BIGINT(20) NOT NULL,
                                                                                      `POSITION_TEMPLATE_VERSION_TECID` BIGINT(20) NOT NULL,
                                                                                      `TYPE` VARCHAR(45) NOT NULL,
                                                                                      PRIMARY KEY (`TECID`),
                                                                                      CONSTRAINT `fk_POSITION_TEMPLATE_VERSION_FUNCTION_FUNCTION_OPERATIONAL1`
                                                                                          FOREIGN KEY (`FUNCTION_OPERATIONAL_TECID`)
                                                                                              REFERENCES `cgdis_portal_db`.`FUNCTION_OPERATIONAL` (`TECID`)
                                                                                              ON DELETE NO ACTION
                                                                                              ON UPDATE NO ACTION,
                                                                                      CONSTRAINT `fk_POSITION_TEMPLATE_VERSION_FUNCTION_POSITION_TEMPLATE_VERSI1`
                                                                                          FOREIGN KEY (`POSITION_TEMPLATE_VERSION_TECID`)
                                                                                              REFERENCES `cgdis_portal_db`.`POSITION_TEMPLATE_VERSION` (`TECID`)
                                                                                              ON DELETE NO ACTION
                                                                                              ON UPDATE NO ACTION)
    ENGINE = InnoDB;

CREATE INDEX `fk_POSITION_TEMPLATE_VERSION_FUNCTION_FUNCTION_OPERATIONAL1_idx` ON `cgdis_portal_db`.`POSITION_TEMPLATE_VERSION_FUNCTION` (`FUNCTION_OPERATIONAL_TECID` ASC);

CREATE INDEX `fk_POSITION_TEMPLATE_VERSION_FUNCTION_POSITION_TEMPLATE_VER_idx` ON `cgdis_portal_db`.`POSITION_TEMPLATE_VERSION_FUNCTION` (`POSITION_TEMPLATE_VERSION_TECID` ASC);
