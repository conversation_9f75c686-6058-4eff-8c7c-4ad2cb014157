INSERT INTO `cgdis_portal_db`.`TRANSLATION` (`TECLOCK`, `DESCRIPTION`, `LANGUAGE`, `TRANSLATION`, `TRANSLATION_KEY`)
VALUES (0, 'operational occupations', 'fr', 'Chef de peloton stagiaire', 'i18n.data.operational_occupations.CHPELOTSTAG')

INSERT INTO `cgdis_portal_db`.`TRANSLATION` (`TECLOCK`, `DESCRIPTION`, `LANGUAGE`, `TRANSLATION`, `TRANSLATION_KEY`)
VALUES (0, 'operational occupations', 'fr', 'Chef de section stagiaire', 'i18n.data.operational_occupations.CHSECTSTAG')

INSERT INTO `cgdis_portal_db`.`TRANSLATION` (`TECLOCK`, `DESCRIPTION`, `LANGUAGE`, `TRANSLATION`, `TRANSLATION_KEY`)
VALUES (0, 'operational occupations', 'en', '<PERSON> de peloton stagiaire', 'i18n.data.operational_occupations.CHPELOTSTAG')

INSERT INTO `cgdis_portal_db`.`TRANSLATION` (`TECLOCK`, `DESCRIPTION`, `LANGUAGE`, `TRANSLATION`, `TRANSLATION_KEY`)
VALUES (0, 'operational occupations', 'en', 'Chef de section stagiaire', 'i18n.data.operational_occupations.CHSECTSTAG')

INSERT INTO `cgdis_portal_db`.`OPERATIONAL_FUNCTION` (`TECLOCK`, `LABEL`, `PARTIAL_FULFILL`)
VALUES (0, 'COM Chef de section stagiaire', '0')

INSERT INTO `cgdis_portal_db`.`OPERATIONAL_FUNCTION` (`TECLOCK`, `LABEL`, `PARTIAL_FULFILL`)
VALUES (0, 'COM Chef de peloton stagiaire', '0')


