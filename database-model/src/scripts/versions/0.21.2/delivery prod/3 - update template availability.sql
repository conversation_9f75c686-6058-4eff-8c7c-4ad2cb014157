update `cgdis_portal_db`.`NOTIFICATION_MAIL`
set BODY='<!DOCTYPE html>
<html><head>
    <meta charset="utf-8">
    <!-- utf-8 works for most cases -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- Forcing initial-scale shouldn''t be necessary -->
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- Use the latest (edge) version of IE rendering engine -->
    <title>EmailTemplate-Responsive</title>
    <!-- The title tag shows in email notifications, like Android 4.4. -->
    <!-- Please use an inliner tool to convert all CSS to inline as inpage or external CSS is removed by email clients -->
    <!-- important in CSS is used to prevent the styles of currently inline CSS from overriding the ones mentioned in media queries when corresponding screen sizes are encountered -->

    <!-- CSS Reset -->
    <style type="text/css">
/* What it does: Remove spaces around the email design added by some email clients. */
      /* Beware: It can remove the padding / margin and add a background color to the compose a reply window. */
html,  body {
	margin: 0 !important;
	padding: 0 !important;
	height: 100% !important;
	width: 100% !important;
}
/* What it does: Stops email clients resizing small text. */
* {
	-ms-text-size-adjust: 100%;
	-webkit-text-size-adjust: 100%;
}
/* What it does: Forces Outlook.com to display emails full width. */
.ExternalClass {
	width: 100%;
}
/* What is does: Centers email on Android 4.4 */
div[style*="margin: 16px 0"] {
	margin: 0 !important;
}
/* What it does: Stops Outlook from adding extra spacing to tables. */
table,  td {
	mso-table-lspace: 0pt !important;
	mso-table-rspace: 0pt !important;
}
/* What it does: Fixes webkit padding issue. Fix for Yahoo mail table alignment bug. Applies table-layout to the first 2 tables then removes for anything nested deeper. */
table {
	border-spacing: 0 !important;
	border-collapse: collapse !important;
	table-layout: fixed !important;
	margin: 0 auto !important;
}
table table table {
	table-layout: auto;
}

.medico_table table{
  border-collapse: collapse;
  width: 100%;
  table-layout: fixed;
}

.medico_table table, .medico_table th, .medico_table td {
  border: 1px solid #B1B1B1;
}

.medico_table th, .medico_table td{
	padding: 5px;

}
img {
	width: 1.5rem;
	height: auto;
}

/* What it does: Overrides styles added when Yahoo''s auto-senses a link. */
.yshortcuts a {
	border-bottom: none !important;
}
/* What it does: Another work-around for iOS meddling in triggered links. */
a[x-apple-data-detectors] {
	color: inherit !important;
}
</style>

    <!-- Progressive Enhancements -->
    <style type="text/css">

        /* What it does: Hover styles for buttons */
        .button-td,
        .button-a {
            transition: all 100ms ease-in;
        }
        .button-td:hover,
        .button-a:hover {
            background: #555555 !important;
            border-color: #555555 !important;
        }

        /* Media Queries */
        @media screen and (max-width: 600px) {

            .email-container {
                width: 100% !important;
            }

            /* What it does: Forces elements to resize to the full width of their container. Useful for resizing images beyond their max-width. */
            .fluid,
            .fluid-centered {
                max-width: 100% !important;
                height: auto !important;
                margin-left: auto !important;
                margin-right: auto !important;
            }
            /* And center justify these ones. */
            .fluid-centered {
                margin-left: auto !important;
                margin-right: auto !important;
            }

            /* What it does: Forces table cells into full-width rows. */
            .stack-column,
            .stack-column-center {
                display: block !important;
                width: 100% !important;
                max-width: 100% !important;
                direction: ltr !important;
            }
            /* And center justify these ones. */
            .stack-column-center {
                text-align: center !important;
            }

            /* What it does: Generic utility class for centering. Useful for images, buttons, and nested tables. */
            .center-on-narrow {
                text-align: center !important;
                display: block !important;
                margin-left: auto !important;
                margin-right: auto !important;
                float: none !important;
            }
            table.center-on-narrow {
                display: inline-block !important;
            }

        }

    </style>
    </head>
    <body bgcolor="#ffffff" width="100%" style="margin: 0;" yahoo="yahoo">
    <table bgcolor="#ffffff" cellpadding="0" cellspacing="0" border="0" height="100%" width="100%" style="border-collapse:collapse;">
      <tbody><tr>
        <td><center style="width: 100%;">

            <!-- Email Header : BEGIN -->
            <table align="center" width="800" class="email-container">
            <tbody><tr>
                <td style="padding-top: 10px; padding-bottom: 5px; text-align: center;" bgcolor="#e30613">
					<img src="https://112.public.lu/dam-assets/pictures/divers/headerportailcgdis/pcgdis-mail-header.png" alt="alt_text" border="0" style="width: 100%; height: auto;">

				</td>
              </tr>
          </tbody></table>
            <!-- Email Header : END -->

            <!-- Email Body : BEGIN -->
            <table cellspacing="0" cellpadding="0" border="0" align="center" bgcolor="#ffffff" width="800" class="email-container" style=" border-left: 1px solid #ABABAB; border-right: 1px solid #ABABAB; -webkit-box-shadow: 0px 0px 106px -21px rgba(0,0,0,0.39);
-moz-box-shadow: 0px 0px 106px -21px rgba(0,0,0,0.39);
box-shadow: 0px 0px 106px -21px rgba(0,0,0,0.39);">


            <!-- 1 Column Text : BEGIN -->
            <tbody><tr>
                <td style="padding: 40px; text-align: left; font-family: arial; font-size: 15px; mso-height-rule: exactly; line-height: 20px; color: #12326e;">
					<p>Bonjour ,</p>
					<p>Une nouvelle personne est disponible dans votre centre:  <span th:text="${context.person.firstName}"></span> <span th:text="${context.person.lastName}"></span></p>
					<p>Voici les d&eacute;tails de la disponibilit&eacute; : </p>
					<table class="medico_table" width="100%" style=''border: 1px solid #B1B1B1;''>
							<tbody><tr>
								<th width="20%" style=''font-size:10pt;font-weight: 550;border: 1px solid #B1B1B1;padding: 5px;font-family:"Arial",sans-serif;text-align: left;color:#12326E''>Entit&eacute(s)</th>
								<th width="9%" style=''font-size:10pt;font-weight: 550;border: 1px solid #B1B1B1;padding: 5px;font-family:"Arial",sans-serif;text-align: left;color:#12326E''>Casern&eacute</th>
								<th width="20%" style=''font-size:10pt;font-weight: 550;border: 1px solid #B1B1B1;padding: 5px;font-family:"Arial",sans-serif;text-align: left;color:#12326E''>Interventions</th>
								<th width="9%" style=''font-size:10pt;font-weight: 550;border: 1px solid #B1B1B1;padding: 5px;font-family:"Arial",sans-serif;text-align: left;color:#12326E''>Type</th>
								<th width="21%" style=''font-size:10pt;font-weight: 550;border: 1px solid #B1B1B1;padding: 5px;font-family:"Arial",sans-serif;text-align: left;color:#12326E''>Début</th>
								<th width="21%" style=''font-size:10pt;font-weight: 550;border: 1px solid #B1B1B1;padding: 5px;font-family:"Arial",sans-serif;text-align: left;color:#12326E''>Fin</th>
							</tr>
							<tr >
								<td width="20%" style=''font-size:10pt;padding: 5px;border: 1px solid #B1B1B1;font-family:"Arial",sans-serif;color:#12326E''>
								<ul style="margin: 0;padding: 0;list-style-type: none;">
									<li th:each="oneEntity: ${context.availability.entitiesAvailability}" style="margin: 0;padding: 0;list-style-type: none;">
										<span th:text="${oneEntity.entity.name}"></span>
									</li>
								</ul>
								</td>
								<td width="9%" style=''font-size:10pt;padding: 5px;border: 1px solid #B1B1B1;font-family:"Arial",sans-serif;color:#12326E''>
								<ul style="margin: 0;padding: 0;list-style-type: none;">
									<li th:each="oneEntity: ${context.availability.entitiesAvailability}" style="margin: 0;padding: 0;list-style-type: none;">
										<span th:text="${oneEntity.getAcceptBarracked() ? ''✔'' : ''✘''}"></span>
									</li>
								</ul>
								</td>
								<td width="20%" style=''font-size:10pt;padding: 5px;border: 1px solid #B1B1B1;font-family:"Arial",sans-serif;color:#12326E''>
								<ul style="margin: 0;padding: 0;list-style-type: none;">
								<li th:each="oneIntervention: ${context.availability.interventionTypes}" style="margin: 0;padding: 0;list-style-type: none;">
									<span th:if="${ oneIntervention.label==''fire''}" >Incendie/Sauvetage</span>
									<span th:if="${ oneIntervention.label==''ambulance''}" >Secours &agrave; personnes</span>
									<span th:if="${ oneIntervention.label==''others''}" >Autres</span>
								</li>
								</ul>
								</td>
								<td width="9%" style=''font-size:10pt;padding: 5px;border: 1px solid #B1B1B1;font-family:"Arial",sans-serif;color:#12326E''><span th:text="${context.availability.type.name() == ''PROFESSIONAL'' ? ''Pro'' : ''Vol''}"></span></td>
								<td width="20%" style=''font-size:10pt;padding: 5px;border: 1px solid #B1B1B1;font-family:"Arial",sans-serif;color:#12326E''><span th:text="${#temporals.format(context.availability.startDateTime, ''dd/MM/yyyy HH:mm'')}"></span></td>
								<td width="21%" style=''font-size:10pt;padding: 5px;border: 1px solid #B1B1B1;font-family:"Arial",sans-serif;color:#12326E''><span th:text="${#temporals.format(context.availability.endDateTime, ''dd/MM/yyyy HH:mm'')}"></span></td>
							</tr>

						</tbody></table>
					<br/>
                </td>
              </tr>
            <!-- 1 Column Text : BEGIN -->


          </tbody></table>
            <!-- Email Body : END -->

            <!-- Email Footer : BEGIN -->
            <table align="center" width="800" bgcolor="#e30613" class="email-container">
            <tbody><tr>
                <td style="padding: 20px 10px;font-size: 12px; font-family: arial; mso-height-rule: exactly; line-height:18px; text-align: left; color: #ffffff;">Corps Grand-Ducal d''Incendie et de Secours</td>
				<td style="padding: 20px 10px;font-size: 12px; font-family: sans-serif; mso-height-rule: exactly; line-height:18px; text-align: right; color: #888888;">
					<a href="https://facebook.com/cgdis112" target="_blank"><img src="https://112.public.lu/dam-assets/pictures/divers/headerportailcgdis/pcgdis-mail-facebook-white.png/_jcr_content/renditions/thumb-mdpi.png" width="24" height="24"></img></a>&nbsp;
					<a href="https://twitter.com/cgdislux" target="_blank"><img src="https://112.public.lu/dam-assets/pictures/divers/headerportailcgdis/pcgdis-mail-twitter-white.png/_jcr_content/renditions/thumb-mdpi.png" width="24" height="24"></img></a>&nbsp;
					<a href="https://www.youtube.com/channel/UCnlgFPDkPktnS0z5OEZjiWQ" target="_blank"><img src="https://112.public.lu/dam-assets/pictures/divers/headerportailcgdis/pcgdis-mail-youtube-white.png/_jcr_content/renditions/thumb-mdpi.png" width="24" height="24"></img></a>&nbsp;
					<a href="https://www.instagram.com/cgdislux" target="_blank"><img src="https://112.public.lu/dam-assets/pictures/divers/headerportailcgdis/pcgdis-mail-insta-white.png/_jcr_content/renditions/thumb-mdpi.png" width="24" height="24"></img></a>&nbsp;
					<a href="https://www.cgdis.lu" target="_blank"><img src="https://112.public.lu/dam-assets/pictures/divers/headerportailcgdis/pcgdis-mail-web-white.png/_jcr_content/renditions/thumb-mdpi.png" width="24" height="24"></img></a>
              </td></tr>
          </tbody></table>
            <!-- Email Footer : END -->

          </center></td>
      </tr>
    </tbody></table>


</body></html>'
where TYPE='AVAILABILITY_CREATION';