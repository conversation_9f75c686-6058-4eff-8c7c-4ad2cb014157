CREATE TABLE IF NOT EXISTS `cgdis_portal_db`.`AUDIT_PERM_CONFIG_DPCE_COPY`
(
    `TECID`                      BIGINT(20)  NOT NULL,
    `PERM_DEPLOYMENT_PLAN_TECID` BIGINT(20)  NULL,
    `PERM_CATEGORY_TECID`        BIGINT(20)  NULL,
    `PERM_CATEGORY_NAME`         VARCHAR(30) NULL,
    `ENTITY_TECID`               BIGINT(20)  NULL,
    `TO_ENTITY_TECID`            BIGINT(20)  NULL,
    `START_HOUR`                 smallint    NULL,
    `END_HOUR`                   smallint    NULL,
    `TO_START_HOUR`              smallint    NULL,
    `TO_END_HOUR`                smallint    NULL,
    PRIMARY KEY (`TECID`),
    CONSTRAINT `FK_AUDIT_PERM_CONFIG_DPCE_COPY_AUDIT` FOREIGN KEY (`TECID`) REFERENCES `cgdis_portal_db`.`AUDIT` (`TECID`) ON DELETE NO ACTION ON UPDATE NO ACTION
);
