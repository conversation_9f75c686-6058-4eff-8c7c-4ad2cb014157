--
--
--
--DELIMITER $$
--
--CREATE PROCEDURE `getPersonWithPrestationDurationFillingRange`(
--IN startRange datetime ,
--IN endRange datetime ,
--IN personTecids varchar(2000)
--)
--BEGIN
--  select person_tecid -- INTO personTecids
--from (
--select tecid,
--             PRESTATION_START_DATE,
--             START_DATETIME,
--             ENd_DATETIME,
--             PERSOn_TECId,
--             maxDates(startRange,lag(START_DATETIME, 1) over (
--                 partition by PERSON_TECID
--                 order by START_DATETIME
--                 )
--                 ) as previousstart
--              ,
--            case
--				when (lag(ENd_DATETIME,1) over(
--				partition by PERSON_TECID
--				order by START_DATETIME
--				) = '0000-00-00' or lag(ENd_DATETIME,1) over(
--				partition by PERSON_TECID
--				order by START_DATETIME
--				) is null )  then minDates(endRange,lag(ENd_DATETIME,1) over(
--				partition by PERSON_TECID
--				order by START_DATETIME
--				))
--				 when lag(ENd_DATETIME,1) over(
--				partition by PERSON_TECID
--				order by START_DATETIME
--				) > ENd_DATETIME then minDates(endRange,lag(ENd_DATETIME,1) over(
--				partition by PERSON_TECID
--				order by START_DATETIME
--				))
--				else minDates(endRange,END_DATETIME) end
--
--			as previousend
--              ,
--
--             case
--                 -- 1ere prestation de la personne => on cumule la durée de prestation
--                 when ( lag(ENd_DATETIME, 1) over (
--                     partition by PERSON_TECID
--                     order by START_DATETIME, END_DATETIME
--                     ) = '0000-00-00' or lag(ENd_DATETIME, 1) over (
--                     partition by PERSON_TECID
--                     order by START_DATETIME, END_DATETIME
--                     ) is null) then TIME_TO_SEC(TIMEDIFF(minDates(endRange,END_DATETIME), maxDates(startRange,START_DATETIME)))
--
--                 -- prestations qui se suivent => on cumule la durée de prestation
--                 when START_DATETIME >= lag(ENd_DATETIME, 1) over (
--                     partition by PERSON_TECID
--                     order by START_DATETIME, END_DATETIME
--                     ) then TIME_TO_SEC(TIMEDIFF(minDates(endRange,END_DATETIME), maxDates(startRange,START_DATETIME)))
--
--                 -- Prestation qui commence qui est dans le slot précédent => on l'annule
--                 when START_DATETIME < lag(ENd_DATETIME, 1) over (
--                     partition by PERSON_TECID
--                     order by START_DATETIME, END_DATETIME
--                     )
--                     and
--                      END_DATETIME < lag(ENd_DATETIME, 1) over (
--                          partition by PERSON_TECID
--                          order by START_DATETIME, END_DATETIME
--                          )
--                     then 0
--
--                 -- prestations qui commencent pendant la prestation précedente ==> on cumule la durée à partir de la date de fin de la prestation précédente
--                 when START_DATETIME < lag(ENd_DATETIME, 1) over (
--                     partition by PERSON_TECID
--                     order by START_DATETIME, END_DATETIME
--                     ) then TIME_TO_SEC(TIMEDIFF(minDates(endRange,END_DATETIME), lag(END_DATETIME, 1) over (
--                     partition by PERSON_TECID
--                     order by START_DATETIME, END_DATETIME
--                     )))
--
--                 else '0'
--                 end
--                   as duration
--
--      from PRESTATION prestation7_
--      where
--        -- prestation7_.PRESTATION_START_DATE='2022-09-20'
--        prestation7_.PRESTATION_START_DATE= cast(startRange as date)
--        and  prestation7_.START_DATETIME < endRange
--        AND prestation7_.END_DATETIME > startRange
--
--        and case when personTecids is null then 1=1 else  FIND_IN_SET(prestation7_.person_tecid ,personTecids ) end
--        -- and prestation7_.person_tecid in (409, 133)
--
--      order by person_tecid, START_DATETIME) titi
--group by person_tecid
--having sum(duration) >= TIME_TO_SEC(TIMEDIFF(endRange, startRange)) and sum(duration) > 0
--;
--
-- END
--
--$$
--DELIMITER ;

DELIMITER $$

CREATE or replace PROCEDURE `getPersonWithPrestationDurationSumNotFillingRange`(
IN startRange datetime ,
IN endRange datetime ,
IN personTecids varchar(2000)
)
BEGIN
  select person_tecid -- INTO personTecids
  , sum(duration)
from (
select tecid,
             PRESTATION_START_DATE,
             START_DATETIME,
             ENd_DATETIME,
             PERSOn_TECId,
             maxDates(startRange,lag(START_DATETIME, 1) over (
                 partition by PERSON_TECID
                 order by START_DATETIME
                 )
                 )as previousstart
              ,
            case
				when ( lag(ENd_DATETIME,1) over(
				partition by PERSON_TECID
				order by START_DATETIME
				) = '0000-00-00' or lag(ENd_DATETIME,1) over(
				partition by PERSON_TECID
				order by START_DATETIME
				) is null ) then minDates(endRange,lag(ENd_DATETIME,1) over(
				partition by PERSON_TECID
				order by START_DATETIME
				))
				 when lag(ENd_DATETIME,1) over(
				partition by PERSON_TECID
				order by START_DATETIME
				) > ENd_DATETIME then minDates(endRange,lag(ENd_DATETIME,1) over(
				partition by PERSON_TECID
				order by START_DATETIME
				))
				else minDates(endRange,END_DATETIME) end

			as previousend
              ,

             case
                 -- 1ere prestation de la personne => on cumule la durée de prestation
                 when ( lag(ENd_DATETIME, 1) over (
                     partition by PERSON_TECID
                     order by START_DATETIME, END_DATETIME
                     ) = '0000-00-00' or lag(ENd_DATETIME, 1) over (
                     partition by PERSON_TECID
                     order by START_DATETIME, END_DATETIME
                     ) is null ) then TIME_TO_SEC(TIMEDIFF(minDates(endRange,END_DATETIME), maxDates(startRange,START_DATETIME)))

                 -- prestations qui se suivent => on cumule la durée de prestation
                 when START_DATETIME >= lag(ENd_DATETIME, 1) over (
                     partition by PERSON_TECID
                     order by START_DATETIME, END_DATETIME
                     ) then TIME_TO_SEC(TIMEDIFF(minDates(endRange,END_DATETIME), maxDates(startRange,START_DATETIME)))

                 -- Prestation qui commence qui est dans le slot précédent => on l'annule
                 when START_DATETIME < lag(ENd_DATETIME, 1) over (
                     partition by PERSON_TECID
                     order by START_DATETIME, END_DATETIME
                     )
                     and
                      END_DATETIME < lag(ENd_DATETIME, 1) over (
                          partition by PERSON_TECID
                          order by START_DATETIME, END_DATETIME
                          )
                     then 0

                 -- prestations qui commencent pendant la prestation précedente ==> on cumule la durée à partir de la date de fin de la prestation précédente
                 when START_DATETIME < lag(ENd_DATETIME, 1) over (
                     partition by PERSON_TECID
                     order by START_DATETIME, END_DATETIME
                     ) then TIME_TO_SEC(TIMEDIFF(minDates(endRange,END_DATETIME), lag(END_DATETIME, 1) over (
                     partition by PERSON_TECID
                     order by START_DATETIME, END_DATETIME
                     )))

                 else '0'
                 end
                   as duration

      from PRESTATION prestation7_
      where
        -- prestation7_.PRESTATION_START_DATE='2022-09-20'
        prestation7_.PRESTATION_START_DATE= cast(startRange as date)
        and  prestation7_.START_DATETIME < endRange
        AND prestation7_.END_DATETIME > startRange
          and case when personTecids is null then 1=1 else  FIND_IN_SET(prestation7_.person_tecid ,personTecids ) end

      order by person_tecid, START_DATETIME
      ) titi
group by person_tecid
 having sum(duration) < TIME_TO_SEC(TIMEDIFF(endRange, startRange)) and sum(duration) > 0

;

 END


$$
DELIMITER ;
