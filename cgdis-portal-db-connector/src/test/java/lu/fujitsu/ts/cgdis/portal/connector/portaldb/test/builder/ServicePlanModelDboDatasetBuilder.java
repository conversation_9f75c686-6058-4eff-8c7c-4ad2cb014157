package lu.fujitsu.ts.cgdis.portal.connector.portaldb.test.builder;

import lu.fujitsu.ts.cgdis.portal.connector.portaldb.dbos.serviceplan.ServicePlanModelDbo;

import java.util.Collection;

public class ServicePlanModelDboDatasetBuilder extends AbstractDboDatasetBuilder<ServicePlanModelDbo> {

    /**
     * Instantiates a new Vehicle dbo dataset builder.
     */
    public ServicePlanModelDboDatasetBuilder() {
        super("SERVICE_PLAN_MODEL");
    }

    @Override
    protected String[] getColumnNames() {
        return new String[]{"TECID", "TECLOCK",
                "NAME",
                "HAS_VEHICLE",
                "ENTITY_TECID",
                "INTERVENTION_TYPE_ID",
        };
    }

    @Override
    protected Object[] getDboValues(ServicePlanModelDbo dbo) {
        return new Object[]{
                dbo.getTecid(),
                dbo.getTeclock(),
                dbo.getName(),
                dbo.getVehicle(),
                dbo.getEntity()==null?null:dbo.getEntity().getTecid(),
                dbo.getInterventionType()==null?null:dbo.getInterventionType().getTecid()

        };
    }

    @Override
    protected Collection<?> getJoinedDbos(ServicePlanModelDbo dbo) {
        return null;
    }

}
