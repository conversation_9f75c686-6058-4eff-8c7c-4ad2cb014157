package lu.fujitsu.ts.cgdis.portal.connector.portaldb.test.builder;

import lu.fujitsu.ts.cgdis.portal.connector.portaldb.dbos.serviceplan.ServicePlanTimeSlotDbo;

import java.util.Collection;

/**
 * The type Vehicle dbo dataset builder.
 */
public class ServicePlanTimeSlotDboDatasetBuilder extends AbstractDboDatasetBuilder<ServicePlanTimeSlotDbo> {

    /**
     * Instantiates a new Vehicle dbo dataset builder.
     */
    public ServicePlanTimeSlotDboDatasetBuilder() {
        super("SERVICE_PLAN_TIME_SLOT");
    }

    @Override
    protected String[] getColumnNames() {
        return new String[]{"TECID", "TECLOCK",
                "START_TIME",
                "END_TIME",
                "SERVICE_PLAN_VERSION_TECID",
                "SLOT_ORDER",
                "ONLY_FIRST_DAY"
        };
    }

    @Override
    protected Object[] getDboValues(ServicePlanTimeSlotDbo dbo) {
        return new Object[]{
                dbo.getTecid(),
                dbo.getTeclock(),
                convertTime(dbo.getStartTime()),
                convertTime(dbo.getEndTime()),
                dbo.getVersion()==null?dbo.getServicePlanVersionId():dbo.getVersion().getTecid(),
                dbo.getSlotOrder(),
                convertBoolean(dbo.isOnlyFirstDay())
        };
    }

    @Override
    protected Collection<?> getJoinedDbos(ServicePlanTimeSlotDbo dbo) {
        if (dbo.getCompletionStatus() != null ){
            dbo.getCompletionStatus().forEach(completionStatus -> completionStatus.setTimeslot(dbo));
        }
        return dbo.getCompletionStatus();
    }
}
