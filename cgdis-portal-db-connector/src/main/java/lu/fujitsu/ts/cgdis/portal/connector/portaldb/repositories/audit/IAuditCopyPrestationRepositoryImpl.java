package lu.fujitsu.ts.cgdis.portal.connector.portaldb.repositories.audit;

import lu.fujitsu.ts.cgdis.portal.connector.portaldb.dbos.audit.AuditCopyPrestationDbo;
import lu.fujitsu.ts.cgdis.portal.connector.portaldb.repositories.AbstractRepositoryImpl;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;

/**
 * Repository implementation for AuditCopyPrestationDbo.
 */
@Repository
public class IAuditCopyPrestationRepositoryImpl extends AbstractRepositoryImpl<AuditCopyPrestationDbo> implements IAuditCopyPrestationRepositoryCustom {

    @PersistenceContext
    private EntityManager entityManager;

    /**
     * Constructor that sets the entity class.
     */
    public IAuditCopyPrestationRepositoryImpl() {
        super(AuditCopyPrestationDbo.class);
    }

    /**
     * Returns the EntityManager instance.
     *
     * @return the EntityManager instance
     */
    @Override
    protected EntityManager getEntityManager() {
        return entityManager;
    }
}
