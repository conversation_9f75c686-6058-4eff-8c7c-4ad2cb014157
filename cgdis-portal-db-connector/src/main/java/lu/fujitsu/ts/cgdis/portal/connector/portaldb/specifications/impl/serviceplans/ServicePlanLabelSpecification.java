package lu.fujitsu.ts.cgdis.portal.connector.portaldb.specifications.impl.serviceplans;

import lu.fujitsu.ts.cgdis.portal.connector.portaldb.dbos.serviceplan.ServicePlanDbo;
import lu.fujitsu.ts.cgdis.portal.connector.portaldb.specifications.SpecificationWithFrom;

import javax.persistence.criteria.AbstractQuery;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.From;
import javax.persistence.criteria.Predicate;

/**
 * The type Service plan label specification.
 */
public class ServicePlanLabelSpecification implements SpecificationWithFrom<ServicePlanDbo> {
    private final String label;

    /**
     * Instantiates a new Service plan label specification.
     *
     * @param label the label
     */
    public ServicePlanLabelSpecification(String label) {
        this.label = label;
    }

    @Override
    public <T> Predicate toPredicateFrom(From<T, ServicePlanDbo> from, AbstractQuery<?> query, CriteriaBuilder cb) {
        return cb.equal(from.get(ServicePlanDbo.Fields.LABEL), label);
    }
}
