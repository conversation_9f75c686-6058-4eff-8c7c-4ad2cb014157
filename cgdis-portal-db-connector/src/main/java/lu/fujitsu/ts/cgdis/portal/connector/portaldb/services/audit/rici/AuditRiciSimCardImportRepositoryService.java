package lu.fujitsu.ts.cgdis.portal.connector.portaldb.services.audit.rici;

import lu.fujitsu.ts.cgdis.portal.connector.portaldb.dbos.audit.AuditRiciSimCardImportDbo;
import lu.fujitsu.ts.cgdis.portal.connector.portaldb.repositories.audit.rici.IAuditRiciSimCardImportRepository;
import lu.fujitsu.ts.cgdis.portal.connector.portaldb.services.audit.AbstractAuditRepositoryService;
import lu.fujitsu.ts.cgdis.portal.core.domain.audit.AuditRiciSimCardImport;
import lu.fujitsu.ts.cgdis.portal.core.domain.audit.AuditType;
import lu.fujitsu.ts.cgdis.portal.core.logging.LoggingCategory;
import lu.fujitsu.ts.cgdis.portal.core.logging.LoggingDomain;
import lu.fujitsu.ts.cgdis.portal.core.security.IAuditSecurityConstraint;
import lu.fujitsu.ts.eportal.server.connector.jpa.specifications.secured.AbstractComposedSecuredSpecificationBuilder;
import lu.fujitsu.ts.eportal.server.logging.milestones.LogMilestone;
import org.dozer.Mapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
@LogMilestone(category = LoggingCategory.REPOSITORY_SERVICE, domain = LoggingDomain.AUDIT)
public class AuditRiciSimCardImportRepositoryService extends AbstractAuditRepositoryService<AuditRiciSimCardImport, AuditRiciSimCardImportDbo, IAuditRiciSimCardImportRepository> {

    @Autowired
    public AuditRiciSimCardImportRepositoryService(IAuditRiciSimCardImportRepository repository, AbstractComposedSecuredSpecificationBuilder<IAuditSecurityConstraint, AuditRiciSimCardImportDbo> specBuilder, Mapper mapper) {
        super(repository, specBuilder, AuditRiciSimCardImportDbo.class, AuditRiciSimCardImport.class, mapper);
    }

    @Override
    protected void additionalSortBuilder(Map<String, String> sorts) {
        // Prefixed (official)
        sorts.put("riciSimCardImportFileName", AuditRiciSimCardImportDbo.Fields.FILE_NAME);
        sorts.put("riciSimCardImportStatus", AuditRiciSimCardImportDbo.Fields.IMPORT_STATUS);
        sorts.put("riciSimCardImportTotalRecords", AuditRiciSimCardImportDbo.Fields.TOTAL_RECORDS);
        sorts.put("riciSimCardImportSuccessfulRecords", AuditRiciSimCardImportDbo.Fields.SUCCESSFUL_RECORDS);
        sorts.put("riciSimCardImportValidationErrors", AuditRiciSimCardImportDbo.Fields.VALIDATION_ERRORS);
        sorts.put("riciSimCardImportImportErrors", AuditRiciSimCardImportDbo.Fields.IMPORT_ERRORS);

        // Unprefixed (for robustness)
        sorts.put("fileName", AuditRiciSimCardImportDbo.Fields.FILE_NAME);
        sorts.put("importStatus", AuditRiciSimCardImportDbo.Fields.IMPORT_STATUS);
        sorts.put("totalRecords", AuditRiciSimCardImportDbo.Fields.TOTAL_RECORDS);
        sorts.put("successfulRecords", AuditRiciSimCardImportDbo.Fields.SUCCESSFUL_RECORDS);
        sorts.put("validationErrors", AuditRiciSimCardImportDbo.Fields.VALIDATION_ERRORS);
        sorts.put("importErrors", AuditRiciSimCardImportDbo.Fields.IMPORT_ERRORS);
    }

    @Override
    public AuditType getAuditType() {
        return AuditType.RICI_SIM_CARD_IMPORT;
    }
}
