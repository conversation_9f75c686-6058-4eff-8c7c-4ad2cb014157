package lu.fujitsu.ts.cgdis.portal.connector.portaldb.utils.internalstatus;

/**
 * The interface Internal status utils.
 *
 * @param <S> the type parameter
 */
public interface IInternalStatusUtils<S> {
    /**
     * Gets ongoing status.
     *
     * @return the ongoing status
     */
    S getOngoingStatus();

    /**
     * Gets past status.
     *
     * @return the past status
     */
    S getPastStatus();
}
