package lu.fujitsu.ts.cgdis.portal.connector.portaldb.services.permamonitor;

import lu.fujitsu.ts.cgdis.portal.connector.portaldb.dbos.permamonitor.PermCategoryDbo;
import lu.fujitsu.ts.cgdis.portal.connector.portaldb.dbos.permamonitor.PermSubcategoryDbo;
import lu.fujitsu.ts.cgdis.portal.connector.portaldb.repositories.permamonitor.IPermCategoryRepository;
import lu.fujitsu.ts.cgdis.portal.core.domain.permamonitor.PermCategory;
import lu.fujitsu.ts.cgdis.portal.core.domain.permamonitor.PermCategoryName;
import lu.fujitsu.ts.cgdis.portal.core.domain.permamonitor.PermCategoryWithSubcategories;
import lu.fujitsu.ts.cgdis.portal.core.domain.permamonitor.PermSubcategory;
import lu.fujitsu.ts.cgdis.portal.core.logging.LoggingCategory;
import lu.fujitsu.ts.cgdis.portal.core.logging.LoggingDomain;
import lu.fujitsu.ts.cgdis.portal.core.security.ICGDISSecurityConstraint;
import lu.fujitsu.ts.cgdis.portal.services.permamonitor.IPermCategoryRORepositoryService;
import lu.fujitsu.ts.eportal.server.connector.jpa.services.SecuredRORepositoryService;
import lu.fujitsu.ts.eportal.server.connector.jpa.specifications.secured.ISecuredSpecificationBuilder;
import lu.fujitsu.ts.eportal.server.core.exceptions.NotFoundException;
import lu.fujitsu.ts.eportal.server.core.utils.EPortalConverter;
import lu.fujitsu.ts.eportal.server.core.utils.IEPortalConverter;
import lu.fujitsu.ts.eportal.server.logging.milestones.LogMilestone;
import lu.fujitsu.ts.eportal.server.logging.milestones.LoggableType;
import lu.fujitsu.ts.eportal.server.logging.milestones.LoggableValue;
import org.dozer.Mapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;


/**
 * Service class for managing PermCategory entities.
 * Extends SecuredRORepositoryService to provide secured read-only operations.
 */
@Service
@LogMilestone(category = LoggingCategory.REPOSITORY_SERVICE, domain = LoggingDomain.PERMAMONITOR)
@Transactional(readOnly = true)
public class PermCategoryRORepositoryService extends SecuredRORepositoryService<ICGDISSecurityConstraint, PermCategory, PermCategoryDbo, Long, IPermCategoryRepository> implements IPermCategoryRORepositoryService {

    private static final Logger LOGGER = LoggerFactory.getLogger(PermCategoryRORepositoryService.class);

    private final IEPortalConverter<PermSubcategoryDbo, PermSubcategory> subcategoryDbo2ModelConverter;
    private final IEPortalConverter<PermCategoryDbo, PermCategoryWithSubcategories> categoryDbo2CategoryWithSubcategoriesConverter;

    /**
     * Constructor for PermCategoryRepositoryService.
     *
     * @param repository  the repository for PermCategory entities
     * @param mapper      the mapper for entity to DBO conversion
     * @param specBuilder the specification builder for security constraints
     */
    public PermCategoryRORepositoryService(IPermCategoryRepository repository, Mapper mapper, ISecuredSpecificationBuilder<ICGDISSecurityConstraint, PermCategoryDbo> specBuilder) {
        super(PermCategory.class, PermCategoryDbo.class, repository, mapper, specBuilder);
        this.subcategoryDbo2ModelConverter = new EPortalConverter<>(PermSubcategoryDbo.class, PermSubcategory.class, mapper);
        categoryDbo2CategoryWithSubcategoriesConverter = new EPortalConverter<>(PermCategoryDbo.class, PermCategoryWithSubcategories.class, mapper);
    }

    @Override
    @LogMilestone(action = "get all categories", logResult = @LoggableValue(type = LoggableType.COLLECTION))
    public List<PermCategory> getAllPermCategories() {
        LOGGER.debug("Getting all perm categories");
        List<PermCategoryDbo> all = this.repository.findAll();
        return dbo2ModelConverter.convert(all);
    }

    @Override
    @LogMilestone(action = "get all categories with subcategories", logResult = @LoggableValue(type = LoggableType.COLLECTION))
    public List<PermCategoryWithSubcategories> getAllPermCategoriesWithSubcategories() {
        LOGGER.debug("Getting all perm categories with subcategories");
        List<PermCategoryDbo> all = this.repository.findAll();
        return categoryDbo2CategoryWithSubcategoriesConverter.convert(all);

    }

    @Override
    @LogMilestone(action = "get all subcategories", logResult = @LoggableValue(type = LoggableType.COLLECTION))
    public List<PermSubcategory> getAllPermSubcategories(@LoggableValue PermCategoryName category) {
        LOGGER.debug("Getting all perm subcategories for category {}", category);
        return this.repository.findDistinctColumns(PermSubcategoryDbo.class,
                (root, query, criteriaBuilder) -> criteriaBuilder.equal(root.get(PermCategoryDbo.Fields.NAME), category),
                (root, criteriaQuery, criteriaBuilder) -> root.join(PermCategoryDbo.Fields.SUBCATEGORIES)
        ).stream().map(subcategoryDbo2ModelConverter::convert).collect(Collectors.toList());
    }

    @Override
    @LogMilestone(action = "get category by name", logResult = @LoggableValue())
    public Optional<PermCategory> getByName(ICGDISSecurityConstraint securityConstraint, @LoggableValue PermCategoryName name) {
        LOGGER.debug("Getting perm category by name {}", name);
        try {
            return Optional.of(
                    this.dbo2ModelConverter.convert(this.findOne(securityConstraint, (root, query, criteriaBuilder) -> criteriaBuilder.equal(root.get(PermCategoryDbo.Fields.NAME), name)))
            );
        } catch (NotFoundException e) {
            LOGGER.warn("No permamonitor category found with name {}", name);
            return Optional.empty();
        }
    }

    @Override
    @LogMilestone(action = "belongs subcategory to category", logResult = @LoggableValue)
    public boolean belongsSubcategoryToCategory(@LoggableValue Long subcategoryId, @LoggableValue Long categoryTecid) {
        boolean belongs = this.repository.hasSubcategrory(categoryTecid, subcategoryId);
        LOGGER.debug("Checking if subcategory {} belongs to category {}: {}", subcategoryId, categoryTecid, belongs);
        return belongs;
    }
}
