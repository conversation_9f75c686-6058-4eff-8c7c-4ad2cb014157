package lu.fujitsu.ts.cgdis.portal.connector.portaldb.specifications.audit;

import lu.fujitsu.ts.cgdis.portal.connector.portaldb.dbos.BaseDbo;
import lu.fujitsu.ts.cgdis.portal.connector.portaldb.dbos.audit.*;
import lu.fujitsu.ts.cgdis.portal.connector.portaldb.dbos.permamonitor.PermDeploymentPlanDbo;
import lu.fujitsu.ts.cgdis.portal.core.domain.audit.AuditActionType;
import lu.fujitsu.ts.cgdis.portal.core.domain.audit.AuditType;
import lu.fujitsu.ts.cgdis.portal.core.security.IAuditSecurityConstraint;
import lu.fujitsu.ts.eportal.server.connector.jpa.specifications.SpecificationUtils;
import lu.fujitsu.ts.eportal.server.connector.jpa.specifications.secured.AbstractComposedSecuredSpecificationBuilder;
import lu.fujitsu.ts.eportal.server.core.domain.SearchCriterion;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.persistence.criteria.Root;
import javax.persistence.criteria.Subquery;
import java.time.LocalDateTime;

/**
 * The type availability specification builder.
 */
@Service
public class AuditSpecificationBuilder extends AbstractComposedSecuredSpecificationBuilder<IAuditSecurityConstraint, AuditDbo> {

    public AuditSpecificationBuilder() {
        super();
        addBuilder("serviceplan", this::servicePlanSpecBuilder);
        addBuilder("slotStartDatetime", this::slotStartDatetimeBuilder);
        addBuilder("slotEndDatetime", this::slotEndDatetimeBuilder);
        addBuilder("deploymentPlanName", this::deploymentPlanNameBuilder);
        addBuilder("permDeploymentPlanStartDate", this::permDeploymentPlanStartDateSpecification);
        addBuilder("permServicePlanName", this::permServicePlanNameSpecification);
        addBuilder("permServicePlanCategoryName", this::permServicePlanCategoryNameSpecification);
        addBuilder("permServicePlanSubcategoryName", this::permServicePlanSubcategoryNameSpecification);
        addBuilder("permServicePlanDeploymentPlanName", this::permServicePlanDeploymentPlanNameSpecification);
        addBuilder("permBookmarked", this::permBookmarkedSpecification);

        addBuilder("permConfigDpceEntityTecid", this::permConfigDpceEntityTecidSpecification);
        addBuilder("permConfigDpceCategoryName", this::permConfigDpceCategoryNameSpecification);
        addBuilder("permConfigDpceStartHour", this::permConfigDpceStartHourSpecification);
        addBuilder("permConfigDpceDayValue", this::permConfigDpceDayValueSpecification);

        addBuilder("permConfigDpceCopyEntityTecid", this::permConfigDpceCopyEntityTecidSpecification);
        addBuilder("permConfigDpceCopyToEntityTecid", this::permConfigDpceCopyToEntityTecidSpecification);
        addBuilder("permConfigDpceCopyCategoryName", this::permConfigDpceCopyCategoryNameSpecification);
        addBuilder("permConfigDpceCopyStartHour", this::permConfigDpceCopyStartHourSpecification);
        addBuilder("permConfigDpceCopyToStartHour", this::permConfigDpceCopyToStartHourSpecification);
        addBuilder("permConfigDpceCopyToEndHour", this::permConfigDpceCopyToEndHourSpecification);
        addBuilder("permConfigDpceCopyDeploymentPlanName", this::permConfigDpceCopyDeploymentNameSpecification);

        // RICI SIM card audit specifications
        addBuilder("riciSimCardTecid", this::riciSimCardTecidSpecification);
        addBuilder("riciAssociatedPagerTecid", this::riciAssociatedPagerTecidSpecification); // Filters by Pager Tec ID
        addBuilder("associatedPagerId", this::riciSimCardAssociatedPagerIdSpecification); // Filters by Pager String ID
        addBuilder("riciSimCardIccid", this::riciSimCardIccidSpecification);
        addBuilder("riciSimCardMsisdn", this::riciSimCardMsisdnSpecification);
        addBuilder("riciSimCardStatus", this::riciSimCardStatusSpecification);

        // RICI RIC Range audit specifications
        addBuilder("riciRicRangeTecid", this::riciRicRangeTecidSpecification);
        addBuilder("riciRicRangeName", this::riciRicRangeNameSpecification);
        addBuilder("riciRicRangeType", this::riciRicRangeTypeSpecification);
        addBuilder("riciRicRangeEntityTecid", this::riciRicRangeEntityTecidSpecification);

        // RICI Pager audit specifications
        addBuilder("riciPagerTecid", this::riciPagerTecidSpecification);
        addBuilder("riciPagerId", this::riciPagerIdSpecification);
        addBuilder("riciPagerSerialNumber", this::riciPagerSerialNumberSpecification);
        addBuilder("riciPagerManufacturer", this::riciPagerManufacturerSpecification);
        addBuilder("riciPagerModel", this::riciPagerModelSpecification);
        addBuilder("riciPagerStatus", this::riciPagerStatusSpecification);
        addBuilder("riciPagerAssociatedSimCardTecid", this::riciPagerAssociatedSimCardTecidSpecification);
        addBuilder("riciPagerAssociatedSimCardIccid", this::riciPagerAssociatedSimCardIccidSpecification);
        addBuilder("riciPagerAssignmentType", this::riciPagerAssignmentTypeSpecification);
        addBuilder("riciPagerAssignedPersonTecid", this::riciPagerAssignedPersonTecidSpecification);
        addBuilder("riciPagerAssignedPersonName", this::riciPagerAssignedPersonNameSpecification);
        addBuilder("riciPagerAssignedEntityTecid", this::riciPagerAssignedEntityTecidSpecification);
        addBuilder("riciPagerAssignedEntityName", this::riciPagerAssignedEntityNameSpecification);

        // RICI Schema audit specifications
        addBuilder("riciSchemaTecid", this::riciSchemaTecidSpecification);
        addBuilder("riciSchemaName", this::riciSchemaNameSpecification);
        addBuilder("riciSchemaAlias", this::riciSchemaAliasSpecification);
        addBuilder("riciSchemaDescription", this::riciSchemaDescriptionSpecification);
        addBuilder("riciSchemaFunctionCodeATecid", this::riciSchemaFunctionCodeATecidSpecification);
        addBuilder("riciSchemaFunctionCodeAName", this::riciSchemaFunctionCodeANameSpecification);
        addBuilder("riciSchemaFunctionCodeBTecid", this::riciSchemaFunctionCodeBTecidSpecification);
        addBuilder("riciSchemaFunctionCodeBName", this::riciSchemaFunctionCodeBNameSpecification);
        addBuilder("riciSchemaFunctionCodeCTecid", this::riciSchemaFunctionCodeCTecidSpecification);
        addBuilder("riciSchemaFunctionCodeCName", this::riciSchemaFunctionCodeCNameSpecification);
        addBuilder("riciSchemaFunctionCodeDTecid", this::riciSchemaFunctionCodeDTecidSpecification);
        addBuilder("riciSchemaFunctionCodeDName", this::riciSchemaFunctionCodeDNameSpecification);

        // New RICI SIM Card Import audit specifications
        addBuilder("riciSimCardImportFileName", this::riciSimCardImportFileNameSpecification);
        addBuilder("riciSimCardImportStatus", this::riciSimCardImportStatusSpecification);
        addBuilder("riciSimCardImportTotalRecords", this::riciSimCardImportTotalRecordsSpecification);
        addBuilder("riciSimCardImportSuccessfulRecords", this::riciSimCardImportSuccessfulRecordsSpecification);
        addBuilder("riciSimCardImportValidationErrors", this::riciSimCardImportValidationErrorsSpecification);
        addBuilder("riciSimCardImportImportErrors", this::riciSimCardImportImportErrorsSpecification);

        // New RICI Pager Import audit specifications
        addBuilder("riciPagerImportFileName", this::riciPagerImportFileNameSpecification);
        addBuilder("riciPagerImportStatus", this::riciPagerImportStatusSpecification);
        addBuilder("riciPagerImportTotalRecords", this::riciPagerImportTotalRecordsSpecification);
        addBuilder("riciPagerImportSuccessfulRecords", this::riciPagerImportSuccessfulRecordsSpecification);
        addBuilder("riciPagerImportValidationErrors", this::riciPagerImportValidationErrorsSpecification);
        addBuilder("riciPagerImportImportErrors", this::riciPagerImportImportErrorsSpecification);

        // RICI Alert Group audit specifications


    }

    @Override
    public Specification<AuditDbo> buildSecurityConstraint(IAuditSecurityConstraint securityConstraint) {
        return (root, criteriaQuery, criteriaBuilder) -> criteriaBuilder.conjunction();
    }


    public Specification<AuditDbo> servicePlanSpecBuilder(SearchCriterion criterion) {
        return (root, criteriaQuery, criteriaBuilder) -> SpecificationUtils.buildPredicate(root.joinSet("servicePlans").get("tecid"), criterion, criteriaBuilder);
    }

    public Specification<AuditDbo> slotStartDatetimeBuilder(SearchCriterion criterion) {
        return (root, criteriaQuery, criteriaBuilder) -> {
            LocalDateTime dateTime = (LocalDateTime) criterion.getValue();
            Subquery<Long> subqueryAuditSlot1 = criteriaQuery.subquery(Long.class);
            Root<AuditTimeSlotDbo> slot1 = subqueryAuditSlot1.from(AuditTimeSlotDbo.class);

            subqueryAuditSlot1.where(
                    criteriaBuilder.lessThanOrEqualTo(slot1.get("startDatetime"), dateTime)
            );
            subqueryAuditSlot1.select(
                    slot1.get("tecid")
            );

            Subquery<Long> subqueryAuditSlot2 = criteriaQuery.subquery(Long.class);
            Root<AuditTimeSlotDbo> slot2 = subqueryAuditSlot2.from(AuditTimeSlotDbo.class);

            subqueryAuditSlot2.where(
                    criteriaBuilder.lessThanOrEqualTo(slot2.get("targetDatetime"), dateTime)
            );
            subqueryAuditSlot2.select(
                    slot2.get("tecid")
            );

            Subquery<Long> subqueryAuditPrestation = criteriaQuery.subquery(Long.class);
            Root<AuditPrestationDbo> prestation = subqueryAuditPrestation.from(AuditPrestationDbo.class);

            subqueryAuditPrestation.where(
                    criteriaBuilder.lessThanOrEqualTo(prestation.get("startDatetime"), dateTime)
            );
            subqueryAuditPrestation.select(
                    prestation.get("tecid")
            );

            return criteriaBuilder.or(
                    criteriaBuilder.and(
                            criteriaBuilder.equal(root.get("actionType"), AuditActionType.SPLIT),
                            root.get("tecid").in(subqueryAuditSlot1)
                    ),
                    criteriaBuilder.and(
                            criteriaBuilder.equal(root.get("actionType"), AuditActionType.MERGE),
                            root.get("tecid").in(subqueryAuditSlot2)
                    ),
                    criteriaBuilder.and(
                            criteriaBuilder.equal(root.get("type"), AuditType.PRESTATION),
                            root.get("tecid").in(subqueryAuditPrestation)
                    )
            );
        };
    }

    public Specification<AuditDbo> slotEndDatetimeBuilder(SearchCriterion criterion) {
        return (root, criteriaQuery, criteriaBuilder) -> {
            LocalDateTime dateTime = (LocalDateTime) criterion.getValue();
            Subquery<Long> subqueryAuditSlot1 = criteriaQuery.subquery(Long.class);
            Root<AuditTimeSlotDbo> slot1 = subqueryAuditSlot1.from(AuditTimeSlotDbo.class);

            subqueryAuditSlot1.where(
                    criteriaBuilder.greaterThanOrEqualTo(slot1.get("endDatetime"), dateTime)
            );
            subqueryAuditSlot1.select(
                    slot1.get("tecid")
            );

            Subquery<Long> subqueryAuditPrestation = criteriaQuery.subquery(Long.class);
            Root<AuditPrestationDbo> prestation = subqueryAuditPrestation.from(AuditPrestationDbo.class);

            subqueryAuditPrestation.where(
                    criteriaBuilder.greaterThanOrEqualTo(prestation.get("endDatetime"), dateTime)
            );
            subqueryAuditPrestation.select(
                    prestation.get("tecid")
            );

            return criteriaBuilder.or(
                    criteriaBuilder.and(
                            criteriaBuilder.equal(root.get("type"), AuditType.SLOT),
                            root.get("tecid").in(subqueryAuditSlot1)
                    ),
                    criteriaBuilder.and(
                            criteriaBuilder.equal(root.get("type"), AuditType.PRESTATION),
                            root.get("tecid").in(subqueryAuditPrestation)
                    )
            );
        };
    }

    public Specification<AuditDbo> deploymentPlanNameBuilder(SearchCriterion criterion) {
        return (root, criteriaQuery, criteriaBuilder) -> {

            Subquery<Long> subqueryAuditPrestation = criteriaQuery.subquery(Long.class);
            Root<AuditPermDeploymentPlanDbo> deploymentPlanRoot = subqueryAuditPrestation.from(AuditPermDeploymentPlanDbo.class);

            subqueryAuditPrestation.where(
                    SpecificationUtils.buildPredicate(deploymentPlanRoot.get(AuditPermDeploymentPlanDbo.Fields.NAME), criterion, criteriaBuilder)
            );

            return
                    criteriaBuilder.and(
                            criteriaBuilder.equal(root.get(AuditDbo.Fields.TYPE), AuditType.PERM_DEPLOYMENT_PLAN),
                            root.get(BaseDbo.Fields.TECID).in(subqueryAuditPrestation.select(deploymentPlanRoot.get(BaseDbo.Fields.TECID)))

                    );
        };
    }

    public Specification<AuditDbo> permDeploymentPlanStartDateSpecification(SearchCriterion criterion) {
        return permDeploymentPlanSpecification(criterion, AuditPermDeploymentPlanDbo.Fields.START_DATE);
    }

    public Specification<AuditDbo> permDeploymentPlanSpecification(SearchCriterion criterion, String fieldName) {
        return (root, criteriaQuery, criteriaBuilder) -> {
            Subquery<Long> subqueryAuditPrestation = criteriaQuery.subquery(Long.class);
            Root<AuditPermDeploymentPlanDbo> auditDeploymentPlanRoot = subqueryAuditPrestation.from(AuditPermDeploymentPlanDbo.class);
            subqueryAuditPrestation.where(
                    SpecificationUtils.buildPredicate(
                            auditDeploymentPlanRoot.get(fieldName), criterion, criteriaBuilder
                    )
            );
            return criteriaBuilder.and(
                    criteriaBuilder.equal(root.get(AuditDbo.Fields.TYPE), AuditType.PERM_DEPLOYMENT_PLAN),
                    root.get(BaseDbo.Fields.TECID).in(subqueryAuditPrestation.select(auditDeploymentPlanRoot.get(BaseDbo.Fields.TECID)))
            );
        };
    }

    public Specification<AuditDbo> permServicePlanNameSpecification(SearchCriterion criterion) {
        return buildPermServicePlanSpecification(criterion, AuditPermServicePlanCategoryDbo.Fields.SERVICE_PLAN_NAME);
    }

    public Specification<AuditDbo> permServicePlanCategoryNameSpecification(SearchCriterion criterion) {
        return buildPermServicePlanSpecification(criterion, AuditPermServicePlanCategoryDbo.Fields.CATEGORY_NAME);
    }

    public Specification<AuditDbo> permServicePlanSubcategoryNameSpecification(SearchCriterion criterion) {
        return buildPermServicePlanSpecification(criterion, AuditPermServicePlanCategoryDbo.Fields.SUBCATEGORY_NAME);
    }

    public Specification<AuditDbo> permBookmarkedSpecification(SearchCriterion criterion) {
        return buildPermServicePlanSpecification(criterion, AuditPermServicePlanCategoryDbo.Fields.BOOKMARKED);
    }

    public Specification<AuditDbo> permServicePlanDeploymentPlanNameSpecification(SearchCriterion criterion) {
        return (root, criteriaQuery, criteriaBuilder) -> {
            Subquery<Long> subqueryAuditPrestation = criteriaQuery.subquery(Long.class);
            Root<AuditPermServicePlanCategoryDbo> auditServicePlanRoot = subqueryAuditPrestation.from(AuditPermServicePlanCategoryDbo.class);
            subqueryAuditPrestation.where(
                    SpecificationUtils.buildPredicate(
                            auditServicePlanRoot.join(AuditPermServicePlanCategoryDbo.Fields.DEPLOYMENT_PLAN).get(PermDeploymentPlanDbo.Fields.NAME), criterion, criteriaBuilder
                    )
            );
            return criteriaBuilder.and(
                    criteriaBuilder.equal(root.get(AuditDbo.Fields.TYPE), AuditType.PERM_SERVICE_PLAN),
                    root.get(BaseDbo.Fields.TECID).in(subqueryAuditPrestation.select(auditServicePlanRoot.get(BaseDbo.Fields.TECID)))
            );
        };
    }

    private Specification<AuditDbo> buildPermServicePlanSpecification(SearchCriterion criterion, String fieldName) {
        return (root, criteriaQuery, criteriaBuilder) -> {
            Subquery<Long> subqueryAuditPrestation = criteriaQuery.subquery(Long.class);
            Root<AuditPermServicePlanCategoryDbo> auditServicePlanRoot = subqueryAuditPrestation.from(AuditPermServicePlanCategoryDbo.class);
            subqueryAuditPrestation.where(
                    SpecificationUtils.buildPredicate(auditServicePlanRoot.get(fieldName), criterion, criteriaBuilder)
            );
            return criteriaBuilder.and(
                    criteriaBuilder.equal(root.get(AuditDbo.Fields.TYPE), AuditType.PERM_SERVICE_PLAN),
                    root.get(BaseDbo.Fields.TECID).in(subqueryAuditPrestation.select(auditServicePlanRoot.get(BaseDbo.Fields.TECID)))
            );
        };
    }

    private Specification<AuditDbo> permConfigDpceCategoryNameSpecification(SearchCriterion criterion) {
        return permConfigDpceSpecification(criterion, AuditPermConfigDpceDbo.Fields.CATEGORY_NAME);
    }

    private Specification<AuditDbo> permConfigDpceEntityTecidSpecification(SearchCriterion criterion) {
        return permConfigDpceSpecification(criterion, AuditPermConfigDpceDbo.Fields.ENTITY_TECID);
    }

    private Specification<AuditDbo> permConfigDpceStartHourSpecification(SearchCriterion criterion) {
        return permConfigDpceSpecification(criterion, AuditPermConfigDpceDbo.Fields.START_HOUR);
    }

    private Specification<AuditDbo> permConfigDpceDayValueSpecification(SearchCriterion criterion) {
        return permConfigDpceSpecification(criterion, AuditPermConfigDpceDbo.Fields.DAY_VALUE);
    }


    private Specification<AuditDbo> permConfigDpceSpecification(SearchCriterion criterion, String fieldName) {
        return (root, criteriaQuery, criteriaBuilder) -> {
            Subquery<Long> subqueryAuditPrestation = criteriaQuery.subquery(Long.class);
            Root<AuditPermConfigDpceDbo> auditServicePlanRoot = subqueryAuditPrestation.from(AuditPermConfigDpceDbo.class);
            subqueryAuditPrestation.where(
                    SpecificationUtils.buildPredicate(auditServicePlanRoot.get(fieldName), criterion, criteriaBuilder)
            );
            return criteriaBuilder.and(
                    criteriaBuilder.equal(root.get(AuditDbo.Fields.TYPE), AuditType.PERM_CONFIG_DPCE),
                    root.get(BaseDbo.Fields.TECID).in(subqueryAuditPrestation.select(auditServicePlanRoot.get(BaseDbo.Fields.TECID)))
            );
        };
    }

    private Specification<AuditDbo> permConfigDpceCopyCategoryNameSpecification(SearchCriterion criterion) {
        return permConfigDpceCopySpecification(criterion, AuditPermConfigDpceCopyDbo.Fields.CATEGORY_NAME);
    }

    private Specification<AuditDbo> permConfigDpceCopyEntityTecidSpecification(SearchCriterion criterion) {
        return permConfigDpceCopySpecification(criterion, AuditPermConfigDpceCopyDbo.Fields.ENTITY_TECID);
    }

    private Specification<AuditDbo> permConfigDpceCopyToEntityTecidSpecification(SearchCriterion criterion) {
        return permConfigDpceCopySpecification(criterion, AuditPermConfigDpceCopyDbo.Fields.TO_ENTITY_TECID);
    }

    private Specification<AuditDbo> permConfigDpceCopyStartHourSpecification(SearchCriterion criterion) {
        return permConfigDpceCopySpecification(criterion, AuditPermConfigDpceCopyDbo.Fields.START_HOUR);
    }

    private Specification<AuditDbo> permConfigDpceCopyToStartHourSpecification(SearchCriterion criterion) {
        return permConfigDpceCopySpecification(criterion, AuditPermConfigDpceCopyDbo.Fields.TO_START_HOUR);
    }

    private Specification<AuditDbo> permConfigDpceCopyToEndHourSpecification(SearchCriterion criterion) {
        return permConfigDpceCopySpecification(criterion, AuditPermConfigDpceCopyDbo.Fields.TO_END_HOUR);
    }


    private Specification<AuditDbo> permConfigDpceCopySpecification(SearchCriterion criterion, String fieldName) {
        return (root, criteriaQuery, criteriaBuilder) -> {
            Subquery<Long> subqueryAuditPrestation = criteriaQuery.subquery(Long.class);
            Root<AuditPermConfigDpceCopyDbo> auditServicePlanRoot = subqueryAuditPrestation.from(AuditPermConfigDpceCopyDbo.class);
            subqueryAuditPrestation.where(
                    SpecificationUtils.buildPredicate(auditServicePlanRoot.get(fieldName), criterion, criteriaBuilder)
            );
            return criteriaBuilder.and(
                    criteriaBuilder.equal(root.get(AuditDbo.Fields.TYPE), AuditType.PERM_CONFIGDPCE_COPY),
                    root.get(BaseDbo.Fields.TECID).in(subqueryAuditPrestation.select(auditServicePlanRoot.get(BaseDbo.Fields.TECID)))
            );
        };
    }

    private Specification<AuditDbo> permConfigDpceCopyDeploymentNameSpecification(SearchCriterion criterion) {
        return (root, criteriaQuery, criteriaBuilder) -> {
            Subquery<Long> subqueryAuditPrestation = criteriaQuery.subquery(Long.class);
            Root<AuditPermConfigDpceCopyDbo> auditServicePlanRoot = subqueryAuditPrestation.from(AuditPermConfigDpceCopyDbo.class);
            subqueryAuditPrestation.where(
                    SpecificationUtils.buildPredicate(
                            auditServicePlanRoot.join(AuditPermConfigDpceCopyDbo.Fields.DEPLOYMENT_PLAN).get(PermDeploymentPlanDbo.Fields.NAME), criterion, criteriaBuilder
                    )
            );
            return criteriaBuilder.and(
                    criteriaBuilder.equal(root.get(AuditDbo.Fields.TYPE), AuditType.PERM_CONFIGDPCE_COPY),
                    root.get(BaseDbo.Fields.TECID).in(subqueryAuditPrestation.select(auditServicePlanRoot.get(BaseDbo.Fields.TECID)))
            );
        };
    }

    // RICI SIM card audit specifications
    private Specification<AuditDbo> riciSimCardTecidSpecification(SearchCriterion criterion) {
        return riciSimCardSpecification(criterion, "simCardTecid");
    }

    private Specification<AuditDbo> riciAssociatedPagerTecidSpecification(SearchCriterion criterion) {
        return riciSimCardSpecification(criterion, "associatedPagerTecid");
    }

    private Specification<AuditDbo> riciSimCardIccidSpecification(SearchCriterion criterion) {
        return riciSimCardSpecification(criterion, "iccid");
    }

    private Specification<AuditDbo> riciSimCardMsisdnSpecification(SearchCriterion criterion) {
        return riciSimCardSpecification(criterion, "msisdn");
    }

    private Specification<AuditDbo> riciSimCardStatusSpecification(SearchCriterion criterion) {
        return riciSimCardSpecification(criterion, "status");
    }

    private Specification<AuditDbo> riciSimCardAssociatedPagerIdSpecification(SearchCriterion criterion) {
        return riciSimCardSpecification(criterion, "associatedPagerId");
    }

    private Specification<AuditDbo> riciSimCardSpecification(SearchCriterion criterion, String fieldName) {
        return (root, criteriaQuery, criteriaBuilder) -> {
            Subquery<Long> subqueryAuditRiciSimCard = criteriaQuery.subquery(Long.class);
            Root<AuditRiciSimCardDbo> auditRiciSimCardRoot = subqueryAuditRiciSimCard.from(AuditRiciSimCardDbo.class);
            subqueryAuditRiciSimCard.where(
                    SpecificationUtils.buildPredicate(auditRiciSimCardRoot.get(fieldName), criterion, criteriaBuilder)
            );
            return criteriaBuilder.and(
                    criteriaBuilder.equal(root.get(AuditDbo.Fields.TYPE), AuditType.RICI_SIM_CARD),
                    root.get(BaseDbo.Fields.TECID).in(subqueryAuditRiciSimCard.select(auditRiciSimCardRoot.get(BaseDbo.Fields.TECID)))
            );
        };
    }

    // RICI RIC Range audit specifications
    private Specification<AuditDbo> riciRicRangeTecidSpecification(SearchCriterion criterion) {
        return riciRicRangeSpecification(criterion, "ricRangeTecid"); // Use string literal
    }

    private Specification<AuditDbo> riciRicRangeNameSpecification(SearchCriterion criterion) {
        return riciRicRangeSpecification(criterion, "name"); // Use string literal
    }

    private Specification<AuditDbo> riciRicRangeTypeSpecification(SearchCriterion criterion) {
        // Assuming type is stored as String or Enum
        return riciRicRangeSpecification(criterion, "ricRangeType"); // Use string literal
    }

    private Specification<AuditDbo> riciRicRangeEntityTecidSpecification(SearchCriterion criterion) {
        return riciRicRangeSpecification(criterion, "entityTecid"); // Use string literal
    }

    private Specification<AuditDbo> riciRicRangeSpecification(SearchCriterion criterion, String fieldName) {
        return (root, criteriaQuery, criteriaBuilder) -> {
            Subquery<Long> subqueryAuditRiciRicRange = criteriaQuery.subquery(Long.class);
            Root<AuditRiciRicRangeDbo> auditRiciRicRangeRoot = subqueryAuditRiciRicRange.from(AuditRiciRicRangeDbo.class);
            subqueryAuditRiciRicRange.where(
                    SpecificationUtils.buildPredicate(auditRiciRicRangeRoot.get(fieldName), criterion, criteriaBuilder)
            );
            return criteriaBuilder.and(
                    criteriaBuilder.equal(root.get(AuditDbo.Fields.TYPE), AuditType.RICI_RIC_RANGE),
                    root.get(BaseDbo.Fields.TECID).in(subqueryAuditRiciRicRange.select(auditRiciRicRangeRoot.get(BaseDbo.Fields.TECID)))
            );
        };
    }

    // RICI Pager audit specifications implementation
    private Specification<AuditDbo> riciPagerTecidSpecification(SearchCriterion criterion) {
        return riciPagerSpecification(criterion, AuditRiciPagerDbo.Fields.PAGER_TECID);
    }

    private Specification<AuditDbo> riciPagerIdSpecification(SearchCriterion criterion) {
        return riciPagerSpecification(criterion, AuditRiciPagerDbo.Fields.PAGER_ID);
    }

    private Specification<AuditDbo> riciPagerSerialNumberSpecification(SearchCriterion criterion) {
        return riciPagerSpecification(criterion, AuditRiciPagerDbo.Fields.SERIAL_NUMBER);
    }

    private Specification<AuditDbo> riciPagerManufacturerSpecification(SearchCriterion criterion) {
        return riciPagerSpecification(criterion, AuditRiciPagerDbo.Fields.MANUFACTURER);
    }

    private Specification<AuditDbo> riciPagerModelSpecification(SearchCriterion criterion) {
        return riciPagerSpecification(criterion, AuditRiciPagerDbo.Fields.MODEL);
    }

    private Specification<AuditDbo> riciPagerStatusSpecification(SearchCriterion criterion) {
        return riciPagerSpecification(criterion, AuditRiciPagerDbo.Fields.STATUS);
    }

    private Specification<AuditDbo> riciPagerAssociatedSimCardTecidSpecification(SearchCriterion criterion) {
        return riciPagerSpecification(criterion, AuditRiciPagerDbo.Fields.ASSOCIATED_SIM_CARD_TECID);
    }

    private Specification<AuditDbo> riciPagerAssociatedSimCardIccidSpecification(SearchCriterion criterion) {
        return riciPagerSpecification(criterion, AuditRiciPagerDbo.Fields.ASSOCIATED_SIM_CARD_ICCID);
    }

    private Specification<AuditDbo> riciPagerAssignmentTypeSpecification(SearchCriterion criterion) {
        return riciPagerSpecification(criterion, AuditRiciPagerDbo.Fields.ASSIGNMENT_TYPE);
    }

    private Specification<AuditDbo> riciPagerAssignedPersonTecidSpecification(SearchCriterion criterion) {
        return riciPagerSpecification(criterion, AuditRiciPagerDbo.Fields.ASSIGNED_PERSON_TECID);
    }

    private Specification<AuditDbo> riciPagerAssignedPersonNameSpecification(SearchCriterion criterion) {
        return riciPagerSpecification(criterion, AuditRiciPagerDbo.Fields.ASSIGNED_PERSON_NAME);
    }

    private Specification<AuditDbo> riciPagerAssignedEntityTecidSpecification(SearchCriterion criterion) {
        return riciPagerSpecification(criterion, AuditRiciPagerDbo.Fields.ASSIGNED_ENTITY_TECID);
    }

    private Specification<AuditDbo> riciPagerAssignedEntityNameSpecification(SearchCriterion criterion) {
        return riciPagerSpecification(criterion, AuditRiciPagerDbo.Fields.ASSIGNED_ENTITY_NAME);
    }

    private Specification<AuditDbo> riciPagerSpecification(SearchCriterion criterion, String fieldName) {
        return (root, criteriaQuery, criteriaBuilder) -> {
            Subquery<Long> subqueryAuditRiciPager = criteriaQuery.subquery(Long.class);
            Root<AuditRiciPagerDbo> auditRiciPagerRoot = subqueryAuditRiciPager.from(AuditRiciPagerDbo.class);
            subqueryAuditRiciPager.where(
                    SpecificationUtils.buildPredicate(auditRiciPagerRoot.get(fieldName), criterion, criteriaBuilder)
            );
            return criteriaBuilder.and(
                    criteriaBuilder.equal(root.get(AuditDbo.Fields.TYPE), AuditType.RICI_PAGER),
                    root.get(BaseDbo.Fields.TECID).in(subqueryAuditRiciPager.select(auditRiciPagerRoot.get(BaseDbo.Fields.TECID)))
            );
        };
    }

    // RICI Schema audit specifications implementation
    private Specification<AuditDbo> riciSchemaTecidSpecification(SearchCriterion criterion) {
        return riciSchemaSpecification(criterion, AuditRiciSchemaDbo.Fields.SCHEMA_TECID);
    }

    private Specification<AuditDbo> riciSchemaNameSpecification(SearchCriterion criterion) {
        return riciSchemaSpecification(criterion, AuditRiciSchemaDbo.Fields.SCHEMA_NAME);
    }

    private Specification<AuditDbo> riciSchemaAliasSpecification(SearchCriterion criterion) {
        return riciSchemaSpecification(criterion, AuditRiciSchemaDbo.Fields.SCHEMA_ALIAS);
    }

    private Specification<AuditDbo> riciSchemaDescriptionSpecification(SearchCriterion criterion) {
        return riciSchemaSpecification(criterion, AuditRiciSchemaDbo.Fields.DESCRIPTION);
    }

    private Specification<AuditDbo> riciSchemaFunctionCodeATecidSpecification(SearchCriterion criterion) {
        return riciSchemaSpecification(criterion, AuditRiciSchemaDbo.Fields.FUNCTION_CODE_A_TECID);
    }

    private Specification<AuditDbo> riciSchemaFunctionCodeANameSpecification(SearchCriterion criterion) {
        return riciSchemaSpecification(criterion, AuditRiciSchemaDbo.Fields.FUNCTION_CODE_A_NAME);
    }

    private Specification<AuditDbo> riciSchemaFunctionCodeBTecidSpecification(SearchCriterion criterion) {
        return riciSchemaSpecification(criterion, AuditRiciSchemaDbo.Fields.FUNCTION_CODE_B_TECID);
    }

    private Specification<AuditDbo> riciSchemaFunctionCodeBNameSpecification(SearchCriterion criterion) {
        return riciSchemaSpecification(criterion, AuditRiciSchemaDbo.Fields.FUNCTION_CODE_B_NAME);
    }

    private Specification<AuditDbo> riciSchemaFunctionCodeCTecidSpecification(SearchCriterion criterion) {
        return riciSchemaSpecification(criterion, AuditRiciSchemaDbo.Fields.FUNCTION_CODE_C_TECID);
    }

    private Specification<AuditDbo> riciSchemaFunctionCodeCNameSpecification(SearchCriterion criterion) {
        return riciSchemaSpecification(criterion, AuditRiciSchemaDbo.Fields.FUNCTION_CODE_C_NAME);
    }

    private Specification<AuditDbo> riciSchemaFunctionCodeDTecidSpecification(SearchCriterion criterion) {
        return riciSchemaSpecification(criterion, AuditRiciSchemaDbo.Fields.FUNCTION_CODE_D_TECID);
    }

    private Specification<AuditDbo> riciSchemaFunctionCodeDNameSpecification(SearchCriterion criterion) {
        return riciSchemaSpecification(criterion, AuditRiciSchemaDbo.Fields.FUNCTION_CODE_D_NAME);
    }

    private Specification<AuditDbo> riciSchemaSpecification(SearchCriterion criterion, String fieldName) {
        return (root, criteriaQuery, criteriaBuilder) -> {
            Subquery<Long> subqueryAuditRiciSchema = criteriaQuery.subquery(Long.class);
            Root<AuditRiciSchemaDbo> auditRiciSchemaRoot = subqueryAuditRiciSchema.from(AuditRiciSchemaDbo.class);
            subqueryAuditRiciSchema.where(
                    SpecificationUtils.buildPredicate(auditRiciSchemaRoot.get(fieldName), criterion, criteriaBuilder)
            );
            return criteriaBuilder.and(
                    criteriaBuilder.equal(root.get(AuditDbo.Fields.TYPE), AuditType.RICI_SCHEMA),
                    root.get(BaseDbo.Fields.TECID).in(subqueryAuditRiciSchema.select(auditRiciSchemaRoot.get(BaseDbo.Fields.TECID)))
            );
        };
    }

    // New RICI SIM Card Import audit specifications
    private Specification<AuditDbo> riciSimCardImportFileNameSpecification(SearchCriterion criterion) {
        return riciSimCardImportSpecification(criterion, AuditRiciSimCardImportDbo.Fields.FILE_NAME);
    }

    private Specification<AuditDbo> riciSimCardImportStatusSpecification(SearchCriterion criterion) {
        return riciSimCardImportSpecification(criterion, AuditRiciSimCardImportDbo.Fields.IMPORT_STATUS);
    }

    private Specification<AuditDbo> riciSimCardImportTotalRecordsSpecification(SearchCriterion criterion) {
        return riciSimCardImportSpecification(criterion, AuditRiciSimCardImportDbo.Fields.TOTAL_RECORDS);
    }

    private Specification<AuditDbo> riciSimCardImportSuccessfulRecordsSpecification(SearchCriterion criterion) {
        return riciSimCardImportSpecification(criterion, AuditRiciSimCardImportDbo.Fields.SUCCESSFUL_RECORDS);
    }

    private Specification<AuditDbo> riciSimCardImportValidationErrorsSpecification(SearchCriterion criterion) {
        return riciSimCardImportSpecification(criterion, AuditRiciSimCardImportDbo.Fields.VALIDATION_ERRORS);
    }

    private Specification<AuditDbo> riciSimCardImportImportErrorsSpecification(SearchCriterion criterion) {
        return riciSimCardImportSpecification(criterion, AuditRiciSimCardImportDbo.Fields.IMPORT_ERRORS);
    }

    private Specification<AuditDbo> riciSimCardImportSpecification(SearchCriterion criterion, String fieldName) {
        return (root, criteriaQuery, criteriaBuilder) -> {
            Subquery<Long> subqueryAuditRiciSimCardImport = criteriaQuery.subquery(Long.class);
            Root<AuditRiciSimCardImportDbo> auditRiciSimCardImportRoot = subqueryAuditRiciSimCardImport.from(AuditRiciSimCardImportDbo.class);
            subqueryAuditRiciSimCardImport.where(
                    SpecificationUtils.buildPredicate(auditRiciSimCardImportRoot.get(fieldName), criterion, criteriaBuilder)
            );
            return criteriaBuilder.and(
                    criteriaBuilder.equal(root.get(AuditDbo.Fields.TYPE), AuditType.RICI_SIM_CARD_IMPORT),
                    root.get(BaseDbo.Fields.TECID).in(subqueryAuditRiciSimCardImport.select(auditRiciSimCardImportRoot.get(BaseDbo.Fields.TECID)))
            );
        };
    }

    // New RICI Pager Import audit specifications
    private Specification<AuditDbo> riciPagerImportFileNameSpecification(SearchCriterion criterion) {
        return riciPagerImportSpecification(criterion, AuditRiciPagerImportDbo.Fields.FILE_NAME);
    }

    private Specification<AuditDbo> riciPagerImportStatusSpecification(SearchCriterion criterion) {
        return riciPagerImportSpecification(criterion, AuditRiciPagerImportDbo.Fields.IMPORT_STATUS);
    }

    private Specification<AuditDbo> riciPagerImportTotalRecordsSpecification(SearchCriterion criterion) {
        return riciPagerImportSpecification(criterion, AuditRiciPagerImportDbo.Fields.TOTAL_RECORDS);
    }

    private Specification<AuditDbo> riciPagerImportSuccessfulRecordsSpecification(SearchCriterion criterion) {
        return riciPagerImportSpecification(criterion, AuditRiciPagerImportDbo.Fields.SUCCESSFUL_RECORDS);
    }

    private Specification<AuditDbo> riciPagerImportValidationErrorsSpecification(SearchCriterion criterion) {
        return riciPagerImportSpecification(criterion, AuditRiciPagerImportDbo.Fields.VALIDATION_ERRORS);
    }

    private Specification<AuditDbo> riciPagerImportImportErrorsSpecification(SearchCriterion criterion) {
        return riciPagerImportSpecification(criterion, AuditRiciPagerImportDbo.Fields.IMPORT_ERRORS);
    }

    private Specification<AuditDbo> riciPagerImportSpecification(SearchCriterion criterion, String fieldName) {
        return (root, criteriaQuery, criteriaBuilder) -> {
            Subquery<Long> subqueryAuditRiciPagerImport = criteriaQuery.subquery(Long.class);
            Root<AuditRiciPagerImportDbo> auditRiciPagerImportRoot = subqueryAuditRiciPagerImport.from(AuditRiciPagerImportDbo.class);
            subqueryAuditRiciPagerImport.where(
                    SpecificationUtils.buildPredicate(auditRiciPagerImportRoot.get(fieldName), criterion, criteriaBuilder)
            );
            return criteriaBuilder.and(
                    criteriaBuilder.equal(root.get(AuditDbo.Fields.TYPE), AuditType.RICI_PAGER_IMPORT),
                    root.get(BaseDbo.Fields.TECID).in(subqueryAuditRiciPagerImport.select(auditRiciPagerImportRoot.get(BaseDbo.Fields.TECID)))
            );
        };
    }


}
