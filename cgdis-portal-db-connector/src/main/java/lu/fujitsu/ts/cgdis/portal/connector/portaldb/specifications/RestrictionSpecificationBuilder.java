package lu.fujitsu.ts.cgdis.portal.connector.portaldb.specifications;

import lu.fujitsu.ts.cgdis.portal.connector.portaldb.dbos.person.RestrictionDbo;
import lu.fujitsu.ts.cgdis.portal.core.domain.ICGDISPortalUserDetails;
import lu.fujitsu.ts.eportal.server.connector.jpa.specifications.SpecificationUtils;
import lu.fujitsu.ts.eportal.server.connector.jpa.specifications.secured.AbstractComposedSecuredSpecificationBuilder;
import lu.fujitsu.ts.eportal.server.core.domain.SearchCriterion;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

/**
 * The type Restriction specification builder.
 */
@Service
public class RestrictionSpecificationBuilder extends AbstractComposedSecuredSpecificationBuilder<ICGDISPortalUserDetails, RestrictionDbo> {

    /**
     * Instantiates a new restriction specification builder.
     */
    public RestrictionSpecificationBuilder() {
        super();
        addBuilder("restrictionDescription", this::restrictionDescriptionSpecificationBuilder);
        addBuilder("restriction", this::restrictionSpecificationBuilder);
    }

    @Override
    public Specification<RestrictionDbo> buildSecurityConstraint(ICGDISPortalUserDetails icgdisPortalUserDetails) {
        return (root, criteriaQuery, criteriaBuilder) -> criteriaBuilder.conjunction();
    }

    private Specification<RestrictionDbo> restrictionDescriptionSpecificationBuilder(SearchCriterion criterion) {
        return (root, criteriaQuery, criteriaBuilder) -> SpecificationUtils.buildPredicate(root.get("description"), criterion, criteriaBuilder);
    }

    private Specification<RestrictionDbo> restrictionSpecificationBuilder(SearchCriterion criterion) {
        return (root, criteriaQuery, criteriaBuilder) -> SpecificationUtils.buildPredicate(root.get("externalId"), criterion, criteriaBuilder);
    }

}


