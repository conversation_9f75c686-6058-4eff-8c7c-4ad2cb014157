package lu.fujitsu.ts.cgdis.portal.connector.portaldb.specifications.permamonitor;

import lu.fujitsu.ts.cgdis.portal.connector.portaldb.dbos.PrestationAllowanceConfigurationDbo;
import lu.fujitsu.ts.cgdis.portal.connector.portaldb.dbos.permamonitor.PermDeploymentPlanDbo;
import lu.fujitsu.ts.cgdis.portal.connector.portaldb.specifications.impl.prestations.allowance.PrestationAllowanceConfigurationByVersionSpecification;
import lu.fujitsu.ts.cgdis.portal.core.pojo.prestation.allowance.PrestationAllowanceVersion;
import lu.fujitsu.ts.cgdis.portal.core.security.ICGDISSecurityConstraint;
import lu.fujitsu.ts.eportal.server.connector.jpa.specifications.secured.AbstractComposedSecuredSpecificationBuilder;
import lu.fujitsu.ts.eportal.server.core.domain.SearchCriterion;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

/**
 * Service for building specifications for PermDeploymentPlanDbo.
 */
@Service
public class PermDeploymentPlanSpecificationBuilder extends AbstractComposedSecuredSpecificationBuilder<ICGDISSecurityConstraint, PermDeploymentPlanDbo> {

    /**
     * Constructor for PermDeplloymentPlanSpecificationBuilder.
     * Initializes the builder.
     */
    public PermDeploymentPlanSpecificationBuilder() {
        super();
//        addBuilder("startDate", this::startDateSpecification);
    }


    /**
     * Builds a specification for PrestationAllowanceConfigurationDbo based on the version.
     *
     * @param searchCriterion the search criterion containing the version
     * @return the specification for PrestationAllowanceConfigurationDbo
     */
    private Specification<PrestationAllowanceConfigurationDbo> configVersionSpecification(SearchCriterion searchCriterion) {
        return new PrestationAllowanceConfigurationByVersionSpecification((PrestationAllowanceVersion) searchCriterion.getValue());
    }

//    private Specification<PrestationAllowanceConfigurationDbo> startDateSpecification(SearchCriterion searchCriterion) {
//        return new PrestationAllowanceConfigurationByDateSpecification((LocalDate) searchCriterion.getValue());
//    }

    /**
     * Builds the security constraint specification for PermDeploymentPlanDbo.
     *
     * @param securityConstraint the security constraint
     * @return the specification for PermDeploymentPlanDbo
     */
    @Override
    public Specification<PermDeploymentPlanDbo> buildSecurityConstraint(ICGDISSecurityConstraint securityConstraint) {
        // TODO complete the security constraint
        return (root, criteriaQuery, criteriaBuilder) -> criteriaBuilder.conjunction();
    }

}
