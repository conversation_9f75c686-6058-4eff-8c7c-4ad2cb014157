package lu.fujitsu.ts.cgdis.portal.connector.portaldb.dbos.serviceplan;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.FieldNameConstants;
import lu.fujitsu.ts.cgdis.portal.connector.portaldb.dbos.BaseDbo;
import lu.fujitsu.ts.cgdis.portal.connector.portaldb.dbos.OperationalFunctionDbo;
import lu.fujitsu.ts.cgdis.portal.connector.portaldb.dbos.PositionTemplateDbo;

import javax.persistence.*;
import java.util.Set;

/**
 * The type Service plan position dbo.
 */
@Data
@EqualsAndHashCode(callSuper = true,exclude = {"version","functions", "positionTemplate","modelVersion"})
@ToString(callSuper = true,exclude = {"version","functions", "positionTemplate","modelVersion"})
@Entity
@Table(name = "SERVICE_PLAN_POSITION")
@FieldNameConstants
public class ServicePlanPositionDbo extends BaseDbo {

    /**
     * The label.
     */
    @Column(name= "label")
    private String label;

    /**
     * The completion type.
     */
    @Column(name= "completion_type")
    private String completionType;

    /**
     * The Order.
     */
    @Column(name= "POSITION_ORDER")
    private Long order;

    /**
     * The icon.
     */
    @Column(name= "icon")
    private String icon;

    @ManyToMany()
    @JoinTable(
            name = "SERVICE_PLAN_POSITION_FUNCTIONS",
            joinColumns = { @JoinColumn(name = "SERVICE_PLAN_POSITION_TECID") },
            inverseJoinColumns = { @JoinColumn(name = "FUNCTION_TECID") }
    )
    private Set<OperationalFunctionDbo> functions;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "POSITION_TEMPLATE_TECID")
    private PositionTemplateDbo positionTemplate;

    /**
     * The service plan model version.
     */
    @Column(name = "POSITION_TEMPLATE_TECID",insertable = false ,updatable=false)
    private Long positionTemplateId;

    /**
     * The service plan model version.
     */
    @Column(name = "SERVICE_PLAN_MODEL_VERSION_TECID",insertable = false ,updatable=false)
    private Long versionId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "SERVICE_PLAN_MODEL_VERSION_TECID", insertable = false,updatable = false)
    private ServicePlanModelVersionDbo modelVersion;


}
