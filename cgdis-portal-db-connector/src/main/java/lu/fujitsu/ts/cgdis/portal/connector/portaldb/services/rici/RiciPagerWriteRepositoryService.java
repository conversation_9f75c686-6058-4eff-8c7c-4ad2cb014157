package lu.fujitsu.ts.cgdis.portal.connector.portaldb.services.rici;

import lu.fujitsu.ts.cgdis.portal.connector.portaldb.dbos.person.PersonDbo;
import lu.fujitsu.ts.cgdis.portal.connector.portaldb.dbos.rici.*;
import lu.fujitsu.ts.cgdis.portal.connector.portaldb.repositories.IPersonRepository;
import lu.fujitsu.ts.cgdis.portal.connector.portaldb.repositories.rici.IRiciAlertGroupPersonRepository;
import lu.fujitsu.ts.cgdis.portal.connector.portaldb.repositories.rici.IRiciAlertGroupRepository;
import lu.fujitsu.ts.cgdis.portal.connector.portaldb.repositories.rici.IRiciPagerRepository;
import lu.fujitsu.ts.cgdis.portal.connector.portaldb.services.SecuredOnlyWriteAndPartialRepositoryService;
import lu.fujitsu.ts.cgdis.portal.core.domain.rici.RiciPager;
import lu.fujitsu.ts.cgdis.portal.core.domain.rici.RiciPagerWithPassword;
import lu.fujitsu.ts.cgdis.portal.core.domain.rici.RiciPersonWithPagerAndAlertGroups;
import lu.fujitsu.ts.cgdis.portal.core.domain.rici.enums.RiciPagerStatus;
import lu.fujitsu.ts.cgdis.portal.core.exception.CGDISTechnicalException;
import lu.fujitsu.ts.cgdis.portal.core.exception.rici.PagerIdExistsException;
import lu.fujitsu.ts.cgdis.portal.core.exception.rici.PagerSerialNumberExistsException;
import lu.fujitsu.ts.cgdis.portal.core.logging.LoggingCategory;
import lu.fujitsu.ts.cgdis.portal.core.logging.LoggingDomain;
import lu.fujitsu.ts.cgdis.portal.core.security.ICGDISSecurityConstraint;
import lu.fujitsu.ts.cgdis.portal.services.rici.IRiciPagerPartialUpdate;
import lu.fujitsu.ts.cgdis.portal.services.rici.IRiciPagerWriteRepositoryService;
import lu.fujitsu.ts.eportal.server.connector.jpa.specifications.secured.ISecuredSpecificationBuilder;
import lu.fujitsu.ts.eportal.server.core.exceptions.NotFoundException;
import lu.fujitsu.ts.eportal.server.core.utils.EPortalConverter;
import lu.fujitsu.ts.eportal.server.logging.milestones.LogMilestone;
import org.dozer.Mapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

/**
 * Service class for write operations on RiciPager entities.
 * This class extends SecuredOnlyWriteAndPartialRepositoryService to provide secured write operations.
 */
@Service
@LogMilestone(category = LoggingCategory.REPOSITORY_SERVICE, domain = LoggingDomain.RICI)
@Transactional
public class RiciPagerWriteRepositoryService extends SecuredOnlyWriteAndPartialRepositoryService<ICGDISSecurityConstraint, RiciPager, RiciPagerDbo, Long, IRiciPagerRepository, IRiciPagerPartialUpdate> implements IRiciPagerWriteRepositoryService {

    private static final Logger LOGGER = LoggerFactory.getLogger(RiciPagerWriteRepositoryService.class);
    private final IPersonRepository personRepository;
    private final IRiciAlertGroupRepository riciAlertGroupRepository;
    private final IRiciAlertGroupPersonRepository riciAlertGroupPersonRepository;
    private EPortalConverter<RiciAlertGroupPersonDbo, RiciPersonWithPagerAndAlertGroups> personDbo2ModelConverter;

    /**
     * Constructor for RiciPagerWriteRepositoryService.
     *
     * @param repository  the repository for RiciPagerDbo entities
     * @param mapper      the mapper for entity to DBO conversion
     * @param specBuilder the specification builder for secured specifications
     */
    public RiciPagerWriteRepositoryService(IRiciPagerRepository repository, Mapper mapper, ISecuredSpecificationBuilder<ICGDISSecurityConstraint, RiciPagerDbo> specBuilder, IPersonRepository personRepository, IRiciAlertGroupRepository riciAlertGroupRepository, IRiciAlertGroupPersonRepository riciAlertGroupPersonRepository) {
        super(RiciPager.class, RiciPagerDbo.class, repository, mapper, specBuilder);
        this.personRepository = personRepository;
        this.riciAlertGroupRepository = riciAlertGroupRepository;
        this.personDbo2ModelConverter = new EPortalConverter(RiciAlertGroupPersonDbo.class, RiciPersonWithPagerAndAlertGroups.class, mapper);

        this.riciAlertGroupPersonRepository = riciAlertGroupPersonRepository;
    }

    // Override the new create method from the interface
    @Override
    @Transactional // Ensure transactionality for the overridden method
    public RiciPager create(ICGDISSecurityConstraint securityConstraint, RiciPager model, Long simCardTecid) {
        if (!canCreate(securityConstraint, model)) {
            // Throw security exception or handle appropriately
            throw new SecurityException("User does not have permission to create this RiciPager.");
        }

        LOGGER.debug("Manually mapping RiciPager domain to DBO for creation: {}, SIM Tecid: {}", model, simCardTecid);

        // Manual mapping from Domain (model) to DBO
        RiciPagerDbo dbo = new RiciPagerDbo();
        dbo.setPagerId(model.getPagerId());
        dbo.setSerialNumber(model.getSerialNumber());
        dbo.setManufacturer(model.getManufacturer());
        dbo.setModel(model.getModel());
        dbo.setDeliveryDate(model.getDeliveryDate());
        dbo.setStatus(model.getStatus()); // Assuming RiciPagerDbo uses the same enum

        // Handle SIM card association - Use the directly passed simCardTecid
        dbo.setSimCardTecid(simCardTecid); // Set the Long field in DBO directly

        // Explicitly DO NOT map assignment/other related fields during initial creation:
        // dbo.setActiveAssignment(..)
        // dbo.setRiciPagerAssignments(..)
        // dbo.setRiciPagerMaintenance(..)
        // dbo.setRiciPagerProgramming(..)
        // dbo.setActiveAssignmentTecid(..)

        // Audit fields (createdBy, createdOn, etc.) are typically handled by JPA @PrePersist or similar mechanisms,
        // or should be set here if manual handling is required. Assuming JPA handles them for now.

        LOGGER.debug("Saving manually mapped RiciPagerDbo: {}", dbo);
        RiciPagerDbo savedDbo = this.repository.save(dbo);
        LOGGER.debug("Saved RiciPagerDbo: {}", savedDbo);

        // Convert the saved DBO back to the Domain model to return
        RiciPager createdPager = this.dbo2ModelConverter.convert(savedDbo);

        if (createdPager != null) {
            // Eagerly fetch the maintenance record if it exists
            RiciPagerMaintenanceDbo maintenanceDbo = savedDbo.getRiciPagerMaintenance();
            if (maintenanceDbo != null) {
                // Create RiciPagerWithPassword instead of modifying RiciPager
                return RiciPagerWithPassword.from(createdPager, maintenanceDbo.getMaintenancePassword());
            }
            return createdPager;
        } else {
            LOGGER.error("Failed to convert saved DBO to domain model");
            throw new CGDISTechnicalException("Failed to convert saved pager to domain model");
        }
    }

    // Optional: Provide a default implementation for the original create method or remove it
    // from the interface if it's truly replaced. If kept, it should likely delegate.
    // Note: The parent class SecuredOnlyWriteAndPartialRepositoryService already has a create(constraint, model)
    // which uses Dozer. We might not need to override it here if the interface doesn't declare it.
    // If IRiciPagerWriteRepositoryService *does* declare create(constraint, model), we need this override.
    // Assuming the interface requires it based on previous steps:
    @Override
    @Transactional
    public RiciPager create(ICGDISSecurityConstraint securityConstraint, RiciPager model) {
        // Delegate to the new method, assuming null simCardTecid if called this way
        LOGGER.warn("Calling create(model) without simCardTecid. SIM will not be associated.");
        return this.create(securityConstraint, model, null);
    }

    // Remove the updatePager method implementation
    // @Override
    // @Transactional // Ensure transactionality
    // @LogMilestone(action = "update pager")
    // public RiciPager updatePager(ICGDISSecurityConstraint securityConstraint, Long tecid, RiciPager pagerData) throws NotFoundException { ... }


    /**
     * Checks if the current user can modify the entity with the given ID.
     *
     * @param securityConstraint the security constraint to apply
     * @param id                 the ID of the entity to check
     * @return true if the user can modify the entity, false otherwise
     */
    @Override
    public boolean canModify(ICGDISSecurityConstraint securityConstraint, Long id) {
        // TODO: Implement proper security check
        return true;
    }

    /**
     * Checks if the current user can create the given entity.
     *
     * @param securityConstraint the security constraint to apply
     * @param model              the entity to check
     * @return true if the user can create the entity, false otherwise
     */
    @Override
    public boolean canCreate(ICGDISSecurityConstraint securityConstraint, RiciPager model) {
        if (this.repository.existsBySerialNumber(model.getSerialNumber())) {
            throw new PagerSerialNumberExistsException();
        }
        return true;
    }

    /**
     * Checks if the current user can partially update the given entity.
     *
     * @param securityConstraint the security constraint to apply
     * @param model              the entity to check
     * @return true if the user can partially update the entity, false otherwise
     */
    @Override
    public boolean canPartialUpdate(ICGDISSecurityConstraint securityConstraint, IRiciPagerPartialUpdate model) {

        if (this.repository.existsBySerialNumber(model.getNewSerialNumber(null))) {
            this.repository.findBySerialNumber(model.getNewSerialNumber(null)).ifPresent(r -> {
                if (!r.getTecid().equals(model.getTechnicalId())) {
                    throw new PagerSerialNumberExistsException();
                }
            });
        }

        if (this.repository.existsByPagerId(model.getNewPagerId(null))) {
            this.repository.findByPagerId(model.getNewPagerId(null)).ifPresent(r -> {
                if (!r.getTecid().equals(model.getTechnicalId())) {
                    throw new PagerIdExistsException();
                }
            });
        }

        return true;
    }

    /**
     * Applies a partial update to the given entity.
     *
     * @param model the partial update to apply
     * @param dbo   the entity to update
     * @return the updated entity
     */
    @Override
    protected RiciPagerDbo applyPartialUpdate(IRiciPagerPartialUpdate model, RiciPagerDbo dbo) {
        LOGGER.debug("Applying partial update for RiciPager Tecid: {}", model.getTechnicalId());

        // Get proposed new values
        String newPagerId = model.getNewPagerId(dbo.getPagerId());
        String newSerialNumber = model.getNewSerialNumber(dbo.getSerialNumber());
        String newManufacturer = model.getNewManufacturer(dbo.getManufacturer());
        String newModel = model.getNewModel(dbo.getModel());
        java.time.LocalDate newDeliveryDate = model.getNewDeliveryDate(dbo.getDeliveryDate());
        RiciPagerStatus newStatus = model.getNewStatus(dbo.getStatus());
        Long newSimCardTecid = model.getNewSimCardTecid(dbo.getSimCardTecid()); // This can be null

        // Only update fields if the model provided a non-null value
        // (except for simCardTecid which *can* be explicitly set to null)
        // For Pager ID, if newPagerId is null, it means the user wants to clear it.
        dbo.setPagerId(newPagerId);
        
        if (newSerialNumber != null) {
            // Check for blank string as well for required fields if necessary, though @NotNull handles null
            if (newSerialNumber.trim().isEmpty()) {
                LOGGER.warn("Attempted to set SerialNumber to blank for Pager Tecid: {}. Ignoring.", model.getTechnicalId());
            } else {
                dbo.setSerialNumber(newSerialNumber);
            }
        }
        if (newManufacturer != null) {
            dbo.setManufacturer(newManufacturer);
        }
        if (newModel != null) {
            dbo.setModel(newModel);
        }
        if (newDeliveryDate != null) {
            dbo.setDeliveryDate(newDeliveryDate);
        }
        if (newStatus != null) {
            dbo.setStatus(newStatus);
        }

        // Always apply the simCardTecid update, as setting it to null is a valid operation here
        dbo.setSimCardTecid(newSimCardTecid);

        // Audit fields (updatedBy, updatedOn) are handled by AuditingEntityListener
        // ActiveAssignmentTecid is not updated here
        LOGGER.debug("Applied partial update, DBO state before save: {}", dbo);
        return dbo;
    }

    /**
     * Indicates whether the entity should be refreshed after a partial update.
     *
     * @return true if the entity should be refreshed, false otherwise
     */
    @Override
    protected boolean mustRefresAfterPartialUpdatehDbo() {
        return true;
    }

    /**
     * Indicates whether the partial update should be flushed immediately.
     *
     * @return true if the partial update should be flushed, false otherwise
     */
    @Override
    protected boolean mustFlushPartialUpdate() {
        return true;
    }

    @Override
    @Transactional
    @LogMilestone(action = "update active assignment tecid")
    public void updateActiveAssignmentTecid(ICGDISSecurityConstraint securityConstraint, Long pagerTecid, Long assignmentTecid) throws NotFoundException {
        if (!canModify(securityConstraint, pagerTecid)) {
            // Throw security exception or handle appropriately
            throw new SecurityException("User does not have permission to modify this RiciPager (Tecid: " + pagerTecid + ").");
        }

        LOGGER.debug("Updating active assignment Tecid for Pager Tecid: {} to Assignment Tecid: {}", pagerTecid, assignmentTecid);

        RiciPagerDbo pagerDbo = this.repository.findById(pagerTecid)
                .orElseThrow(() -> new NotFoundException("RiciPager not found with tecid: " + pagerTecid));

        pagerDbo.setActiveAssignmentTecid(assignmentTecid);

        // Save the updated DBO. JPA will handle the update query.
        // No need to map back to domain object as this method returns void.
        this.repository.save(pagerDbo);
        LOGGER.info("Successfully updated Pager (Tecid: {}) with Active Assignment Tecid: {}", pagerTecid, assignmentTecid);
    }

    @Override
    public RiciPersonWithPagerAndAlertGroups updateAlertGroup(
            ICGDISSecurityConstraint securityConstraint,
            Long personTecid,
            Long alertGroupTecid,
            Boolean alertGroupValue
    ) throws NotFoundException {
        LOGGER.info("Updating alert group (tecid={}) for person (tecid={}) with value={}", alertGroupTecid, personTecid, alertGroupValue);

        PersonDbo personDbo = personRepository.findById(personTecid)
                .orElseThrow(() -> {
                    LOGGER.warn("Person not found with tecid={}", personTecid);
                    return new NotFoundException("Person not found with tecid: " + personTecid);
                });

        RiciAlertGroupDbo alertGroupDbo = riciAlertGroupRepository.findById(alertGroupTecid)
                .orElseThrow(() -> {
                    LOGGER.warn("Alert group not found with tecid={}", alertGroupTecid);
                    return new NotFoundException("Alert group not found with tecid: " + alertGroupTecid);
                });

        RiciAlertGroupPersonDboId id = new RiciAlertGroupPersonDboId(alertGroupTecid, personTecid);
        Optional<RiciAlertGroupPersonDbo> alertGroupPersonDbo = riciAlertGroupPersonRepository.findById(id);

        if (alertGroupPersonDbo.isPresent()) {
            LOGGER.debug("Found existing RiciAlertGroupPersonDbo with id={}", id);

            if (Boolean.FALSE.equals(alertGroupValue) && alertGroupPersonDbo.get().getTransmissionDatetime() == null) {
                LOGGER.info("Deleting alert group person entry because value is FALSE and no transmission timestamp exists (tecid={}, tecid={})", alertGroupTecid, personTecid);
                riciAlertGroupPersonRepository.delete(alertGroupPersonDbo.get());
                return null;
            } else {
                alertGroupPersonDbo.get().setValue(alertGroupValue);
                RiciAlertGroupPersonDbo result = riciAlertGroupPersonRepository.save(alertGroupPersonDbo.get());
                LOGGER.info("Updated alert group person (tecid={}, tecid={}) with new value={}", alertGroupTecid, personTecid, alertGroupValue);

                return personDbo2ModelConverter.convert(result);
            }

        } else {
            LOGGER.debug("No existing RiciAlertGroupPersonDbo found for id={}, creating new entry", id);

            RiciAlertGroupPersonDbo newAlertGroupPersonDbo = new RiciAlertGroupPersonDbo();
            newAlertGroupPersonDbo.setId(id);
            newAlertGroupPersonDbo.setValue(alertGroupValue);
            newAlertGroupPersonDbo.setAlertGroupDbo(alertGroupDbo);
            newAlertGroupPersonDbo.setPersonDbo(personDbo);

            RiciAlertGroupPersonDbo result = riciAlertGroupPersonRepository.save(newAlertGroupPersonDbo);
            LOGGER.info("Created new alert group person (tecid={}, tecid={}) with value={}", alertGroupTecid, personTecid, alertGroupValue);

            return personDbo2ModelConverter.convert(result);

        }
    }


}
