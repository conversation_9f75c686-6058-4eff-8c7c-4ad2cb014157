package lu.fujitsu.ts.cgdis.portal.connector.portaldb.specifications;

import lu.fujitsu.ts.cgdis.portal.connector.portaldb.dbos.prestation.allowance.PrestationAllowanceResultDbo;
import lu.fujitsu.ts.cgdis.portal.core.domain.ICGDISPortalUserDetails;
import lu.fujitsu.ts.eportal.server.connector.jpa.specifications.secured.AbstractSecuredSpecificationBuilder;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
public class AllowanceSpecificationBuilder extends AbstractSecuredSpecificationBuilder<ICGDISPortalUserDetails, PrestationAllowanceResultDbo> {

    @Override
    public Specification<PrestationAllowanceResultDbo> buildSecurityConstraint(ICGDISPortalUserDetails icgdisPortalUserDetails) {
        return (root, criteriaQuery, criteriaBuilder) -> criteriaBuilder.conjunction();
    }
}
