package lu.fujitsu.ts.cgdis.portal.connector.portaldb.services.person;

import lu.fujitsu.ts.cgdis.portal.connector.portaldb.dbos.BaseDbo;
import lu.fujitsu.ts.cgdis.portal.connector.portaldb.dbos.person.*;
import lu.fujitsu.ts.cgdis.portal.connector.portaldb.repositories.IPersonRepository;
import lu.fujitsu.ts.cgdis.portal.connector.portaldb.repositories.SelectPathBuilder;
import lu.fujitsu.ts.cgdis.portal.connector.portaldb.services.AbstractBaseCGDISSecuredRepositoryService;
import lu.fujitsu.ts.cgdis.portal.connector.portaldb.specifications.impl.SpecificationJointUtils;
import lu.fujitsu.ts.cgdis.portal.core.converter.EportalConverterFactory;
import lu.fujitsu.ts.cgdis.portal.core.domain.ICGDISPortalUserDetails;
import lu.fujitsu.ts.cgdis.portal.core.domain.assignement.Assignment;
import lu.fujitsu.ts.cgdis.portal.core.domain.person.*;
import lu.fujitsu.ts.cgdis.portal.core.logging.LoggingCategory;
import lu.fujitsu.ts.cgdis.portal.core.logging.LoggingDomain;
import lu.fujitsu.ts.cgdis.portal.services.person.IGeneralContactInformationRepositoryService;
import lu.fujitsu.ts.cgdis.portal.services.person.IOperationalContactInformationRepositoryService;
import lu.fujitsu.ts.cgdis.portal.services.person.IPersonCRUDRepositoryService;
import lu.fujitsu.ts.eportal.server.connector.jpa.specifications.SpecificationUtils;
import lu.fujitsu.ts.eportal.server.connector.jpa.specifications.secured.AbstractComposedSecuredSpecificationBuilder;
import lu.fujitsu.ts.eportal.server.core.exceptions.NotFoundException;
import lu.fujitsu.ts.eportal.server.logging.milestones.LogMilestone;
import lu.fujitsu.ts.eportal.server.logging.milestones.LoggableType;
import lu.fujitsu.ts.eportal.server.logging.milestones.LoggableValue;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.dozer.Mapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
@LogMilestone(category = LoggingCategory.REPOSITORY_SERVICE, domain = LoggingDomain.PERSON)
public class PersonCRUDRepositoryService extends AbstractBaseCGDISSecuredRepositoryService<PersonCRUD, PersonDbo, IPersonRepository> implements IPersonCRUDRepositoryService {

    private static final Logger LOGGER = LoggerFactory.getLogger(PersonCRUDRepositoryService.class);

    private final EportalConverterFactory converterFactory;

    private final IGeneralContactInformationRepositoryService generalContactInformationRepositoryService;

    private final IOperationalContactInformationRepositoryService operationalContactInformationRepositoryService;

    @Autowired
    public PersonCRUDRepositoryService(IPersonRepository repository, AbstractComposedSecuredSpecificationBuilder<ICGDISPortalUserDetails, PersonDbo> specBuilder, Mapper mapper, EportalConverterFactory converterFactory, IGeneralContactInformationRepositoryService generalContactInformationRepositoryService, IOperationalContactInformationRepositoryService operationalContactInformationRepositoryService) {
        super("Person CRUD", PersonCRUD.class, PersonDbo.class, repository, specBuilder, mapper);
        this.converterFactory = converterFactory;
        this.generalContactInformationRepositoryService = generalContactInformationRepositoryService;
        this.operationalContactInformationRepositoryService = operationalContactInformationRepositoryService;
    }

    @Override

    public boolean canModify(ICGDISPortalUserDetails icgdisPortalUserDetails, Long aLong) {
        return true;
    }


    @Override
    @LogMilestone(action = "create new person")

    public PersonCRUD create(PersonCRUD personCRUD) {
        return super.create(preparePersonCRUDBeforeSaveOrUpdate(personCRUD));
    }

    @Override
    @LogMilestone(action = "update person")

    public PersonCRUD update(ICGDISPortalUserDetails icgdisPortalUserDetails, @LoggableValue("tecid") PersonCRUD model) {
        return super.update(icgdisPortalUserDetails, preparePersonCRUDBeforeSaveOrUpdate(model));
    }

    private PersonCRUD preparePersonCRUDBeforeSaveOrUpdate(PersonCRUD person) {
        // To save General contact information, the person must be added to the General contact information
        if (person.getGeneralContactInformation() != null && person.getGeneralContactInformation().getPerson() == null) {
            LOGGER.debug("Add person into generalContactInformationCRUD for person creation");
            person.getGeneralContactInformation().setPerson(person);
        }

        // To save operational contact information, the person must be added to the operational contact information
        if (person.getOperationalContactInformation() != null && person.getOperationalContactInformation().getPerson() == null) {
            LOGGER.debug("Add person into operationalContactInformationCRUD for person creation");
            person.getOperationalContactInformation().setPerson(person);
        }

        // To save operational contact information, the person must be added to the operational contact information
        if (person.getOperationalVolunteerInternship() != null) {
            LOGGER.debug("Add person into operationalVolunteerIntership for person creation");
            person.getOperationalVolunteerInternship().setPerson(person);
        }

        if (person.getOperationalYoungFirefighter() != null) {
            LOGGER.debug("Add person into operationalYoungFirefighter for person creation");
            person.getOperationalYoungFirefighter().setPerson(person);
        }


        return person;
    }

    @Override
    @Transactional
    @LogMilestone(action = "find person by tic")
    public PersonCRUD findByTic(String tic) throws NotFoundException {
        LOGGER.debug("Find person with tic {}", tic);
        Optional<PersonDbo> one = repository.findOne(SpecificationUtils.and(
                byTic(tic)));
        if (one.isPresent()) {
            return dbo2ModelConverter.convert(one.get());
        }
        throw new NotFoundException(String.format("Person with tic %s not found", tic));
    }

    private Specification<PersonDbo> byTic(String tic) {
        return (root, criteriaQuery, criteriaBuilder) -> criteriaBuilder.equal(root.get("tic"), tic);
    }

    @Override
    @LogMilestone(action = "get bank accounts for persons")
    public Map<Long, BankAccount> getBankAccounts(@LoggableValue(type = LoggableType.COLLECTION, logItems = 20) Collection<Long> personIds) {
        LOGGER.debug("Get bank accounts for persons {}", personIds);
        return getRelatedEntitiesForPersons(personIds, PersonDbo.Fields.BANK_ACCOUNTS, BankAccountDbo.class, BankAccount.class);
    }

    @Override
    @LogMilestone(action = "get addresses for persons")
    public Map<Long, Address> getAddresses(@LoggableValue(type = LoggableType.COLLECTION, logItems = 20) Collection<Long> personIds) {
        LOGGER.debug("Get addresses for persons {}", personIds);
        return getRelatedEntitiesForPersons(personIds, PersonDbo.Fields.ADDRESSES, AddressDbo.class, Address.class);
    }


    @Override
    @LogMilestone(action = "get general contact information for persons")
    public Map<Long, GeneralContactInformation> getGeneralContactInformation(@LoggableValue(type = LoggableType.COLLECTION, logItems = 20)Collection<Long> personIds) {
        LOGGER.debug("Get general contact information for persons {}", personIds);
        return generalContactInformationRepositoryService.findByPersonIds(personIds);
    }

    @Override
    @LogMilestone(action = "get operational contact information for persons")
    public Map<Long, OperationalContactInformation> getOperationalContactInformation(@LoggableValue(type = LoggableType.COLLECTION, logItems = 20)Collection<Long> personIds) {
        LOGGER.debug("Get operational contact information for persons {}", personIds);
        return operationalContactInformationRepositoryService.findByPersonIds(personIds);
    }

    @Override
    @LogMilestone(action = "get assignments for persons")
    public Map<Long, List<Assignment>> getAssignments(@LoggableValue(type = LoggableType.COLLECTION, logItems = 20)Collection<Long> personIds) {
        LOGGER.debug("Get assignments for persons {}", personIds);
        return getManyRelatedEntitiesForPersons(personIds, PersonDbo.Fields.ASSIGNMENTS, AssignmentDbo.class, Assignment.class);
    }


    @Override
    @LogMilestone(action = "get operational occupations for persons")
    public Map<Long, List<PersonOperationalOccupation>> getOperationalOccupations(@LoggableValue(type = LoggableType.COLLECTION, logItems = 20)Collection<Long> personIds) {
        LOGGER.debug("Get operational occupations for persons {}", personIds);
        return getManyRelatedEntitiesForPersons(personIds, PersonDbo.Fields.PERSON_OPERATIONAL_OCCUPATIONS, PersonOperationalOccupationDbo.class, PersonOperationalOccupation.class);
    }

    @Override
    @LogMilestone(action = "get managerial occupations for persons")
    public Map<Long, List<PersonManagerialOccupation>> getManagerialOccupations(@LoggableValue(type = LoggableType.COLLECTION, logItems = 20)Collection<Long> personIds) {
        LOGGER.debug("Get managerial occupations for persons {}", personIds);
        return getManyRelatedEntitiesForPersons(personIds, PersonDbo.Fields.PERSON_MANAGERIAL_OCCUPATIONS, PersonManagerialOccupationDbo.class, PersonManagerialOccupation.class);
    }

    @Override
    @LogMilestone(action = "get grades for persons")
    public Map<Long, List<PersonOperationalGrade>> getGrades(@LoggableValue(type = LoggableType.COLLECTION, logItems = 20)Collection<Long> personIds) {
        LOGGER.debug("Get grades for persons {}", personIds);
        return getManyRelatedEntitiesForPersons(personIds, PersonDbo.Fields.PERSON_OPERATIONAL_GRADES, PersonOperationalGradeDbo.class, PersonOperationalGrade.class);
    }

    @Override
    @LogMilestone(action = "get driving licences for persons")
    public Map<Long, List<DrivingLicense>> getDrivingLicenses(@LoggableValue(type = LoggableType.COLLECTION, logItems = 20)Collection<Long> personIds) {
        LOGGER.debug("Get driving licences for persons {}", personIds);
        return getManyRelatedEntitiesForPersons(personIds, PersonDbo.Fields.DRIVING_LICENSES, DrivingLicenseDbo.class, DrivingLicense.class);
    }

    @Override
    @LogMilestone(action = "get aptitudes for persons")
    public Map<Long, List<PersonAptitude>> getAptitudes(@LoggableValue(type = LoggableType.COLLECTION, logItems = 20)Collection<Long> personIds) {
        LOGGER.debug("Get aptitudes for persons {}", personIds);
        return getManyRelatedEntitiesForPersons(personIds, PersonDbo.Fields.APTITUDES, PersonAptitudeDbo.class, PersonAptitude.class);
    }

    @Override
    @LogMilestone(action = "get restrictions for persons")
    public Map<Long, List<PersonRestriction>> getRestrictions(@LoggableValue(type = LoggableType.COLLECTION, logItems = 20)Collection<Long> personIds) {
        LOGGER.debug("Get restrictions for persons {}", personIds);
        return getManyRelatedEntitiesForPersons(personIds, PersonDbo.Fields.RESTRICTIONS, PersonRestrictionDbo.class, PersonRestriction.class);
    }

    @Override
    @LogMilestone(action = "get suspensions periods for persons")
    public Map<Long, List<SuspensionPeriod>> getSuspensions(@LoggableValue(type = LoggableType.COLLECTION, logItems = 20)Collection<Long> personIds) {
        LOGGER.debug("Get suspensions periods for persons {}", personIds);
        return getManyRelatedEntitiesForPersons(personIds, PersonDbo.Fields.SUSPENSION_PERIODS, SuspensionPeriodDbo.class, SuspensionPeriod.class);
    }
    @Override
    @LogMilestone(action = "get remarks for persons")
    public Map<Long, List<PersonMedicalRemark>> getRemarks(@LoggableValue(type = LoggableType.COLLECTION, logItems = 20)Collection<Long> personIds) {
        LOGGER.debug("Get remarks for persons {}", personIds);
        return getManyRelatedEntitiesForPersons(personIds, PersonDbo.Fields.REMARKS, PersonMedicalRemarkDbo.class,PersonMedicalRemark.class);
    }

    private <D, T> Map<Long, T> getRelatedEntitiesForPersons(Collection<Long> personIds, String fieldName, Class<D> dboClass, Class<T> domainClass) {
        SelectPathBuilder personTecidPathBuilder = (root, criteriaQuery, criteriaBuilder) -> root.get(BaseDbo.Fields.TECID);
        List<Object[]> distinctColumns = repository.findDistinctColumns(Arrays.asList(
                        ImmutablePair.of(Long.class, personTecidPathBuilder),
                        ImmutablePair.of(dboClass, (root, criteriaQuery, criteriaBuilder) -> SpecificationJointUtils.innerJoin(root, fieldName))
                )
                , (root, query, criteriaBuilder) -> root.get(BaseDbo.Fields.TECID).in(personIds)
                , Arrays.asList(personTecidPathBuilder));
        return distinctColumns
                .stream().collect(Collectors.toMap(
                        objects -> (Long) objects[0],
                        objects -> converterFactory.convert(dboClass, domainClass, (D) objects[1])
                ));
    }

    private <D, T> Map<Long, List<T>> getManyRelatedEntitiesForPersons(Collection<Long> personIds, String fieldName, Class<D> dboClass, Class<T> domainClass) {
        SelectPathBuilder personTecidPathBuilder = (root, criteriaQuery, criteriaBuilder) -> root.get(BaseDbo.Fields.TECID);
        List<Object[]> distinctColumns = repository.findDistinctColumns(Arrays.asList(
                        ImmutablePair.of(Long.class, personTecidPathBuilder),
                        ImmutablePair.of(dboClass, (root, criteriaQuery, criteriaBuilder) -> SpecificationJointUtils.innerJoin(root, fieldName))
                )
                , (root, query, criteriaBuilder) -> root.get(BaseDbo.Fields.TECID).in(personIds)
                , null);
        return distinctColumns
                .stream()
                .map(oneTuple -> ImmutablePair.of((Long) oneTuple[0], converterFactory.convert(dboClass, domainClass, (D) oneTuple[1])))
                .collect(Collectors.groupingBy(
                        ImmutablePair::getLeft,
                        Collectors.collectingAndThen(Collectors.toList(), list -> list.stream().map(ImmutablePair::getRight).collect(Collectors.toList())
                        )));
    }


}
