package lu.fujitsu.ts.cgdis.portal.connector.portaldb.dbos.rici;

import lombok.*;
import lombok.experimental.Accessors;
import lombok.experimental.FieldNameConstants;
import lombok.experimental.SuperBuilder;
import lu.fujitsu.ts.cgdis.portal.connector.portaldb.dbos.BaseDbo;
import lu.fujitsu.ts.cgdis.portal.connector.portaldb.dbos.CGDISEntityDBO;
import lu.fujitsu.ts.cgdis.portal.core.domain.rici.RiciRicRangeType;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.LinkedHashSet;
import java.util.Set;

/**
 * Entity representing a RIC range in the RICI system.
 */
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@ToString(onlyExplicitlyIncluded = true, callSuper = true)
@EqualsAndHashCode(onlyExplicitlyIncluded = true, callSuper = true)
@Accessors(chain = true)
@Entity
@Table(name = RiciRicRangeDbo.TABLE_NAME)
@FieldNameConstants
public class RiciRicRangeDbo extends BaseDbo implements Serializable {
    public static final String TABLE_NAME = "RICI_RIC_RANGE";
    public static final String COLUMN_NAME_NAME = "NAME";
    public static final String COLUMN_RANGESTART_NAME = "RANGE_START";
    public static final String COLUMN_RANGESTART_STRING_NAME = "RANGE_START_STRING";
    public static final String COLUMN_RANGEEND_STRING_NAME = "RANGE_END_STRING";
    public static final String COLUMN_RANGEEND_NAME = "RANGE_END";
    public static final String COLUMN_TYPE_NAME = "TYPE";
    public static final String COLUMN_ACTIVE_ALLOCATION_ENTITY_TECID = "ACTIVE_ALLOCATION_ENTITY_TECID";
    public static final String COLUMN_DELETE_DATETIME_NAME = "DELETE_DATETIME";
    private static final long serialVersionUID = 1L;

    /**
     * The name of the RIC range.
     */
    @ToString.Include
    @EqualsAndHashCode.Include
    @Column(name = COLUMN_NAME_NAME, nullable = false, length = 50)
    private String name;

    /**
     * The start of the RIC range.
     */
    @ToString.Include
    @EqualsAndHashCode.Include
    @Column(name = COLUMN_RANGESTART_NAME, nullable = false)
    private Long rangeStart;

    /**
     * The start of the RIC range in string.
     */
    @ToString.Include
    @EqualsAndHashCode.Include
    @Column(name = COLUMN_RANGESTART_STRING_NAME, nullable = false)
    private String rangeStartString;

    /**
     * The end of the RIC range.
     */
    @ToString.Include
    @EqualsAndHashCode.Include
    @Column(name = COLUMN_RANGEEND_NAME, nullable = false)
    private Long rangeEnd;

    /**
     * The end of the RIC range in string.
     */
    @ToString.Include
    @EqualsAndHashCode.Include
    @Column(name = COLUMN_RANGEEND_STRING_NAME, nullable = false)
    private String rangeEndString;

    /**
     * The type of the RIC range.
     */
    @Column(name = COLUMN_TYPE_NAME, nullable = false, length = 20)
    @Enumerated(EnumType.STRING)
    private RiciRicRangeType type;

    /**
     * The entity associated with this RIC range.
     */
    @OneToMany(mappedBy = "ricRange", fetch = FetchType.LAZY)
    @Builder.Default
    private Set<RiciRicRangeEntityDbo> ricRangeEntities = new LinkedHashSet<>();

    /**
     * The active allocation of the RIC range.
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = COLUMN_ACTIVE_ALLOCATION_ENTITY_TECID, updatable = false, insertable = false)
    private CGDISEntityDBO activeAllocationEntity;


    /**
     * The active allocation Tecid of the RIC range.
     */
    @Column(name = COLUMN_ACTIVE_ALLOCATION_ENTITY_TECID)
    private Long activeAllocationEntityTecid;

    /**
     * The datetime of physical deletion.
     */
    @ToString.Include
    @EqualsAndHashCode.Include
    @Column(name = COLUMN_DELETE_DATETIME_NAME)
    private LocalDateTime deleteDatetime;

    @OneToMany(mappedBy = "riciRicRange")
    private Set<RiciAlertGroupDbo> riciAlertGroups = new LinkedHashSet<>();
}
