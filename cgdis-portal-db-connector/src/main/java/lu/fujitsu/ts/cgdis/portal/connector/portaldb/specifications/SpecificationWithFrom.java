package lu.fujitsu.ts.cgdis.portal.connector.portaldb.specifications;

import org.springframework.data.jpa.domain.Specification;

import javax.persistence.criteria.AbstractQuery;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.From;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;

/**
 * Get predicate from  {@link From} or  {@link Root}
 *
 * @param <DBO> the type parameter
 */
public interface SpecificationWithFrom<DBO> extends Specification<DBO> {


    default Predicate toPredicate(Root<DBO> root, CriteriaQuery<?> query, CriteriaBuilder criteriaBuilder){
        return toPredicateFrom(root, query, criteriaBuilder);
    }



    /**
     * To predicate from predicate.
     *
     * @param <T>   the type parameter
     * @param from  the from
     * @param query the query
     * @param cb    the cb
     * @return the predicate
     */
    <T> Predicate toPredicateFrom(From<T, DBO> from, AbstractQuery<?> query, CriteriaBuilder cb) ;

}
