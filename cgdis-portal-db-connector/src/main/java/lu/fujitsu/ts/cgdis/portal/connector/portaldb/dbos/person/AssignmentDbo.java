package lu.fujitsu.ts.cgdis.portal.connector.portaldb.dbos.person;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.FieldNameConstants;
import lu.fujitsu.ts.cgdis.portal.connector.portaldb.dbos.BaseDbo;
import lu.fujitsu.ts.cgdis.portal.connector.portaldb.dbos.CGDISEntityDBO;
import lu.fujitsu.ts.cgdis.portal.connector.portaldb.dbos.serviceplan.ServicePlanVersionDbo;
import lu.fujitsu.ts.cgdis.portal.core.domain.enums.AssignmentType;
import lu.fujitsu.ts.cgdis.portal.core.domain.enums.PrimaryType;
import org.hibernate.annotations.BatchSize;

import javax.persistence.*;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Objects;
import java.util.Set;

/**
 * The type Assignment dbo.
 */
@Entity
@Table(name = "ASSIGNMENT")
@Data
@ToString(exclude = {"person", "functions"})
@EqualsAndHashCode(callSuper = true, exclude = {"person", "functions"})
@FieldNameConstants
public class AssignmentDbo extends BaseDbo {
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "PERSON_TECID")
    private PersonDbo person;

    @Column(name = "PERSON_TECID", insertable = false, updatable = false)
    private Long personTecid;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "ENTITY_TECID")
    private CGDISEntityDBO entity;
    @Column(name = "PRIMARY_TYPE")
    @Enumerated(EnumType.STRING)
    private PrimaryType primaryType;
    @Column(name = "ASSIGNMENT_TYPE")
    @Enumerated(EnumType.STRING)
    private AssignmentType type;
    @Column(name = "START_DATE")
    private LocalDate startDate;
    @Column(name = "END_DATE")
    private LocalDate endDate;
    @Column(name = "ASSIGNMENT_EXTERNAL_TECID")
    private Long assignmentExternalTecid;
    /**
     * The delete date time.
     */
    @Column(name= "DELETE_DATETIME")
    private LocalDateTime deleteDateTime;

    @OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.REMOVE)
    @JoinColumn(name="ASSIGNMENT_TECID", referencedColumnName="TECID",insertable = false, updatable = false)
    @BatchSize(size = 10)
    private Set<AssignmentFunctionOperationalDbo> functions;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        if (!super.equals(o)) return false;
        AssignmentDbo that = (AssignmentDbo) o;
        return Objects.equals(primaryType, that.primaryType) &&
                Objects.equals(type, that.type) &&
                Objects.equals(startDate, that.startDate) &&
                Objects.equals(endDate, that.endDate) &&
                Objects.equals(deleteDateTime, that.deleteDateTime) &&
                Objects.equals(assignmentExternalTecid, that.assignmentExternalTecid);
    }

    @Override
    public int hashCode() {

        return Objects.hash(super.hashCode(), primaryType, type, startDate, endDate, assignmentExternalTecid, deleteDateTime);
    }
}
