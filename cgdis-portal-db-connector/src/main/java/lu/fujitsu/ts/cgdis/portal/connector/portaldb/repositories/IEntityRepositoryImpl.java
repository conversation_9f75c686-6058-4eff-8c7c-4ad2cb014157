package lu.fujitsu.ts.cgdis.portal.connector.portaldb.repositories;

import lu.fujitsu.ts.cgdis.portal.connector.portaldb.dbos.CGDISEntityDBO;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;

/**
 * The type Entity repository.
 */
@Repository
public class IEntityRepositoryImpl extends AbstractRepositoryImpl<CGDISEntityDBO> implements IEntityRepositoryCustom {
    /**
     * The Entity manager.
     */
    @PersistenceContext
    private EntityManager entityManager;

    /**
     * Instantiates a new Entity repository.
     */
    public IEntityRepositoryImpl() {
        super(CGDISEntityDBO.class);
    }

    @Override
    protected EntityManager getEntityManager() {
        return entityManager;
    }


}
