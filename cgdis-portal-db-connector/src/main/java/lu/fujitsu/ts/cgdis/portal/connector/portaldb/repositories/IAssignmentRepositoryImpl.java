package lu.fujitsu.ts.cgdis.portal.connector.portaldb.repositories;

import lu.fujitsu.ts.cgdis.portal.connector.portaldb.dbos.person.AssignmentDbo;
import lu.fujitsu.ts.cgdis.portal.connector.portaldb.dbos.person.PersonDbo;
import lu.fujitsu.ts.cgdis.portal.core.domain.assignement.AssignmentStatus;
import lu.fujitsu.ts.cgdis.portal.core.domain.enums.AssignmentType;
import lu.fujitsu.ts.cgdis.portal.core.domain.enums.PrimaryType;
import org.apache.commons.lang3.BooleanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.jpa.domain.Specification;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Tuple;
import javax.persistence.TypedQuery;
import javax.persistence.criteria.*;
import java.util.function.BiFunction;
import java.util.function.BiPredicate;

/**
 * The type Assignment repository.
 */
public class IAssignmentRepositoryImpl extends AbstractRepositoryImpl<AssignmentDbo> implements IAssignmentRepositoryCustom {
    /**
     * The constant LOGGER.
     */
    private static final Logger LOGGER = LoggerFactory.getLogger(IAssignmentRepositoryImpl.class);

    /**
     * The Entity manager.
     */
    @PersistenceContext
    private EntityManager entityManager;

    /**
     * Instantiates a new Assignment repository.
     */
    public IAssignmentRepositoryImpl() {
        super(AssignmentDbo.class);
    }

    @Override
    protected EntityManager getEntityManager() {
        return entityManager;
    }

    @Override
    public AssignmentStatus getAssignementStatusForEntity(Specification<AssignmentDbo> specs) {
        LOGGER.debug("getAssignementStatus");

        CriteriaBuilder criteriaBuilder = getEntityManager().getCriteriaBuilder();
        CriteriaQuery<Tuple> criteriaQuery = criteriaBuilder.createQuery(Tuple.class);
        Root<AssignmentDbo> root = criteriaQuery.from(AssignmentDbo.class);
        Join<AssignmentDbo, PersonDbo> person = root.join("person", JoinType.INNER);
        Expression<Integer> literal1 = criteriaBuilder.literal(1);
        Expression<Integer> literal0 = criteriaBuilder.literal(0);
        Expression<Integer> selectPro = criteriaBuilder.selectCase().when(criteriaBuilder.equal(root.get("type"), AssignmentType.PRO), literal1).otherwise(literal0).as(Integer.class);
        Expression<Integer> selectVol = criteriaBuilder.selectCase().when(criteriaBuilder.equal(root.get("type"), AssignmentType.VOL), literal1).otherwise(literal0).as(Integer.class);
        Expression<Integer> selectExt = criteriaBuilder.selectCase().when(criteriaBuilder.equal(root.get("type"), AssignmentType.EXT), literal1).otherwise(literal0).as(Integer.class);
        Expression<Integer> selectPrimaryTypeTechnicalPro = criteriaBuilder.selectCase().when(
                criteriaBuilder.and(
                        criteriaBuilder.equal(root.get(AssignmentDbo.Fields.PRIMARY_TYPE), PrimaryType.TECHNICAL),
                        criteriaBuilder.equal(root.get("type"), AssignmentType.PRO)
                ),
                literal1
        ).otherwise(literal0).as(Integer.class);
        Expression<Integer> selectPrimaryTypeTechnicalVol = criteriaBuilder.selectCase().when(
                criteriaBuilder.and(
                        criteriaBuilder.equal(root.get(AssignmentDbo.Fields.PRIMARY_TYPE), PrimaryType.TECHNICAL),
                        criteriaBuilder.equal(root.get("type"), AssignmentType.VOL)
                ),
                literal1
        ).otherwise(literal0).as(Integer.class);
        BiFunction<String, AssignmentType, Expression<Integer>> computePersonStatusCase = (personAttribute, assignmentType) -> {
            return criteriaBuilder.selectCase().when(criteriaBuilder.and(
                    criteriaBuilder.isTrue(person.get(personAttribute)), criteriaBuilder.equal(root.get("type"), assignmentType)
            ), literal1).otherwise(literal0).as(Integer.class);
        };
        Expression<Integer> selectCandidate = computePersonStatusCase.apply("isCandidate", AssignmentType.VOL);
        Expression<Integer> selectIntern = computePersonStatusCase.apply("isIntern", AssignmentType.VOL);
        Expression<Integer> selectOperationalFirefighter = computePersonStatusCase.apply("isOperationalFirefighter", AssignmentType.VOL);
        Expression<Integer> selectProfessionalAdmTech = computePersonStatusCase.apply("isProfessionalAdmTech", AssignmentType.PRO);

        Expression<Integer> selectProfessionalOperational = computePersonStatusCase.apply("isProfessionalOperational", AssignmentType.PRO);
        Expression<Integer> selectRetired = computePersonStatusCase.apply("isRetired", AssignmentType.PRO);
        Expression<Integer> selectSamu = computePersonStatusCase.apply("isSamu", AssignmentType.EXT);
        Expression<Integer> selectSupportFirefighter = computePersonStatusCase.apply("isSupportFirefighter", AssignmentType.VOL);
        Expression<Integer> selectVeteran = computePersonStatusCase.apply("isVeteran", AssignmentType.VOL);
        Expression<Integer> selectYoungFirefighter = computePersonStatusCase.apply("isYoungFirefighter", AssignmentType.VOL);

        criteriaQuery.multiselect(
                criteriaBuilder.max(selectPro),
                criteriaBuilder.max(selectVol),
                criteriaBuilder.max(selectExt),
                criteriaBuilder.max(selectCandidate),
                criteriaBuilder.max(selectIntern),
                criteriaBuilder.max(selectOperationalFirefighter),
                criteriaBuilder.max(selectProfessionalAdmTech),
                criteriaBuilder.max(selectProfessionalOperational),
                criteriaBuilder.max(selectRetired),
                criteriaBuilder.max(selectSamu),
                criteriaBuilder.max(selectSupportFirefighter),
                criteriaBuilder.max(selectVeteran),
                criteriaBuilder.max(selectYoungFirefighter),
                criteriaBuilder.max(selectPrimaryTypeTechnicalPro),
                criteriaBuilder.max(selectPrimaryTypeTechnicalVol)
        );

        // Apply specifications
        applyWhere(root, criteriaQuery, criteriaBuilder, specs);


        TypedQuery<Tuple> query = getEntityManager().createQuery(criteriaQuery);
        Integer one = 1;
        Integer zero = 0;
        BiPredicate<Tuple, Integer> tupleMapping = (tuple, index) -> {
            Integer value = tuple.get(index, Integer.class);
            if (value == null ){
                return false;
            }
            return BooleanUtils.toBoolean(value, one, zero);
        };
        Tuple result = query.getSingleResult();
        if(result.getElements().isEmpty()){
            return AssignmentStatus.builder().build();
        }
        return AssignmentStatus.builder()
                .isProfessional(tupleMapping.test(result, 0))
                .isVolunteer(tupleMapping.test(result, 1))
                .isExternal(tupleMapping.test(result, 2))
                .isCandidate(tupleMapping.test(result, 3))
                .isIntern(tupleMapping.test(result, 4))
                .isOperationalFirefighter(tupleMapping.test(result, 5))
                .isProfessionalAdmTech(tupleMapping.test(result, 6))
                .isProfessionalOperational(tupleMapping.test(result, 7))
                .isRetired(tupleMapping.test(result, 8))
                .isSamu(tupleMapping.test(result, 9))
                .isSupportFirefighter(tupleMapping.test(result, 10))
                .isVeteran(tupleMapping.test(result, 11))
                .isYoungFirefighter(tupleMapping.test(result, 12))
                .isPrimaryTypeTechnicalPro(tupleMapping.test(result, 13))
                .isPrimaryTypeTechnicalVol(tupleMapping.test(result, 14))
                .build();


    }
}
