package lu.fujitsu.ts.cgdis.portal.connector.portaldb.specifications.impl.serviceplans;

import lu.fujitsu.ts.cgdis.portal.connector.portaldb.dbos.serviceplan.ServicePlanDbo;
import lu.fujitsu.ts.cgdis.portal.connector.portaldb.specifications.SpecificationWithFrom;

import javax.persistence.criteria.AbstractQuery;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.From;
import javax.persistence.criteria.Predicate;

/**
 * The type Service plan closed specification.
 */
public class ServicePlanClosedSpecification implements SpecificationWithFrom<ServicePlanDbo> {


    @Override
    public <T> Predicate toPredicateFrom(From<T, ServicePlanDbo> servicePlan, AbstractQuery<?> query, CriteriaBuilder cb) {
       return cb.not(new ServicePlanNotClosedSpecification().toPredicateFrom(servicePlan, query, cb));

    }
}
