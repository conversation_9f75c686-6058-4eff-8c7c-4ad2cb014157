package lu.fujitsu.ts.cgdis.portal.connector.portaldb.services.serviceplan;

import lu.fujitsu.ts.cgdis.portal.connector.portaldb.dbos.BaseDbo;
import lu.fujitsu.ts.cgdis.portal.connector.portaldb.dbos.InterventionTypeDbo;
import lu.fujitsu.ts.cgdis.portal.connector.portaldb.dbos.VehicleDbo;
import lu.fujitsu.ts.cgdis.portal.connector.portaldb.dbos.VehicleStatusHistoricDbo;
import lu.fujitsu.ts.cgdis.portal.connector.portaldb.dbos.serviceplan.*;
import lu.fujitsu.ts.cgdis.portal.connector.portaldb.repositories.IServicePlanRepository;
import lu.fujitsu.ts.cgdis.portal.connector.portaldb.specifications.impl.SpecificationJointUtils;
import lu.fujitsu.ts.cgdis.portal.connector.portaldb.specifications.impl.serviceplans.ArchivedServicePlanSpecification;
import lu.fujitsu.ts.cgdis.portal.connector.portaldb.specifications.impl.vehicles.CurrentUsedVehiclesByServicePlanSpecification;
import lu.fujitsu.ts.cgdis.portal.connector.portaldb.specifications.impl.vehicles.CurrentlyUsedBoxesSpecification;
import lu.fujitsu.ts.cgdis.portal.core.domain.BaseModel;
import lu.fujitsu.ts.cgdis.portal.core.domain.InterventionType;
import lu.fujitsu.ts.cgdis.portal.core.domain.VehicleStatusHistoric;
import lu.fujitsu.ts.cgdis.portal.core.domain.VehicleWithoutEntity;
import lu.fujitsu.ts.cgdis.portal.core.domain.enums.ServicePlanCompletionStatus;
import lu.fujitsu.ts.cgdis.portal.core.domain.serviceplan.*;
import lu.fujitsu.ts.cgdis.portal.core.domain.serviceplanmodel.ServicePlanModel;
import lu.fujitsu.ts.cgdis.portal.core.exception.CGDISTechnicalException;
import lu.fujitsu.ts.cgdis.portal.core.security.ICGDISSecurityConstraint;
import lu.fujitsu.ts.cgdis.portal.core.security.utils.CGDISPortalSecurityHolder;
import lu.fujitsu.ts.cgdis.portal.services.serviceplan.ICommonServicePlanRepositoryService;
import lu.fujitsu.ts.cgdis.portal.services.serviceplan.IOptionalBackupGroupRepositoryService;
import lu.fujitsu.ts.eportal.server.connector.jpa.services.SecuredRepositoryService;
import lu.fujitsu.ts.eportal.server.connector.jpa.specifications.SpecificationUtils;
import lu.fujitsu.ts.eportal.server.connector.jpa.specifications.secured.AbstractComposedSecuredSpecificationBuilder;
import lu.fujitsu.ts.eportal.server.core.domain.Fetches;
import lu.fujitsu.ts.eportal.server.core.domain.SearchCriterion;
import lu.fujitsu.ts.eportal.server.core.exceptions.NotFoundException;
import lu.fujitsu.ts.eportal.server.core.utils.EPortalConverter;
import lu.fujitsu.ts.eportal.server.core.utils.IEPortalConverter;
import lu.fujitsu.ts.eportal.server.logging.milestones.LogMilestone;
import lu.fujitsu.ts.eportal.server.logging.milestones.LoggableType;
import lu.fujitsu.ts.eportal.server.logging.milestones.LoggableValue;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.dozer.Mapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.jpa.domain.Specification;

import javax.persistence.criteria.Join;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Root;
import javax.persistence.criteria.Subquery;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * The Service plan repository service.
 *
 * @param <S> the type parameter
 * @param <V> the type parameter
 */
public abstract class CommonServicePlanRepositoryService<
        S extends AbstractServicePlan<?>,
        V extends AbstractServicePlanAndVersion<S>
        >
        extends SecuredRepositoryService<ICGDISSecurityConstraint, S, ServicePlanDbo, Long, IServicePlanRepository> implements ICommonServicePlanRepositoryService<S, V> {

    private static final Logger LOGGER = LoggerFactory.getLogger(CommonServicePlanRepositoryService.class);

    /**
     * The Service plan dbo service plan with versions converter.
     */
    protected final EPortalConverter<ServicePlanDbo, ServicePlanWithVersions> servicePlanDboServicePlanWithVersionsConverter;
    /**
     * The Vehicles to model converter.
     */
    protected final EPortalConverter<VehicleDbo, VehicleWithoutEntity> vehiclesToModelConverter;
    /**
     * The Versions to model converter.
     */
    protected final EPortalConverter<ServicePlanVersionDbo, ServicePlanVersion> versionsToModelConverter;
    /**
     * The Base dbo converter.
     */
    protected final EPortalConverter<BaseDbo, BaseModel> baseDboConverter;
    /**
     * The Service plan els status dbo converter.
     */
    protected final EPortalConverter<ServicePlanElsStatusDbo, ServicePlanElsStatus> servicePlanElsStatusDboConverter;
    /**
     * The Service plan converter.
     */
    protected final EPortalConverter<ServicePlan, ServicePlanDbo> servicePlanConverter;
    /**
     * The Vehicle status historic dbo 2 model converter.
     */
    protected final EPortalConverter<VehicleStatusHistoricDbo, VehicleStatusHistoric> vehicleStatusHistoricDbo2ModelConverter;
    /**
     * The Service els status dbo 2 model converter.
     */
    protected final EPortalConverter<ServicePlanElsStatusDbo, ServicePlanElsStatus> serviceElsStatusDbo2ModelConverter;
    /**
     * The Service plan model dbo 2 model converter.
     */
    protected final IEPortalConverter<ServicePlanModelDbo, ServicePlanModel> servicePlanModelDbo2ModelConverter;
    protected final IOptionalBackupGroupRepositoryService optionalBackupGroupRepositoryService;

    private final EPortalConverter<InterventionTypeDbo, InterventionType> interventionTypeDbo2ModelConverter;

    /**
     * The Repository.
     */
    protected IServicePlanRepository repository;


    private Supplier<Fetches> defaultFetches;



    /**
     * Instantiates a new Common service plan repository service.
     *
     * @param entityTYpe     the entity t ype
     * @param modelClass     the model class
     * @param repository     the repository
     * @param specBuilder    the spec builder
     * @param mapper         the mapper
     * @param defaultFetches the default fetches
     */
    public CommonServicePlanRepositoryService(String entityTYpe, Class<S> modelClass, IServicePlanRepository repository, AbstractComposedSecuredSpecificationBuilder<ICGDISSecurityConstraint, ServicePlanDbo> specBuilder, Mapper mapper, IOptionalBackupGroupRepositoryService optionalBackupGroupRepositoryService, Supplier<Fetches> defaultFetches) {
        super(entityTYpe, modelClass, ServicePlanDbo.class, repository, specBuilder, mapper, defaultFetches.get());
        this.optionalBackupGroupRepositoryService = optionalBackupGroupRepositoryService;
        this.defaultFetches = defaultFetches;
        servicePlanDboServicePlanWithVersionsConverter = new EPortalConverter<>(ServicePlanDbo.class, ServicePlanWithVersions.class, mapper);
        vehiclesToModelConverter = new EPortalConverter<>(VehicleDbo.class, VehicleWithoutEntity.class, mapper);
        versionsToModelConverter = new EPortalConverter<>(ServicePlanVersionDbo.class, ServicePlanVersion.class, mapper);
        baseDboConverter = new EPortalConverter<>(BaseDbo.class, BaseModel.class, mapper);
        this.servicePlanConverter = new EPortalConverter<>(ServicePlan.class, ServicePlanDbo.class, mapper);
        this.servicePlanElsStatusDboConverter = new EPortalConverter<>(ServicePlanElsStatusDbo.class, ServicePlanElsStatus.class, mapper);
        this.vehicleStatusHistoricDbo2ModelConverter = new EPortalConverter<>(VehicleStatusHistoricDbo.class, VehicleStatusHistoric.class, mapper);
        this.serviceElsStatusDbo2ModelConverter = new EPortalConverter<>(ServicePlanElsStatusDbo.class, ServicePlanElsStatus.class, mapper);
        this.servicePlanModelDbo2ModelConverter = new EPortalConverter<>(ServicePlanModelDbo.class, ServicePlanModel.class, mapper);
        this.repository = repository;
        interventionTypeDbo2ModelConverter = new EPortalConverter<>(InterventionTypeDbo.class, InterventionType.class, mapper);;
    }

    /**
     * Filter by version id
     *
     * @param versionId the service plan version id
     * @return the specification
     */
    protected static Specification<ServicePlanDbo> byVersionId(Long versionId) {
        return (root, query, cb) -> cb.equal(root.join("versions").get("tecid"), versionId);
    }

    /**
     * By time slotd specification.
     *
     * @param timeSlotId the time slot id
     * @return the specification
     */
    protected static Specification<ServicePlanDbo> byTimeSlotd(Long timeSlotId) {
        return (root, query, cb) -> {

            Join<ServicePlanDbo, ServicePlanVersionDbo> versions = SpecificationJointUtils.getJoin(root, "versions", JoinType.INNER);
            Join<ServicePlanVersionDbo, ServicePlanTimeSlotDbo> slots = SpecificationJointUtils.getJoin(versions, "servicePlanTimeSlots", JoinType.INNER);
            return cb.equal(slots.get("tecid"), timeSlotId);
        };
    }


    /**
     * Filter by ID
     *
     * @param servicePlanId the service plan id
     * @return the specification
     */
    protected static Specification<ServicePlanDbo> byId(Long servicePlanId) {
        return (root, query, cb) -> cb.equal(root.get("tecid"), servicePlanId);
    }

    /**
     * By ids specification.
     *
     * @param servicePlanIds the service plan ids
     * @return the specification
     */
    protected static Specification<ServicePlanDbo> byIds(Collection<Long> servicePlanIds) {
        return (root, query, cb) -> root.get("tecid").in(servicePlanIds);
    }

    /**
     * Filter by label except id not closured without version
     *
     * @param label         the label
     * @param servicePlanId the service plan id
     * @return the specification
     */
    protected static Specification<ServicePlanDbo> byLabelExceptIdNotClosuredWithoutVersion(String label, Long servicePlanId) {
        return (root, query, cb) -> {
            Join versions = (Join) root.fetch("versions", JoinType.LEFT);
            return cb.and(
                    cb.equal(root.get("label"), label),
                    cb.notEqual(root.get("tecid"), servicePlanId),
                    versions.get("endDate").isNull()
            );
        };
    }

    /**
     * Filter by label not closured without version
     *
     * @param label the label
     * @return the specification
     */
    protected static Specification<ServicePlanDbo> byLabelNotClosuredWithoutVersion(String label) {
        return (root, query, cb) -> {
            Join versions = (Join) root.fetch("versions", JoinType.LEFT);
            return cb.and(
                    cb.equal(root.get("label"), label),
                    versions.get("endDate").isNull()
            );
        };
    }

    /**
     * Filter service plan model by entities
     *
     * @param entityTecid the entity tecid
     * @return the specification
     */
    protected static Specification<ServicePlanDbo> byEntity(Long entityTecid) {
        return (root, query, cb) -> cb.or(
                cb.equal(root.get("entity").get("tecid"), entityTecid),
                cb.equal(root.get("entity").get("mainEntity").get("tecid"), entityTecid)
        );
    }

    /**
     * Filter service plan model by entities
     *
     * @param names the entities names
     * @return the specification
     */
    protected static Specification<ServicePlanDbo> byEntityNames(List<String> names) {
        return (root, query, cb) -> root.get("entity").get("name").in(names);
    }

    /**
     * By entity and secondary entity ids specification.
     *
     * @param ids the ids
     * @return the specification
     */
    protected static Specification<ServicePlanDbo> byEntityAndSecondaryEntityIds(Collection<Long> ids) {
        return (root, query, cb) -> cb.or(
                root.get("entity").get("tecid").in(ids),
                root.get("entity").get("mainEntity").get("tecid").in(ids)
        );
    }


    /**
     * Creates a specification to filter `ServicePlanDbo` entities by their associated vehicle IDs.
     *
     * @param vehicleTecids A collection of vehicle IDs (`tecid`) to filter by.
     * @return A `Specification` that matches `ServicePlanDbo` entities with the given vehicle IDs.
     */
    protected static Specification<ServicePlanDbo> byVehicles(Collection<Long> vehicleTecids) {
        return (root, query, cb) -> root.get("vehicle").get("tecid").in(vehicleTecids);
    }

    /**
     * By vehicle specification.
     *
     * @param boxTecid the box tecid
     * @return the specification
     */
    protected static Specification<ServicePlanDbo> byBox(Long boxTecid) {
        return (root, query, cb) -> cb.equal(root.get("box").get("tecid"), boxTecid);
    }

    /**
     * By vehicle foreign id specification.
     *
     * @param vehicleId the vehicle id
     * @return the specification
     */
    protected static Specification<ServicePlanDbo> byVehicleForeignId(String vehicleId) {
        return (root, query, cb) -> cb.equal(root.get("vehicle").get("id"), vehicleId);
    }

    /**
     * By portal label specification.
     *
     * @param portalLabel the portal label
     * @return the specification
     */
    protected static Specification<ServicePlanDbo> byPortalLabel(String portalLabel) {
        return (root, query, cb) -> cb.equal(root.get("portalLabel"), portalLabel);
    }


    /**
     * Filter by closure date
     *
     * @param closureDate the closure date
     * @return the specification
     */
    protected static Specification<ServicePlanDbo> byClosureDate(LocalDate closureDate) {
        return (root, query, cb) -> {

            Subquery<Long> subqueryEndDateNot = query.subquery(Long.class);
            Root<ServicePlanVersionDbo> fromEndDate = subqueryEndDateNot.from(ServicePlanVersionDbo.class);
            subqueryEndDateNot.where(
                    cb.or(
                            cb.greaterThan(fromEndDate.get("endDate"), closureDate),
                            cb.isNull(fromEndDate.get("endDate"))
                    )
            );
            Subquery<Long> subqueryHasVersion = query.subquery(Long.class);
            Root<ServicePlanVersionDbo> fromHasVersion = subqueryHasVersion.from(ServicePlanVersionDbo.class);
            subqueryHasVersion.where(
                    cb.equal(fromHasVersion.get("endDate"), closureDate)
            );
            return cb.and(
                    cb.not(root.get("tecid").in(subqueryEndDateNot.select(fromEndDate.get("servicePlanId")))),
                    cb.and(root.get("tecid").in(subqueryHasVersion.select(fromHasVersion.get("servicePlanId"))))
            );


        };
    }

    /**
     * Get all service plan that are archived (i.e. does not have versions where the enddate is after today)
     *
     * @return list of service plan dbo
     */
    protected static Specification<ServicePlanDbo> archived() {
        return new ArchivedServicePlanSpecification();
    }

    /**
     * Get all service plan that are not available for today (i.e. does not have a version where the startdate is before or is today)
     *
     * @return list of service plan dbo
     */
    protected static Specification<ServicePlanDbo> futures() {
        return (root, query, cb) -> {
            Subquery<Long> subqueryStartDateNot = query.subquery(Long.class);
            Root<ServicePlanVersionDbo> fromStartDate = subqueryStartDateNot.from(ServicePlanVersionDbo.class);
            subqueryStartDateNot.where(
                    cb.lessThanOrEqualTo(fromStartDate.get("startDate"), LocalDate.now())
            );
            Subquery<Long> subqueryHasVersion = query.subquery(Long.class);
            Root<ServicePlanVersionDbo> fromHasVersion = subqueryHasVersion.from(ServicePlanVersionDbo.class);
            return cb.and(
                    cb.not(root.get("tecid").in(subqueryStartDateNot.select(fromStartDate.get("servicePlanId")))),
                    cb.and(root.get("tecid").in(subqueryHasVersion.select(fromHasVersion.get("servicePlanId"))))
            );
        };
    }

    /**
     * Get all service plan that are available for today (i.e. does have a version with a startdate <= today and an endate >= today)
     *
     * @return list of service plan dbo
     */
    protected static Specification<ServicePlanDbo> current() {
        return (root, query, cb) -> {
            Subquery<Long> subquery = query.subquery(Long.class);
            Root<ServicePlanVersionDbo> from = subquery.from(ServicePlanVersionDbo.class);
            subquery.where(
                    cb.and(
                            cb.lessThanOrEqualTo(from.get("startDate"), LocalDate.now()),
                            cb.or(
                                    cb.greaterThanOrEqualTo(from.get("endDate"), LocalDate.now()),
                                    cb.isNull(from.get("endDate"))
                            )
                    )
            );
            return root.get("tecid").in(subquery.select(from.get("servicePlanId")));
        };
    }

    /**
     * Current 2 specification.
     *
     * @return the specification
     */
    protected static Specification<ServicePlanDbo> current2() {
        return (root, query, cb) -> {
            Subquery<Long> subquery = query.subquery(Long.class);
            Root<ServicePlanVersionDbo> from = subquery.from(ServicePlanVersionDbo.class);
            subquery.where(
                    cb.isNull(from.get("endDate"))
            );
            return root.get("tecid").in(subquery.select(from.get("servicePlanId")));
        };
    }

    /**
     * Get all service plan that are archived (i.e. does not have versions where the enddate is after today)
     *
     * @param date the date
     * @return list of service plan dbo
     */
    protected static Specification<ServicePlanDbo> archivedByDate(LocalDate date) {
        return (root, query, cb) -> {
            Subquery<Long> subqueryEndDateNot = query.subquery(Long.class);
            Root<ServicePlanVersionDbo> fromEndDate = subqueryEndDateNot.from(ServicePlanVersionDbo.class);
            subqueryEndDateNot.where(
                    cb.or(
                            cb.greaterThanOrEqualTo(fromEndDate.get("endDate"), LocalDate.now()),
                            cb.isNull(fromEndDate.get("endDate"))
                    )
            );
            Subquery<Long> subqueryDate = query.subquery(Long.class);
            Root<ServicePlanVersionDbo> fromToDate = subqueryDate.from(ServicePlanVersionDbo.class);
            subqueryDate.where(
                    cb.lessThanOrEqualTo(fromToDate.get("startDate"), date),
                    cb.or(
                            cb.greaterThanOrEqualTo(fromToDate.get("endDate"), date),
                            cb.isNull(fromToDate.get("endDate"))
                    )
            );
            Subquery<Long> subqueryHasVersion = query.subquery(Long.class);
            Root<ServicePlanVersionDbo> fromHasVersion = subqueryHasVersion.from(ServicePlanVersionDbo.class);
            return cb.and(
                    cb.not(root.get("tecid").in(subqueryEndDateNot.select(fromEndDate.get("servicePlanId")))),
                    cb.and(root.get("tecid").in(subqueryHasVersion.select(fromHasVersion.get("servicePlanId")))),
                    cb.and(root.get("tecid").in(subqueryDate.select(fromToDate.get("servicePlanId"))))
            );
        };
    }

    /**
     * Get all service plan that are not available for today (i.e. does not have a version where the startdate is before or is today)
     *
     * @param date the date
     * @return list of service plan dbo
     */
    protected static Specification<ServicePlanDbo> futuresByDate(LocalDate date) {
        return (root, query, cb) -> {
            Subquery<Long> subqueryStartDateNot = query.subquery(Long.class);
            Root<ServicePlanVersionDbo> fromStartDate = subqueryStartDateNot.from(ServicePlanVersionDbo.class);
            subqueryStartDateNot.where(
                    cb.lessThanOrEqualTo(fromStartDate.get("startDate"), LocalDate.now())
            );
            Subquery<Long> subqueryDate = query.subquery(Long.class);
            Root<ServicePlanVersionDbo> fromToDate = subqueryDate.from(ServicePlanVersionDbo.class);
            subqueryDate.where(
                    cb.lessThanOrEqualTo(fromToDate.get("startDate"), date),
                    cb.or(
                            cb.greaterThanOrEqualTo(fromToDate.get("endDate"), date),
                            cb.isNull(fromToDate.get("endDate"))
                    )
            );
            Subquery<Long> subqueryHasVersion = query.subquery(Long.class);
            Root<ServicePlanVersionDbo> fromHasVersion = subqueryHasVersion.from(ServicePlanVersionDbo.class);
            return cb.and(
                    cb.not(root.get("tecid").in(subqueryStartDateNot.select(fromStartDate.get("servicePlanId")))),
                    cb.and(root.get("tecid").in(subqueryHasVersion.select(fromHasVersion.get("servicePlanId")))),
                    cb.and(root.get("tecid").in(subqueryDate.select(fromToDate.get("servicePlanId"))))
            );
        };
    }

    /**
     * Get all service plan that are available for today (i.e. does have a version with a startdate <= today and an endate >= today)
     *
     * @param date the date
     * @return list of service plan dbo
     */
    protected static Specification<ServicePlanDbo> currentByDate(LocalDate date) {
        return (root, query, cb) -> {
            Subquery<Long> subquery = query.subquery(Long.class);
            Root<ServicePlanVersionDbo> from = subquery.from(ServicePlanVersionDbo.class);
            subquery.where(
                    cb.and(
                            cb.lessThanOrEqualTo(from.get("startDate"), date),
                            cb.or(
                                    cb.greaterThanOrEqualTo(from.get("endDate"), date),
                                    cb.isNull(from.get("endDate"))
                            ),
                            cb.lessThanOrEqualTo(from.get("startDate"), LocalDate.now()),
                            cb.or(
                                    cb.greaterThanOrEqualTo(from.get("endDate"), LocalDate.now()),
                                    cb.isNull(from.get("endDate"))
                            )
                    )
            );
            return root.get("tecid").in(subquery.select(from.get("servicePlanId")));
        };
    }


    /**
     * Filter on version having starting before the date in parameter (date exclusive)
     *
     * @param startDate the start date
     * @return specification specification
     */
    protected Specification<ServicePlanDbo> byVersionStartDateBefore(LocalDate startDate) {
        return (root, criteriaQuery, criteriaBuilder) -> criteriaBuilder.lessThan(root.join("versions").get("startDate"), startDate);
    }

    /**
     * Model id specification
     *
     * @param modelId the model id
     * @return the specification
     */
    protected Specification<ServicePlanDbo> byModelId(Long modelId) {
        return (root, criteriaQuery, criteriaBuilder) -> criteriaBuilder.equal(root.get("model").get("tecid"), modelId);
    }

    /**
     * By vehicle types specification
     *
     * @param vehicleTypes the vehicle types
     * @return the specification
     */
    protected Specification<ServicePlanDbo> byVehicleTypes(List<String> vehicleTypes) {
        return (root, criteriaQuery, criteriaBuilder) ->
                !CollectionUtils.isEmpty(vehicleTypes) ?
                        root.get("vehicle").get("type").in(vehicleTypes) :
                        criteriaBuilder.conjunction()
                ;
    }


    /**
     * {@inheritDoc}
     */
    @Override
    public List<S> getCurrentServicePlans(ICGDISSecurityConstraint securityConstraint) {
        List<ServicePlanDbo> result = findAll(securityConstraint, current());
        return dbo2ModelConverter.convert(result);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<S> getArchivedServicePlans(ICGDISSecurityConstraint securityConstraint) {
        List<ServicePlanDbo> result = findAll(securityConstraint, archived());
        return dbo2ModelConverter.convert(result);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<S> getFutureServicePlans(ICGDISSecurityConstraint securityConstraint) {
        List<ServicePlanDbo> result = findAll(securityConstraint, futures());
        return dbo2ModelConverter.convert(result);
    }


    /**
     * {@inheritDoc}
     */
    @Override
    public List<S> getServicePlanByModelId(ICGDISSecurityConstraint securityConstraint, Long modelId) throws NotFoundException {
        List<ServicePlanDbo> dbo = findAll(securityConstraint, byModelId(modelId));
        if (dbo == null) {
            throw new NotFoundException(String.format("No service plan found for model with id %s", modelId));
        }
        return dbo2ModelConverter.convert(dbo);
    }

    @Override
    public List<S> getServicePlanByModelIdAndVehiculeType(ICGDISSecurityConstraint securityConstraint, Long modelId, List<String> vehicleTypes) {
        List<ServicePlanDbo> dbo = findAll(securityConstraint, SpecificationUtils.and(byModelId(modelId), byVehicleTypes(vehicleTypes)));
        if (dbo == null) {
            return new ArrayList<>();
        }
        return dbo2ModelConverter.convert(dbo);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @LogMilestone(action = "get active service plan ids")
    public List<Long> getCurrentServicePlanIds(ICGDISSecurityConstraint securityConstraint) {
        LOGGER.debug("get all service plan ids for active plan");
        return this.repository.findDistinctColumns(Long.class, SpecificationUtils.and(
                specBuilder.buildSecurityConstraint(securityConstraint),
                current()
        ), (root, criteriaQuery, criteriaBuilder) -> root.get("tecid"));
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @LogMilestone(action = "count service plan by model and beginning before date", logResult = {@LoggableValue})
    public long countByModelIdBeginningBeforeDate(ICGDISSecurityConstraint securityConstraint, @LoggableValue Long modelId, @LoggableValue LocalDate startDate) {
        LOGGER.debug("count number of service plans for model {} and having at least one version beginning before {}", modelId, startDate);

        long count = count(
                securityConstraint,
                SpecificationUtils.distinct(SpecificationUtils.and(
                        byModelId(modelId),
                        byVersionStartDateBefore(startDate)
                ))
        );

        LOGGER.debug("found {} service plans for model id {} and beginning before {}", count, modelId, startDate);
        return count;
    }


    /**
     * {@inheritDoc}
     */
    @Override
    public long countByModelId(ICGDISSecurityConstraint securityConstraint, Long modelId) {
        return count(securityConstraint, byModelId(modelId));
    }

    @Override
    public long countByModelIdAndVehicleTypes(ICGDISSecurityConstraint securityConstraint, Long modelId, List<String> vehicleTypes) {
        return count(securityConstraint, SpecificationUtils.and(
                byModelId(modelId),
                byVehicleTypes(vehicleTypes)
        ));
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<S> getUniqueVersionsByLabelExceptIdWithoutVersion(ICGDISSecurityConstraint securityConstraint, String label, Long servicePlanId) {
        List<ServicePlanDbo> result = findAll(securityConstraint,
                (servicePlanId == null ?
                        byLabelNotClosuredWithoutVersion(label)
                        :
                        byLabelExceptIdNotClosuredWithoutVersion(label, servicePlanId)
                )
        );
        return dbo2ModelConverter.convert(result);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @LogMilestone(action = "get service plan version by model id and starting between 2 dates")
    public List<ServicePlanVersion> getVersionsByModelIdAndStartDate(ICGDISSecurityConstraint securityConstraint, @LoggableValue Long modelId, @LoggableValue LocalDate startDate, @LoggableValue LocalDate endDate) {
        LOGGER.debug("get service plan version for model id {} and starting between {} and {}", modelId, startDate, endDate);
        return versionsToModelConverter.convert(repository.getVersionsByStartDate(byModelId(modelId), startDate, endDate));
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @LogMilestone(action = "get service plan with the closure date")
    public List<Long> getClosedAtDate(ICGDISSecurityConstraint securityConstraint, @LoggableValue LocalDate closeDate) {
        LOGGER.debug("Get all service plan ids with closure date {}", closeDate);
        Specification<ServicePlanDbo> specifications = SpecificationUtils.and(
                specBuilder.buildSecurityConstraint(CGDISPortalSecurityHolder.getConnectedUserDetails()),
                byClosureDate(closeDate)
        );
        return repository.findDistinctColumns(Long.class, specifications, (root, criteriaQuery, criteriaBuilder) -> root.get("tecid"));

    }


    /**
     * {@inheritDoc}
     */
    @Override
    public List<S> getCurrentServicePlanForEntity(ICGDISSecurityConstraint securityConstraint, Long entityId) {
        List<ServicePlanDbo> result = findAll(
                securityConstraint,
                SpecificationUtils.and(
                        current(),
                        byEntity(entityId),
                        orderByPortalLabel()
                )
        );
        return dbo2ModelConverter.convert(result);
    }

    @Override
    public List<S> getCurrentServicePlanForEntityOrChild(ICGDISSecurityConstraint securityConstraint, Long entityId, List<String> allChildren) {
        List<ServicePlanDbo> result = findAll(
                securityConstraint,
                SpecificationUtils.and(
                        current(),
                        allChildren != null ? byEntityNames(allChildren) : byEntity(entityId),
                        orderByPortalLabel()
                )
        );
        return dbo2ModelConverter.convert(result);
    }

    @Override
    public List<S> getCurrentServicePlanForVehicle(ICGDISSecurityConstraint securityConstraint, Collection<Long> vehicleIds) {
        List<ServicePlanDbo> result = findAll(
                securityConstraint,
                SpecificationUtils.and(
                        current(),
                        byVehicles(vehicleIds)
                )
        );
        return dbo2ModelConverter.convert(result);
    }

    @Override
    public List<S> getCurrentServicePlanForVehicleForeignId(ICGDISSecurityConstraint securityConstraint, String vehicleForeignId) {
        List<ServicePlanDbo> result = findAll(
                securityConstraint,
                SpecificationUtils.and(
                        current(),
                        byVehicleForeignId(vehicleForeignId)
                )
        );
        return dbo2ModelConverter.convert(result);
    }

    @Override
    public List<S> getCurrentAndFuturServicePlanForVehicle(ICGDISSecurityConstraint securityConstraint, Long vehicleId) {
        List<ServicePlanDbo> result = findAll(
                securityConstraint,
                SpecificationUtils.and(
                        SpecificationUtils.not(archived()),
                        byVehicles(Arrays.asList(vehicleId))
                )
        );
        return dbo2ModelConverter.convert(result);
    }


    /**
     * Gets current and futur service plan for box.
     *
     * @param securityConstraint the security constraint
     * @param boxId              the box id
     * @return the current and futur service plan for box
     */
    @Override
    public List<S> getCurrentAndFuturServicePlanForBox(ICGDISSecurityConstraint securityConstraint, Long boxId) {
        List<ServicePlanDbo> result = findAll(
                securityConstraint,
                SpecificationUtils.and(
                        SpecificationUtils.not(archived()),
                        byBox(boxId)
                )
        );
        return dbo2ModelConverter.convert(result);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<ServicePlanVersion> getAllVersions(ICGDISSecurityConstraint securityConstraint, Long modelId, LocalDate startDate, LocalDate endDate) {
        LOGGER.debug("get all service plan version for model id {} and starting between {} and {}", modelId, startDate, endDate);
        return versionsToModelConverter.convert(repository.getVersionsByDates(byModelId(modelId), startDate, endDate));
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public S getByVersion(ICGDISSecurityConstraint securityConstraint, Long versionId) throws NotFoundException {
        return dbo2ModelConverter.convert(findOne(securityConstraint, SpecificationUtils.fetch(byVersionId(versionId), defaultFetches.get())));
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @LogMilestone(action = "get all service plans vehicles", logResult = @LoggableValue(type = LoggableType.COLLECTION))
    public List<VehicleWithoutEntity> getVehicles(ICGDISSecurityConstraint securityConstraint) {
        LOGGER.debug("get all service plans vehicles");
        return vehiclesToModelConverter.convert(this.repository.getVehicles(securityConstraint));
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean canModify(ICGDISSecurityConstraint securityConstraint, Long aLong) {
        return true;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<Long> getAllCurrentUsedVehicleIdsWithoutSecurity(Long servicePlanId) {
        Specification<ServicePlanDbo> specifications = new CurrentUsedVehiclesByServicePlanSpecification(servicePlanId);
        return this.repository.findDistinctColumns(Long.class, specifications, (root, criteriaQuery, criteriaBuilder) -> root.get("vehicle").get("tecid"));

    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<Long> getAllCurrentUsedBoxIdsWithoutSecurity(Long servicePlanId) {
        Specification<ServicePlanDbo> specifications = new CurrentlyUsedBoxesSpecification(servicePlanId);
        return this.repository.findDistinctColumns(Long.class, specifications, (root, criteriaQuery, criteriaBuilder) -> root.get("box").get("tecid"));

    }


    /**
     * {@inheritDoc}
     */
    @Override
    public Page<? extends S> searchCurrent(ICGDISSecurityConstraint securityConstraint, List<SearchCriterion> searchCriteria, PageRequest pageRequest) {
        Page<ServicePlanDbo> all = this.repository.findAll(
                SpecificationUtils.fetch(
                        SpecificationUtils.distinct(
                                SpecificationUtils.and(
                                        specBuilder.build(securityConstraint, searchCriteria),
                                        current()
                                )),
                        this.defaultFetches.get()
                ),
                buildPageRequest(pageRequest)
        );


        return this.dbo2ModelConverter.convert(all);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Page<? extends S> searchArchived(ICGDISSecurityConstraint securityConstraint, List<SearchCriterion> searchCriteria, PageRequest pageRequest) {
        Page<ServicePlanDbo> all = this.repository.findAll(
                SpecificationUtils.and(
                        specBuilder.build(securityConstraint, searchCriteria),
                        archived()
                ),
                buildPageRequest(pageRequest)
        );
        return this.dbo2ModelConverter.convert(all);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Page<? extends S> searchFuture(ICGDISSecurityConstraint securityConstraint, List<SearchCriterion> searchCriteria, PageRequest pageRequest) {
        Page<ServicePlanDbo> all = this.repository.findAll(
                SpecificationUtils.and(
                        specBuilder.build(securityConstraint, searchCriteria),
                        futures()
                ),
                buildPageRequest(pageRequest)
        );
        return this.dbo2ModelConverter.convert(all);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Page<? extends S> searchAll(ICGDISSecurityConstraint securityConstraint, List<SearchCriterion> searchCriteria, PageRequest pageRequest) {
        Page<ServicePlanDbo> all = this.repository.findAll(
                SpecificationUtils.and(
                        specBuilder.build(securityConstraint, searchCriteria),
                        SpecificationUtils.or(
                                futures(),
                                archived(),
                                current()
                        )
                ),
                buildPageRequest(pageRequest)
        );
        return this.dbo2ModelConverter.convert(all);
    }

    @Override
    @LogMilestone(action = "get service plan name")
    public String getName(ICGDISSecurityConstraint securityConstraint, @LoggableValue Long servicePlanId) throws NotFoundException {
        LOGGER.debug("get name of service plan {}", servicePlanId);
        Specification<ServicePlanDbo> specifications = SpecificationUtils.and(
                specBuilder.buildSecurityConstraint(securityConstraint),
                byId(servicePlanId)
        );
        List<String> portalLabel = this.repository.findDistinctColumns(String.class, specifications, (root, criteriaQuery, criteriaBuilder) -> root.get("portalLabel"));
        if (CollectionUtils.isEmpty(portalLabel)) {
            throw new NotFoundException(String.format("No service plan found with id %s", servicePlanId));
        }
        return portalLabel.get(0);
    }

    @Override
    @LogMilestone(action = "get service plan labels")
    public Map<Long, String> getLabels(ICGDISSecurityConstraint securityConstraint, Collection<Long> servicePlanIds) {
        LOGGER.debug("get labels of service plan {}", servicePlanIds);
        if (CollectionUtils.isEmpty(servicePlanIds)) {
            return new HashMap<>();
        }
        Specification<ServicePlanDbo> specifications = SpecificationUtils.and(
                specBuilder.buildSecurityConstraint(securityConstraint),
                byIds(servicePlanIds)
        );
        return this.repository.findDistinctColumns(
                        Arrays.asList(
                                ImmutablePair.of(Long.class, (root, criteriaQuery, criteriaBuilder) -> root.get(BaseDbo.Fields.TECID)),
                                ImmutablePair.of(String.class, (root, criteriaQuery, criteriaBuilder) -> root.get(ServicePlanDbo.Fields.LABEL))
                        )
                        , specifications
                        , null)
                .stream()
                .collect(Collectors.toMap(objects -> (Long) objects[0], objects -> (String) objects[1]));

    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<S> getCurrentServicePlansByDate(ICGDISSecurityConstraint securityConstraint, LocalDate date) {
        List<ServicePlanDbo> result = findAll(securityConstraint, currentByDate(date));
        return dbo2ModelConverter.convert(result);
    }

    @Override
    @LogMilestone(action = "get service plan ids by date", logResult = @LoggableValue(type = LoggableType.COLLECTION))
    public List<Long> getServicePlanIdsByDate(ICGDISSecurityConstraint securityConstraint, @LoggableValue LocalDate date) {
        Specification<ServicePlanDbo> specifications = SpecificationUtils.and(
                specBuilder.buildSecurityConstraint(securityConstraint),
                byDate(date)
        );
        return this.repository.findDistinctColumns(Long.class, specifications, (root, criteriaQuery, criteriaBuilder) -> root.get("tecid"));


    }

    @Override
    @LogMilestone(action = "get service plan ids by date", logResult = @LoggableValue(type = LoggableType.COLLECTION))
    public List<S> getServicePlansByDate(ICGDISSecurityConstraint securityConstraint, LocalDate date) {
        Specification<ServicePlanDbo> specifications = SpecificationUtils.and(
                specBuilder.buildSecurityConstraint(securityConstraint),
                byDate(date)
        );
        List<ServicePlanDbo> result = findAll(securityConstraint, specifications);
        return dbo2ModelConverter.convert(result);

    }

    /**
     * Get all service plan that are available for the date in parameter (i.e. does have a version with a startdate <= thedate and an endate >= thedate)
     *
     * @return list of service plan dbo
     */
    private static Specification<ServicePlanDbo> byDate(LocalDate date) {
        return (root, query, cb) -> {
            Subquery<Long> subquery = query.subquery(Long.class);
            Root<ServicePlanVersionDbo> from = subquery.from(ServicePlanVersionDbo.class);
            subquery.where(
                    cb.and(
                            cb.lessThanOrEqualTo(from.get("startDate"), date),
                            cb.or(
                                    cb.greaterThanOrEqualTo(from.get("endDate"), date),
                                    cb.isNull(from.get("endDate"))
                            )

                    )
            );
            return root.get("tecid").in(subquery.select(from.get("servicePlanId")));
        };
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<S> getArchivedServicePlansByDate(ICGDISSecurityConstraint securityConstraint, LocalDate date) {
        List<ServicePlanDbo> result = findAll(securityConstraint, archivedByDate(date));
        return dbo2ModelConverter.convert(result);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<S> getFutureServicePlansByDate(ICGDISSecurityConstraint securityConstraint, LocalDate date) {
        List<ServicePlanDbo> result = findAll(securityConstraint, futuresByDate(date));
        return dbo2ModelConverter.convert(result);
    }

    @Override
    @LogMilestone(action = "get base model for service plan ")
    public BaseModel getBaseModel(ICGDISSecurityConstraint securityConstraint, @LoggableValue Long servicePlanId) throws NotFoundException {
        LOGGER.debug("Get base model for service plan {}", servicePlanId);

        BaseDbo baseDbo = this.repository.getBaseDbo(SpecificationUtils.and(
                specBuilder.buildSecurityConstraint(securityConstraint),
                buildFindOneSpecification(servicePlanId)
        ));
        return baseDboConverter.convert(baseDbo);
    }

    @Override
    @LogMilestone(action = "get current version ids by entity names", logResult = @LoggableValue(type = LoggableType.COLLECTION))
    public List<Long> getCurrentVersionIdsByEntityIds(ICGDISSecurityConstraint securityConstraint, @LoggableValue(type = LoggableType.COLLECTION) Collection<Long> entityTecids) {
        LOGGER.debug("get current version ids by entity names");
        Specification specifications = SpecificationUtils.and(
                specBuilder.buildSecurityConstraint(securityConstraint),
                current(),
                byEntityAndSecondaryEntityIds(entityTecids)
        );
        return this.repository.findDistinctColumns(Long.class, specifications, (root, criteriaQuery, criteriaBuilder) -> root.join("versions").get("tecid"));
    }

    @Override
    @LogMilestone(action = "get min and max dates of service plan", logResult = @LoggableValue({"startDate", "endDate"}))
    public ServicePlanDates getMinAndMaxDates(ICGDISSecurityConstraint securityConstraint, @LoggableValue Long servicePlanId) throws NotFoundException {
        LOGGER.debug("get min and max dates for service plan {}", servicePlanId);
        Specification<ServicePlanDbo> specifications = SpecificationUtils.and(
                this.specBuilder.buildSecurityConstraint(securityConstraint),
                byId(servicePlanId)
        );
        Optional<ServicePlanDates> optionalDates = this.repository.getMinAndMaxDates(specifications);
        if (optionalDates.isPresent()) {
            ServicePlanDates dates = optionalDates.get();
            LOGGER.debug("Found min date={} and max date={} for service plan {}", dates.getStartDate(), dates.getEndDate(), servicePlanId);
            return dates;
        }
        throw new NotFoundException("No dates found for service plan " + servicePlanId);
    }

    @Override
    @LogMilestone(action = "get vehicle status for service plan")
    public VehicleStatusHistoric getVehicleStatusForServicePlan(ICGDISSecurityConstraint securityConstraint, @LoggableValue Long servicePlanId) throws NotFoundException {
        LOGGER.debug("get vehicle status for service plan {}", servicePlanId);
        List<VehicleStatusHistoricDbo> vehicle = this.repository.findDistinctColumns(
                VehicleStatusHistoricDbo.class,
                SpecificationUtils.and(
                        specBuilder.buildSecurityConstraint(securityConstraint),
                        byId(servicePlanId)
                ),
                (root, criteriaQuery, criteriaBuilder) -> root.get("vehicle").get("currentStatus"));
        if (CollectionUtils.isEmpty(vehicle)) {
            throw new NotFoundException("No vehicle status found for service plan " + servicePlanId);
        }

        if (vehicle.size() > 1) {
            throw new CGDISTechnicalException("More than 1  vehicle status found for service plan " + servicePlanId + ",  found " + vehicle.size());
        }
        return vehicleStatusHistoricDbo2ModelConverter.convert(vehicle.get(0));

    }

    @Override
    @LogMilestone(action = "get els status for servie plan and completion status")
    public ServicePlanElsStatus getElsStatusByServicePlanAndCompletionStatus(ICGDISSecurityConstraint securityConstraint, @LoggableValue Long servicePlanId, @LoggableValue ServicePlanCompletionStatus completionStatus) throws NotFoundException {
        LOGGER.debug("get els status for servie plan {} and completion status {}", servicePlanId, completionStatus);
        List<ServicePlanElsStatusDbo> elsStatuses = this.repository.findDistinctColumns(
                ServicePlanElsStatusDbo.class,
                SpecificationUtils.and(
                        specBuilder.buildSecurityConstraint(securityConstraint),
                        byId(servicePlanId),
                        byCompletionStatus(completionStatus)
                ),
                (root, criteriaQuery, criteriaBuilder) -> SpecificationJointUtils.getJoin(root, "elsStatuses"));
        if (CollectionUtils.isEmpty(elsStatuses)) {
            throw new NotFoundException("No els status found for service plan " + servicePlanId + " and completion status " + completionStatus);
        }

        if (elsStatuses.size() > 1) {
            throw new CGDISTechnicalException("More than 1  els status found for service plan " + servicePlanId + " and completion status " + completionStatus + " ,  found " + elsStatuses.size());
        }
        return serviceElsStatusDbo2ModelConverter.convert(elsStatuses.get(0));


    }

    @Override
    @LogMilestone(action = "has servie plan at least one version")
    public boolean hasVersions(ICGDISSecurityConstraint securityConstraint, @LoggableValue Long servicePlanTecid) {
        LOGGER.debug("Check is service plan {} has at least one version", servicePlanTecid);
        return this.repository.countDistinctColumns(SpecificationUtils.and(
                        specBuilder.buildSecurityConstraint(securityConstraint),
                        byId(servicePlanTecid)
                ),
                (root, criteriaQuery, criteriaBuilder) -> root.join("versions")
        ) > 0;

    }

    @Override
    @LogMilestone(action = "get model for service plan")
    public ServicePlanModel getModelForServicePlan(ICGDISSecurityConstraint securityConstraint, @LoggableValue Long servicePlanId) throws NotFoundException {
        LOGGER.debug("get model for service plan {}", servicePlanId);
        List<ServicePlanModelDbo> model = this.repository.findDistinctColumns(ServicePlanModelDbo.class,
                SpecificationUtils.and(
                        specBuilder.buildSecurityConstraint(securityConstraint),
                        byId(servicePlanId)

                ),
                (root, criteriaQuery, criteriaBuilder) -> root.join("model"));
        if (CollectionUtils.isEmpty(model)) {
            throw new NotFoundException("No model found for service plan " + servicePlanId);
        }

        return servicePlanModelDbo2ModelConverter.convert(model.get(0));
    }

    @Override
    @LogMilestone(action = "get model id for service plan")
    public Long getModelIdForServicePlan(ICGDISSecurityConstraint securityConstraint, @LoggableValue Long servicePlanId) throws NotFoundException {
        LOGGER.debug("get model id for service plan {}", servicePlanId);
        List<Long> model = this.repository.findDistinctColumns(Long.class,
                SpecificationUtils.and(
                        specBuilder.buildSecurityConstraint(securityConstraint),
                        byId(servicePlanId)

                ),
                (root, criteriaQuery, criteriaBuilder) -> root.join("model").get("tecid"));
        if (CollectionUtils.isEmpty(model)) {
            throw new NotFoundException("No model found for service plan " + servicePlanId);
        }

        return model.get(0);
    }

    @Override
    @LogMilestone(action = "get model id for service plan")
    public Long getModelIdForServicePlan(@LoggableValue Long servicePlanId) throws NotFoundException {
        LOGGER.debug("get model id for service plan {}", servicePlanId);
        List<Long> model = this.repository.findDistinctColumns(Long.class,
                byId(servicePlanId)
                ,
                (root, criteriaQuery, criteriaBuilder) -> root.join("model").get("tecid"));
        if (CollectionUtils.isEmpty(model)) {
            throw new NotFoundException("No model found for service plan " + servicePlanId);
        }

        return model.get(0);
    }

    @Override
    public List<S> findActivePlanByBackupForeignKeys(Set<String> foreignKeys) {
        return this.findActivePlanMapByBackupForeignKeys(foreignKeys).values().stream().flatMap(List::stream).collect(Collectors.toList());
    }

    @Override
    public Map<String, List<S>> findActivePlanMapByBackupForeignKeys(Set<String> foreignKeys) {
        List<ServicePlanDbo> dbos = this.repository.findAll(
                SpecificationUtils.and(
                        byBackupGroup(foreignKeys),
                        byDate(LocalDate.now())
                )
        );
        return dbo2ModelConverter.convert(dbos).stream()
                .collect(Collectors.groupingBy(s -> s.getBackupGroup(), Collectors.toList()));
    }

    @Override
    public List<S> findActivePlanByOptionalForeignKeys(Set<String> foreignKeys) {
        return this.findActivePlanMapByOptionalForeignKeys(foreignKeys).values().stream().flatMap(List::stream).collect(Collectors.toList());
    }

    @Override
    public Map<String, List<S>> findActivePlanMapByOptionalForeignKeys(Set<String> foreignKeys) {
        List<ServicePlanDbo> dbos = this.repository.findAll(
                SpecificationUtils.and(
                        byOptionalGroup(foreignKeys),
                        byDate(LocalDate.now())
                )
        );
        return dbo2ModelConverter.convert(dbos).stream()
                .collect(Collectors.groupingBy(s -> s.getOptionalGroup(), Collectors.toList()));
    }

    @Override
    public Map<String, List<S>> findActivePlanMapByOptionalOrBackupForeignKeys(Set<String> foreignKeys) {
        List<ServicePlanDbo> dbos = this.repository.findAll(
                SpecificationUtils.and(
                        SpecificationUtils.or(
                                byBackupGroup(foreignKeys),
                                byOptionalGroup(foreignKeys)
                        ),
                        byDate(LocalDate.now())
                )
        );
        return dbo2ModelConverter.convert(dbos).stream()
                .collect(Collectors.groupingBy(s -> s.getOptionalGroup(), Collectors.toList()));
    }



    @Override
    @LogMilestone(action = "get service plan by time slot id", logResult = @LoggableValue("tecid"))
    public S getByTimeSlotId(ICGDISSecurityConstraint securityConstraint, @LoggableValue Long timeslotId) throws NotFoundException {
        LOGGER.debug("get service plan with time slot id {}", timeslotId);
        return dbo2ModelConverter.convert(
                this.repository.findOne(SpecificationUtils.fetch(SpecificationUtils.and(
                                specBuilder.buildSecurityConstraint(securityConstraint),
                                byTimeSlotd(timeslotId)
                        )
                        , defaultFetches.get())).orElseThrow(() -> new NotFoundException("No service plan found with time slot " + timeslotId))
        );

    }

    @Override
    public S getByPortalName(ICGDISSecurityConstraint securityConstraint, String servicePlanName) throws NotFoundException {
        LOGGER.debug("get service plan with name {}", servicePlanName);
        return dbo2ModelConverter.convert(
                this.repository.findOne(SpecificationUtils.fetch(SpecificationUtils.and(
                                specBuilder.buildSecurityConstraint(securityConstraint),
                                byPortalLabel(servicePlanName)
                        )
                        , defaultFetches.get())).orElseThrow(() -> new NotFoundException("No service plan found with name " + servicePlanName))
        );
    }

    @Override
    public S getByPortalNameWithoutSecurity(String servicePlanName) throws NotFoundException {
        LOGGER.debug("get service plan with name {}", servicePlanName);
        return dbo2ModelConverter.convert(
                this.repository.findOne(SpecificationUtils.fetch(
                        byPortalLabel(servicePlanName)

                        , defaultFetches.get())).orElseThrow(() -> new NotFoundException("No service plan found with name " + servicePlanName))
        );
    }


    protected List<S> getByIds(@LoggableValue(type = LoggableType.COLLECTION, logItems = 10) Collection<Long> servicePlanIds, Fetches fetches) {
        LOGGER.debug("get service plans by ids: {}", servicePlanIds);
        return dbo2ModelConverter.convert(
                this.repository.findAll(SpecificationUtils.fetch(
                        byIds(servicePlanIds)
                        , fetches))
        );
    }

    /**
     * By completion status specification.
     *
     * @param completionStatus the completion status
     * @return the specification
     */
    protected static Specification<ServicePlanDbo> byCompletionStatus(ServicePlanCompletionStatus completionStatus) {
        return (root, query, cb) -> cb.equal(SpecificationJointUtils.getJoin(root, "elsStatuses").get("statusType"), completionStatus);
    }

    private static Specification<ServicePlanDbo> byBackupGroup(Set<String> foreignKeys) {
        return (root, query, cb) -> root.get("backupGroup").in(foreignKeys);

    }

    private static Specification<ServicePlanDbo> byOptionalGroup(Set<String> foreignKeys) {
        return (root, query, cb) -> root.get("optionalGroup").in(foreignKeys);

    }

    private static Specification<ServicePlanDbo> orderByPortalLabel() {
        return (root, criteriaQuery, criteriaBuilder) -> {
            criteriaQuery.orderBy(criteriaBuilder.asc(root.get("portalLabel")));
            return criteriaBuilder.conjunction();
        };

    }


    @Override
    public List<S> findActiveServicePLanHavingServicePlanHasOptionalBackupGroup(ICGDISSecurityConstraint securityConstraint, List<Long> servicePlanIds) {
        // Need to find service plan label to retrieve optional/backup group foreign key by their labels.
        // After search service plan using the foreign key in optional/backup group.
        Map<Long, String> labels = this.getLabels(securityConstraint, servicePlanIds);
        if (labels.isEmpty())        {
            return Collections.emptyList();
        }

        Set<String> foreignKeysByLables = this.optionalBackupGroupRepositoryService.findForeignKeysByLables(new HashSet<>(labels.values()));
        if ( foreignKeysByLables.isEmpty()) {
            return Collections.emptyList();
        }

        return findActivePlanMapByOptionalOrBackupForeignKeys(foreignKeysByLables).values().stream().flatMap(List::stream).collect(Collectors.toList());
    }

    @Override
    @LogMilestone(action = "get intervention type for service plan")
    public Optional<InterventionType> getInterventionTypeForServicePlan(@LoggableValue Long servicePlanId) {
        LOGGER.debug("get intervention type for service plan {}", servicePlanId);
        Optional<InterventionTypeDbo> interventionTypeForServicePlan = repository.getInterventionTypeForServicePlan(servicePlanId);
        return interventionTypeForServicePlan.map(interventionTypeDbo2ModelConverter::convert);
    }
}
