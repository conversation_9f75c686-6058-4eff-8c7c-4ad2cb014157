package lu.fujitsu.ts.cgdis.portal.connector.portaldb.dbos.person;

import lombok.Data;
import lombok.ToString;
import lu.fujitsu.ts.cgdis.portal.connector.portaldb.dbos.BaseDbo;

import javax.persistence.*;
import java.util.Collection;
import java.util.Objects;

@Data
@Entity
@Table(name = "OPERATIONAL_YOUNG_FIREFIGHTER")
@ToString(exclude = {
        "tutors",
        "person"
})
public class OperationalYoungFirefighterDbo extends BaseDbo {

    @ManyToMany()
    @JoinTable(
            name = "OPERATIONAL_YOUNG_FIREFIGHTER_TUTOR",
            joinColumns = {@JoinColumn(name = "OPERATIONAL_YOUNG_FIREFIGHTER_TECID")},
            inverseJoinColumns = {@JoinColumn(name = "TUTOR_TECID")}
    )
    private Collection<TutorDbo> tutors;

    @Column(name = "PARENTAL_CONSENT")
    private boolean parentalConsent;

    @JoinColumn(name = "PERSON_TECID", referencedColumnName = "TECID")
    @OneToOne
    private PersonDbo person;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        if (!super.equals(o)) return false;
        OperationalYoungFirefighterDbo that = (OperationalYoungFirefighterDbo) o;
        return parentalConsent == that.parentalConsent &&
                Objects.equals(tutors, that.tutors) &&
                Objects.equals(person, that.person);
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), tutors, parentalConsent, person);
    }
}
