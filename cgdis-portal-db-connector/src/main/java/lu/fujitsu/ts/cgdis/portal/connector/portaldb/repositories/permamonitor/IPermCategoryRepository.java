package lu.fujitsu.ts.cgdis.portal.connector.portaldb.repositories.permamonitor;

import lu.fujitsu.ts.cgdis.portal.connector.portaldb.dbos.permamonitor.PermCategoryDbo;
import lu.fujitsu.ts.cgdis.portal.connector.portaldb.dbos.permamonitor.PermSubcategoryDbo;
import lu.fujitsu.ts.cgdis.portal.connector.portaldb.repositories.IBaseCGDISRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/**
 * Repository interface for managing `PermCategoryDbo` entities.
 * <p>
 * This interface extends the `IBaseCGDISRepository` interface to inherit basic CRUD operations
 * and the `IPermCategoryRepositoryCustom` interface for custom repository methods.
 */
@Repository
public interface IPermCategoryRepository extends IBaseCGDISRepository<PermCategoryDbo>, IPermCategoryRepositoryCustom {

    /**
     * Checks if a category has a specific subcategory.
     *
     * @param categoryTecid    the ID of the category
     * @param subcategoryTecid the ID of the subcategory
     * @return true if the category has the subcategory, false otherwise
     */
    @Query("SELECT CASE WHEN COUNT(c) > 0 THEN true ELSE false END FROM PermCategoryDbo c inner join c.subcategories s WHERE c.tecid = :categoryTecid  AND s.tecid = :subcategoryTecid")
    boolean hasSubcategrory(@Param("categoryTecid") Long categoryTecid, @Param("subcategoryTecid") Long subcategoryTecid);

    /**
     * Retrieves a specific subcategory of a category.
     *
     * @param permCategoryTecid the ID of the category
     * @param subcategoryTecid  the ID of the subcategory
     * @return the subcategory entity if found
     */
    @Query("SELECT s FROM PermCategoryDbo c inner join c.subcategories s WHERE c.tecid = :categoryTecid  AND s.tecid = :subcategoryTecid")
    PermSubcategoryDbo getSubcategory(@Param("categoryTecid") Long permCategoryTecid, @Param("subcategoryTecid") Long subcategoryTecid);
}
