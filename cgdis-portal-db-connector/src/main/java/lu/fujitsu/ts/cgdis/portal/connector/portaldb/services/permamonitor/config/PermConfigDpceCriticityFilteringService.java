package lu.fujitsu.ts.cgdis.portal.connector.portaldb.services.permamonitor.config;

import lu.fujitsu.ts.cgdis.portal.core.domain.permamonitor.config.IPermConfigDpceCriticityDate;
import lu.fujitsu.ts.cgdis.portal.core.domain.permamonitor.config.PermConfigDayType;
import lu.fujitsu.ts.cgdis.portal.core.exception.CGDISTechnicalException;
import lu.fujitsu.ts.cgdis.portal.core.logging.LoggingCategory;
import lu.fujitsu.ts.cgdis.portal.core.logging.LoggingDomain;
import lu.fujitsu.ts.cgdis.portal.services.permamonitor.config.IPermConfigDpceCriticityFilteringService;
import lu.fujitsu.ts.cgdis.portal.services.publicholiday.IPublicHolidayRepositoryService;
import lu.fujitsu.ts.eportal.server.logging.milestones.LogMilestone;
import lu.fujitsu.ts.eportal.server.logging.milestones.LoggableType;
import lu.fujitsu.ts.eportal.server.logging.milestones.LoggableValue;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

import static lu.fujitsu.ts.cgdis.portal.core.domain.permamonitor.config.PermConfigDayType.*;

/**
 * Service class for managing PermConfigDpceCriticity read-only operations.
 */
@Service
@LogMilestone(category = LoggingCategory.REPOSITORY_SERVICE, domain = LoggingDomain.PERMAMONITOR)
@Transactional(readOnly = false)
public class PermConfigDpceCriticityFilteringService implements IPermConfigDpceCriticityFilteringService {

    private static final Logger LOGGER = LoggerFactory.getLogger(PermConfigDpceCriticityFilteringService.class);

    private final IPublicHolidayRepositoryService publicHolidayRepositoryService;

    public PermConfigDpceCriticityFilteringService(IPublicHolidayRepositoryService publicHolidayRepositoryService) {
        this.publicHolidayRepositoryService = publicHolidayRepositoryService;
    }

    @Override
    @LogMilestone(action = "filter configurations by date", logResult = @LoggableValue(type = LoggableType.COLLECTION))
    public <T extends IPermConfigDpceCriticityDate> List<T> filterByDate(@LoggableValue(type = LoggableType.COLLECTION) List<T> allConfigs, @LoggableValue LocalDate date) {
        LOGGER.debug("Filtering configurations by date {}", date);
        PermConfigDayType dayType = this.getDayType(date);
        return allConfigs.stream().filter(config -> config.getDayValue().equals(dayType)).collect(Collectors.toList());

    }

    @Override
    @LogMilestone(action = "filter configurations by date and time", logResult = @LoggableValue(type = LoggableType.COLLECTION))
    public <T extends IPermConfigDpceCriticityDate> List<T> filterByDateTime(@LoggableValue(type = LoggableType.COLLECTION) List<T> allConfigs, @LoggableValue LocalDateTime dateTime) {
        LOGGER.debug("Filtering configurations by date and time {}", dateTime);
        PermConfigDayType dayType = this.getDayType(dateTime.toLocalDate());
        return allConfigs.stream()
                .filter(config -> config.getDayValue().equals(dayType))
                .filter(config -> config.getStartHour().equals((short) dateTime.getHour()))
                .collect(Collectors.toList());
    }

    @Override
    @LogMilestone(action = "check if configuration is valid at date", logResult = @LoggableValue)
    public <T extends IPermConfigDpceCriticityDate> boolean isValidAtDate(@LoggableValue T oneConfig, @LoggableValue LocalDate date) {
        LOGGER.debug("Checking if configuration {} is valid at date {}", oneConfig, date);
        PermConfigDayType dayType = this.getDayType(date);
        return dayType.equals(oneConfig.getDayValue());
    }

    @Override
    @LogMilestone(action = "check if configuration is valid at date and time", logResult = @LoggableValue)
    public <T extends IPermConfigDpceCriticityDate> boolean isValidAtDateTime(@LoggableValue T oneConfig, @LoggableValue LocalDateTime datetime) {
        LOGGER.debug("Checking if configuration {} is valid at date and time {}", oneConfig, datetime);
        PermConfigDayType dayType = this.getDayType(datetime.toLocalDate());
        return dayType.equals(oneConfig.getDayValue());
    }

    @Override
    @LogMilestone(action = "determine day type for date", logResult = @LoggableValue)
    public PermConfigDayType getDayType(@LoggableValue LocalDate date) {
        LOGGER.debug("Determining day type for date {}", date);
        return getDayType(date, this.publicHolidayRepositoryService.isPublicHoliday(date));
    }

    @Override
    @LogMilestone(action = "determine day type for date considering public holidays", logResult = @LoggableValue)
    public PermConfigDayType getDayType(@LoggableValue(type = LoggableType.COLLECTION) List<LocalDate> publicHolidays, @LoggableValue LocalDate date) {
        LOGGER.debug("Determining day type for date {} and public holidays {}", date, publicHolidays);
        return getDayType(date, publicHolidays.contains(date));
    }


    private PermConfigDayType getDayType(LocalDate date, boolean isPublicHoliday) {
        LOGGER.debug("Determining day type for date {} and public holiday {}", date, isPublicHoliday);
        if (isPublicHoliday) {
            return PermConfigDayType.PUBLIC_HOLIDAY;
        }
        switch (date.getDayOfWeek()) {
            case MONDAY:
                return MONDAY;
            case TUESDAY:
                return TUESDAY;
            case WEDNESDAY:
                return WEDNESDAY;
            case THURSDAY:
                return THURSDAY;
            case FRIDAY:
                return FRIDAY;
            case SATURDAY:
                return SATURDAY;
            case SUNDAY:
                return SUNDAY;
            default:
                throw new CGDISTechnicalException("Invalid day of week");
        }

    }
}
