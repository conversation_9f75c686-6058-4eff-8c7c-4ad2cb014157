package lu.fujitsu.ts.cgdis.portal.connector.portaldb.specifications.impl.vehicles;

import lu.fujitsu.ts.cgdis.portal.connector.portaldb.dbos.VehicleDbo;
import lu.fujitsu.ts.cgdis.portal.connector.portaldb.dbos.serviceplan.ServicePlanDbo;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.data.jpa.domain.Specification;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.From;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import javax.persistence.criteria.Subquery;
import java.util.Collection;


/**
 * The type Free vehicles or in entities specification.
 */
public class FreeVehiclesSpecification implements Specification<VehicleDbo> {

    /**
     * The Excluded Entities.
     */
    private Collection<String> excludedEntities;

    /**
     * Instantiates a new Free Vehicles Specification
     */
    public FreeVehiclesSpecification(){
    }

    /**
     * Instantiates a new Free Vehicles Specification
     * Filter on excluded entities given in parameter
     *
     * @param excludedEntities filter vehicles excluded entities
     */
    public FreeVehiclesSpecification(Collection<String> excludedEntities){
        this.excludedEntities = excludedEntities;
    }

    @Override
    public Predicate toPredicate(Root<VehicleDbo> root, CriteriaQuery<?> query, CriteriaBuilder criteriaBuilder) {
        return toPredicateFrom(root, query, criteriaBuilder);
    }

    /**
     * To predicate from predicate.
     *
     * @param from            the from
     * @param criteriaQuery   the criteria query
     * @param criteriaBuilder the criteria builder
     * @return the predicate
     */
    public Predicate toPredicateFrom(From from, CriteriaQuery<?> criteriaQuery, CriteriaBuilder criteriaBuilder) {

        Subquery<Long> vehicleIdsFreeSubquery = criteriaQuery.subquery(Long.class);
        Root<ServicePlanDbo> servicePlan = vehicleIdsFreeSubquery.from(ServicePlanDbo.class);


        CurrentUsedVehiclesByServicePlanSpecification currentUsedVehiclesByServicePlanSpecification = new CurrentUsedVehiclesByServicePlanSpecification();
        vehicleIdsFreeSubquery.where(
                currentUsedVehiclesByServicePlanSpecification.toPredicate(servicePlan, criteriaQuery, criteriaBuilder)
                );

        return CollectionUtils.isNotEmpty(excludedEntities) ?
                criteriaBuilder.and(criteriaBuilder.not(from.get("tecid").in(vehicleIdsFreeSubquery.select(servicePlan.get("vehicle").get("tecid")))),
                    criteriaBuilder.not(from.get("entity").get("name").in(excludedEntities)))
                : criteriaBuilder.not(from.get("tecid").in(vehicleIdsFreeSubquery.select(servicePlan.get("vehicle").get("tecid"))));

    }


}
