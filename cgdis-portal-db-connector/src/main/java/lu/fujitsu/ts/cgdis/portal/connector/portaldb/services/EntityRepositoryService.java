package lu.fujitsu.ts.cgdis.portal.connector.portaldb.services;

import lu.fujitsu.ts.cgdis.portal.connector.portaldb.dbos.CGDISEntityDBO;
import lu.fujitsu.ts.cgdis.portal.connector.portaldb.repositories.IEntityRepository;
import lu.fujitsu.ts.cgdis.portal.core.domain.CGDISEntity;
import lu.fujitsu.ts.cgdis.portal.core.domain.CGDISEntityWithParentAndMain;
import lu.fujitsu.ts.cgdis.portal.core.domain.CGDISSecurityConstraint;
import lu.fujitsu.ts.cgdis.portal.core.domain.enums.CGDISEntityType;
import lu.fujitsu.ts.cgdis.portal.core.logging.LoggingCategory;
import lu.fujitsu.ts.cgdis.portal.core.logging.LoggingDomain;
import lu.fujitsu.ts.cgdis.portal.core.security.ICGDISSecurityConstraint;
import lu.fujitsu.ts.cgdis.portal.services.entity.IEntityRepositoryService;
import lu.fujitsu.ts.eportal.server.connector.jpa.services.SecuredRepositoryService;
import lu.fujitsu.ts.eportal.server.connector.jpa.specifications.SpecificationUtils;
import lu.fujitsu.ts.eportal.server.connector.jpa.specifications.secured.AbstractComposedSecuredSpecificationBuilder;
import lu.fujitsu.ts.eportal.server.core.domain.Fetches;
import lu.fujitsu.ts.eportal.server.core.domain.SearchCriterion;
import lu.fujitsu.ts.eportal.server.core.utils.EPortalConverter;
import lu.fujitsu.ts.eportal.server.logging.milestones.LogMilestone;
import lu.fujitsu.ts.eportal.server.logging.milestones.LoggableType;
import lu.fujitsu.ts.eportal.server.logging.milestones.LoggableValue;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.dozer.Mapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * The Entity repository service.
 */
@Service
@LogMilestone(category = LoggingCategory.REPOSITORY_SERVICE, domain = LoggingDomain.ENTITY)
public class EntityRepositoryService extends SecuredRepositoryService<ICGDISSecurityConstraint, CGDISEntity, CGDISEntityDBO, Long, IEntityRepository> implements IEntityRepositoryService {

    private EPortalConverter<CGDISEntityDBO, CGDISEntityWithParentAndMain> dbo2ModelWithParentAndMainConverter;

    @Autowired
    public EntityRepositoryService(IEntityRepository repository, AbstractComposedSecuredSpecificationBuilder<ICGDISSecurityConstraint, CGDISEntityDBO> specBuilder, Mapper mapper) {
        super("Entity", CGDISEntity.class, CGDISEntityDBO.class, repository, specBuilder, mapper, null);
        this.dbo2ModelWithParentAndMainConverter = new EPortalConverter(CGDISEntityDBO.class, CGDISEntityWithParentAndMain.class, mapper);

    }

    @Override
    public boolean canModify(ICGDISSecurityConstraint securityConstraint, Long aLong) {
        return true;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @LogMilestone(action = "search entities")
    public Page<? extends CGDISEntity> searchAll(CGDISSecurityConstraint securityConstraint, List<SearchCriterion> searchCriteria, PageRequest pageRequest) {

        List<Specification<CGDISEntityDBO>> collect = searchCriteria.stream().map(specBuilder::build).collect(Collectors.toList());
        collect.add(SpecificationUtils.or(
                specBuilder.buildSecurityConstraint(securityConstraint),
                byNullParents()
        ));

        Page<CGDISEntityDBO> all = this.repository.findAll(
                SpecificationUtils.and(
                        collect.toArray(new Specification[]{})
                ),
                buildPageRequest(pageRequest)
        );
        return this.dbo2ModelWithParentAndMainConverter.convert(all);
    }


    @Override
    @LogMilestone(action = "get all entities name")
    public Set<String> getAllEntitiesName() {
        List<String> entityNames = this.repository.findDistinctColumns(
                String.class,
                (root, query, criteriaBuilder) -> criteriaBuilder.conjunction(),
                (root, criteriaQuery, criteriaBuilder) -> root.get("name")
        );
        return new HashSet<>(entityNames);
    }

    @Override
    @LogMilestone(action = "get all entities name")
    public Set<Long> getAllEntitiesIds() {
        List<Long> entityIds = this.repository.findDistinctColumns(
                Long.class,
                (root, query, criteriaBuilder) -> criteriaBuilder.conjunction(),
                (root, criteriaQuery, criteriaBuilder) -> root.get("tecid")
        );
        return new HashSet<>(entityIds);
    }

    @Override
    @LogMilestone(action = "find main entity")
    public Optional<CGDISEntity> findMainEntity(Long entityId) {
        Specification specifications = (root, criteriaQuery, criteriaBuilder) -> criteriaBuilder.equal(root.get("tecid"), entityId);
        List<CGDISEntityDBO> mainEntity = this.repository.findDistinctColumns(CGDISEntityDBO.class,
                specifications,
                (root, criteriaQuery, criteriaBuilder) -> root.join("mainEntity"));
        if (mainEntity.isEmpty()) {
            return Optional.empty();
        }
        return Optional.of(dbo2ModelConverter.convert(mainEntity.get(0)));
    }

    @Override
    @LogMilestone(action = "find main entities")
    public Map<Long, Optional<CGDISEntity>> findMainEntities(@LoggableValue(type = LoggableType.COLLECTION) Collection<Long> entityIds) {
        Specification<CGDISEntityDBO> specifications = SpecificationUtils.fetch(
                (root, criteriaQuery, criteriaBuilder) -> root.get("tecid").in(entityIds)
                ,
                Fetches.build(Fetches.Type.INNER, "mainEntity")
        );

        List<CGDISEntityDBO> mainEntity = this.repository.findDistinctColumns(CGDISEntityDBO.class,
                specifications,
                (root, criteriaQuery, criteriaBuilder) -> root);

        return mainEntity.stream()
                .map(oneEntity -> ImmutablePair.of(oneEntity.getTecid(), oneEntity.getMainEntity()))
                .collect(Collectors.toMap(ImmutablePair::getLeft, p -> dbo2ModelConverter.convert(Optional.ofNullable(p.getRight()))));


    }

    /**
     * Filter by null parents (do not take account of NATIONAL entities)
     *
     * @return the specification
     */
    private static Specification<CGDISEntityDBO> byNullParents() {
        return (root, query, cb) -> cb.and(
                cb.isNull(root.get("parent")),
                cb.notEqual(root.get("type"), CGDISEntityType.NATIONAL)

        );
    }

}
