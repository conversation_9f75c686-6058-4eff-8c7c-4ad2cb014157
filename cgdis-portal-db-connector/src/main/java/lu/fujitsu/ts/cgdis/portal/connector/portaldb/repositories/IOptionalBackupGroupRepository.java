package lu.fujitsu.ts.cgdis.portal.connector.portaldb.repositories;

import lu.fujitsu.ts.cgdis.portal.connector.portaldb.dbos.serviceplan.OptionalBackupGroupDbo;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

/**
 * The interface Optional/Backup group repository.
 */
@Repository
public interface IOptionalBackupGroupRepository extends IBaseCGDISRepository<OptionalBackupGroupDbo>, IOptionalBackupGroupRepositoryCustom {

    /**
     * Delete by foreign key not in.
     *
     * @param foreignKeys the foreign keys
     */
    @Modifying
    @Query("update  OptionalBackupGroupDbo g set g.available = false where g.foreignKey not in (:foreignKeys)")
    int deleteByForeignKeyNotIn(@Param("foreignKeys") Set<String> foreignKeys);

    @Query("select g from OptionalBackupGroupDbo g where g.foreignKey not in (:foreignKeys)")
    List<OptionalBackupGroupDbo> findByForeignKeyNotIn(@Param("foreignKeys") Set<String> foreignKeys);

    /**
     * Delete by foreign key.
     *
     * @param foreignKey the foreign key
     */
    @Modifying
    @Query("update  OptionalBackupGroupDbo g set g.available = false where g.foreignKey = :foreignKey")
    int deleteByForeignKey(@Param("foreignKey") String foreignKey);

}
