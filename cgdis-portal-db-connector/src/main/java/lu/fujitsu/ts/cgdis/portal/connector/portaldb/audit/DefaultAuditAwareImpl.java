package lu.fujitsu.ts.cgdis.portal.connector.portaldb.audit;

import lu.fujitsu.ts.cgdis.portal.core.security.utils.CGDISPortalSecurityHolder;
import org.springframework.data.domain.AuditorAware;

import java.util.Optional;

public class DefaultAuditAwareImpl implements AuditorAware<String> {
    @Override
    public Optional<String> getCurrentAuditor() {
        return Optional.of(CGDISPortalSecurityHolder.getConnectedUserLogin());
    }
}
