package lu.fujitsu.ts.cgdis.portal.connector.portaldb.repositories.audit;

import lu.fujitsu.ts.cgdis.portal.connector.portaldb.dbos.audit.AuditDbo;
import lu.fujitsu.ts.cgdis.portal.connector.portaldb.repositories.AbstractRepositoryImpl;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;

/**
 * The type Audit repository.
 */
@Repository
public class IAuditRepositoryImpl extends AbstractRepositoryImpl<AuditDbo> implements IAuditRepositoryCustom {

    /**
     * The Entity manager.
     */
    @PersistenceContext
    private EntityManager entityManager;


    /**
     * Instantiates a new Audit repository.
     */
    public IAuditRepositoryImpl() {
        super(AuditDbo.class);
    }

    @Override
    protected EntityManager getEntityManager() {
        return entityManager;
    }



}
