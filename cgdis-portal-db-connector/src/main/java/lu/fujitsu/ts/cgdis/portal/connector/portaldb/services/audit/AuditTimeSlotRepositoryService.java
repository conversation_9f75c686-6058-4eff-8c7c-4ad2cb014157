package lu.fujitsu.ts.cgdis.portal.connector.portaldb.services.audit;

import lu.fujitsu.ts.cgdis.portal.connector.portaldb.dbos.audit.AuditDbo;
import lu.fujitsu.ts.cgdis.portal.connector.portaldb.dbos.audit.AuditTimeSlotDbo;
import lu.fujitsu.ts.cgdis.portal.connector.portaldb.repositories.SelectPathBuilder;
import lu.fujitsu.ts.cgdis.portal.connector.portaldb.repositories.audit.IAuditTimeSlotRepository;
import lu.fujitsu.ts.cgdis.portal.core.domain.audit.Audit;
import lu.fujitsu.ts.cgdis.portal.core.domain.audit.AuditActionType;
import lu.fujitsu.ts.cgdis.portal.core.domain.audit.AuditTimeSlot;
import lu.fujitsu.ts.cgdis.portal.core.domain.audit.AuditType;
import lu.fujitsu.ts.cgdis.portal.core.logging.LoggingCategory;
import lu.fujitsu.ts.cgdis.portal.core.logging.LoggingDomain;
import lu.fujitsu.ts.cgdis.portal.core.security.IAuditSecurityConstraint;
import lu.fujitsu.ts.eportal.server.connector.jpa.specifications.secured.AbstractComposedSecuredSpecificationBuilder;
import lu.fujitsu.ts.eportal.server.core.domain.SearchCriterion;
import lu.fujitsu.ts.eportal.server.logging.milestones.LogMilestone;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.dozer.Mapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Repository service for AuditTimeSlot entities.
 * Manages persistence operations for audit time slots.
 */
@Service
@LogMilestone(category = LoggingCategory.REPOSITORY_SERVICE, domain = LoggingDomain.AUDIT)
public class AuditTimeSlotRepositoryService extends AbstractAuditRepositoryService<AuditTimeSlot, AuditTimeSlotDbo, IAuditTimeSlotRepository> {
    private static final Logger LOGGER = LoggerFactory.getLogger(AuditTimeSlotRepositoryService.class);

    /**
     * Constructs an AuditTimeSlotRepositoryService with required dependencies.
     *
     * @param repository  The JPA repository for AuditTimeSlotDbo entities
     * @param specBuilder The specification builder for security constraints
     * @param mapper      The object mapper for DTO conversion
     */
    public AuditTimeSlotRepositoryService(IAuditTimeSlotRepository repository, AbstractComposedSecuredSpecificationBuilder<IAuditSecurityConstraint, AuditTimeSlotDbo> specBuilder, Mapper mapper) {
        super(repository, specBuilder, AuditTimeSlotDbo.class, AuditTimeSlot.class, mapper);
    }

    @Override
    protected void additionalSortBuilder(Map<String, String> sorts) {
    }

    /**
     * Searches for audit records based on provided constraints, types, and criteria.
     *
     * @param constraint  Security constraint to apply to the search
     * @param types       Collection of audit types to filter by
     * @param criteria    Collection of search criteria to apply
     * @param pageRequest Pagination information
     * @return A page of Audit records matching the criteria
     */
    @Override
    public Page<Audit> search(IAuditSecurityConstraint constraint, Collection<AuditType> types, Collection<SearchCriterion> criteria, PageRequest pageRequest) {
        return null;
    }

    @Override
    public Page<AuditTimeSlot> search(IAuditSecurityConstraint constraint, Collection<SearchCriterion> criteria, PageRequest pageRequest) {
        // Build a path selector that determines the datetime to use for sorting
        // If action type is SPLIT, use the end datetime, otherwise use the start datetime
        SelectPathBuilder end = (root, criteriaQuery, criteriaBuilder) ->
                criteriaBuilder.selectCase().when(
                                criteriaBuilder.equal(root.get(AuditDbo.Fields.ACTION_TYPE), AuditActionType.SPLIT),
                                root.get(AuditTimeSlotDbo.Fields.END_DATETIME)
                        )
                        .otherwise(root.get(AuditTimeSlotDbo.Fields.START_DATETIME));

        // Create custom mapping for targetEndDatetime
        Map<String, SelectPathBuilder> sorts = new HashMap<>();
        sorts.put("targetEndDatetime", end);

        // Execute query
        Page<Object[]> distinctColumnsWithSort = this.repository.findDistinctColumnsWithSort(
                Arrays.asList(
                        // First column: The full AuditTimeSlotDbo entity object
                        ImmutablePair.of(AuditTimeSlotDbo.class, (root, criteriaQuery, criteriaBuilder) -> root),

                        // Second column: The calculated end datetime for sorting purposes
                        ImmutablePair.of(AuditTimeSlotDbo.class, end)

                ),
                specBuilder.build(constraint, criteria),
                pageRequest,
                getSortBuilder(), null, sorts

        );
        // Extract the AuditTimeSlotDbo objects from the result
        List<AuditTimeSlot> collect = distinctColumnsWithSort.getContent().stream()
                .map(objects -> dbo2ModelConverter.convert((AuditTimeSlotDbo) objects[0]))
                .collect(Collectors.toList());
        return new PageImpl(collect, pageRequest, distinctColumnsWithSort.getTotalElements());
    }

    /**
     * Returns the audit type handled by this repository service.
     *
     * @return The audit type for time slots
     */
    @Override
    public AuditType getAuditType() {
        return AuditType.SLOT;
    }
}
