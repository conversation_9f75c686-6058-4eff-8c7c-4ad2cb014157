package lu.fujitsu.ts.cgdis.portal.connector.portaldb.dbos.prestation.allowance;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.FieldNameConstants;
import lu.fujitsu.ts.cgdis.portal.connector.portaldb.dbos.person.PersonDbo;
import lu.fujitsu.ts.cgdis.portal.core.pojo.prestation.allowance.PrestationAllowanceVersion;
import org.hibernate.annotations.BatchSize;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.LinkedHashSet;
import java.util.Objects;
import java.util.Set;

@NoArgsConstructor
@Getter
@Setter
@ToString(onlyExplicitlyIncluded = true)
@Entity
@Table(name = PrestationAllowanceResultDbo.TABLE_NAME)
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn(discriminatorType = DiscriminatorType.STRING, name = PrestationAllowanceResultDbo.COLUMN_ALLOWANCEVERSION_NAME)
@FieldNameConstants
@EntityListeners(AuditingEntityListener.class)
public abstract class PrestationAllowanceResultDbo implements Serializable {
    public static final String TABLE_NAME = "PRESTATION_ALLOWANCE";
    public static final String COLUMN_ID_NAME = "TECID";
    public static final String COLUMN_TECLOCK_NAME = "TECLOCK";
    public static final String COLUMN_PERSONTECID_NAME = "PERSON_TECID";
    public static final String COLUMN_ALLOWANCEDATE_NAME = "ALLOWANCE_DATE";
    public static final String COLUMN_ALLOWANCE_NAME = "ALLOWANCE";
    public static final String COLUMN_ALLOWANCEVERSION_NAME = "ALLOWANCE_VERSION";
    public static final String COLUMN_CREATEDBY_NAME = "CREATED_BY";
    public static final String COLUMN_CREATEDON_NAME = "CREATED_ON";
    private static final long serialVersionUID = -2045758619651356336L;




    public PrestationAllowanceResultDbo(Long id, Long teclock, Long personTecid, LocalDate allowanceDate, BigDecimal allowance, PrestationAllowanceVersion version, Set<PrestationAllowanceEntityDbo> entities) {
        this.id = id;
        this.teclock = teclock;
        this.personTecid = personTecid;
        this.allowanceDate = allowanceDate;
        this.allowance = allowance;
        this.version = version;
        this.entities = entities;
    }

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = COLUMN_ID_NAME, nullable = false)
    @ToString.Include
    private Long id;

    @Version
    @Column(name = COLUMN_TECLOCK_NAME, nullable = false)
    private Long teclock;

    @NotNull
    @Column(name = COLUMN_PERSONTECID_NAME, nullable = false)
    @ToString.Include
    private Long personTecid;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = COLUMN_PERSONTECID_NAME, insertable = false, updatable = false)
    private PersonDbo person;

    @NotNull
    @Column(name = COLUMN_ALLOWANCEDATE_NAME, nullable = false)
    @ToString.Include
    private LocalDate allowanceDate;

    @NotNull
    @Column(name = COLUMN_ALLOWANCE_NAME, nullable = false, precision = 10, scale = 4)
    @ToString.Include
    private BigDecimal allowance;

    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = COLUMN_ALLOWANCEVERSION_NAME, nullable = false, length = 5, insertable = false, updatable = false)
    @ToString.Include
    private PrestationAllowanceVersion version;

    @BatchSize(size = 50)
    @OneToMany(mappedBy = PrestationAllowanceEntityDbo.Fields.PRESTATION_ALLOWANCE, cascade = CascadeType.REMOVE ,orphanRemoval = true)
    private Set<PrestationAllowanceEntityDbo> entities = new LinkedHashSet<>();

    @Size(max = 10)
    @Column(name = COLUMN_CREATEDBY_NAME, nullable = false, length = 10,updatable = false)
    @CreatedBy
    private String createdBy;

    @Column(name = COLUMN_CREATEDON_NAME, nullable = false,updatable = false)
    @CreatedDate
    private LocalDateTime createdOn;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        PrestationAllowanceResultDbo that = (PrestationAllowanceResultDbo) o;
        return Objects.equals(id, that.id) && Objects.equals(teclock, that.teclock) && Objects.equals(personTecid, that.personTecid) && Objects.equals(allowanceDate, that.allowanceDate) && Objects.equals(allowance, that.allowance) && version == that.version;
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, teclock, personTecid, allowanceDate, allowance, version);
    }
}
