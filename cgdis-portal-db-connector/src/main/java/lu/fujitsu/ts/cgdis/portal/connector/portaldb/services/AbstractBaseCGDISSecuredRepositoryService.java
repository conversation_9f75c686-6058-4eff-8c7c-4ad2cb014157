package lu.fujitsu.ts.cgdis.portal.connector.portaldb.services;

import io.micrometer.core.annotation.Timed;
import lu.fujitsu.ts.cgdis.portal.core.domain.ICGDISPortalUserDetails;
import lu.fujitsu.ts.cgdis.portal.core.security.utils.CGDISPortalSecurityHolder;
import lu.fujitsu.ts.cgdis.portal.services.IBaseCGDISSecuredRepositoryService;
import lu.fujitsu.ts.eportal.server.connector.jpa.services.SecuredRepositoryService;
import lu.fujitsu.ts.eportal.server.connector.jpa.specifications.secured.ISecuredSpecificationBuilder;
import lu.fujitsu.ts.eportal.server.core.domain.Fetches;
import lu.fujitsu.ts.eportal.server.core.domain.IBaseModel;
import lu.fujitsu.ts.eportal.server.core.domain.SearchCriterion;
import lu.fujitsu.ts.eportal.server.core.exceptions.NotFoundException;
import lu.fujitsu.ts.eportal.server.core.utils.IEPortalConverter;
import org.dozer.Mapper;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;

/**
 * The type Abstract base cgdis secured repository service.
 *
 * @param <E> the type parameter
 * @param <D> the type parameter
 * @param <R> the type parameter
 */
public abstract class AbstractBaseCGDISSecuredRepositoryService<E extends IBaseModel<Long>, D, R extends JpaRepository<D, Long> & JpaSpecificationExecutor<D>> extends SecuredRepositoryService<ICGDISPortalUserDetails, E, D, Long, R> implements IBaseCGDISSecuredRepositoryService<E> {


    /**
     * Instantiates a new Abstract base cgdis secured repository service.
     *
     * @param entityType  the entity type
     * @param eClass      the e class
     * @param dClass      the d class
     * @param repository  the repository
     * @param specBuilder the spec builder
     * @param mapper      the mapper
     * @param fetches     the fetches
     */
    public AbstractBaseCGDISSecuredRepositoryService(String entityType, Class<? extends E> eClass, Class<D> dClass, R repository, ISecuredSpecificationBuilder<ICGDISPortalUserDetails, D> specBuilder, Mapper mapper, Fetches... fetches) {
        super(entityType, eClass, dClass, repository, specBuilder, mapper, fetches);
    }

    @Override

    public void flush() {
        repository.flush();
    }

    @Override

    public E get(Long id) throws NotFoundException {
        return get(CGDISPortalSecurityHolder.getConnectedUserDetails(), id);
    }

    @Override

    public E get(Long id, Fetches... fetches) throws NotFoundException {
        return get(CGDISPortalSecurityHolder.getConnectedUserDetails(), id, fetches);
    }

    /**
     * Get t.
     *
     * @param <T>       the type parameter
     * @param id        the id
     * @param converter the converter
     * @param fetches   the fetches
     * @return the t
     * @throws NotFoundException the not found exception
     */
    protected <T> T get(Long id, IEPortalConverter<D, T> converter, Fetches... fetches) throws NotFoundException {
        return get(CGDISPortalSecurityHolder.getConnectedUserDetails(), id, converter, fetches);
    }

    @Override

    public List<E> getAll(Fetches... fetches) {
        return getAll(CGDISPortalSecurityHolder.getConnectedUserDetails(), fetches);
    }

    /**
     * Gets all.
     *
     * @param <T>       the type parameter
     * @param converter the converter
     * @param fetches   the fetches
     * @return the all
     */
    protected <T> List<T> getAll(IEPortalConverter<D, T> converter, Fetches... fetches) {
        return getAll(CGDISPortalSecurityHolder.getConnectedUserDetails(), converter, fetches);
    }

    @Override

    public long countAll() {
        return countAll(CGDISPortalSecurityHolder.getConnectedUserDetails());
    }


    @Override

    public List<E> getAll() {
        return getAll(CGDISPortalSecurityHolder.getConnectedUserDetails());
    }


    @Override

    public List<E> getAll(Sort sort, Fetches... fetches) {
        return getAll(CGDISPortalSecurityHolder.getConnectedUserDetails(), sort, fetches);
    }

    /**
     * Gets all.
     *
     * @param <T>       the type parameter
     * @param sort      the sort
     * @param converter the converter
     * @param fetches   the fetches
     * @return the all
     */
    protected <T> List<T> getAll(Sort sort, IEPortalConverter<D, T> converter, Fetches... fetches) {
        return getAll(CGDISPortalSecurityHolder.getConnectedUserDetails(), sort, converter, fetches);
    }

    @Override

    public Page<E> getAll(Pageable pageable, Fetches... fetches) {
        return getAll(CGDISPortalSecurityHolder.getConnectedUserDetails(), pageable, fetches);
    }

    /**
     * Gets all.
     *
     * @param <T>       the type parameter
     * @param pageable  the pageable
     * @param converter the converter
     * @param fetches   the fetches
     * @return the all
     */
    protected <T> Page<T> getAll(Pageable pageable, IEPortalConverter<D, T> converter, Fetches... fetches) {
        return getAll(CGDISPortalSecurityHolder.getConnectedUserDetails(), pageable, converter, fetches);
    }

    @Override

    public E create(E e) {
        return create(CGDISPortalSecurityHolder.getConnectedUserDetails(), e);
    }

    @Override

    public E update(E e) {
        return update(CGDISPortalSecurityHolder.getConnectedUserDetails(), e);
    }

    @Override

    public void delete(E e) {
        delete(CGDISPortalSecurityHolder.getConnectedUserDetails(), e);
    }

    @Override

    public void deleteById(Long aLong) {
        deleteById(CGDISPortalSecurityHolder.getConnectedUserDetails(), aLong);
    }

    @Override

    public List<E> search(List<SearchCriterion> list, Fetches... fetches) {
        return search(CGDISPortalSecurityHolder.getConnectedUserDetails(), list, fetches);
    }

    /**
     * Search list.
     *
     * @param <T>       the type parameter
     * @param list      the list
     * @param converter the converter
     * @param fetches   the fetches
     * @return the list
     */
    protected <T> List<T> search(List<SearchCriterion> list, IEPortalConverter<D, T> converter, Fetches... fetches) {
        return search(CGDISPortalSecurityHolder.getConnectedUserDetails(), list, converter, fetches);
    }

    @Override

    public List<E> search(List<SearchCriterion> list, Sort sort, Fetches... fetches) {
        return search(CGDISPortalSecurityHolder.getConnectedUserDetails(), list, sort, fetches);
    }

    /**
     * Search list.
     *
     * @param <T>       the type parameter
     * @param list      the list
     * @param sort      the sort
     * @param converter the converter
     * @param fetches   the fetches
     * @return the list
     */
    protected <T> List<T> search(List<SearchCriterion> list, Sort sort, IEPortalConverter<D, T> converter, Fetches... fetches) {
        return search(CGDISPortalSecurityHolder.getConnectedUserDetails(), list, sort, converter, fetches);
    }

    @Override

    public Page<E> search(List<SearchCriterion> list, PageRequest pageRequest, Fetches... fetches) {
        return search(CGDISPortalSecurityHolder.getConnectedUserDetails(), list, pageRequest, fetches);
    }

    /**
     * Search page.
     *
     * @param <T>         the type parameter
     * @param list        the list
     * @param pageRequest the page request
     * @param converter   the converter
     * @param fetches     the fetches
     * @return the page
     */
    protected <T> Page<T> search(List<SearchCriterion> list, PageRequest pageRequest, IEPortalConverter<D, T> converter, Fetches... fetches) {
        return search(CGDISPortalSecurityHolder.getConnectedUserDetails(), list, pageRequest, converter, fetches);
    }

    @Override

    public long count(List<SearchCriterion> list) {
        return count(CGDISPortalSecurityHolder.getConnectedUserDetails(), list);
    }


    /**
     * Find one dbo.
     *
     * @param specification the specification
     * @return the dbo
     */
    protected D findOne(Specification<D> specification) throws NotFoundException {
        return findOne(CGDISPortalSecurityHolder.getConnectedUserDetails(), specification);
    }

    /**
     * Find all list.
     *
     * @param specification the specification
     * @return the list
     */
    protected List<D> findAll(Specification<D> specification) {
        return findAll(CGDISPortalSecurityHolder.getConnectedUserDetails(), specification);
    }

    /**
     * Find all page.
     *
     * @param specification the specification
     * @param pageable      the pageable
     * @return the page
     */
    protected Page<D> findAll(Specification<D> specification, Pageable pageable) {
        return findAll(CGDISPortalSecurityHolder.getConnectedUserDetails(), specification, pageable);
    }

    /**
     * Find all list.
     *
     * @param specification the specification
     * @param sort          the sort
     * @return the list
     */
    protected List<D> findAll(Specification<D> specification, Sort sort) {
        return findAll(CGDISPortalSecurityHolder.getConnectedUserDetails(), specification, sort);
    }

    /**
     * Count long.
     *
     * @param specification the specification
     * @return the long
     */
    protected long count(Specification<D> specification) {
        return count(CGDISPortalSecurityHolder.getConnectedUserDetails(), specification);
    }


}
