package lu.fujitsu.ts.cgdis.portal.connector.portaldb.repositories;

import lu.fujitsu.ts.cgdis.portal.connector.portaldb.dbos.BaseDbo;
import lu.fujitsu.ts.cgdis.portal.connector.portaldb.dbos.CGDISEntityDBO;
import lu.fujitsu.ts.cgdis.portal.connector.portaldb.dbos.InterventionTypeDbo;
import lu.fujitsu.ts.cgdis.portal.connector.portaldb.dbos.VehicleDbo;
import lu.fujitsu.ts.cgdis.portal.connector.portaldb.dbos.serviceplan.*;
import lu.fujitsu.ts.cgdis.portal.connector.portaldb.specifications.impl.SpecificationJointUtils;
import lu.fujitsu.ts.cgdis.portal.connector.portaldb.specifications.impl.prestations.PrestationPartiallyBetweenDateSpecification;
import lu.fujitsu.ts.cgdis.portal.connector.portaldb.specifications.impl.serviceplans.versions.ServicePlanVersionBetweenDateSpecification;
import lu.fujitsu.ts.cgdis.portal.core.domain.planning.PrestationPlanningItem;
import lu.fujitsu.ts.cgdis.portal.core.domain.serviceplan.prestation.PrestationStatistics;
import lu.fujitsu.ts.cgdis.portal.core.logging.LoggingCategory;
import lu.fujitsu.ts.cgdis.portal.core.logging.LoggingDomain;
import lu.fujitsu.ts.cgdis.portal.core.pojo.prestation.allowance.PrestationForAllowance;
import lu.fujitsu.ts.cgdis.portal.core.security.ICGDISSecurityConstraint;
import lu.fujitsu.ts.cgdis.portal.core.utils.DateTimeUtils;
import lu.fujitsu.ts.eportal.server.connector.jpa.services.DBUtils;
import lu.fujitsu.ts.eportal.server.logging.milestones.LogMilestone;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Tuple;
import javax.persistence.TypedQuery;
import javax.persistence.criteria.*;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static java.time.temporal.ChronoUnit.MILLIS;

/**
 * The interface for person repository.
 */
@Repository
@LogMilestone(category = LoggingCategory.REPOSITORY, domain = LoggingDomain.PRESTATION)
public class IPrestationRepositoryImpl extends AbstractRepositoryImpl<PrestationDbo> implements IPrestationRepositoryCustom {

    private static final Logger LOGGER = LoggerFactory.getLogger(IPrestationRepositoryImpl.class);

    @PersistenceContext
    private EntityManager entityManager;

    /**
     * Instantiates a new Abstract repository.
     */
    public IPrestationRepositoryImpl() {
        super(PrestationDbo.class);
    }

    @Override
    protected EntityManager getEntityManager() {
        return entityManager;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public PrestationStatistics getStatistics(ICGDISSecurityConstraint securityConstraint, long personId, LocalDate from, LocalDate to, Boolean isPro, String portalLabel, String positionLabel, String entityLabel, Boolean barracked) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<Tuple> query = cb.createTupleQuery();
        Root<PrestationDbo> prestation = query.from(PrestationDbo.class);
        Path<ServicePlanPositionDbo> position = prestation.get(PrestationDbo.Fields.POSITION);
        Root<ServicePlanModelDbo> model = query.from(ServicePlanModelDbo.class);
        SetJoin<ServicePlanModelDbo, ServicePlanModelVersionDbo> versions = model.joinSet(ServicePlanModelDbo.Fields.VERSIONS);
        Predicate isProPredicate = null;
        if (isPro == null) {
            isProPredicate = cb.or(cb.equal(prestation.get(PrestationDbo.Fields.IS_PROFESSIONAL), true), cb.equal(prestation.get("isProfessional"), false));
        } else {
            isProPredicate = cb.and(cb.equal(prestation.get(PrestationDbo.Fields.IS_PROFESSIONAL), isPro));
        }

        Predicate positionPredicate = positionLabel != null ? cb.like(position.get(ServicePlanPositionDbo.Fields.LABEL), "%" + positionLabel + "%") : cb.conjunction();

        Subquery<Long> subquery = query.subquery(Long.class);
        Root<ServicePlanDbo> servicePlan = subquery.from(ServicePlanDbo.class);
        Join<ServicePlanDbo, ServicePlanVersionDbo> versionsPlan = servicePlan.join(ServicePlanDbo.Fields.VERSIONS);
        Join<ServicePlanVersionDbo, ServicePlanTimeSlotDbo> timeslots = versionsPlan.join(ServicePlanVersionDbo.Fields.SERVICE_PLAN_TIME_SLOTS);
        subquery.where(
                barracked != null ?
                        cb.and(
                            cb.equal(versionsPlan.join(ServicePlanVersionDbo.Fields.SERVICE_PLAN_TYPE).get(ServicePlanTypeDbo.Fields.IS_BARRACKED), barracked),
                            new ServicePlanVersionBetweenDateSpecification(from, to).toPredicateFromPath(versionsPlan,subquery, cb)
                        ): cb.conjunction(),
                portalLabel != null ? cb.like(servicePlan.get(ServicePlanDbo.Fields.PORTAL_LABEL), "%" + portalLabel + "%") : cb.conjunction(),
                entityLabel != null ? cb.like(servicePlan.get(ServicePlanDbo.Fields.ENTITY).get(CGDISEntityDBO.Fields.NAME), "%" + entityLabel + "%") : cb.conjunction()
        );
        query.where(
                cb.equal(prestation.get(PrestationDbo.Fields.PERSON).get(BaseDbo.Fields.TECID), personId),
                cb.equal(versions.get(BaseDbo.Fields.TECID), position.get(ServicePlanPositionDbo.Fields.VERSION_ID)),
                new PrestationPartiallyBetweenDateSpecification(LocalDateTime.of(from, LocalTime.MIDNIGHT), LocalDateTime.of(to.plusDays(1), LocalTime.MIDNIGHT), false,false).toPredicateFromPath(prestation, query, cb),
                isProPredicate,
                positionPredicate,
                (portalLabel != null || entityLabel != null || barracked != null) ? prestation.get(PrestationDbo.Fields.TIME_SLOT_ID).in(subquery.select(timeslots.get(BaseDbo.Fields.TECID))) : cb.conjunction()
        );

        Path<String> intervType = model.get(ServicePlanModelDbo.Fields.INTERVENTION_TYPE).get(InterventionTypeDbo.Fields.LABEL);
        query.multiselect(intervType.alias("type"), prestation.get(PrestationDbo.Fields.START_DATE_TIME).alias("from"), prestation.get(PrestationDbo.Fields.END_DATE_TIME).alias("to"));
        List<Tuple> result = entityManager.createQuery(query).getResultList();
        PrestationStatistics stats = new PrestationStatistics();
        stats.setPersonId(personId);
        stats.setFrom(from);
        stats.setTo(to);
        LocalDateTime startTimeMin = LocalDateTime.of(from, LocalTime.MIDNIGHT);
        LocalDateTime endTimeMax = LocalDateTime.of(to.plus(1, ChronoUnit.DAYS), LocalTime.MIDNIGHT);


        if (result != null) {
            result.forEach(t -> stats.addPrestation(t.get("type", String.class), Duration.between(DateTimeUtils.max(t.get("from", LocalDateTime.class), startTimeMin), DateTimeUtils.min(t.get("to", LocalDateTime.class), endTimeMax)).getSeconds()));
        }
        return stats;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Map<Long, List<PrestationPlanningItem>> findPrestations(ICGDISSecurityConstraint securityConstraint, Collection<Long> personIds, LocalDateTime startDateTime, LocalDateTime endDateTime) {
        if (personIds.isEmpty()) {
            return new HashMap<>();
        }
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<Tuple> query = cb.createTupleQuery();

        Root<PrestationDbo> prestation = query.from(PrestationDbo.class);

        Path<ServicePlanPositionDbo> position = prestation.join(PrestationDbo.Fields.POSITION, JoinType.INNER);

//        prestation.joinSet("")


        Join<PrestationDbo, ServicePlanTimeSlotDbo> timeSlot = prestation.join(PrestationDbo.Fields.TIME_SLOT, JoinType.INNER);
        Join<ServicePlanTimeSlotDbo, ServicePlanVersionDbo> version = timeSlot.join(ServicePlanTimeSlotDbo.Fields.VERSION, JoinType.INNER);
        Join<ServicePlanVersionDbo, ServicePlanDbo> plan = version.join(ServicePlanVersionDbo.Fields.SERVICE_PLAN, JoinType.INNER);

        LocalDate localStartDate = startDateTime.toLocalDate();
        LocalDate prestationDate = localStartDate.minusDays(1);
        LocalDate localEndDate = endDateTime.toLocalDate();
        LocalDate prestationEndDate = localEndDate;
        query.where(
                cb.and(
                        new PrestationPartiallyBetweenDateSpecification(startDateTime, endDateTime, false,false).toPredicateFromPath(prestation, query, cb),
                        prestation.get(PrestationDbo.Fields.PERSON_ID).in(personIds)
                )
        );

        query.orderBy(cb.asc(prestation.get("startDateTime")));

        query.distinct(true);
        Join<ServicePlanDbo, CGDISEntityDBO> entityJoin = plan.join(ServicePlanDbo.Fields.ENTITY, JoinType.LEFT);
        Join<CGDISEntityDBO, CGDISEntityDBO> mainEntity = entityJoin.join(CGDISEntityDBO.Fields.MAIN_ENTITY, JoinType.LEFT);
        query.multiselect(
                prestation.get(PrestationDbo.Fields.PERSON_ID).alias("personId"),
                prestation.get(BaseDbo.Fields.TECID).alias("prestationId"),
                prestation.get(PrestationDbo.Fields.START_DATE_TIME).alias("startDateTime"),
                prestation.get(PrestationDbo.Fields.END_DATE_TIME).alias("endDateTime"),
                prestation.get(PrestationDbo.Fields.BARRACKED).alias("barracked"),
                position.get(ServicePlanPositionDbo.Fields.LABEL).alias("position"),
                position.get(ServicePlanPositionDbo.Fields.COMPLETION_TYPE).alias("positionCompletionType"),
                plan.get(ServicePlanDbo.Fields.PORTAL_LABEL).alias("servicePlanName"),
                version.join(ServicePlanVersionDbo.Fields.SERVICE_PLAN_TYPE, JoinType.INNER).get(ServicePlanTypeDbo.Fields.LABEL).alias("servicePlanType"),
                plan.<ServicePlanDbo, VehicleDbo>join(ServicePlanDbo.Fields.VEHICLE, JoinType.LEFT).get(VehicleDbo.Fields.REGISTRATION).alias("vehicleRegistration"),
                entityJoin.get(CGDISEntityDBO.Fields.NAME).alias("entity"),
                entityJoin.get(BaseDbo.Fields.TECID).alias("entityTecid"),
                mainEntity.get(CGDISEntityDBO.Fields.NAME).alias("mainEntity"),
                mainEntity.get(BaseDbo.Fields.TECID).alias("mainEntityTecid"),
                plan.<ServicePlanDbo, ServicePlanModelDbo>join(ServicePlanDbo.Fields.MODEL, JoinType.INNER)
                        .<ServicePlanModelDbo, InterventionTypeDbo>join(ServicePlanModelDbo.Fields.INTERVENTION_TYPE, JoinType.INNER).get(InterventionTypeDbo.Fields.LABEL).alias("interventionType"),
                plan.get(ServicePlanDbo.Fields.EXCLUSIVE).alias("exclusive"), plan.get(ServicePlanDbo.Fields.ELS_STATUS_MANAGEMENT).alias("elsStatusManagement")
        );

        List<Tuple> resultList = entityManager.createQuery(query).getResultList();
        if (resultList != null) {
            return resultList.stream().collect(Collectors.groupingBy(tuple -> (Long) tuple.get(0), Collectors.mapping(DBUtils.tupleMapper(PrestationPlanningItem.class), Collectors.toList())));
        } else {
            return Collections.emptyMap();
        }

    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<PrestationDbo> findOutOfBondsPrestationsForModel(ICGDISSecurityConstraint securityConstraint, long modelId, Specification<PrestationDbo> additionnalSpecifications) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<PrestationDbo> query = cb.createQuery(PrestationDbo.class);
        Root<PrestationDbo> prestation = query.from(PrestationDbo.class);
        Path<Object> position = prestation.get("position");
        Root<ServicePlanModelDbo> model = query.from(ServicePlanModelDbo.class);
        SetJoin<Object, Object> versions = model.joinSet("versions");
        query.where(
                cb.and(
                        cb.equal(versions.get("tecid"), position.get("versionId")),
                        cb.or(
                                cb.lessThan(prestation.get("startDateTime"), versions.get("startDate")),
                                cb.greaterThan(prestation.get("startDateTime"), versions.get("endDate"))
                        ),
                        additionnalSpecifications == null ? cb.conjunction() : additionnalSpecifications.toPredicate(prestation, query, cb)
                )
        );
        query.select(prestation);
        return entityManager.createQuery(query).getResultList();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<PrestationDbo> findOutOfBondsPrestationsForPlan(ICGDISSecurityConstraint securityConstraint, long planId, Specification<PrestationDbo> additionnalSpecifications) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<PrestationDbo> query = cb.createQuery(PrestationDbo.class);
        Root<PrestationDbo> prestation = query.from(PrestationDbo.class);
        Root<ServicePlanDbo> plan = query.from(ServicePlanDbo.class);
        SetJoin<Object, Object> versions = plan.joinSet("versions");
        SetJoin<Object, Object> timeslots = versions.joinSet("servicePlanTimeSlots");


        query.where(
                cb.and(
                        cb.equal(timeslots.get("tecid"), prestation.get("timeSlotId")),
                        cb.or(
                                cb.lessThan(prestation.get("startDateTime"), versions.get("startDate")),
                                cb.greaterThan(prestation.get("startDateTime"), versions.get("endDate"))
                        ),
                        additionnalSpecifications == null ? cb.conjunction() : additionnalSpecifications.toPredicate(prestation, query, cb)
                )
        );

        query.select(prestation);
        return entityManager.createQuery(query).getResultList();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Set<LocalDateTime> findDaysWithAvailability(ICGDISSecurityConstraint securityConstraint, long personId, LocalDate start, LocalDate end) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<Tuple> query = cb.createTupleQuery();
        Root<PrestationDbo> from = query.from(PrestationDbo.class);
        query.where(
                cb.equal(from.get(PrestationDbo.Fields.PERSON).get(BaseDbo.Fields.TECID), personId),
                cb.and(
                        cb.greaterThanOrEqualTo(from.get(PrestationDbo.Fields.END_DATE_TIME), start.atStartOfDay())
                        , cb.lessThanOrEqualTo(from.get(PrestationDbo.Fields.START_DATE_TIME), end.atTime(LocalTime.MAX))
                )
        );
        query.multiselect(from.get(PrestationDbo.Fields.START_DATE_TIME).alias("startDateTime"), from.get(PrestationDbo.Fields.END_DATE_TIME).alias("endDateTime"));
        List<Tuple> result = entityManager.createQuery(query).getResultList();

        // adding of days between two dates
        List<LocalDateTime> dateTimes = new ArrayList<>();
        for (Tuple tuple : result) {
            long numOfDaysBetween = ChronoUnit.DAYS.between(((LocalDateTime) tuple.get("startDateTime")).toLocalDate(), ((LocalDateTime) tuple.get("endDateTime")).toLocalDate());
            List<LocalDateTime> localDateTimes = IntStream.iterate(0, i -> i + 1)
                    .limit(numOfDaysBetween)
                    .mapToObj(i -> ((LocalDateTime) tuple.get("startDateTime")).plusDays(i))
                    .collect(Collectors.toList());
            dateTimes.addAll(localDateTimes);
            dateTimes.add(((LocalDateTime) tuple.get("startDateTime")).toLocalDate().atTime(LocalTime.MIN));
            if (((LocalDateTime) tuple.get("endDateTime")).toLocalTime().equals(LocalTime.MIN)) {
                dateTimes.add(((LocalDateTime) tuple.get("endDateTime")).minus(1, MILLIS));
            } else {
                dateTimes.add((LocalDateTime) tuple.get("endDateTime"));
            }
        }

        return new HashSet<>(dateTimes);
    }

    @Override
    @Transactional
    @LogMilestone(action = "link prestations from time slot to time slot between 2 dates inclusives")
    public void linkPrestationsToTimeSlot(Long previousTimeSlotTecid, Long newTimeSlotTecid, LocalDateTime startDate, LocalDateTime endDate) {
        LOGGER.debug("link prestations from time slot {} to time slot {} between {} and {} ", previousTimeSlotTecid, newTimeSlotTecid, startDate, endDate);
        CriteriaBuilder cb = getEntityManager().getCriteriaBuilder();
        CriteriaUpdate<PrestationDbo> query = cb.createCriteriaUpdate(PrestationDbo.class);
        Root<PrestationDbo> root = query.from(PrestationDbo.class);

        query.set(root.get("timeSlotId"), newTimeSlotTecid);

        Predicate predicates = cb.and(
                cb.equal(root.get("timeSlotId"), previousTimeSlotTecid),
                cb.greaterThanOrEqualTo(root.get("startDateTime"), startDate),
                cb.greaterThanOrEqualTo(root.get(PrestationDbo.Fields.PRESTATION_START_DATE), startDate.minusDays(1).toLocalDate())
        );

        if (endDate != null) {
            cb.lessThanOrEqualTo(root.get("endDateTime"), endDate);
            cb.lessThanOrEqualTo(root.get(PrestationDbo.Fields.PRESTATION_START_DATE), endDate.plusDays(1).toLocalDate());
        }

        query.where(predicates);

        int updated = getEntityManager().createQuery(query).executeUpdate();
        LOGGER.debug("{} prestations linked  to time slot {} from time slot {} between {} and {} ", updated, newTimeSlotTecid, previousTimeSlotTecid, startDate, endDate);
    }

    @Override
    public Map<Long, Long> checkMultiplePrestations(Collection<Long> personIds, LocalDateTime startDateTime, LocalDateTime endDateTime) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<Tuple> query = cb.createTupleQuery();
        Root<PrestationDbo> from = query.from(PrestationDbo.class);
        query.where(
                cb.and(
                        from.get("person").get("tecid").in(personIds),
                        new PrestationPartiallyBetweenDateSpecification(startDateTime, endDateTime).toPredicate(from, query, cb)
                )
        );


        query.multiselect(from.get("person").get("tecid").alias("personTecid"), cb.countDistinct(from.get("tecid")).alias("number"));
        query.groupBy(from.get("person").get("tecid"));
        List<Tuple> result = entityManager.createQuery(query).getResultList();

        Map<Long, Long> map = new HashMap<>();
        if (result != null) {
            result.forEach(t -> map.put(t.get("personTecid", Long.class), t.get("number", Long.class)));
        }

        return map;

    }


    @Override
    public  List<PrestationForAllowance> findForAllowance(Specification<PrestationDbo> specifications) {
        CriteriaBuilder criteriaBuilder = getEntityManager().getCriteriaBuilder();
        CriteriaQuery<Tuple> criteriaQuery = criteriaBuilder.createQuery(Tuple.class);
        Root<PrestationDbo> root = criteriaQuery.from(PrestationDbo.class);

        // Apply specifications
        applyWhere(root,criteriaQuery,criteriaBuilder,specifications);

        // Apply select
        From<PrestationDbo, ServicePlanTimeSlotDbo> slotJoin = SpecificationJointUtils.getJoin(root,PrestationDbo.Fields.TIME_SLOT, JoinType.INNER);
        From< ServicePlanTimeSlotDbo, ServicePlanVersionDbo> versionJoin = SpecificationJointUtils.getJoin(slotJoin,ServicePlanTimeSlotDbo.Fields.VERSION, JoinType.INNER);
        From< ServicePlanVersionDbo, ServicePlanDbo> servicePlanJoin = SpecificationJointUtils.getJoin(versionJoin,ServicePlanVersionDbo.Fields.SERVICE_PLAN, JoinType.INNER);
        From< ServicePlanDbo, CGDISEntityDBO> entityJoin = SpecificationJointUtils.getJoin(servicePlanJoin,ServicePlanDbo.Fields.ENTITY, JoinType.INNER);
        From<  ServicePlanVersionDbo, ServicePlanTypeDbo> servicePlanTypeJoin = SpecificationJointUtils.getJoin(versionJoin,ServicePlanVersionDbo.Fields.SERVICE_PLAN_TYPE, JoinType.INNER);

        // Column to select
        List<Selection<?>> select = new ArrayList<>();
        select.add(root.get(BaseDbo.Fields.TECID).as(Long.class));
        select.add(root.get(BaseDbo.Fields.TECLOCK).as(Long.class));
        select.add(root.get(PrestationDbo.Fields.PERSON_ID).as(Long.class));
        select.add(root.get(PrestationDbo.Fields.PRESTATION_START_DATE).as(LocalDate.class));
        select.add(root.get(PrestationDbo.Fields.START_DATE_TIME).as(LocalDateTime.class));
        select.add(root.get(PrestationDbo.Fields.END_DATE_TIME).as(LocalDateTime.class));
        select.add(root.get(PrestationDbo.Fields.IS_PROFESSIONAL).as(Boolean.class));
        select.add(servicePlanTypeJoin.get(ServicePlanTypeDbo.Fields.IS_BARRACKED).as(Boolean.class).alias("isBarracked"));
        select.add(servicePlanJoin.get(ServicePlanDbo.Fields.ENTITY_TECID).as(Long.class).alias("entityTecid"));
        select.add(servicePlanJoin.get(BaseDbo.Fields.TECID).as(Long.class).alias("servicePlanTecid"));
        select.add(entityJoin.get(CGDISEntityDBO.Fields.MAIN_ENTITY_TECID).as(Long.class).alias("mainEntityTecid"));

        criteriaQuery.multiselect(select);

        TypedQuery<Tuple> query = getEntityManager().createQuery(criteriaQuery);

        return query.getResultList().stream()
                .map(tuple-> PrestationForAllowance.builder()
                        .tecid(tuple.get(0,Long.class))
                        .teclock(tuple.get(1,Long.class))
                        .personTecid(tuple.get(2,Long.class))
                        .prestationStartDate(tuple.get(3,LocalDate.class))
                        .startDateTime(tuple.get(4,LocalDateTime.class))
                        .endDateTime(tuple.get(5,LocalDateTime.class))
                        .isProfessional(tuple.get(6, Boolean.class))
                        .barracked(tuple.get(7, Boolean.class))
                        .entityTecid(tuple.get(8,Long.class))
                        .servicePlanTecid(tuple.get(9,Long.class))
                        .mainEntityTecid(tuple.get(10,Long.class))


                        .build()).collect(Collectors.toList());

    }
}
