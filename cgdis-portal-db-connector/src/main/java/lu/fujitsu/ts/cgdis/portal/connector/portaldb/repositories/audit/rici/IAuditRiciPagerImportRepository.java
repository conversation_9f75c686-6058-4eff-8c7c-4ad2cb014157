package lu.fujitsu.ts.cgdis.portal.connector.portaldb.repositories.audit.rici;

import lu.fujitsu.ts.cgdis.portal.connector.portaldb.dbos.audit.AuditRiciPagerImportDbo;
import lu.fujitsu.ts.cgdis.portal.connector.portaldb.repositories.IBaseCGDISRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface IAuditRiciPagerImportRepository extends IBaseCGDISRepository<AuditRiciPagerImportDbo>, IAuditRiciPagerImportRepositoryCustom {
}
