package lu.fujitsu.ts.cgdis.portal.connector.portaldb.services.audit.rici;

import lu.fujitsu.ts.cgdis.portal.connector.portaldb.dbos.audit.AuditRiciSimCardDbo;
import lu.fujitsu.ts.cgdis.portal.connector.portaldb.repositories.audit.rici.IAuditRiciSimCardRepository;
import lu.fujitsu.ts.cgdis.portal.connector.portaldb.services.audit.AbstractAuditRepositoryService;
import lu.fujitsu.ts.cgdis.portal.core.domain.audit.AuditRiciSimCard;
import lu.fujitsu.ts.cgdis.portal.core.domain.audit.AuditType;
import lu.fujitsu.ts.cgdis.portal.core.logging.LoggingCategory;
import lu.fujitsu.ts.cgdis.portal.core.logging.LoggingDomain;
import lu.fujitsu.ts.cgdis.portal.core.security.IAuditSecurityConstraint;
import lu.fujitsu.ts.eportal.server.connector.jpa.specifications.secured.AbstractComposedSecuredSpecificationBuilder;
import lu.fujitsu.ts.eportal.server.logging.milestones.LogMilestone;
import org.dozer.Mapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
@LogMilestone(category = LoggingCategory.REPOSITORY_SERVICE, domain = LoggingDomain.AUDIT)
public class AuditRiciSimCardRepositoryService extends AbstractAuditRepositoryService<AuditRiciSimCard, AuditRiciSimCardDbo, IAuditRiciSimCardRepository> {

    @Autowired
    public AuditRiciSimCardRepositoryService(IAuditRiciSimCardRepository repository, AbstractComposedSecuredSpecificationBuilder<IAuditSecurityConstraint, AuditRiciSimCardDbo> specBuilder, Mapper mapper) {
        super(repository, specBuilder, AuditRiciSimCardDbo.class, AuditRiciSimCard.class, mapper);
    }

    @Override
    protected void additionalSortBuilder(Map<String, String> sorts) {
        // Fields from AuditRiciSimCardDbo
        sorts.put("simCardTecid", AuditRiciSimCardDbo.Fields.SIM_CARD_TECID);
        sorts.put("associatedPagerTecid", AuditRiciSimCardDbo.Fields.ASSOCIATED_PAGER_ID);
        sorts.put("iccid", AuditRiciSimCardDbo.Fields.ICCID);
        sorts.put("msisdn", AuditRiciSimCardDbo.Fields.MSISDN);
        // PIN is usually not sorted by, but can be added if needed
        // sorts.put("pin", AuditRiciSimCardDbo.Fields.pin);
        sorts.put("status", AuditRiciSimCardDbo.Fields.STATUS);
        sorts.put("associatedPagerId", AuditRiciSimCardDbo.Fields.ASSOCIATED_PAGER_ID);

        // It's good practice to also support the prefixed versions if the frontend might send them
        sorts.put("riciSimCardTecid", AuditRiciSimCardDbo.Fields.SIM_CARD_TECID);
        sorts.put("riciAssociatedPagerTecid", AuditRiciSimCardDbo.Fields.ASSOCIATED_PAGER_TECID);
        sorts.put("riciSimCardIccid", AuditRiciSimCardDbo.Fields.ICCID);
        sorts.put("riciSimCardMsisdn", AuditRiciSimCardDbo.Fields.MSISDN);
        sorts.put("riciSimCardStatus", AuditRiciSimCardDbo.Fields.STATUS);
        // No prefix for associatedPagerId as it's already specific enough
    }

    @Override
    public AuditType getAuditType() {
        return AuditType.RICI_SIM_CARD;
    }
}
