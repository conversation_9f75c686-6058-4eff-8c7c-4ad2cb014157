package lu.fujitsu.ts.cgdis.portal.connector.portaldb.specifications.impl.serviceplanmodel;

import lu.fujitsu.ts.cgdis.portal.connector.portaldb.dbos.BaseDbo;
import lu.fujitsu.ts.cgdis.portal.connector.portaldb.dbos.serviceplan.ServicePlanModelDbo;
import lu.fujitsu.ts.cgdis.portal.connector.portaldb.specifications.SpecificationWithPath;

import javax.persistence.criteria.AbstractQuery;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Path;
import javax.persistence.criteria.Predicate;
import java.util.Collection;
import java.util.HashSet;
import java.util.Set;

/**
 * ServicePlanModelIdSpecification is a class that implements the SpecificationWithPath interface
 * for filtering ServicePlanModelDbo entities based on their TECID values.
 *
 * @param <D> a type that extends ServicePlanModelDbo
 */
public class ServicePlanModelIdSpecification<D extends ServicePlanModelDbo> implements SpecificationWithPath<D> {

    /**
     * A list of TECID values used for filtering.
     */
    private Set<Long> tecids;

    /**
     * Constructs a new ServicePlanModelIdSpecification with a list of TECID values.
     *
     * @param tecids a list of TECID values
     */
    public ServicePlanModelIdSpecification(Collection<Long> tecids) {
        this.tecids = new HashSet<>(tecids);
    }

    /**
     * Constructs a new ServicePlanModelIdSpecification with a single TECID value.
     *
     * @param tecid a single TECID value
     */
    public ServicePlanModelIdSpecification(Long tecid) {
        this.tecids = new HashSet<>(1);
        this.tecids.add(tecid);
    }

    /**
     * Generates a Predicate for filtering ServicePlanModelDbo entities based on their TECID values.
     *
     * @param path  the path to the ServicePlanModelDbo entity
     * @param query the query being built
     * @param cb    the CriteriaBuilder used to build the query
     * @return a Predicate for filtering ServicePlanModelDbo entities based on their TECID values
     */
    @Override
    public Predicate toPredicateFromPath(Path<D> path, AbstractQuery<?> query, CriteriaBuilder cb) {
        Path<Long> tecidPath = path.get(BaseDbo.Fields.TECID);
        return tecidPath.in(tecids);
    }
}
