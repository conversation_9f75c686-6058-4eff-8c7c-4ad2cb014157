package lu.fujitsu.ts.cgdis.portal.connector.portaldb.repositories.audit.rici;

import lu.fujitsu.ts.cgdis.portal.connector.portaldb.dbos.audit.AuditRiciSimCardDbo;
import lu.fujitsu.ts.cgdis.portal.connector.portaldb.repositories.AbstractRepositoryImpl;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;

@Repository
public class IAuditRiciSimCardRepositoryImpl extends AbstractRepositoryImpl<AuditRiciSimCardDbo>  implements IAuditRiciSimCardRepositoryCustom {

    @PersistenceContext
    private EntityManager entityManager;
    /**
     * Constructor that sets the entity class.
     */
    public IAuditRiciSimCardRepositoryImpl() {
        super(AuditRiciSimCardDbo.class);
    }

    /**
     * Returns the EntityManager instance.
     *
     * @return the EntityManager instance
     */
    @Override
    protected EntityManager getEntityManager() {
        return entityManager;
    }


    @Override
    public void refresh(Object entity) {
        entityManager.refresh(entity);
    }
}
