package lu.fujitsu.ts.cgdis.portal.connector.portaldb.repositories.audit.permamonitor;

import lu.fujitsu.ts.cgdis.portal.connector.portaldb.dbos.audit.AuditPermConfigDpceDbo;
import lu.fujitsu.ts.cgdis.portal.connector.portaldb.repositories.IBaseCGDISRepository;
import org.springframework.stereotype.Repository;

/**
 * Repository interface for managing `AuditPermConfigDpceDbo` entities.
 * Extends `IBaseCGDISRepository` and `IAuditPermConfigDpceRepositoryCustom`.
 */
@Repository
public interface IAuditPermConfigDpceRepository extends IBaseCGDISRepository<AuditPermConfigDpceDbo>, IAuditPermConfigDpceRepositoryCustom {

}
