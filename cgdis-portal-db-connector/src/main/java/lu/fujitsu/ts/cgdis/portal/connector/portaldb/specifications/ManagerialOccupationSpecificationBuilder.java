package lu.fujitsu.ts.cgdis.portal.connector.portaldb.specifications;

import lu.fujitsu.ts.cgdis.portal.connector.portaldb.dbos.person.ManagerialOccupationDbo;
import lu.fujitsu.ts.cgdis.portal.core.domain.ICGDISPortalUserDetails;
import lu.fujitsu.ts.eportal.server.connector.jpa.specifications.secured.AbstractSecuredSpecificationBuilder;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
public class ManagerialOccupationSpecificationBuilder extends AbstractSecuredSpecificationBuilder<ICGDISPortalUserDetails, ManagerialOccupationDbo> {

    @Override
    public Specification<ManagerialOccupationDbo> buildSecurityConstraint(ICGDISPortalUserDetails icgdisPortalUserDetails) {
        return (root, criteriaQuery, criteriaBuilder) -> criteriaBuilder.conjunction();
    }

}
