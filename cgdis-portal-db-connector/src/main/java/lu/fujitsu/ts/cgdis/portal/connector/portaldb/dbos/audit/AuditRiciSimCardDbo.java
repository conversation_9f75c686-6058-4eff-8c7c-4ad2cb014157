package lu.fujitsu.ts.cgdis.portal.connector.portaldb.dbos.audit;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.FieldNameConstants;

import javax.persistence.*;

/**
 * Represents an audit record for a RICI SIM card.
 * Extends the base AuditDbo class.
 */
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Data
@Entity
@SecondaryTable(name = AuditRiciSimCardDbo.TABLE_AUDIT_RICI_SIM_CARD)
@DiscriminatorValue(AuditRiciSimCardDbo.TYPE_RICI_SIM_CARD)
@FieldNameConstants
public class AuditRiciSimCardDbo extends AuditDbo {

    /**
     * The name of the secondary table for audit RiciSimCard.
     */
    public static final String TABLE_AUDIT_RICI_SIM_CARD = "AUDIT_RICI_SIM_CARD";

    /**
     * The column name for the SIM card TECID.
     */
    public static final String SIM_CARD_TECID_COLUMN = "SIM_CARD_TECID";

    /**
     * The column name for the associated Pager TECID.
     */
    public static final String ACCOCIATED_PAGER_TECID_COLUMN = "ACCOCIATED_PAGER_TECID";

    /**
     * The column name for the ICCID.
     */
    public static final String ICCID_COLUMN = "ICCID";

    /**
     * The column name for the MSISDN.
     */
    public static final String MSISDN_COLUMN = "MSISDN";

    /**
     * The column name for the PIN.
     */
    public static final String PIN_COLUMN = "PIN";

    /**
     * The column name for the status.
     */
    public static final String STATUS_COLUMN = "STATUS";

    /**
     * The column name for the associated Pager ID (string identifier).
     */
    public static final String ASSOCIATED_PAGER_ID_COLUMN = "ASSOCIATED_PAGER_ID";

    /**
     * The discriminator value for RICI SIM card audit records.
     */
    public static final String TYPE_RICI_SIM_CARD = "RICI_SIM_CARD";

    /**
     * The TECID of the SIM card.
     */
    @Column(table = TABLE_AUDIT_RICI_SIM_CARD, name = SIM_CARD_TECID_COLUMN)
    @ToString.Include
    private Long simCardTecid;

    /**
     * The TECID of the SIM card.
     */
    @Column(table = TABLE_AUDIT_RICI_SIM_CARD, name = ACCOCIATED_PAGER_TECID_COLUMN)
    @ToString.Include
    private Long associatedPagerTecid;

    /**
     * The ICCID of the SIM card.
     */
    @Column(table = TABLE_AUDIT_RICI_SIM_CARD, name = ICCID_COLUMN)
    private String iccid;

    /**
     * The MSISDN of the SIM card.
     */
    @Column(table = TABLE_AUDIT_RICI_SIM_CARD, name = MSISDN_COLUMN)
    private String msisdn;

    /**
     * The PIN of the SIM card.
     */
    @Column(table = TABLE_AUDIT_RICI_SIM_CARD, name = PIN_COLUMN)
    private String pin;

    /**
     * The status of the SIM card.
     */
    @Column(table = TABLE_AUDIT_RICI_SIM_CARD, name = STATUS_COLUMN)
    private String status;

    /**
     * The string identifier (pagerId) of the associated pager.
     */
    @Column(table = TABLE_AUDIT_RICI_SIM_CARD, name = ASSOCIATED_PAGER_ID_COLUMN)
    private String associatedPagerId;
}
