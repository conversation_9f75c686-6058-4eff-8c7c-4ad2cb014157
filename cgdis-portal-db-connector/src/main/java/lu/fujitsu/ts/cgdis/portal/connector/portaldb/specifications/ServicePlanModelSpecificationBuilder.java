package lu.fujitsu.ts.cgdis.portal.connector.portaldb.specifications;

import lu.fujitsu.ts.cgdis.portal.connector.portaldb.dbos.serviceplan.ServicePlanModelDbo;
import lu.fujitsu.ts.cgdis.portal.core.security.ICGDISSecurityConstraint;
import lu.fujitsu.ts.eportal.server.connector.jpa.specifications.SpecificationUtils;
import lu.fujitsu.ts.eportal.server.connector.jpa.specifications.secured.AbstractComposedSecuredSpecificationBuilder;
import lu.fujitsu.ts.eportal.server.core.domain.SearchCriterion;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

/**
 * The type Service plan model specification builder.
 */
@Service
public class ServicePlanModelSpecificationBuilder extends AbstractComposedSecuredSpecificationBuilder<ICGDISSecurityConstraint, ServicePlanModelDbo> {

    public ServicePlanModelSpecificationBuilder() {
        super();
        addBuilder("entityType", this::entityTypeSpecificationBuilder);
        addBuilder("interventionTypeId", this::interventionTypeSpecificationBuilder);
        addBuilder("startDate", this::startDateSpecificationBuilder);
        addBuilder("endDate", this::endDateSpecificationBuilder);
    }

    @Override
    public Specification<ServicePlanModelDbo> buildSecurityConstraint(ICGDISSecurityConstraint securityConstraint) {
        return (root, criteriaQuery, criteriaBuilder) -> CollectionUtils.isEmpty(securityConstraint.getAccessibleEntities()) ? criteriaBuilder.disjunction() : root.get("entity").get("name").in(securityConstraint.getAccessibleEntities());
    }

    private Specification<ServicePlanModelDbo> entityTypeSpecificationBuilder(SearchCriterion criterion) {
        return (root, criteriaQuery, criteriaBuilder) -> SpecificationUtils.buildPredicate(root.join("entity").get("type"), criterion, criteriaBuilder);
    }

    private Specification<ServicePlanModelDbo> interventionTypeSpecificationBuilder(SearchCriterion criterion) {
        return (root, criteriaQuery, criteriaBuilder) -> SpecificationUtils.buildPredicate(root.join("interventionType").get("tecid"), criterion, criteriaBuilder);
    }

    private Specification<ServicePlanModelDbo> startDateSpecificationBuilder(SearchCriterion criterion) {
        return (root, criteriaQuery, criteriaBuilder) -> SpecificationUtils.buildPredicate(root.join("versions").get("startDate"), criterion, criteriaBuilder);
    }

    private Specification<ServicePlanModelDbo> endDateSpecificationBuilder(SearchCriterion criterion) {
        return (root, criteriaQuery, criteriaBuilder) -> SpecificationUtils.buildPredicate(root.join("versions").get("endDate"), criterion, criteriaBuilder);
    }

}
