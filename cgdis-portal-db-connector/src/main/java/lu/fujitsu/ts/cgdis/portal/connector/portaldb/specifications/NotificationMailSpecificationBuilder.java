package lu.fujitsu.ts.cgdis.portal.connector.portaldb.specifications;

import lu.fujitsu.ts.cgdis.portal.connector.portaldb.dbos.notification.NotificationMailDBO;
import lu.fujitsu.ts.cgdis.portal.core.domain.ICGDISPortalUserDetails;
import lu.fujitsu.ts.eportal.server.connector.jpa.specifications.secured.AbstractSecuredSpecificationBuilder;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

/**
 * The type Notification mail specification builder.
 */
@Service
public class NotificationMailSpecificationBuilder extends AbstractSecuredSpecificationBuilder<ICGDISPortalUserDetails, NotificationMailDBO> {
    @Override
    public Specification<NotificationMailDBO> buildSecurityConstraint(ICGDISPortalUserDetails icgdisPortalUserDetails) {
        return (root, criteriaQuery, criteriaBuilder) -> criteriaBuilder.conjunction();
    }

}
