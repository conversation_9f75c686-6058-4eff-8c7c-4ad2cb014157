package lu.fujitsu.ts.cgdis.portal.connector.portaldb.dbos.person;


import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lu.fujitsu.ts.cgdis.portal.connector.portaldb.dbos.BaseDbo;

import javax.persistence.*;
import java.time.LocalDate;
import java.util.Objects;

@Data
@Entity
@Table(name = "OPERATIONAL_VOLUNTEER_INTERNSHIP")
@EqualsAndHashCode(exclude = "person", callSuper = true)
@ToString(exclude = {"person"}, callSuper = true)
public class OperationalVolunteerInternshipDbo extends BaseDbo {

    @Column(name = "START_DATE")
    private LocalDate startDate;

    @Column(name = "END_DATE")
    private LocalDate endDate;

    @Column(name = "DURATION")
    private Long duration;

    @JoinColumn(name = "PERSON_TECID")
    @OneToOne
    private PersonDbo person;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        if (!super.equals(o)) return false;
        OperationalVolunteerInternshipDbo that = (OperationalVolunteerInternshipDbo) o;
        return Objects.equals(startDate, that.startDate) &&
                Objects.equals(endDate, that.endDate) &&
                Objects.equals(duration, that.duration) &&
                Objects.equals(person, that.person);
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), startDate, endDate, duration, person);
    }
}
