package lu.fujitsu.ts.cgdis.portal.connector.portaldb.dbos.export;


import lombok.Data;
import lombok.EqualsAndHashCode;
import lu.fujitsu.ts.cgdis.portal.connector.portaldb.dbos.BaseDbo;
import lu.fujitsu.ts.cgdis.portal.connector.portaldb.dbos.CGDISEntityDBO;
import lu.fujitsu.ts.cgdis.portal.core.domain.export.ExportProfilType;
import lu.fujitsu.ts.cgdis.portal.core.domain.volunteer.AvailabilityType;

import javax.persistence.*;
import java.time.LocalDateTime;
import java.util.Set;

/**
 * The type Export profile dbo.
 */
@Data
@Entity
@Table(name = "EXPORT_PROFILE")
@EqualsAndHashCode(callSuper = true)
public class ExportProfileDbo extends BaseDbo {

    /**
     * The Name.
     */
    @Column(name = "NAME")
    private String name;

    /**
     * The Entity.
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "ENTITY_ID")
    private CGDISEntityDBO entity;

    /**
     * The Cron.
     */
    @Column(name = "CRON")
    private String cron;

    /**
     * The Last execution.
     */
    @Column(name = "LAST_EXECUTION")
    private LocalDateTime lastExecution;

    /**
     * The Next execution.
     */
    @Column(name = "NEXT_EXECUTION")
    private LocalDateTime nextExecution;

    /**
     * The Export vehicles.
     */
    @Column(name = "EXPORT_VEHICLES")
    private boolean exportVehicles;

    /**
     * The Export persons.
     */
    @Column(name = "EXPORT_PERSONS")
    private boolean exportPersons;

    /**
     * The Export persons.
     */
    @Column(name = "EXPORT_SERVICE_PLANS")
    private boolean exportServicePlans;

    /**
     * The Export on server.
     */
    @Column(name = "EXPORT_ON_SERVER")
    private boolean exportOnServer;

    /**
     * The number day to export for service plan
     */
    @Column(name = "EXPORT_NUMBER_DAYS")
    private Integer exportNumberDays;

    @Column(name = "EXPORT_FORMAT")
    @Enumerated(EnumType.STRING)
    private ExportProfilType exportType;

    /**
     * The Emails.
     */
    @OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "EXPORT_PROFILE_TECID", referencedColumnName = "TECID")
    private Set<ExportProfileEmailDbo> emails;

}
