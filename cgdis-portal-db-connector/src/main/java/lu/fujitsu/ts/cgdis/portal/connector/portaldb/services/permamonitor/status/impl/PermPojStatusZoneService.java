package lu.fujitsu.ts.cgdis.portal.connector.portaldb.services.permamonitor.status.impl;

import lu.fujitsu.ts.cgdis.portal.connector.portaldb.repositories.ICGDISEntityRepository;
import lu.fujitsu.ts.cgdis.portal.connector.portaldb.services.permamonitor.status.PermPojStatusGroupServiceFactory;
import lu.fujitsu.ts.cgdis.portal.core.logging.LoggingCategory;
import lu.fujitsu.ts.cgdis.portal.core.logging.LoggingDomain;
import lu.fujitsu.ts.cgdis.portal.core.pojo.permamonitor.config.PermConfigDpceCriticityDboInfo;
import lu.fujitsu.ts.cgdis.portal.core.pojo.permamonitor.schedule.row.PermSchedule;
import lu.fujitsu.ts.cgdis.portal.core.pojo.permamonitor.status.PermPojStatusResult;
import lu.fujitsu.ts.cgdis.portal.services.permamonitor.config.IPermConfigDpceCriticityRORepositoryService;
import lu.fujitsu.ts.cgdis.portal.services.permamonitor.schedule.IPermDefaultScheduleRepositoryService;
import lu.fujitsu.ts.eportal.server.logging.milestones.LogMilestone;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

@Service("permPojStatusZoneService")
@LogMilestone(category = LoggingCategory.REPOSITORY_SERVICE, domain = LoggingDomain.PERMAMONITOR)
@Transactional(readOnly = true)
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class PermPojStatusZoneService extends AbstractPermPojStatusWithChildrenService {

    private static final Logger LOGGER = LoggerFactory.getLogger(PermPojStatusZoneService.class);

    private final PermPojStatusGroupServiceFactory permPojStatusGroupServiceFactory;

    public PermPojStatusZoneService(ICGDISEntityRepository entityRepository,
                                    IPermDefaultScheduleRepositoryService permDefaultScheduleRepositoryService,
                                    IPermConfigDpceCriticityRORepositoryService permConfigDpceCriticityRORepositoryService,
                                    PermPojStatusGroupServiceFactory permPojStatusGroupServiceFactory) {
        super(entityRepository, permDefaultScheduleRepositoryService, permConfigDpceCriticityRORepositoryService);
        this.permPojStatusGroupServiceFactory = permPojStatusGroupServiceFactory;
    }


    @Override
    protected List<Long> getAllChildrenTecids(Long zoneTecid) {
        List<Long> directChildrenTecids = entityRepository.getDirectChildrenTecids(zoneTecid);
        directChildrenTecids.add(zoneTecid);
        return directChildrenTecids;
    }

    @Override
    protected Map<Long, PermPojStatusResult> getStatusForChildrenEntities(PermSchedule schedule, Map<Long, PermConfigDpceCriticityDboInfo> criticityConfigForEntity, List<Long> allChildrenTecids) {
        return permPojStatusGroupServiceFactory.getService(getSecurityConstraint(), getAtDatetime(), getCategoryName()).getStatusForMainEntities(allChildrenTecids);
    }
}
