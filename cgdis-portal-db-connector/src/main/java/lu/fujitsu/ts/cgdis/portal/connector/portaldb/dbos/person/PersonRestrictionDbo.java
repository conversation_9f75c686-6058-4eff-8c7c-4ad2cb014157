package lu.fujitsu.ts.cgdis.portal.connector.portaldb.dbos.person;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lu.fujitsu.ts.cgdis.portal.connector.portaldb.dbos.BaseDbo;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import java.time.LocalDate;

/**
 * The type Person restriction dbo.
 */
@Data
@Entity
@Table(name = "PERSON_RESTRICTION")
@EqualsAndHashCode(callSuper = true,exclude = {
        "restriction",
        "person"
})
@ToString(callSuper = true,exclude = {
        "restriction",
        "person"
})
public class PersonRestrictionDbo extends BaseDbo {

    /**
     * The Start date.
     */
    @Column(name = "START_DATE")
    private LocalDate startDate;

    /**
     * The User comment.
     */
    @Column(name = "USER_COMMENT")
    private String userComment;

    /**
     * The External id.
     */
    @Column(name = "EXTERNAL_ID")
    private Long externalId;

    /**
     * The Restriction.
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "RESTRICTION_TECID")
    private RestrictionDbo restriction;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "PERSON_TECID")
    private PersonDbo person;




}
