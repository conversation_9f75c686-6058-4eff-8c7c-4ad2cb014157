package lu.fujitsu.ts.cgdis.portal.connector.portaldb.specifications.audit;

import lu.fujitsu.ts.cgdis.portal.connector.portaldb.dbos.audit.AuditDbo;
import lu.fujitsu.ts.cgdis.portal.connector.portaldb.dbos.audit.AuditRiciSimCardDbo;
import lu.fujitsu.ts.cgdis.portal.core.security.IAuditSecurityConstraint;
import lu.fujitsu.ts.eportal.server.connector.jpa.specifications.SpecificationUtils;
import lu.fujitsu.ts.eportal.server.connector.jpa.specifications.secured.AbstractComposedSecuredSpecificationBuilder;
import lu.fujitsu.ts.eportal.server.core.domain.SearchCriterion;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

/**
 * Specification builder for {@link AuditRiciSimCardDbo} entities.
 * This builder is used to create JPA Specifications for filtering audit records related to RICI SIM cards.
 */
@Service
public class AuditRiciSimCardSpecificationBuilder extends AbstractComposedSecuredSpecificationBuilder<IAuditSecurityConstraint, AuditRiciSimCardDbo> {

    public AuditRiciSimCardSpecificationBuilder() {
        super();
        // Filters based on fields in AuditDbo (superclass)
        addBuilder("actionDate", this::actionDateBuilder); // Corresponds to actionDatetime
        addBuilder("actionType", this::actionTypeBuilder);
        addBuilder("personTecid.cgdisRegistrationNumber", this::personRegistrationNumberBuilder);

        // Filters based on fields in AuditRiciSimCardDbo
        addBuilder("riciSimCardIccid", this::iccidBuilder);
        addBuilder("riciSimCardMsisdn", this::msisdnBuilder);
        addBuilder("riciSimCardStatus", this::statusBuilder);
        addBuilder("associatedPagerId", this::associatedPagerIdBuilder);
        addBuilder("simCardTecid", this::simCardTecidBuilder); // If direct filtering by simCardTecid is needed
        addBuilder("associatedPagerTecid", this::associatedPagerTecidBuilder); // If direct filtering by associatedPagerTecid is needed
    }

    @Override
    public Specification<AuditRiciSimCardDbo> buildSecurityConstraint(IAuditSecurityConstraint securityConstraint) {
        // Implement security constraints if necessary, otherwise return a conjunction (no additional restrictions).
        return (root, criteriaQuery, criteriaBuilder) -> criteriaBuilder.conjunction();
    }

    // Builder methods for fields from AuditDbo
    private Specification<AuditRiciSimCardDbo> actionDateBuilder(SearchCriterion criterion) {
        return (root, query, criteriaBuilder) ->
                SpecificationUtils.buildPredicate(root.get(AuditDbo.Fields.ACTION_DATE), criterion, criteriaBuilder);
    }

    private Specification<AuditRiciSimCardDbo> actionTypeBuilder(SearchCriterion criterion) {
        return (root, query, criteriaBuilder) ->
                SpecificationUtils.buildPredicate(root.get(AuditDbo.Fields.ACTION_TYPE), criterion, criteriaBuilder);
    }

    private Specification<AuditRiciSimCardDbo> personRegistrationNumberBuilder(SearchCriterion criterion) {
        // Assumes PersonLightDbo has a field "cgdisRegistrationNumber"
        return (root, query, criteriaBuilder) ->
                SpecificationUtils.buildPredicate(root.get(AuditDbo.Fields.PERSON_TECID).get("cgdisRegistrationNumber"), criterion, criteriaBuilder);
    }

    // Builder methods for fields from AuditRiciSimCardDbo
    private Specification<AuditRiciSimCardDbo> iccidBuilder(SearchCriterion criterion) {
        return (root, query, criteriaBuilder) ->
                SpecificationUtils.buildPredicate(root.get(AuditRiciSimCardDbo.Fields.ICCID), criterion, criteriaBuilder);
    }

    private Specification<AuditRiciSimCardDbo> msisdnBuilder(SearchCriterion criterion) {
        return (root, query, criteriaBuilder) ->
                SpecificationUtils.buildPredicate(root.get(AuditRiciSimCardDbo.Fields.MSISDN), criterion, criteriaBuilder);
    }

    private Specification<AuditRiciSimCardDbo> statusBuilder(SearchCriterion criterion) {
        return (root, query, criteriaBuilder) ->
                SpecificationUtils.buildPredicate(root.get(AuditRiciSimCardDbo.Fields.STATUS), criterion, criteriaBuilder);
    }

    private Specification<AuditRiciSimCardDbo> associatedPagerIdBuilder(SearchCriterion criterion) {
        return (root, query, criteriaBuilder) ->
                SpecificationUtils.buildPredicate(root.get(AuditRiciSimCardDbo.Fields.ASSOCIATED_PAGER_ID), criterion, criteriaBuilder);
    }

    private Specification<AuditRiciSimCardDbo> simCardTecidBuilder(SearchCriterion criterion) {
        return (root, query, criteriaBuilder) ->
                SpecificationUtils.buildPredicate(root.get(AuditRiciSimCardDbo.Fields.SIM_CARD_TECID), criterion, criteriaBuilder);
    }

    private Specification<AuditRiciSimCardDbo> associatedPagerTecidBuilder(SearchCriterion criterion) {
        return (root, query, criteriaBuilder) ->
                SpecificationUtils.buildPredicate(root.get(AuditRiciSimCardDbo.Fields.ASSOCIATED_PAGER_TECID), criterion, criteriaBuilder);
    }
}
