package lu.fujitsu.ts.cgdis.portal.connector.portaldb.services.serviceplanmodel;

import lu.fujitsu.ts.cgdis.portal.connector.portaldb.dbos.serviceplan.ServicePlanModelVehicleTypeDbo;
import lu.fujitsu.ts.cgdis.portal.connector.portaldb.repositories.IServicePlanModelVehicleTypeRepository;
import lu.fujitsu.ts.cgdis.portal.core.domain.serviceplanmodel.ServicePlanModelVehicleType;
import lu.fujitsu.ts.cgdis.portal.core.logging.LoggingCategory;
import lu.fujitsu.ts.cgdis.portal.core.logging.LoggingDomain;
import lu.fujitsu.ts.cgdis.portal.core.security.ICGDISSecurityConstraint;
import lu.fujitsu.ts.cgdis.portal.services.serviceplanmodel.IServicePlanModelVehicleTypeRepositoryService;
import lu.fujitsu.ts.eportal.server.connector.jpa.services.SecuredRepositoryService;
import lu.fujitsu.ts.eportal.server.connector.jpa.specifications.secured.AbstractComposedSecuredSpecificationBuilder;
import lu.fujitsu.ts.eportal.server.logging.milestones.LogMilestone;
import org.dozer.Mapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * The service plan model vehicle type repository service
 */
@Service
@LogMilestone(category = LoggingCategory.REPOSITORY_SERVICE, domain = LoggingDomain.SERVICE_PLAN_MODEL)
public class ServicePlanModelVehicleTypeRepositoryService extends SecuredRepositoryService<ICGDISSecurityConstraint, ServicePlanModelVehicleType, ServicePlanModelVehicleTypeDbo, Long, IServicePlanModelVehicleTypeRepository> implements IServicePlanModelVehicleTypeRepositoryService {

    @Autowired
    public ServicePlanModelVehicleTypeRepositoryService(IServicePlanModelVehicleTypeRepository repository, AbstractComposedSecuredSpecificationBuilder<ICGDISSecurityConstraint, ServicePlanModelVehicleTypeDbo> specBuilder, Mapper mapper) {
        super("Service plan model vehicle type", ServicePlanModelVehicleType.class, ServicePlanModelVehicleTypeDbo.class, repository, specBuilder, mapper, null);
        this.repository = repository;
    }

    @Override
    public boolean canModify(ICGDISSecurityConstraint icgdisSecurityConstraint, Long aLong) {
        return true;
    }

}
