package lu.fujitsu.ts.cgdis.portal.connector.portaldb.repositories.permamonitor;

import lu.fujitsu.ts.cgdis.portal.connector.portaldb.dbos.permamonitor.PermCategoryDbo;
import lu.fujitsu.ts.cgdis.portal.connector.portaldb.repositories.AbstractBaseRepositoryImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;

/**
 * Implementation of the custom repository interface for PermCategoryDbo.
 */
public class IPermCategoryRepositoryImpl extends AbstractBaseRepositoryImpl<PermCategoryDbo> implements IPermCategoryRepositoryCustom {

    private static final Logger LOGGER = LoggerFactory.getLogger(IPermCategoryRepositoryImpl.class);

    @PersistenceContext
    private EntityManager entityManager;

    /**
     * Constructor that initializes the repository with the PermCategoryDbo class type.
     */
    public IPermCategoryRepositoryImpl() {
        super(PermCategoryDbo.class);
    }

    /**
     * Provides the EntityManager instance for database operations.
     *
     * @return the EntityManager instance
     */
    @Override
    protected EntityManager getEntityManager() {
        return entityManager;
    }
}
