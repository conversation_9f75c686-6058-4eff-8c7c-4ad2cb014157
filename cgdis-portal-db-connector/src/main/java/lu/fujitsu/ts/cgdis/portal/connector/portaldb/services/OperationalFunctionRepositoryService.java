package lu.fujitsu.ts.cgdis.portal.connector.portaldb.services;

import lu.fujitsu.ts.cgdis.portal.connector.portaldb.dbos.OperationalFunctionDbo;
import lu.fujitsu.ts.cgdis.portal.connector.portaldb.repositories.IOperationalFunctionRepository;
import lu.fujitsu.ts.cgdis.portal.core.domain.ICGDISPortalUserDetails;
import lu.fujitsu.ts.cgdis.portal.core.domain.OperationalFunction;
import lu.fujitsu.ts.cgdis.portal.services.entity.IOperationalFunctionRepositoryService;
import lu.fujitsu.ts.eportal.server.connector.jpa.specifications.secured.AbstractSecuredSpecificationBuilder;
import org.dozer.Mapper;
import org.springframework.stereotype.Service;

@Service
public class OperationalFunctionRepositoryService extends AbstractBaseCGDISSecuredRepositoryService<OperationalFunction, OperationalFunctionDbo, IOperationalFunctionRepository> implements IOperationalFunctionRepositoryService {

    public OperationalFunctionRepositoryService(IOperationalFunctionRepository repository, AbstractSecuredSpecificationBuilder<ICGDISPortalUserDetails, OperationalFunctionDbo> specBuilder, Mapper mapper ) {
        super("Operational Function", OperationalFunction.class, OperationalFunctionDbo.class, repository, specBuilder, mapper);
    }

    @Override

    public boolean canModify(ICGDISPortalUserDetails icgdisPortalUserDetails, Long aLong) {
        return false;
    }
}
