package lu.fujitsu.ts.cgdis.portal.connector.portaldb.specifications;

import lu.fujitsu.ts.cgdis.portal.connector.portaldb.dbos.person.OperationalOccupationDbo;
import lu.fujitsu.ts.cgdis.portal.core.domain.ICGDISPortalUserDetails;
import lu.fujitsu.ts.eportal.server.connector.jpa.specifications.secured.AbstractSecuredSpecificationBuilder;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

/**
 * The type operational function specification builder.
 */
@Service
public class OperationalOccupationSpecificationBuilder extends AbstractSecuredSpecificationBuilder<ICGDISPortalUserDetails, OperationalOccupationDbo> {

    @Override
    public Specification<OperationalOccupationDbo> buildSecurityConstraint(ICGDISPortalUserDetails icgdisPortalUserDetails) {
        return (root, criteriaQuery, criteriaBuilder) -> criteriaBuilder.conjunction();
    }

}


