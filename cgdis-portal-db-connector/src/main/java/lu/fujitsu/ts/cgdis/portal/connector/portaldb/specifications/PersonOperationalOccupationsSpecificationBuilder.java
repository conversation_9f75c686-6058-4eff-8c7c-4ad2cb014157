package lu.fujitsu.ts.cgdis.portal.connector.portaldb.specifications;

import lu.fujitsu.ts.cgdis.portal.connector.portaldb.dbos.person.PersonOperationalOccupationDbo;
import lu.fujitsu.ts.cgdis.portal.core.domain.ICGDISPortalUserDetails;
import lu.fujitsu.ts.eportal.server.connector.jpa.specifications.SpecificationUtils;
import lu.fujitsu.ts.eportal.server.connector.jpa.specifications.secured.AbstractComposedSecuredSpecificationBuilder;
import lu.fujitsu.ts.eportal.server.core.domain.SearchCriterion;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

/**
 * The type Person operational occupations specification builder.
 */
@Service
public class PersonOperationalOccupationsSpecificationBuilder extends AbstractComposedSecuredSpecificationBuilder<ICGDISPortalUserDetails, PersonOperationalOccupationDbo> {

    public PersonOperationalOccupationsSpecificationBuilder() {
        super();
        addBuilder("code", this::codeSpecificationBuilder);
        addBuilder("type", this::typeSpecificationBuilder);
        addBuilder("label", this::labelSpecificationBuilder);
    }

    @Override
    public Specification<PersonOperationalOccupationDbo> buildSecurityConstraint(ICGDISPortalUserDetails icgdisPortalUserDetails) {
        return (root, criteriaQuery, criteriaBuilder) -> criteriaBuilder.conjunction();
    }

    private Specification<PersonOperationalOccupationDbo> codeSpecificationBuilder(SearchCriterion criterion) {
        return (root, criteriaQuery, criteriaBuilder) -> SpecificationUtils.buildPredicate(root.get("operationalOccupation").get("code"), criterion, criteriaBuilder);
    }

    private Specification<PersonOperationalOccupationDbo> typeSpecificationBuilder(SearchCriterion criterion) {
        return (root, criteriaQuery, criteriaBuilder) -> SpecificationUtils.buildPredicate(root.get("operationalOccupation").get("type"), criterion, criteriaBuilder);
    }

    private Specification<PersonOperationalOccupationDbo> labelSpecificationBuilder(SearchCriterion criterion) {
        return (root, criteriaQuery, criteriaBuilder) -> SpecificationUtils.buildPredicate(root.get("operationalOccupation").get("label"), criterion, criteriaBuilder);
    }
}
