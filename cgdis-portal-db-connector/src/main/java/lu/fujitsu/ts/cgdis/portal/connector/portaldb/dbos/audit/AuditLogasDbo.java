package lu.fujitsu.ts.cgdis.portal.connector.portaldb.dbos.audit;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;
import javax.persistence.SecondaryTable;

@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@SecondaryTable(name = "AUDIT_LOGAS")
@DiscriminatorValue("LOGAS")
public class AuditLogasDbo extends AuditDbo {

    /**
     * The Prestation tecid.
     */
    @Column(table = "AUDIT_LOGAS", name = "ORIGINAL_USER_NAME")
    private String originalUserName;

    @Column(table = "AUDIT_LOGAS", name = "IMPERSONATED_USER_NAME")
    private String impersonatedUserName;

    @Column(table = "AUDIT_LOGAS", name = "SESSION_ID")
    private String sessionId;
}
