<?xml version="1.0" encoding="UTF-8"?>
<mappings xmlns="http://dozer.sourceforge.net"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://dozer.sourceforge.net
          http://dozer.sourceforge.net/schema/beanmapping.xsd">
    <mapping type="one-way">
        <class-a>lu.fujitsu.ts.cgdis.portal.connector.portaldb.dbos.audit.AuditPermServicePlanCategoryDbo</class-a>
        <class-b>lu.fujitsu.ts.cgdis.portal.core.domain.audit.AuditPermServicePlanCategory</class-b>
        <field>
            <a>deploymentPlan.name</a>
            <b>deploymentPlanName</b>
        </field>
    </mapping>


</mappings>
