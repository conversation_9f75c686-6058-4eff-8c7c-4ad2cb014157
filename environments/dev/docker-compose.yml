version: '2.0'
services:
  openldap:
    image: bitnami/openldap:2
    ports:
      - '1389:1389'
      - '1636:1636'
    environment:
      - LDAP_ADMIN_USERNAME=jcv
      - LDAP_ADMIN_PASSWORD=Welcome1*
      - LDAP_CUSTOM_LDIF_DIR=/bitnami/openldap/ldifs
      - LDAP_ROOT=dc=dom,dc=ad,DC=FUJITSU
    volumes:
      - './openldap/ldifs:/bitnami/openldap/ldifs'
  db:
    build:
      context: ./DB
      dockerfile: Dockerfile-db
    env_file: environment.properties
    volumes:
      - './DB:/DB'
    ports:
      - '2206:3306'
  kafka:
    build:
      context: ./KAFKA
      dockerfile: Dockerfile
    env_file: environment.properties
    ports:
      - '9092:9092'
      - '2181:2181'
  mocks:
    build:
      context: ./MOCKS
    volumes:
      - './MOCKS/_files/:/wiremock/__files/'
      - './MOCKS/mappings/:/wiremock/mappings/'
    ports:
      - '8001:8080'
  mail:
    build:
      context: ./MAIL
    env_file: environment.properties
    ports:
      - '8025:8025'
      - '1025:1025'
  admin:
    image: slydeveloper/spring-boot-admin
    environment:
      - SPRING_BOOT_ADMIN_TITLE=Fujitsu Spring boot admin
      - SPRING_BOOT_ADMIN_USER_NAME=Fujitsu
      - SPRING_BOOT_ADMIN_USER_PASSWORD=Welcome1*
      - SPRING_BOOT_ADMIN_SECURITY_ENABLED=true
    ports:
      - '2222:1111'
    container_name: spring_boot_admin_docker
  redis:
    image: redis:latest
    ports:
      - '6379:6379'
