{"request": {"method": "PUT", "urlPathPattern": "/els/api/0/updatePerson"}, "response": {"status": 200, "headers": {"Content-Type": "application/json"}, "jsonBody": {"ForeignKey": "@TEST@1", "TypeKey": "1", "Name": "<PERSON><PERSON><PERSON><PERSON>", "Additional": "usercgdis", "LocationName": "Mout<PERSON>", "StreetName": "Atwood Pass", "HouseNumberFrom": "51", "HouseNumberTo": null, "PostalCode": "L-5330", "Gender": 0, "Birthday": "1981-12-27"}}}