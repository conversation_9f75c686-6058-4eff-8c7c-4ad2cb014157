FROM jcvercucque/portal:1.0.0
ARG JAR_FILE
ARG BUILD_NUMBER
ARG APP_NAME
ARG APP_VERSION
ARG JAVA_AWT_HEADLESS=false
ENV PROFILES=int
ENV HEAP_SIZE=256m
ENV TZ=Europe/Luxembourg
ENV APP_INST_NAME=${APP_NAME}
RUN apk update && \
    apk upgrade
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone
RUN mkdir /logs
ADD ${JAR_FILE} app.jar
RUN echo "AppName : ${APP_NAME}" > /information.txt &&  echo "Deployed Jar : ${JAR_FILE}" >> /information.txt && echo "Build Number:${BUILD_NUMBER}" >> /information.txt && echo "Application Version: ${APP_VERSION}" >> /information.txt
EXPOSE 8080
EXPOSE 9010
ENTRYPOINT ["java","-Dcom.sun.management.jmxremote","-Dcom.sun.management.jmxremote.local.only=false","-Dcom.sun.management.jmxremote.authenticate=false","-Dcom.sun.management.jmxremote.ssl=false","-Djava.rmi.server.hostname=************","-Dcom.sun.management.jmxremote.port=9010","-Dcom.sun.management.jmxremote.rmi.port=9010","-Djava.security.egd=file:/dev/./urandom","-Dapplication.name=${APP_INST_NAME}","-Dapplication.version=${APP_VERSION}","-Djava.awt.headless=true","-Dspring.profiles.active=${PROFILES}","-Dlogging.file=/logs/${APP_INST_NAME}.log", "-Dlogging.path=/logs","-jar","/app.jar"]
