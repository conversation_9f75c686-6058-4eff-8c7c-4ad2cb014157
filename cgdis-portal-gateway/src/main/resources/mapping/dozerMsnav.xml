<?xml version="1.0" encoding="UTF-8"?>
<mappings xmlns="http://dozer.sourceforge.net"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://dozer.sourceforge.net
          http://dozer.sourceforge.net/schema/beanmapping.xsd">

    <mapping wildcard="false">
        <class-a>lu.fujitsu.ts.cgdis.portal.esb.commons.domain.msnav.UpdateOperationalContactInformationsRequest</class-a>
        <class-b>schemas.dynamics.microsoft.codeunit.operationalcontactinformation.UpdateOperationalContactInformation</class-b>

      <field>
          <a>tic</a>
          <b>tic</b>
      </field>

        <field>
          <a>generalContactInformations.ric</a>
          <b>pager</b>
      </field>

      <field>
        <a>generalContactInformations.ricMobile</a>
        <b>pagerMobile</b>
      </field>

        <field>
          <a>generalContactInformations.privatePhoneNumber</a>
          <b>privatephonenumber</b>
      </field>

        <field>
          <a>generalContactInformations.professionalPhoneNumber</a>
          <b>professionalphonenumber</b>
      </field>

        <field>
          <a>generalContactInformations.privateMobileNumber</a>
          <b>privatemobilenumber</b>
      </field>

        <field>
          <a>generalContactInformations.professionalMobileNumber</a>
          <b>professionalmobilenumber</b>
      </field>

        <field>
          <a>generalContactInformations.privateEmail</a>
          <b>privateemail</b>
      </field>

        <field>
          <a>generalContactInformations.professionalEmail</a>
          <b>professionalemail</b>
      </field>

      <field>
        <a>generalContactInformations.emailPrive</a>
        <b>emailprive</b>
      </field>

      <field>
        <a>generalContactInformations.emailProfessional</a>
        <b>emailprofessionnel</b>
      </field>

    </mapping>




</mappings>
