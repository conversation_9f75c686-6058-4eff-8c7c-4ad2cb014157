package lu.fujitsu.ts.cgdis.portal.gateway.ext.els.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lu.fujitsu.ts.cgdis.portal.gateway.ext.els.converter.ElsLocalDateDeserializer;
import lu.fujitsu.ts.cgdis.portal.gateway.ext.els.converter.ElsLocalDateSerializer;

import java.time.LocalDate;

/**
 * This is a POJO class which represents an ActionForce in ELS
 * <p>
 * The equivalent of an ActionForce is a {@link lu.fujitsu.ts.cgdis.portal.core.domain.person.Person}
 */
//Add the @JsonIgnoreProperties for not being hardly impacted by a change in SwissPhone object 
//This annotation indicates that if we receive from JSON a property that not belongs to the actual and physical properties of the Java Object we will not 
//throw a MappingJsonException
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class ActionForce {


    /**
     * The foreign key of the action force based on the person tecid
     */
    @JsonProperty("ForeignKey")
    private String foreignKey;

    /**
     * The key of the type of the action force.
     */
    @JsonProperty("TypeKey")
    private String typeKey ;

    /**
     * The name of the action force.
     */
    @JsonProperty("Name")
    private String name;

    /**
     * Additional text to the action force.
     */
    @JsonProperty("Additional")
    private String additional;

    /**
     * The name of the location of the action force.
     */
    @JsonProperty("LocationName")
    private String locationName = "Luxembourg Land";

    @JsonProperty("LocationForeignKey")
    private String locationForeignKey="UN442";

    /**
     * The gender of the action force: 0 = male, 1 = female.
     */
    @JsonProperty("Gender")
    private int gender;

    /**
     * The birthday of the action force
     */
    @JsonProperty("Birthday")
    @JsonSerialize(using = ElsLocalDateSerializer.class)
    @JsonDeserialize(using = ElsLocalDateDeserializer.class)
    private LocalDate birthday;


}
