package lu.fujitsu.ts.cgdis.portal.gateway.ext.els.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * This is a POJO class which represents an ActionForceQualifications in ELS
 */
@Data
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class ActionForceQualifications {


    /**
     * The action force foreign key
     */
    @JsonProperty("ActionForceForeignKey")
    private String actionForceForeignKey;

    /**
     * List of qualifications
     */
    @JsonProperty("Qualifications")
    private List<ActionForceQualification> qualifications;

}
