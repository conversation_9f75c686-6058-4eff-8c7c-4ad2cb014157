package lu.fujitsu.ts.cgdis.portal.gateway.ext.els.converter;

import lu.fujitsu.ts.cgdis.portal.core.domain.person.DrivingLicense;
import lu.fujitsu.ts.cgdis.portal.gateway.ext.els.config.properties.ElsProperties;
import lu.fujitsu.ts.cgdis.portal.gateway.ext.els.domain.ActionForce;
import lu.fujitsu.ts.cgdis.portal.gateway.ext.els.domain.ActionForceQualification;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.stream.Collectors;

/**
 * The type Action force qualification converter.
 */
public class ActionForceQualificationConverter {

    /**
     * The constant LOGGER.
     */
    private static final Logger LOGGER = LoggerFactory.getLogger(ActionForceQualificationConverter.class);

    /**
     * The ELS properties.
     */
    private ElsProperties elsProperties;

    /**
     * Instantiates a new Action force qualification converter.
     * @param elsProperties the els properties
     */
    @Autowired
    public ActionForceQualificationConverter(ElsProperties elsProperties) {
        this.elsProperties = elsProperties;
    }

    /**
     * Convert list.
     * @param drivingLicenses the driving licenses
     * @param actionForce the action force
     * @return the list
     */
    public List<ActionForceQualification> convert(List<DrivingLicense> drivingLicenses, ActionForce actionForce) {
        LOGGER.debug("Convert list of driving licenses to list of action force qualification");
        return drivingLicenses.stream()
                .map(drivingLicense -> {
                    ActionForceQualification qualification = new ActionForceQualification();
                    qualification.setActionForceForeignKey(actionForce.getForeignKey());
                    qualification.setQualificationTypeForeignKey(generateKeyForQualificationType(drivingLicense));
                    qualification.setQualificationTypeName(drivingLicense.getCategory());
                    return qualification;
                })
                .collect(Collectors.toList());
    }

    /**
     * Generate key for a qualification type.
     * @param drivingLicense the driving license
     * @return the string
     */
    private String generateKeyForQualificationType(DrivingLicense drivingLicense) {
        String key = this.elsProperties.getQualificationTypeForeignKeyPrefix() + drivingLicense.getTecid();
        LOGGER.debug("Qualification type key generated: {}", key);
        return key;
    }

}
