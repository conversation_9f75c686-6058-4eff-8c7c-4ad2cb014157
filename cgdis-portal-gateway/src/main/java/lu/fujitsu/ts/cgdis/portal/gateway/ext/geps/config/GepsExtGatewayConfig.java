package lu.fujitsu.ts.cgdis.portal.gateway.ext.geps.config;

import lu.fujitsu.ts.cgdis.portal.core.config.CoreConfig;
import lu.fujitsu.ts.cgdis.portal.esb.commons.kafka.KafkaAdminConfig;
import lu.fujitsu.ts.cgdis.portal.esb.commons.kafka.properties.KafkaTopicsProperties;
import lu.fujitsu.ts.cgdis.portal.gateway.ext.geps.config.properties.GepsGatewayProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;

@Configuration
@EnableConfigurationProperties({
        KafkaTopicsProperties.class,
        GepsGatewayProperties.class
})

@Import({KafkaAdminConfig.class, GepsKafkaConfig.class, CoreConfig.class, GepsWebServiceGatewayConvertersConfig.class, GepsWebServiceGatewayConfig.class})
@EnableKafka
@ComponentScan({
        "lu.fujitsu.ts.cgdis.portal.gateway.ext.geps.endpoint",
        "lu.fujitsu.ts.cgdis.portal.gateway.ext.geps.service",
        "lu.fujitsu.ts.cgdis.portal.gateway.ext.geps.interceptor"
})

public class GepsExtGatewayConfig extends WebSecurityConfigurerAdapter {
    @Override
    protected void configure(HttpSecurity http) throws Exception {
        http.authorizeRequests()
                .antMatchers("/9nyNXgcGpNGr2BerMsrA7NmhcqRNHqdubAV4TvVqs8JPVLFEa7/**/*").authenticated()
                .antMatchers("/ws","/ws/**/*").permitAll()
                .and()
                .formLogin().and().httpBasic();
        http.csrf().ignoringAntMatchers("/ws","/ws/**/*","/9nyNXgcGpNGr2BerMsrA7NmhcqRNHqdubAV4TvVqs8JPVLFEa7/**/*");
    }

}
