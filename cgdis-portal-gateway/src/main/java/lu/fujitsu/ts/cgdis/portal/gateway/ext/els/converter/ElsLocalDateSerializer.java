package lu.fujitsu.ts.cgdis.portal.gateway.ext.els.converter;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.ser.std.StdSerializer;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.IOException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

public class ElsLocalDateSerializer extends StdSerializer<LocalDate> {

    private DateTimeFormatter formatter;

    @Autowired
    public ElsLocalDateSerializer() {
        super(LocalDate.class);
        formatter = DateTimeFormatter.ISO_DATE;
    }

    @Override
    public void serialize(LocalDate value, JsonGenerator gen, SerializerProvider provider) throws IOException {

        String dateModel = null;
        if (value != null) {
            dateModel = value.format(formatter);
        }
        gen.writeObject(dateModel);
    }
}