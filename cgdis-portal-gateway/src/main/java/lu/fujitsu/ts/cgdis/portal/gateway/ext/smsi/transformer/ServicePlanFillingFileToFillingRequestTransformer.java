package lu.fujitsu.ts.cgdis.portal.gateway.ext.smsi.transformer;

import lu.fujitsu.ts.cgdis.portal.core.exception.CGDISTechnicalException;
import lu.fujitsu.ts.cgdis.portal.esb.commons.domain.smsi.ServicePlanFillingRequest;
import lu.fujitsu.ts.eportal.server.core.utils.EPortalConverter;
import lu.fujitsu.ts.eportal.server.core.utils.IEPortalConverter;
import org.dozer.Mapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.integration.transformer.GenericTransformer;
import org.springframework.messaging.Message;
import org.springframework.util.MultiValueMap;
import org.springframework.web.multipart.MultipartFile;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.Arrays;
import java.util.List;

public class ServicePlanFillingFileToFillingRequestTransformer implements GenericTransformer<Message<MultiValueMap<String, ?>>, ServicePlanFillingRequest> {

    private static final Logger LOGGER = LoggerFactory.getLogger(ServicePlanFillingFileToFillingRequestTransformer.class);
    private IEPortalConverter<String, ServicePlanFillingRequest> converter;

    /**
     * Instantiates a new UpdateVehicleStateToUpdateRequestTransformer.
     *
     * @param mapper the mapper
     */
    public ServicePlanFillingFileToFillingRequestTransformer(Mapper mapper) {
        this.converter = new EPortalConverter<>(String.class, ServicePlanFillingRequest.class, mapper);
    }

    @Override
    public ServicePlanFillingRequest transform(Message<MultiValueMap<String, ?>> servicePlanFillingMessage) {
        ServicePlanFillingRequest request = new ServicePlanFillingRequest();
        MultipartFile file = (MultipartFile) servicePlanFillingMessage.getPayload().getFirst("file");

        try (BufferedReader reader = new BufferedReader(new InputStreamReader(file.getInputStream()))) {

            String line = reader.readLine();
            if ( line != null) {
                List<String> matricules = Arrays.asList(line.split(";"));
                request.setMatricules(matricules);
            }

        } catch (IOException e) {
            throw new CGDISTechnicalException("Error to parse csv file", e);
        }

        return request;
    }
}
