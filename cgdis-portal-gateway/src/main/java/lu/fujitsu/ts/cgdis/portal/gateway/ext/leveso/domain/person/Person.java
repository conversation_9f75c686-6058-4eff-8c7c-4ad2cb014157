package lu.fujitsu.ts.cgdis.portal.gateway.ext.leveso.domain.person;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class Person {

    @JsonProperty("id")
    private Integer id;

    @JsonProperty("firstName")
    private String firstName;

    @JsonProperty("lastName")
    private String lastName;

    @JsonProperty("academicDegree")
    private String academcDegree;

    @JsonProperty("gender")
    private GenderType gender;

    @JsonProperty("birthday")
    private LocalDateTime birthday;

    @JsonProperty("birthplace")
    private String birthPlace;

    @JsonProperty("number1")
    private String number1;

    @JsonProperty("number2")
    private String number2;

    @JsonProperty("number3")
    private String number3;

    @JsonProperty("number4")
    private String number4;

    @JsonProperty("phone")
    private String phone;

    @JsonProperty("phone2")
    private String phone2;

    @JsonProperty("mobilePhone")
    private String mobilePhone;

    @JsonProperty("mobilePhone2")
    private String mobilePhone2;

    @JsonProperty("email")
    private String email;

    @JsonProperty("email2")
    private String email2;

    @JsonProperty("addressGeneral")
    private String addressGeneral;

    @JsonProperty("addressSuffix")
    private String addressSuffix;

    @JsonProperty("streetName")
    private String streetName;

    @JsonProperty("streetNumber")
    private String streetNumber;

    @JsonProperty("postalCode")
    private String postalCode;

    @JsonProperty("poBox")
    private String poBox;

    @JsonProperty("town")
    private String town;

    @JsonProperty("townSection")
    private String townSection;


    @JsonProperty("county")
    private String county;

    @JsonProperty("countryIso2")
    private String countryIso2;

    @JsonProperty("stateIso")
    private String stateIso;

    @JsonProperty("bankAccounts")
    private List<BankAccount> bankAccounts;

    @JsonProperty("memberships")
    private List<Membership> memberships;

    @JsonProperty("diplomas")
    private List<Diploma> diplomas;
}
