package lu.fujitsu.ts.cgdis.portal.gateway.ext.geps.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import lu.fujitsu.ts.cgdis.portal.core.domain.error.ErrorMessageType;
import lu.fujitsu.ts.cgdis.portal.esb.commons.domain.geps.UpdatePersonRequest;
import lu.fujitsu.ts.cgdis.portal.esb.commons.error.ErrorMessageStrategy;
import lu.fujitsu.ts.cgdis.portal.esb.commons.error.IObjectIdentifierExtractor;
import lu.fujitsu.ts.cgdis.portal.esb.commons.kafka.KafkaAdminConfig;
import lu.fujitsu.ts.cgdis.portal.esb.commons.kafka.KafkaGroups;
import lu.fujitsu.ts.cgdis.portal.esb.commons.kafka.KafkaTopics;
import lu.fujitsu.ts.cgdis.portal.gateway.ext.geps.interceptor.GepsHeaderInterceptor;
import lu.fujitsu.ts.cgdis.portal.gateway.ext.geps.service.UpdatePersonService;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.servlet.ServletRegistrationBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ClassPathResource;
import org.springframework.integration.dsl.IntegrationFlow;
import org.springframework.integration.dsl.IntegrationFlows;
import org.springframework.integration.handler.LoggingHandler;
import org.springframework.integration.kafka.dsl.Kafka;
import org.springframework.integration.kafka.inbound.KafkaMessageDrivenChannelAdapter.ListenerMode;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.config.KafkaListenerContainerFactory;
import org.springframework.kafka.core.ConsumerFactory;
import org.springframework.kafka.core.DefaultKafkaConsumerFactory;
import org.springframework.kafka.listener.ConcurrentMessageListenerContainer;
import org.springframework.kafka.support.serializer.JsonDeserializer;
import org.springframework.messaging.MessageHeaders;
import org.springframework.oxm.jaxb.Jaxb2Marshaller;
import org.springframework.ws.client.core.WebServiceTemplate;
import org.springframework.ws.client.support.interceptor.ClientInterceptor;
import org.springframework.ws.config.annotation.EnableWs;
import org.springframework.ws.soap.SoapVersion;
import org.springframework.ws.soap.saaj.SaajSoapMessageFactory;
import org.springframework.ws.transport.http.MessageDispatcherServlet;
import org.springframework.ws.wsdl.wsdl11.DefaultWsdl11Definition;
import org.springframework.xml.xsd.SimpleXsdSchema;
import org.springframework.xml.xsd.XsdSchema;

import java.util.HashMap;
import java.util.Map;

/**
 * The type Geps web service gateway config.
 */
@EnableWs
@Configuration
public class GepsWebServiceGatewayConfig {

    /**
     * The constant GEPS_INPUT_WS_NAMESPACE_URI.
     */
    public static final String GEPS_INPUT_WS_NAMESPACE_URI = "http://ws.ass.mi.cie.etat.lu/MedicoSharing/";
    /**
     * The Bootstrap servers.
     */
    @Value("${spring.kafka.bootstrap-servers}")
    private String bootstrapServers;
    /**
     * The Mapper.
     */
    @Autowired
    private ObjectMapper mapper;
    /**
     * The Update person service.
     */
    @Autowired
    private UpdatePersonService updatePersonService;




    /**
     * Marshaller jaxb 2 marshaller.
     *
     * @return the jaxb 2 marshaller
     */
    @Bean
    public Jaxb2Marshaller marshaller() {
        Jaxb2Marshaller marshaller = new Jaxb2Marshaller();
        // this package must match the package in the <generatePackage> specified in
        // pom.xml
        marshaller.setCheckForXmlRootElement(false);
        marshaller.setContextPath("lu.fujitsu.ts.cgdis.portal.ws.geps.push");
        return marshaller;
    }


    /**
     * Person information push flow integration flow.
     *
     * @return the integration flow
     */
    @Bean
    public IntegrationFlow personInformationPushFlow() {
        return IntegrationFlows
                .from(
                        Kafka.messageDrivenChannelAdapter(
                                consumerFactory(), ListenerMode.record, KafkaTopics.GEPS_PERSON_UPDATE_PUSH_TOPIC
                        )
                                .errorMessageStrategy(new ErrorMessageStrategy(ErrorMessageType.UPDATE_PERSON_GEPS, mapper, pushPerson2GepsIdentifierExtractor()))
                                .errorChannel(KafkaAdminConfig.ERROR_MESSAGE_CHANNEL)
                )
                .log(LoggingHandler.Level.INFO, message->String.format("Execute message with offset %s and id %s", message.getHeaders().getOrDefault("kafka_offset", "0000"), message.getHeaders().getId()) )
                .handle(updatePersonService, "send")
                .get();
    }

    /**
     * Push person 2 geps identifier extractor object identifier extractor.
     *
     * @return the object identifier extractor
     */
    @Bean
    public IObjectIdentifierExtractor pushPerson2GepsIdentifierExtractor() {
        return new IObjectIdentifierExtractor() {
            @Override
            public <T> String getObjectIdentifier(T payload, MessageHeaders headers) {
                if (payload instanceof UpdatePersonRequest) {
                    return ((UpdatePersonRequest) payload).getTecid().toString();
                }
                return null;
            }
        };
    }

    /**
     * Kafka listener container factory kafka listener container factory.
     *
     * @return the kafka listener container factory
     */
    @Bean
    public KafkaListenerContainerFactory<ConcurrentMessageListenerContainer<String, UpdatePersonRequest>> kafkaListenerContainerFactory() {
        ConcurrentKafkaListenerContainerFactory<String, UpdatePersonRequest> factory = new ConcurrentKafkaListenerContainerFactory<>();
        factory.setConsumerFactory(consumerFactory());
        factory.setConcurrency(2);
        factory.getContainerProperties().setPollTimeout(KafkaAdminConfig.DEFAULT_POLL_TIMEOUT);
        return factory;
    }

    /**
     * Consumer factory consumer factory.
     *
     * @return the consumer factory
     */
    @Bean
    public ConsumerFactory<String, UpdatePersonRequest> consumerFactory() {
        return new DefaultKafkaConsumerFactory<>(consumerConfigs(), new StringDeserializer(), new JsonDeserializer<>(UpdatePersonRequest.class, mapper));
    }

    /**
     * Consumer configs map.
     *
     * @return the map
     */
    @Bean
    public Map<String, Object> consumerConfigs() {
        Map<String, Object> props = new HashMap<>();
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);
        props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, JsonDeserializer.class);
        props.put(ConsumerConfig.GROUP_ID_CONFIG, KafkaGroups.GEPS);
        props.put(ConsumerConfig.HEARTBEAT_INTERVAL_MS_CONFIG,KafkaAdminConfig.DEFAULT_HEARTBEAT_INTERVAL_MS_CONFIG);
        props.put(ConsumerConfig.SESSION_TIMEOUT_MS_CONFIG,KafkaAdminConfig.DEFAULT_SESSION_TIMEOUT_MS_CONFIG);
        props.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG,KafkaAdminConfig.DEFAULT_MAX_POLL_RECORDS_CONFIG);
        return props;
    }

    /**
     * Soap message factory saaj soap message factory.
     *
     * @return the saaj soap message factory
     */
    @Bean
    public SaajSoapMessageFactory soapMessageFactory() {
        SaajSoapMessageFactory soapMessageFactory = new SaajSoapMessageFactory();
        soapMessageFactory.setSoapVersion(SoapVersion.SOAP_11);
        return soapMessageFactory;
    }


    /**
     * Web service template web service template.
     *
     * @return the web service template
     */
    @Bean
    @Autowired
    public WebServiceTemplate webServiceTemplate( @Value("${cgdis-portal.gateway.geps.url}") String gepsUrl) {
        WebServiceTemplate webServiceTemplate = new WebServiceTemplate();
        webServiceTemplate.setDefaultUri(gepsUrl);
        webServiceTemplate.setMarshaller(marshaller());
        webServiceTemplate.setUnmarshaller(marshaller());
        webServiceTemplate.setInterceptors(new ClientInterceptor[]{headerInterceptor()});

        return webServiceTemplate;
    }
    @Bean
    public ClientInterceptor headerInterceptor(){
        return new GepsHeaderInterceptor();
    };


    /**
     * Message dispatcher servlet servlet registration bean.
     *
     * @param applicationContext the application context
     * @return the servlet registration bean
     */
    @Bean
    public ServletRegistrationBean messageDispatcherServlet(ApplicationContext applicationContext) {
        MessageDispatcherServlet servlet = new MessageDispatcherServlet();
        servlet.setApplicationContext(applicationContext);
        servlet.setTransformWsdlLocations(true);
        return new ServletRegistrationBean<>(servlet, "/ws/*");
    }

    /**
     * Input aptitudes and restrictions wsdl definition default wsdl 11 definition.
     *
     * @return the default wsdl 11 definition
     */
    @Bean
    public DefaultWsdl11Definition inputAptitudesAndRestrictionsWSDLDefinition() {
        DefaultWsdl11Definition wsdl11Definition = new DefaultWsdl11Definition();
        wsdl11Definition.setPortTypeName("MedicoSharing");
        wsdl11Definition.setLocationUri("/ws/provideMedicoIntranet");
        wsdl11Definition.setTargetNamespace(GEPS_INPUT_WS_NAMESPACE_URI);
        wsdl11Definition.setSchema(inputAptitudesAndRestrictionsWSSchema());
        return wsdl11Definition;
    }

    /**
     * Input aptitudes and restrictions ws schema xsd schema.
     *
     * @return the xsd schema
     */
    @Bean
    public XsdSchema inputAptitudesAndRestrictionsWSSchema() {
        return new SimpleXsdSchema(new ClassPathResource("xsds/input/MedicoSharing.xsd"));
    }


}
