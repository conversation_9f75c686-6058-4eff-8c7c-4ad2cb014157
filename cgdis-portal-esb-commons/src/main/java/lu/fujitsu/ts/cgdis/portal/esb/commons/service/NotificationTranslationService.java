package lu.fujitsu.ts.cgdis.portal.esb.commons.service;


import lu.fujitsu.ts.cgdis.portal.core.domain.translation.Translation;
import lu.fujitsu.ts.cgdis.portal.esb.commons.email.INotificationTranslationService;
import lu.fujitsu.ts.cgdis.portal.services.translation.ITranslationNotSecuredRepositoryService;
import lu.fujitsu.ts.cgdis.portal.services.translation.ITranslationRepositoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class NotificationTranslationService implements INotificationTranslationService {

    List<Translation> translations;

    @Autowired
    public NotificationTranslationService(ITranslationNotSecuredRepositoryService translationRepositoryService) {
        this.translations = translationRepositoryService.getAll();
    }

    public String getTraductionByLangageAndKey(String lang, String key){
        String finalTranslation = null;
        for (Translation translation : this.translations){
            if (lang.equals(translation.getLanguage()) && key.equals(translation.getTranslationKey())){
                finalTranslation = translation.getTranslation();
            }
        }
        if (finalTranslation != null){
            return finalTranslation;
        } else {
            if ("fr".equals(lang)){
                return null;
            } else {
                return this.getTraductionByLangageAndKey("fr", key);
            }
        }
    }
}
