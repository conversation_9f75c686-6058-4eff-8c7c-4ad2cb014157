package lu.fujitsu.ts.cgdis.portal.esb.commons.domain.els.slots;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Builder;
import lombok.Data;
import lombok.extern.jackson.Jacksonized;
import lu.fujitsu.ts.cgdis.portal.core.utils.json.LocalDatetimeDeserializer;
import lu.fujitsu.ts.cgdis.portal.core.utils.json.LocalDatetimeSerializer;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@Jacksonized
@Builder
public class GetSlotsParameters implements Serializable {

    private String servicePlanLabel;
    @JsonSerialize(using = LocalDatetimeSerializer.class)
    @JsonDeserialize(using = LocalDatetimeDeserializer.class)
    private LocalDateTime startDateTime;
    private Integer nbSlots;
}
