package lu.fujitsu.ts.cgdis.portal.esb.commons.domain.els;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;
import lu.fujitsu.ts.cgdis.portal.core.utils.json.LocalDatetimeDeserializer;
import lu.fujitsu.ts.cgdis.portal.core.utils.json.LocalDatetimeSerializer;

import java.time.LocalDateTime;

/**
 * The type Els vehicle state input request.
 */
@Data
public class ElsVehicleStateInputRequest {


    /**
     * The Update datetime.
     */
    @JsonDeserialize(using = LocalDatetimeDeserializer.class)
    @JsonSerialize(using = LocalDatetimeSerializer.class)
    private LocalDateTime date;

    /**
     * The vehicle tec id
     */
    private String vehicleId;


    /**
     * The tactical state
     */
    private String state;

    /**
     * The From user.
     */
    private String fromUser;




}
