package lu.fujitsu.ts.cgdis.portal.esb.commons.domain.luxdok;

import lombok.Data;

import java.time.LocalDate;
import java.time.LocalTime;

/**
 * The class Vehicle information
 */
@Data
public class VehicleInformation {

    /**
     * The crew
     */
    private CrewInformation crew;

    /**
     * The radio call sign
     */
    private String vehCallName;

    /**
     * The vehicle type
     */
    private String vehType;

    /**
     * The entity of the vehicle
     */
    private String vehStationId;

    /**
     * The entity name
     */
    private String vehStationStr;

    /**
     * The vehicle mission number
     */
    private String vehMissionNbr;

    /**
     * The vehicle alarm date
     */
    private LocalDate vehAlarmD;

    /**
     * The vehicle alarm time
     */
    private LocalTime vehAlarmT;

    /**
     * The vehicle start date
     */
    private LocalDate vehStartD;

    /**
     * The vehicle start time
     */
    private LocalTime vehStartT;

    /**
     * The vehicle end date
     */
    private LocalDate vehEndD;

    /**
     * The vehicle end time
     */
    private LocalTime vehEndT;

}
