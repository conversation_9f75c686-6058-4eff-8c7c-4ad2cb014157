package lu.fujitsu.ts.cgdis.portal.esb.commons.domain.leveso;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * The type Diplomas request.
 */
@Data
@NoArgsConstructor
public class DiplomasRequest {

    /**
     * The Diplomas.
     */
    private List<DiplomaRequestItem> diplomas;
    /**
     * The Diplomas to remove.
     */
    private List<DiplomaToRemoveItem> diplomasToRemove;
    /**
     * The User diplomas.
     */
    private List<UserDiplomaRequestItem> userDiplomas;
    /**
     * The User diplomas to remove.
     */
    private List<UserDiplomaToRemoveItem> userDiplomasToRemove;
}
