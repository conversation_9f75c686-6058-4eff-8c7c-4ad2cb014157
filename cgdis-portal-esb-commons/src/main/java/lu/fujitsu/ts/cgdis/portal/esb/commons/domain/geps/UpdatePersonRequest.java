package lu.fujitsu.ts.cgdis.portal.esb.commons.domain.geps;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lu.fujitsu.ts.cgdis.portal.core.domain.CGDISEntity;
import lu.fujitsu.ts.cgdis.portal.core.domain.person.PersonWithAddressAndContacts;

import java.time.LocalDate;

/**
 * The type Update person request.
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class UpdatePersonRequest extends PersonWithAddressAndContacts {

    /**
     * The Primary assignment entity.
     */
    private CGDISEntity primaryAssignmentEntity;

    /**
     * The Primary assignment startdate.
     */
    private LocalDate primaryAssignmentStartdate;

    /**
     * The National registration number.
     */
    private String nationalRegistrationNumber;


    /**
     * The Suspension.
     */
    private UpdatePersonSuspensionPeriodRequest suspension;



}
